<template>
    <div style="background:#F0F0F0;min-height:100%">
        <van-tabs v-model="activeName" sticky>
            <van-tab title="灌装" name="灌装">
                <div v-for="(item, index) in colloidList" :key="index"
                    style="text-align:left;padding:5px;margin:5px;background:#FFF;" @click="cancelPush(item)">
                    <div style="width:100%">
                        <div style="float:left;font-size:16px;font-weight:800;width:70%;color:green;">{{ item.tankNo }}
                        </div>
                        <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:orange"
                            v-if="item.status == '2'">灌装中</div>
                        <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:gray"
                            v-if="item.status == '3'">灌装完成</div>
                        <div style="clear:both"></div>
                    </div>
                    <div style="background:#f0f0f0;height:1px;width:100%"></div>
                    <div style="width:100%;font-size:14px;color:#888888">胶体编号：{{ item.code }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">胶体名称：{{ item.name }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">胶体批次：{{ item.customer }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">胶体净重：{{ item.volume }}KG</div>
                    <div style="width:100%;font-size:14px;color:#888888">拉料员工：{{ item.pullUser }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">灌装员工：{{ item.acceptUser }}</div>
                    <div style="clear:both"></div>
                </div>
            </van-tab>
            <van-tab title="备料" name="备料">
                <div v-for="(item, index) in materialsList" :key="index"
                    style="text-align:left;padding:5px;margin:5px;background:#FFF;">
                    <div style="background:#f0f0f0;height:1px;width:100%"></div>
                    <div style="width:100%;font-size:14px;color:#888888">包材编号：<span :class="item.errorNum>0?'red_style':''">{{ item.malCode }}</span></div>
                    <div style="width:100%;font-size:14px;color:#888888">包材名称：{{ item.malName }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">客户批次：{{ item.customerBatchCode }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">NCC批次：{{ item.nccBatchCode }}</div>
                    <div style="width:100%;font-size:14px;color:#888888">包材数量：{{ item.mainQuantity }}</div>
                    <div style="width:100%;font-size:14px;color:#888888" v-if="item.errorNum>0">异常描述：{{ item.describe }}</div>
                    <div style="clear:both"></div>
                </div>
            </van-tab>
        </van-tabs>

    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox,Indicator } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            lpId:"",
            tpId:"",
            colloidList:[],
            materialsList:[],
            activeName:'',
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.lpId=this.$route.params.lpId
        this.tpId=this.$route.params.tpId
        this.getUsageTankInfo();
        this.getWmsUsageSummary();
    },
    methods: {
        getUsageTankInfo(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getUsageTankInfo',{params:{lpId:self.lpId}}).then(res=>{
                if(res.data.code==200){
                    self.colloidList=res.data.result
                    if(self.colloidList.length<=0){
                        Toast({
                            message: "暂无灌装记录，请通知加料员加料",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }
            })
        },
        getWmsUsageSummary(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/wmsUsage/getWmsUsageSummary',{params:{tpId:self.tpId}}).then(res=>{
                if(res.data.code==200){
                    self.materialsList=res.data.result
                    console.log("🚀 ~ self.$axios.get ~ self.materialsList:", self.materialsList)
                    if(self.colloidList.length<=0){
                        Toast({
                            message: "暂无备料记录，请通知备料员备料",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }
            })
        },
        cancelPush(item){
            let self=this;
            console.log(item)
            MessageBox.confirm('是否确认撤销该坦克灌装？').then(action => {
                if(action=="confirm"){
                    let params={
                        id:item.id,
                        cancelUser:localStorage.getItem('userCode')
                    }
                    self.$axios.put('/jeecg-boot/app/tank/check/cancelPush',params).then(res=>{
                        if(res.data.code==200){
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            self.getUsageTankInfo();
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })

                }
            })

            
            
        }
    }
}
</script>
<style scoped>
.red_style{
    color: red;
}
</style>