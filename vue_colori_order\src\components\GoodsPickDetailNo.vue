<template>
  <div>
    <van-field v-model="info.name" label="产品" readonly />
    <van-field v-model="info.code" label="编码" readonly />
    <van-field v-model="info.count" label="总量" readonly />
    <van-field v-model="info.defectCount" label="缺数" type="digit" />
    <van-field v-model="info.defectRemark" label="备注" />

    <van-button
      round
      block
      plain
      size="small"
      type="info"
      @click="add"
      style="width: 90%;margin: 0 auto;"
    >
      新增
    </van-button>

    <van-popup v-model="showAdd" position="bottom" :style="{ height: '20%' }">
      <van-field
        v-model="rackName"
        clearable
        label="货位："
        placeholder="请输入货位号"
      />

      <van-button
        type="info"
        round
        @click="addSub"
        style="width: 100%;margin: 0 auto;"
      >
        确定
      </van-button>
    </van-popup>
    <van-divider
      :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }"
    >
      待拣货
    </van-divider>
    <div style="height:15rem;overflow: auto;margin-top:3%;">
      <div
        v-for="(item, index) in dataArr"
        :key="index"
        style="width:90%; text-align: left; margin:3% auto;padding:3%;"
      >
        <van-row>
          <van-col span="12">托码:{{ item.stickerId }}</van-col>
          <van-col span="12">货位:{{ item.rackName }}</van-col>
        </van-row>
        <van-row>
          <van-col span="12">批次号:{{ item.customer }}</van-col>
          <van-col span="12">库存数:{{ item.stock }}</van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-field v-model="item.count" type="digit" label="数量：" />
          </van-col>
        </van-row>
      </div>
    </div>
    <van-divider
      :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }"
    >
      已拣货
    </van-divider>
    <div style="height:15rem;overflow: auto;margin-top:3%;">
      <div
        v-for="(item, index) in dataArr1"
        :key="index"
        style="width:90%; text-align: left; margin:3% auto;background-color: #fbf8fb;padding:3%;border-radius: 5px;"
      >
        <van-row>
          <van-col span="12">托码:{{ item.stickerId }}</van-col>
          <van-col span="12">货位:{{ item.rackName }}</van-col>
        </van-row>
        <van-row>
          <van-col span="12">批次号:{{ item.customer }}</van-col>
          <van-col span="12">已拣货:{{ item.stock }}</van-col>
        </van-row>
      </div>
    </div>
    <van-button
      round
      block
      type="info"
      @click="submit"
      style="width:90%; margin: 0 auto;"
    >
      提交
    </van-button>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      // 新增
      showAdd: false,
      info: {},
      dataArr: [],
      dataArr1: [],
      // 新增货位号
      rackName: ""
    };
  },
  created() {
    if (this.$route.params) {
      // this.dataArr = this.$route.params
      this.info = this.$route.params;
      this.info.defectCount = 0;
      console.log("@", this.$route.params);
      this.dataArr1 = this.$route.params.locationList;
      this.$axios
        .get(
          `/jeecg-boot/app/warehouseUsage/getUsageList?userCode=${localStorage.getItem(
            "userCode"
          )}&code=${this.info.code}&rackName=${this.info.rackName}`
        )
        .then(res => {
          if (res.data.code == 200) {
            this.dataArr = res.data.result;
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    } else {
      this.$router.replace({
        name: "GoodsPick"
      });
    }
  },
  methods: {
    add() {
      this.showAdd = true;
    },
    addSub() {
      this.showAdd = false;
      if (!this.rackName) {
        Toast({
          message: "请填写货位号",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      this.$axios
        .get(
          `/jeecg-boot/app/warehouseUsage/getUsageList?userCode=${localStorage.getItem(
            "userCode"
          )}&code=${this.info.code}&rackName=${this.rackName}`
        )
        .then(res => {
          if (res.data.code == 200) {
            let flag = true;
            this.dataArr.forEach(item => {
              if (
                res.data.result[0].code == item.code &&
                res.data.result[0].rackName == item.rackName
              ) {
                flag = false;
              }
            });
            if (flag) {
              this.dataArr.push(...res.data.result);
            } else {
              // Toast({
              //     message: '已添加',
              //     position: "bottom",
              //     duration: 2000
              // });
            }
            this.showAdd = false;
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    submit() {
      if (this.info.checkerNo == localStorage.getItem("userCode")) {
        let flag = true;
        let allCount = 0;
        if (!this.dataArr) {
          Toast({
            message: "拣货数量为0",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (this.dataArr1) {
          this.dataArr1.forEach(item => {
            allCount += item.stock * 1;
          });
        }
        this.dataArr.forEach(item => {
          if (item.count * 1 > item.stock * 1) {
            flag = false;
          }
          allCount += item.count * 1;
        });
        if (allCount + this.info.defectCount * 1 != this.info.count * 1) {
          flag = false;
        }
        if (this.info.defectCount != null || this.info.defectCount != "") {
          if (!this.info.defectRemark) {
            Toast({
              message: "请填写备注",
              position: "bottom",
              duration: 2000
            });
            return;
          }
        }

        if (flag) {
          this.info.locationList = this.dataArr;
          this.$axios
            .post(`/jeecg-boot/app/warehouseUsage/getPickOutput`, this.info)
            .then(res => {
              if (res.data.code == 200) {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
                this.$router.go(-1);
              } else {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
              }
            });
        } else {
          Toast({
            message: "数量错误,请重新填写数量",
            position: "bottom",
            duration: 2000
          });
        }
      } else {
        Toast({
          message: "您不是本单保管员,没有权限",
          position: "bottom",
          duration: 2000
        });
      }
    }
  }
};
</script>

<style scoped></style>
