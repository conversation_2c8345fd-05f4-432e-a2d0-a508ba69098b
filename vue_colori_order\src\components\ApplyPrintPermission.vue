<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div v-for="(item,index) in attenceList" :key="index" style="padding:15px;margin-bottom:10px;background:white;">
            <div style="text-align:left;">所属账套：{{item.book}}</div>
            <div style="text-align:left;">所属车间：{{item.workshop}}</div>
            <div style="text-align:left;" v-if="item.pageNo!=null && item.pageNo!=''">申请页码：{{item.pageNo}}</div>
            <div style="text-align:left;" v-if="item.tankNo!=null && item.tankNo!=''">坦克编号：{{item.tankNo}}</div>
            <div style="height:1px;background:#888888;width:100%;margin-top:10px;margin-bottom:10px;"></div>
            <div style="color:red;float:left;width:48%;" @click="handleApply('0')" v-if="item.status=='1'">驳回</div>
            <div style="color:green;float:left;width:48%;" @click="handleApply('2')" v-if="item.status=='1'">同意</div>
            <div style="clear:both;"></div>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            clickNum:0,
            isManager:true,
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_attence:require('../../static/images/myAttence.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            attenceList:[],
            applyId:"",
            fee:{}
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')
        this.applyId=this.$route.query.applyId
        this.getApplyPrintNum()
    },
    methods: {
        getApplyPrintNum(){
            let self=this
            self.$axios.get('/jeecg-boot/app/tank/check/getApplyPrintNum',{params:{id:this.applyId,userCode:this.userCode}}).then(res=>{
                if(res.data.code==200){
                    self.attenceList=res.data.result
                }
            })
        },
        handleApply(status){
            let self=this 
            let hint = "";
            if(status == '0'){
                hint="是否确认驳回此申请";
            }else{
                hint="是否确认同意此申请";
            }


            MessageBox.confirm('',{
                message: hint,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.get('/jeecg-boot/app/tank/check/checkApplyPrint',{params:{id:this.applyId,userCode:this.userCode,status:status}}).then(res=>{
                            if(res.data.code==200){
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                })


            
        },
        pushToSpNo(){
            this.$router.push({path:'/checkSpNo'})
        },
        openManager(){
            let self=this
            self.clickNum=self.clickNum+1;
            if(self.userCode=="HI0901071284" || self.userCode=="HI1308030001" 
            || self.userCode=="HI2002250004" || self.userCode=="HI2102220002"){
                if(self.clickNum==5){
                    self.isManager=false;
                    self.clickNum=0
                }
            }
        },
        getDetail(item){
            console.log("item:"+item)
            sessionStorage.setItem('item',JSON.stringify(item))
            this.$router.push({path:'/schedualDetail'})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getAttecdanceInfo()
        },
        
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
</style>