<template>
<div class="pageAdvice">
    <van-tabs v-model="tabActive" @change="onTabChange">
        <van-tab name="1" title="历史信息">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    @load="getAdviceList">
                    <div v-for="(advice, index) in aList" :key="index" style="margin-top: 10px;">
                        <van-cell-group>
                            <van-cell class="vanCellClass" title="类型">
                                <template #default>
                                    <van-tag type="danger" v-if="advice.type=='1'">投诉</van-tag>
                                    <van-tag type="primary" v-else-if="advice.type=='2'">建议</van-tag>
                                    <van-tag color="#999" v-else>未知</van-tag>
                                </template>
                            </van-cell>
                            <van-cell class="vanCellClass" title="是否投诉宿管" :value="advice.isComplaint=='1'?'是':'否'" v-if="advice.type=='1'" ></van-cell>
                            <van-cell class="vanCellClass" title="创建人" :value="advice.staffName" ></van-cell>
                            <van-cell class="vanCellClass" title="创建时间" :value="advice.createTime"></van-cell>
                            <van-cell class="vanCellClass" title="是否回复">
                                <template #default>
                                    <van-tag plain type="danger" v-if="!advice.csId">未回复</van-tag>
                                    <van-tag plain type="success" v-else>已回复</van-tag>
                                </template>
                                <template #label v-if="advice.csId && advice.replyInfoList.length>0">
                                    <div v-for="reply in advice.replyInfoList" >
                                        <span>{{ reply.content }}</span>
                                    </div>
                                </template>
                            </van-cell>
                            <!-- <van-cell class="vanCellClass" title="状态" :value="advice.status"></van-cell> -->
                            <van-field label="内容" name="内容" readonly
                                v-model="advice.content"
                                placeholder="内容"
                                type="textarea" autosize 
                            />
                        </van-cell-group>
                    </div>
                </van-list>
            </van-pull-refresh>
        </van-tab>
        <van-tab name="2" title="建议/投诉">
            <van-form @submit="adviceSubmit">
                <van-field readonly right-icon="arrow" input-align="right"
                    label="类型" name="类型"
                    v-model="adForm.type"
                    placeholder="类型"
                    @focus="onSelectType"
                    :rules="[{ required: true, message: '类型必选' }]"
                />
                <van-cell title="是否投诉宿管" class="vanCellClass" v-if="adForm.type=='投诉'">
                    <template #default>
                        <van-switch v-model="adForm.isComplaint" ></van-switch>
                    </template>
                </van-cell>
                <van-field label="内容" name="内容"
                    v-model="adForm.content"
                    placeholder="内容"
                    type="textarea" rows="3" autosize
                    maxlength="300" show-word-limit
                    :rules="[{ required: true, message: '内容必填' }]"
                />
                <div style="margin: 16px;">
                    <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
                </div>
            </van-form>
        </van-tab>
    </van-tabs>

<!-- 类型Picker -->
<van-popup v-model="popTypeShow" position="bottom" :style="{ height: '45%' }">
    <van-picker title="类型" show-toolbar
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="onTypeCancel" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            confirmLoading: false,
            adForm: {
                type: '',
                content: '',

            },
            tabActive: "1",
            popTypeShow: false,
            typeColumns: ['投诉','建议'],
            iPage: {
                current: 1,
                size: 5,
                total: 1,
            },
            refreshing: false, // 是否下拉刷新
            aList: [],   // 投诉/建议列表
            loading: false, // 是否加载
            finished: false, // 是否完成  停止触底加载

        }
    },
    methods:{
        getAdviceList(){
            if (this.refreshing) {
                this.aList = [];
                this.iPage.current = 1;
                this.refreshing = false;
                this.finished=false
            }
            let rParams={
                creator: localStorage.getItem('userCode'),
                pageNo: this.iPage.current,
                pageSize: this.iPage.size
            }
            this.$axios.get("/dormApi/dm/dmDailyCsInfo/app/list", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.aList.push(...res.result.records);
                        this.iPage={
                            current: res.result.current,
                            size: res.result.size,
                            total: res.result.pages,
                        }
                        // 关闭loading状态
                        this.loading = false;
                        // 判断是否到底了
                        if(this.iPage.current*1>=this.iPage.total*1){
                            this.finished = true;
                        }else{
                            this.iPage.current++;
                        }
                    }else{
                        Toast.fail(res.message)
                        console.log("ERROR", res)
                    }
                }else{
                    Toast.fail("发生错误")
                    console.log("error", rtn)
                }
            })
        },
        onRefresh() { // 列表刷新
            // 处于刷新状态
            this.refreshing=true;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            // 加载数据
            this.getAdviceList();
        },
        adviceSubmit(){
            this.confirmLoading=true
            let params={
                creator: localStorage.getItem('userCode'),
                type: this.adForm.type=='投诉'?"1":"2",
                isComplaint: !this.adForm.isComplaint?"0":"1",
                content: this.adForm.content
            }
            console.log("/dm/dmDailyCsInfo/add", params)
            this.$axios.post("/dormApi/dm/dmDailyCsInfo/add", params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.tabActive="1"
                        this.onTabChange("1","历史消息")
                        Toast.success(res.message)
                        this.adForm={}
                    }else{
                        Toast.fail(res.message)
                        console.log("ERROR", res)
                    }
                }else{
                    Toast.fail("发生错误")
                    console.log("error", rtn)
                }
            }).finally(()=>{
                setTimeout(() => {
                    this.confirmLoading=false
                }, 1500);
            })
        },
        onTypeConfirm(pValue, pIndex){
            this.adForm.type=pValue
            this.popTypeShow=false
        },
        onTypeCancel(){
            this.adForm.type=""
            this.popTypeShow=false
        },
        onSelectType(){
            this.popTypeShow=true
        },
        onTabChange(pName, pTitle){
            if(pName=='1'){// 历史信息
                this.onRefresh()
            }else if(pName=='2'){// 投诉/建议
                this.confirmLoading=false
                this.adForm={}
                this.aList=[]
                this.iPage={
                    current: 1,
                    size: 5,
                    total: 1,
                }
            }
        },
    },
    created(){ },
    filters: {
        // fType(pType){
        //     switch(pType){
        //         case "1": return "投诉";
        //         case "2": return "建议";
        //         default: return pType;
        //     }
        // },
    }
}
</script>
<style scoped>
.pageAdvice{
    background: #F1F1F1;
}
.vanCellClass{
  color: #646566;
  text-align: left;
}
</style>