<template>
  <div class="equipment-inspection-container">

    <van-sticky :offset-top="0">
      <van-nav-bar title="通用巡检" right-text="筛选" @click-right="showFilterPopup = true" left-arrow
        @click-left="onClickLeft" />

    </van-sticky>
    <!-- 设备列表 -->
    <div class="equipment-list">
      <van-empty v-if="equipmentList.length === 0" description="暂无数据" />
      <div v-else>
        <div v-for="(item, index) in equipmentList" :key="index" class="equipment-item">
          <div class="equipment-header">
            <span class="equipment-name">{{ item.area || '未命名设备' }}</span>
            <span class="equipment-status" :class="getStatusClass(item.errorCount)">
              {{ getStatusText(item.errorCount) }}
            </span>
          </div>

          <div class="equipment-info">
            <div class="info-item">
              <span class="label">巡查次数:</span>
              <span class="value">{{ item.inspectCount || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">异常次数:</span>
              <span class="value">{{ item.errorCount || '-' }}</span>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="equipment-buttons">
            <van-button type="info" size="small" @click="viewEquipment(item)">查看</van-button>
            <van-button type="primary" size="small" @click="add(item)">巡检</van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 圆形新增按钮 -->
    <!-- <van-button 
      type="primary" 
      icon="plus" 
      round 
      class="add-button" 
      @click="add"
    /> -->

    <!-- 筛选弹窗 -->
    <van-popup v-model="showFilterPopup" position="bottom" round :style="{ height: '50%' }">
      <van-nav-bar title="筛选条件" left-arrow @click-left="showFilterPopup = false" />
      <van-form @submit="onSubmit" class="filter-form">
        <van-field name="worksDay" label="检查日期" readonly :value="formatDate(worksDay)" @click="showDatePicker = true"
          is-link required />

        <van-field v-model="workshop" name="workshop" label="车间" placeholder="请选择车间" @click="showWorkshopPicker = true"
          is-link readonly />

        <van-field v-model="deviceId" name="deviceId" label="设备ID" placeholder="请输入设备ID" clearable />

        <div class="form-buttons">
          <van-button type="info" block native-type="submit">查询</van-button>
          <van-button type="default" block @click="resetForm">重置</van-button>
        </div>
      </van-form>
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker type="date" title="选择日期" :value="new Date(worksDay)" @confirm="onDateConfirm"
        @cancel="showDatePicker = false" />
    </van-popup>

    <!-- 车间选择器 -->
    <van-popup v-model="showWorkshopPicker" position="bottom">
      <van-picker show-toolbar :columns="workshopList" @confirm="onWorkshopConfirm"
        @cancel="showWorkshopPicker = false" />
    </van-popup>

    <!-- 设备详情弹窗 -->
    <van-popup v-model="showEquipmentDetail" round closeable position="bottom" :style="{ height: '80%' }">
      <div class="equipment-detail-popup">
        <div class="detail-title">设备详情</div>
        <div v-if="currentEquipment" class="detail-content">
          <div class="detail-header">
            <div class="detail-item">
              <span class="detail-label">区域名称:</span>
              <span class="detail-value">{{ currentEquipment.area || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">巡查次数:</span>
              <span class="detail-value">{{ currentEquipment.inspectCount || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">异常次数:</span>
              <span class="detail-value">{{ currentEquipment.errorCount || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <span class="detail-value" :class="getStatusClass(currentEquipment.errorCount)">
                {{ getStatusText(currentEquipment.errorCount) }}
              </span>
            </div>
          </div>

          <!-- 设备列表滚动区域 -->
          <div class="equipment-list-container">
            <div v-if="currentEquipment.equi && currentEquipment.equi.length > 0">
              <div v-for="(equi, equiIndex) in currentEquipment.equi" :key="equiIndex" class="equipment-detail-item">
                <div class="equipment-detail-title"> {{ equi.deviceId }} </div>
                <div class="equipment-detail-title"> {{ equi.deviceName }}</div>

                <!-- 巡检记录列表 -->
                <div v-if="equi.inspectList && equi.inspectList.length > 0" class="inspect-list">
                  <div v-for="(inspect, inspectIndex) in equi.inspectList" :key="inspectIndex" class="inspect-item">
                    <div class="inspect-result">
                      <span class="inspect-label">巡检人员:{{ inspect.creator }}-{{ inspect.createName }}</span>
                    </div>
                    <div class="inspect-result">
                      <span class="inspect-label">第{{inspectIndex+1}} 次 {{ inspect.createTime }}</span>
                      <span class="inspect-value">
                        <span class="detail-value" :class="inspect.result === '正常' ? 'status-normal' : 'status-danger'">
                          {{ inspect.result }}
                        </span>
                      </span>
                    </div>

                    <!-- 图片展示区域 -->
                    <div v-if="inspect.picUrl1" class="image-container">
                      <van-image v-for="(imgUrl, imgIndex) in getImageUrls(inspect.picUrl2)" :key="imgIndex" width="80"
                        height="80" fit="cover" :src="imgUrl"
                        @click="previewImage(getImageUrls(inspect.picUrl2), imgIndex)" class="preview-image" />
                    </div>
                  </div>
                </div>
                <div v-else class="no-data">暂无巡检记录</div>
              </div>
            </div>
            <div v-else class="no-data">暂无设备数据</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 图片预览组件 -->
    <van-image-preview v-model="showImagePreview" :images="previewImages" :start-position="previewIndex"
      :show-index="true" />
  </div>
</template>

<script>
export default {
  name: 'EquipmentInspectionList',
  data() {
    return {
      worksDay: this.getTodayDate(), // 默认为今天
      workshop: '',
      deviceId: '',
      showFilterPopup: false,
      showDatePicker: false,
      showWorkshopPicker: false,
      equipmentList: [],
      workshopList: ['一号车间', '二号车间', '三号车间'], // 这里应该从接口获取，暂时写死
      loading: false,
      showEquipmentDetail: false, // 控制设备详情弹窗显示
      currentEquipment: null, // 当前查看的设备
      showImagePreview: false, // 控制图片预览
      previewImages: [], // 预览图片列表
      previewIndex: 0, // 当前预览的图片索引
    }
  },
  created() {
    // 组件创建时直接查询今日数据
    this.fetchEquipmentList()
  },
  methods: {
    // 获取今天的日期，格式为YYYY-MM-DD
    getTodayDate() {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    // 新增设备检查
    add(record) {
      this.$router.push('/equipmentInspectionAdd?area=' + record.area)
      // 这里可以添加跳转到新增页面的逻辑
    },
    // 查看设备详情
    viewEquipment(record) {
      // 设置当前查看的设备
      this.currentEquipment = record
      console.log("🚀 ~ viewEquipment ~ this.currentEquipment:", this.currentEquipment)
      // 显示详情弹窗
      this.showEquipmentDetail = true
    },
    // 巡检设备
    inspectEquipment(record) {
      this.$router.push(`/equipmentInspectionAdd?area=${record.area}`)
    },
    // 导航栏返回按钮点击事件
    onClickLeft() {
      this.$router.back()
    },

    // 格式化日期显示
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 获取设备状态对应的CSS类名
    getStatusClass(errorCount) {
      return errorCount > 0 ? 'status-danger' : 'status-normal'
    },

    // 获取设备状态对应的文本
    getStatusText(errorCount) {
      return errorCount > 0 ? '异常' : '正常'
    },

    // 查询设备列表数据
    fetchEquipmentList() {
      this.loading = true
      let params = {
        worksDay: this.worksDay
      }

      if (this.workshop) {
        params.workshop = this.workshop
      }

      if (this.deviceId) {
        params.deviceId = this.deviceId
      }

      this.$axios.get('/jeecg-boot/app/equiInspect/getInspectEquiList', { params })
        .then(res => {
          if (res.data.success) {
            this.equipmentList = res.data.result || []
            console.log("🚀 ~ fetchEquipmentList ~ this.equipmentList:", this.equipmentList)
          } else {
            this.$toast.fail(res.data.message || '查询失败')
          }
        })
        .catch(err => {
          console.error('获取设备检查列表失败', err)
          this.$toast.fail('网络错误，请重试')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 表单提交
    onSubmit() {
      this.showFilterPopup = false
      this.fetchEquipmentList()
    },

    // 日期确认
    onDateConfirm(date) {
      this.worksDay = this.formatDate(date)
      this.showDatePicker = false
    },

    // 车间确认
    onWorkshopConfirm(value) {
      this.workshop = value
      this.showWorkshopPicker = false
    },

    // 重置表单
    resetForm() {
      this.worksDay = this.getTodayDate()
      this.workshop = ''
      this.deviceId = ''
    },

    // 获取图片URL数组
    getImageUrls(picUrlStr) {
      if (!picUrlStr) return []
      return picUrlStr.split(',').filter(url => url.trim() !== '')
    },

    // 预览图片
    previewImage(images, index) {
      this.previewImages = images
      this.previewIndex = index
      this.showImagePreview = true
    },
  }
}
</script>

<style scoped>
.equipment-inspection-container {
  padding: 10px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.search-bar {
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
}

.current-filter {
  padding: 5px 16px 10px;
  font-size: 12px;
  color: #666;
}

.equipment-list {
  margin-top: 10px;
}

.equipment-item {
  margin-bottom: 10px;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 8px;
}

.equipment-name {
  font-size: 16px;
  font-weight: bold;
}

.equipment-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.status-normal {
  background-color: #07c160;
  color: #fff;
}

.status-warning {
  background-color: #ff976a;
  color: #fff;
}

.status-danger {
  background-color: #ee0a24;
  color: #fff;
}

.status-unknown {
  background-color: #969799;
  color: #fff;
}

.equipment-info {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 5px;
  font-size: 14px;
}

.equipment-desc {
  margin-top: 5px;
  font-size: 14px;
  color: #646566;
}

.label {
  color: #969799;
  margin-right: 5px;
}

.form-buttons {
  display: flex;
  padding: 16px;
}

.form-buttons .van-button {
  margin: 0 5px;
}

.filter-form {
  padding: 10px 0;
}

/* 新增按钮样式 */
.add-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 99;
}

.equipment-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
}

.equipment-buttons .van-button {
  border-radius: 4px;
}

.equipment-detail-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  text-align: left;
}

.detail-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  margin-bottom: 15px;
}

.equipment-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 5px;
}

.equipment-detail-item {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
}

.equipment-detail-title {
  font-weight: bold;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.inspect-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.inspect-item {
  background-color: white;
  border-radius: 6px;
  padding: 10px;
}

.inspect-result {
  margin-bottom: 8px;
}

.inspect-label {
  color: #646566;
  margin-right: 5px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.preview-image {
  border-radius: 4px;
}

.no-data {
  text-align: center;
  color: #969799;
  padding: 20px 0;
}
</style>
