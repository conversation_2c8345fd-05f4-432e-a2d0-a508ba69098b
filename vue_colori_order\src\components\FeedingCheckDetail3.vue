<template>
  <div style="text-align:left;background-color:#F1F1F1;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="已完成" right-text="手动录入" left-arrow @click-left="onClickLeft" @click-right="onClickRight" />
      <van-button type="info" icon="photograph" @click="check" style="width:100%;">
        重量录入
      </van-button>
    </van-sticky>
    <div style="width:100%;height:100%;overflow: auto;">
      <div v-for="(item, index) in dataArr" :key="index"
        style="text-align: left;background-color:#fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
        <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
          <van-row>
            <van-col span="19">
              <span style="font-size:18px;font-weight: 700;color: #000;">
                {{ item.materialCode }}
              </span>
            </van-col>
            <van-col span="5">
              <span v-if="item.specialNum == 1">
                <!-- <span  style="color:#FF0033">
                  <van-button
                    type="info"
                    @click="addWeight(item)"
                    size="mini"
                    round
                  >
                    重量录入
                  </van-button>
                </span> -->
              </span>
              <span v-else>
                <span v-if="item.status == 6" style="color:#66CC00;">
                  已校验
                </span>
              </span>
            </van-col>
          </van-row>
        </div>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> ID：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.id }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> 原料名称：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.materialName }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6">区域：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.area }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 批次号：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.nccBatchCode }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 供应商：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.supplier }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 重量：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.actualWeight }}KG
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { DatetimePicker, Indicator, MessageBox } from "mint-ui";
import { Toast } from "vant";
export default {
  data() {
    return {
      userCode: localStorage.getItem("userCode"),
      dataArr: [],
      info: {},
    };
  },
  created() {
    this.info = this.$route.params
    if (this.$route.params) {
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });
      this.$axios
        .get(
          `/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.$route.params.id}`
        )
        .then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            this.dataArr = res.data.result.reverse();
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        })
        .finally(() => {
          Indicator.close();
        });
    } else {
      this.$route.go(-1);
    }
  },
  methods: {
    check() {
      let self = this;
      wx.scanQRCode({
        desc: "scanQRCode desc",
        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
        success: function (res) {
          // 回调
          var result = res.resultStr; //当needResult为1时返回处理结果
          // 处理结果 result
          self.checkRequest(result);
        },
        error: function (res) {
          if (res.errMsg.indexOf("function_not_exist") > 0) {
            alert("版本过低请升级");
          }
        }
      });
    },
    onClickRight() {
      MessageBox.prompt("请录入").then(({ value, action }) => {
        if (action == "confirm") {
          //  处理结果  value
          this.checkRequest(value);
        }
      });
    },
    padString(str, length, padChar) {
      while (str.length < length) {
        str += padChar;
      }
      return str;
    },

    checkString(str) {
      if (str.indexOf("+") != "-1") {
        return str;
      } else {
        const targetLength = 11;
        if (str.length === targetLength) {
          return str;
        } else if (str.length < targetLength) {
          return this.padString(str, targetLength, "@");
        } else {
          return str.slice(0, targetLength);
        }
      }
    },
    checkRequest(value) {
      console.log(value.indexOf('T') > -1);
      if (value.indexOf('T') > -1) {
        Indicator.open({
          text: "正在加载中，请稍后……",
          spinnerType: "fading-circle"
        });
        this.$axios
          .get(
            `/jeecg-boot/app/gcMix/getWhStockInfo?stickerId=${value}&mixBookName=${this.info.mixBookName}&mixStoreName=${this.info.mixStoreName}`
          )
          .then(res => {
            console.log(res);
            Indicator.close();
            if (res.data.code == 200) {
              // 校验成功刷新列表
              // res.data.result[0]
              let flag = false;
              let obj = {};
              this.dataArr.forEach(item => {
                if (item.materialCode == res.data.result.productNo) {
                  flag = true;
                  obj = Object.assign({}, item);
                  obj = Object.assign(obj, res.data.result);
                  obj.customer = obj.customerBatchCode;
                }
              });
              if (flag) {
                obj.id = obj.detailId
                obj.weightList = [{
                  stockId: obj.stockId,
                  type: '12',
                  supplier: obj.supplier,
                  nccBatchCode: obj.nccBatchCode,
                  customerBatchCode: obj.customerBatchCode,
                  actualWeight: obj.actualWeight,
                  aboutWeight: obj.aboutWeight,
                  tareWeight: obj.tareWeight,
                }]
                this.addWeight(obj);
              } else {
                Toast.fail("未找到该原料");
              }
            } else {
              let flag = false;
              let obj = {};
              let str = value.substr(0, 11).replace(/@/g, "");
              console.log(str);
              this.dataArr.forEach(item => {
                if (str == item.materialCode) {
                  flag = true;
                  obj = Object.assign({}, item);
                  obj.customer = "";
                  obj.customerBatchCode = "";
                  obj.nccBatchCode = "";
                  obj.supplier = "";
                }
              });
              if (flag) {
                MessageBox.confirm(`没有来料标签,确认要继续录入吗`).then((action) => {
                  this.addWeight(obj);
                });
              } else {
                Toast.fail("未找到该原料");
              }
            }
          });
      } else {
        Indicator.open({
          text: "正在加载中，请稍后……",
          spinnerType: "fading-circle"
        });
        this.$axios
          .get(
            `/jeecg-boot/app/gcMix/getNccStockInfo?nccCode=${this.checkString(
              value
            )}`
          )
          .then(res => {
            Indicator.close();
            if (res.data.code == 200) {
              // 校验成功刷新列表
              // res.data.result[0]
              let flag = false;
              let obj = {};
              this.dataArr.forEach(item => {
                if (item.materialCode == res.data.result[0].code) {
                  flag = true;
                  obj = Object.assign({}, item);
                  obj = Object.assign(obj, res.data.result[0]);
                  obj.customer = obj.customerBatchCode;
                }
              });
              if (flag) {
                console.log(obj);
                this.addWeight(obj);
              } else {
                Toast.fail("未找到该原料");
              }
            } else {
              let flag = false;
              let obj = {};
              let str = value.substr(0, 11).replace(/@/g, "");
              console.log(str);
              this.dataArr.forEach(item => {
                if (str == item.materialCode) {
                  flag = true;
                  obj = Object.assign({}, item);
                  obj.customer = "";
                  obj.customerBatchCode = "";
                  obj.nccBatchCode = "";
                  obj.supplier = "";
                }
              });
              if (flag) {
                MessageBox.confirm(`没有来料标签,确认要继续录入吗`).then((action) => {
                  this.addWeight(obj);
                });
              } else {
                Toast.fail("未找到该原料");
              }
            }
          });
      }
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    addWeight(item) {
      console.log(item.materialCode);

      let allCount = 0
      this.dataArr.forEach(x => {
        if (x.materialCode == item.materialCode) {
          allCount = allCount + x.actualWeight * 1
        }
      })

      let str = ''
      if (item.stickerId == null) {
        str = `<span style='color:black'> 请录入重量,范围为${item.mixWeight}-${item.maxWeight}</span>`
      } else {
        str = `<span style='color:black'> 库存为${item.mainQuantity},<br />范围为${item.mixWeight}-${item.maxWeight}</span>`
      }
      MessageBox.prompt(str).then(({ value, action }) => {
        if (action == "confirm") {
          if (value * 1 > item.maxWeight * 1 || allCount > item.maxWeight * 1) {
            MessageBox.confirm(`已经录入${allCount},继续录入重量为${allCount*1+value*1},超出最大范围${item.maxWeight},还要继续录入吗?`).then((action) => {
              item.actualWeight = value;
              item.creator = localStorage.getItem("userCode");
              item.createName = localStorage.getItem("userName");
              item.userCode = localStorage.getItem("userCode");
              item.userName = localStorage.getItem("userName");
              if (item.weightList && item.weightList.length > 0) {
                item.weightList[0].actualWeight = value;
              }
              item.status = 12;
              console.log('params', item);
              Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle"
              });
              this.$axios
                .put(`/jeecg-boot/app/gcMix/addSpecialWeight`, item)
                .then(res => {
                  if (res.data.code == 200) {
                    // 校验成功刷新列表
                    Indicator.open({
                      text: "正在加载中，请稍后……",
                      spinnerType: "fading-circle"
                    });
                    this.$axios
                      .get(`/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.$route.params.id}`)
                      .then(res => {
                        if (res.data.code == 200) {
                          console.log(res.data.result);
                          this.dataArr = res.data.result.reverse();
                        } else {
                          Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                          });
                        }
                      })
                      .finally(() => {
                        Indicator.close();
                      });
                  } else {
                    Toast.fail(res.data.message);
                  }
                })
                .finally(() => {
                  Indicator.close();
                });
            });
          } else {
            item.actualWeight = value;
            item.creator = localStorage.getItem("userCode");
            item.createName = localStorage.getItem("userName");
            item.userCode = localStorage.getItem("userCode");
            item.userName = localStorage.getItem("userName");
            if (item.weightList && item.weightList.length > 0) {
              item.weightList[0].actualWeight = value;
            }
            item.status = 12;
            console.log('params', item);
            Indicator.open({
              text: "正在加载中，请稍后……",
              spinnerType: "fading-circle"
            });
            this.$axios
              .put(`/jeecg-boot/app/gcMix/addSpecialWeight`, item)
              .then(res => {
                if (res.data.code == 200) {
                  // 校验成功刷新列表
                  Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                  });
                  this.$axios
                    .get(`/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.$route.params.id}`)
                    .then(res => {
                      if (res.data.code == 200) {
                        console.log(res.data.result);
                        this.dataArr = res.data.result.reverse();
                      } else {
                        Toast({
                          message: res.data.message,
                          position: "bottom",
                          duration: 2000
                        });
                      }
                    })
                    .finally(() => {
                      Indicator.close();
                    });
                } else {
                  Toast.fail(res.data.message);
                }
              })
              .finally(() => {
                Indicator.close();
              });
          }



        }
      });
    }
  }
};
</script>

<style scoped></style>
