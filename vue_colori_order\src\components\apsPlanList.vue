<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="24">
            <a-form-item label="时间">
              <a-date-picker :placeholder="$t('pleaseSelect')" valueFormat="YYYY-MM-DD" v-model="queryParam.workDay" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="账套">
              <a-select placeholder="请选择账套" v-model.trim="queryParam.book" @change="getWorkshop">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="(factory, index) in factoryNo" :key="index" :value="factory.name">{{
                  factory.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="车间">
              <a-select placeholder="请输入车间" v-model.trim="queryParam.workshop" @change="getJobCenter">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="(workshop, index) in workshopNo" :key="index" :value="workshop.name">{{
                  workshop.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :sm="24">
            <a-form-item label="工作中心">
              <a-select placeholder="请选择工作中心" v-model.trim="queryParam.jobCenter">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="(item, index) in workCenter" :key="item.id" :value="item.jobCenter">
                  {{ item.jobCenter }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- <a-col :md="6" :sm="24">
            <a-form-item label="产品编码">
              <a-input placeholder="请输入产品编码" @keydown.enter="searchQuery" v-model.trim="queryParam.productNo"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="产品名称">
              <a-input placeholder="请输入产品名称" @keydown.enter="searchQuery"
                v-model.trim="queryParam.productName"></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :md="6" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search" v-has="'wplxq:search'">{{ $t('query')
              }}</a-button>
              <!-- <a-button @click="handleAdd" type="primary" icon="plus" style="margin-left:8px;">{{$t('add')}}</a-button> -->
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <!-- <a-button @click="handleAdd" type="primary" icon="import">导入</a-button>
      <a-button @click="handleAdd" type="primary" icon="download">导出</a-button> -->

      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            {{ $t('delete') }}
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
          selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">{{ $t('empty') }}</a>
      </div>

      <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
        :expandedRowKeys="expandedRowKeys"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange"
        @expand="handleExpand" class="j-table-force-nowrap">
        <span slot="action" slot-scope="text, record">
          <a-popconfirm v-if="record.status == 1" title="确认吗?" @confirm="() => handleAccept(record)">
            <a>接单</a>
          </a-popconfirm>
          <a-divider v-if="record.status == 1||record.status == 2||record.status == 3" type="vertical"/>
          <a v-if="record.status == 1||record.status == 2||record.status == 3" @click="showHangModal(record)">挂起</a>
          <a-divider v-if="record.status == 4" type="vertical"/>
          <a v-if="record.status == 4" @click="showOverTurnModal(record)">结转</a>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <apsJobCenterListModal ref="modalForm" @ok="modalFormOk"></apsJobCenterListModal>

    <!-- 挂起弹框 -->
    <a-modal
      title="挂起原因"
      :visible="hangModalVisible"
      @ok="handleHangConfirm"
      @cancel="handleHangCancel"
      :confirmLoading="hangLoading"
    >
      <a-form-item label="挂起原因">
        <a-textarea
          v-model="hangReason"
          placeholder="请输入挂起原因"
          :rows="4"
          :maxLength="200"
        />
      </a-form-item>
    </a-modal>

    <!-- 结转弹框 -->
    <a-modal
      title="结转原因"
      :visible="overTurnModalVisible"
      @ok="handleOverTurnConfirm"
      @cancel="handleOverTurnCancel"
      :confirmLoading="overTurnLoading"
    >
      <a-form-item label="结转原因">
        <a-textarea
          v-model="overTurnReason"
          placeholder="请输入结转原因"
          :rows="4"
          :maxLength="200"
        />
      </a-form-item>
    </a-modal>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import { apsJobCenterListMixin } from '@/mixins/apsJobCenterListMixin'
import moment from 'moment'
import apsJobCenterListModal from './tabList/form/apsJobCenterListModal.vue'
import { add, reduce, ride, except } from '@/utils/decimal'

export default {
  name: "apsJobCenterList",
  mixins: [apsJobCenterListMixin],
  components: {
    apsJobCenterListModal
  },
  data() {
    return {
      innerData: [],
      expandedRowKeys: [],
      id: ' ',
      description: '',
      // 列表表头
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          width: 80,
          align: "center",
          scopedSlots: { customRender: 'action' },
        },
        {
          title: 'ID',
          align: "center",
          width: 130,
          dataIndex: 'id'
        },
        {
          title: 'MO单号',
          align: "center",
          width: 130,
          dataIndex: 'moId'
        },
        {
          title: 'OG单号',
          align: "center",
          width: 130,
          dataIndex: 'ogId'
        },
        {
          title: '账套',
          align: "center",
          width: 130,
          dataIndex: 'bookName'
        },
        {
          title: '车间',
          align: "center",
          width: 130,
          dataIndex: 'workshop'
        },
        {
          title: '工作中心',
          align: "center",
          width: 130,
          dataIndex: 'jobCenter'
        },
        {
          title: '产品编码',
          align: "center",
          width: 100,
          dataIndex: 'code'
        },
        {
          title: '产品名称',
          align: "center",
          width: 100,
          dataIndex: 'name'
        },
        {
          title: '生产班次',
          align: "center",
          width: 100,
          dataIndex: 'category'
        },
        {
          title: '生产类别',
          align: "center",
          width: 100,
          dataIndex: 'genre'
        },
        {
          title: '产品性质',
          align: "center",
          width: 100,
          dataIndex: 'nature'
        },
        {
          title: '计划产量',
          align: "center",
          width: 80,
          dataIndex: 'plot'
        },
        {
          title: '预计生产时间(H)',
          align: "center",
          width: 100,
          dataIndex: 'planHour'
        },
        {
          title: '预计生产人数',
          align: "center",
          width: 100,
          dataIndex: 'tpnum'
        },
        {
          title: '预计换产时长',
          align: "center",
          width: 100,
          dataIndex: 'tchange'
        },
        {
          title: '计划开始时间',
          align: "center",
          width: 100,
          dataIndex: 'beginTime'
        },
        {
          title: '计划结束时间',
          align: "center",
          width: 100,
          dataIndex: 'endTime'
        },
      ],
      // 分页参数
      // type: "radio",
      url: {
        list: "/gc/gcAps/getApsPlanList",
        exportXlsUrl: "/gc/gcMaterial/exportXls",
        delete: "/nc/ncClassManagement/deleteById",
        deleteBatch: "/test/order/deleteBatch",
        customerListByMainId: "/test/order/listOrderCustomerByMainId",
      },
      factoryNo: [],
      workshopNo: [],
      workCenter: [],
      book: '',
      workshop: '',
      // 挂起弹框相关
      hangModalVisible: false,
      hangReason: '',
      hangLoading: false,
      currentHangRecord: null,
      // 结转弹框相关
      overTurnModalVisible: false,
      overTurnReason: '',
      overTurnLoading: false,
      currentOverTurnRecord: null,
    }
  },
  computed: {
    currentId() {
      return this.id;
    },
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  created() {
    this.getFactoryNo();
  },
  methods: {
    handleAccept(record) {
      getAction(`/gc/gcAps/acceptApsPlan?workDay=${record.workDay}&jobCenter=${record.jobCenter}`).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 显示挂起弹框
    showHangModal(record) {
      this.currentHangRecord = record
      this.hangModalVisible = true
      this.hangReason = ''
    },
    // 确认挂起
    handleHangConfirm() {
      if (!this.hangReason.trim()) {
        this.$message.warning('请输入挂起原因')
        return
      }

      this.hangLoading = true
      const record = this.currentHangRecord
      getAction(`/gc/gcAps/hangApsPlan?id=${record.id}&reason=${encodeURIComponent(this.hangReason)}`).then(res => {
        this.hangLoading = false
        console.log("🚀 ~ getAction ~ res:", res)
        if (res.success) {
          this.$message.success(res.message)
          this.hangModalVisible = false
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      }).catch(() => {
        this.hangLoading = false
        this.$message.error('操作失败')
      })
    },
    // 取消挂起
    handleHangCancel() {
      this.hangModalVisible = false
      this.hangReason = ''
      this.currentHangRecord = null
    },
    // 显示结转弹框
    showOverTurnModal(record) {
      this.currentOverTurnRecord = record
      this.overTurnModalVisible = true
      this.overTurnReason = ''
    },
    // 确认结转
    handleOverTurnConfirm() {
      if (!this.overTurnReason.trim()) {
        this.$message.warning('请输入结转原因')
        return
      }

      this.overTurnLoading = true
      const record = this.currentOverTurnRecord
      getAction(`/gc/gcAps/carryOverApsPlan?id=${record.id}&reason=${encodeURIComponent(this.overTurnReason)}`).then(res => {
        this.overTurnLoading = false
        console.log("🚀 ~ getAction ~ res:", res)
        if (res.success) {
          this.$message.success(res.message)
          this.overTurnModalVisible = false
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      }).catch(() => {
        this.overTurnLoading = false
        this.$message.error('操作失败')
      })
    },
    // 取消结转
    handleOverTurnCancel() {
      this.overTurnModalVisible = false
      this.overTurnReason = ''
      this.currentOverTurnRecord = null
    },
    getCurrentProgressPercent(record) {
      const dayPlot = record.dayPlot || 0
      const dayMainQuantity = record.dayMainQuantity || 0
      if (dayMainQuantity === 0) {
        return 0
      }
      const percent = (dayPlot / dayMainQuantity * 100).toFixed(2)
      return isNaN(percent) ? 0 : parseFloat(percent)
    },

    getCompletionRate(record) {
      const mainQuantity = record.mainQuantity || 0
      const prePlot = record.prePlot || 0
      if (prePlot === 0) {
        return '0%'
      }
      const rate = (mainQuantity / prePlot * 100).toFixed(2)
      return isNaN(rate) ? '0%' : rate + '%'
    },
    getPlanAchievementRate(record) {
      const dayPlot = record.dayPlot || 0
      const dayMainQuantity = record.dayMainQuantity || 0
      if (dayMainQuantity === 0) {
        return '0%'
      }
      const rate = (dayPlot / dayMainQuantity * 100).toFixed(2)
      return isNaN(rate) ? '0%' : rate + '%'
    },
    handleJobCenter(record) {
      this.$refs.modalForm.edit({ ...record, book: this.queryParam.book, workshop: this.queryParam.workshop, workDay: this.queryParam.workDay })
      this.$refs.modalForm.visible = true
    },
    handleExpand(expanded, record) {
      this.expandedRowKeys = [];
      this.innerData = [];
      if (expanded === true) {
        this.loading = true;
        this.expandedRowKeys.push(record.id);
        getAction(this.url.customerListByMainId, { mainId: record.id }).then((res) => {
          if (res.success) {
            this.loading = false;
            this.innerData = res.result.records;
          }
        });
      }
    },
    getFactoryNo() {
      getAction("/gc/gcTechnologyPlan/getFactoryInfo", {}).then((res) => {
        if (res.success) {
          console.log('成功', res)
          this.factoryNo = res.result
        } else {
          console.log('失败', res)
        }
      })
    },
    getWorkshop(factory) {
      this.book = this.factoryNo.filter(item => { return item.name == factory ? item.code : '' })[0].code
      this.workshopNo = []
      getAction("/gc/gcTechnologyPlan/getFactoryInfoByCode", { code: this.book }).then((res) => {
        if (res.success) {
          console.log('成功', res)
          this.workshopNo = res.result
        } else {
          console.log('失败', res)
        }
      })
    },
    // 获取工作中心
    getJobCenter(value) {
      console.log("🚀 ~ getJobCenter ~ value:", value)
      delete this.queryParam.jobCenter
      this.workshopNo.forEach(item => {
        if (item.name == value) {
          this.workshop = item.code
        }
      })
      getAction('/factoryInfo/getJobCenter', { book: this.book, workshop: this.workshop, workType: 4 }).then(res => {
        if (res.success) {
          this.workCenter = res.result
        } else {
          this.$message.warn(res.message)
        }
      })
    }
  },
  filters: {
    filterTime(time) {
      return moment(time).format("YYYY-MM-DD");
    },
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>