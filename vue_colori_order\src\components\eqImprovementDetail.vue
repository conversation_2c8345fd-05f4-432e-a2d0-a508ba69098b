<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="编辑" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-form validate-first @submit="onSubmit">

            <van-field readonly v-model="formData.book" name="book" rows="1" label="账套" type="text" />
            <van-field readonly v-model="formData.workshop" name="workshop" rows="1" label="车间" type="text" />
            <van-field readonly v-model="formData.mitosome" name="mitosome" rows="1" label="工作中心" type="text" />

            <van-field readonly v-model="formData.machineId" name="machineId" rows="1" label="设备ID" type="text" />
            <van-field readonly v-model="formData.machineNo" name="machineNo" rows="1" label="设备编码" type="text" />
            <van-field readonly v-model="formData.machineName" name="machineName" rows="1" label="设备名称" type="text" />
            <van-field v-model="formData.currentSituation" name="currentSituation" rows="3" autosize label="当前状况"
                type="textarea" placeholder="请输入" />
            <van-field v-model="formData.expectSituation" name="expectSituation" rows="3" autosize label="预计情况"
                type="textarea" placeholder="请输入" />

            <van-field readonly clickable name="type" v-model="formData.type" label="改进类型：" placeholder="点击选择改进类型"
                @click="showTypePicker = true" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="typeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>

            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="formData.uploader" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" :max-count="2" />
                </template>
            </van-field>
        </van-form>


        <van-button type="info" @click="addParts" size="mini" style="width:100%">扫码添加备件</van-button>
        <div v-for="(v, index) in detailList" :key="index" style="margin-top:2rem;">
            <van-row>
                <van-col span="24">
                    <van-button style="width: 100%;" size="mini" @click="handleDel(v, index)" plain>删除</van-button>
                </van-col>
            </van-row>
            <van-field readonly v-model="v.partsName" name="partsCount" label="备件名称" placeholder="请输入" type="text" />
            <van-field v-model="v.expectCount" name="partsCount" label="预计数量" placeholder="请输入" type="number"
                @change="e => countChange(e, v)" />
        </div>


        <van-popup v-model="showAddPopup" position="bottom" style="height:20rem;">
            <a-row>
                <a-col :span="6" style="line-height: 2rem;margin-left:1rem;"> 备件 </a-col>
                <a-col :span="16">
                    <a-select placeholder="请选择备件" style="width: 100%;" v-model="addObjData.partsId" :dropdown-match-select-width="false" :dropdown-style="{ maxWidth: '260px', wordWrap: 'break-word', whiteSpace: 'normal', overflow: 'hidden' }" class="custom-select">
                        <!-- :disabled="partsListRes.some(v => v.partsId === item.id)" -->
                        <a-select-option v-for="(item, index) in addObjData.arr" :value="item.id" :key="index" class="custom-option">
                            {{ item.name }}--数量:({{ item.stock }})
                        </a-select-option>
                    </a-select>
                </a-col>
            </a-row>
            <van-field v-model="addObjData.expectCount" name="partsCount" label="预计数量" placeholder="请输入"
                type="number" />

            <van-button type="primary" style="width:100%;margin-top:2rem;" @click="addDetail">新增</van-button>

        </van-popup>


        <van-button type="primary" style="width:100%;margin-top:2rem;" @click="onSubmit">提交</van-button>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
export default {

    data() {
        return {
            info: {},
            formData: {
                uploader: [],
            },

            addObjData: {},

            showAddPopup: false,



            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenterColumns: [],
            showTypePicker: false,
            typeColumns: ['生产维修', '设备项目'],

            pictureList: [],

            detailList: [],
        };
    },
    created() {
        this.info = this.$route.params
        this.formData =JSON.parse(JSON.stringify(Object.assign(this.formData, { ...this.info }))) 

        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
            if (res.data.code == 200) {
                console.log(res.data.result);
                res.data.result.forEach(item => {
                    this.bookNameColumns.push(item.name);
                });
            } else {
            }
        });
        this.$axios.get(`/jeecg-boot/app/improve/getMacImproveDetailList?mainId=${this.info.id}`).then(res => {
            if (res.data.code == 200) {
                this.detailList = res.data.result
            } else {
            }
        });
        this.$axios.get(`/jeecg-boot/app/improve/getMacImprovePictureList?mainId=${this.info.id}`).then(res => {
            if (res.data.code == 200) {
                this.formData.uploader = res.data.result.map(item => {
                    return {
                        url: item.picUrl,
                        id: item.id,
                    }
                })
                this.pictureList = res.data.result.map(item => {
                    return {
                        picUrl: item.picUrl,
                        id: item.id,
                    }
                })
            } else {
            }
        });
    },
    methods: {
        addDetail(){
            this.addObjData.mainId=this.info.id
             let dataarr= this.addObjData.arr.map(item=>{
                if(item.id == this.addObjData.partsId){
                    return {partsName:item.name,partsSpec:item.spec}
                }
            })
            this.addObjData=Object.assign(this.addObjData, this.addObjData,dataarr[0])
            this.addObjData.userCode=localStorage.getItem('userCode')
            this.$axios.post(`/jeecg-boot/app/improve/addMacImproveDetail`,this.addObjData).then(res => {
                if (res.data.code == 200) {
                    this.showAddPopup=false
                    this.$axios.get(`/jeecg-boot/app/improve/getMacImproveDetailList?mainId=${this.info.id}`).then(res => {
                        if (res.data.code == 200) {
                            this.detailList = res.data.result
                        } else {
                        }
                    });
                } else {
                }
            });
        },
        countChange(e, v) {
            console.log(e, v);
        },
        handleDel(v, i) {
            this.$axios.delete(`/jeecg-boot/app/improve/deleteByDetailId?detailId=${v.id}&userCode=${localStorage.getItem('userCode')}`).then(res => {
                if (res.data.code == 200) {
                    this.$axios.get(`/jeecg-boot/app/improve/getMacImproveDetailList?mainId=${this.info.id}`).then(res => {
                        if (res.data.code == 200) {
                            this.detailList = res.data.result
                        } else {
                        }
                    });
                } else {
                }
            });
        },
        addParts() {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果

                                self.getParts(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入备件编号").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getParts(self.code)
                            }
                        });
                    }
                });
        },
        getParts(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/ncApp/parts/getPartsListByCode?code=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        self.$set( self.addObjData,'arr',res.data.result)
                        self.showAddPopup=true
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        handleSearch(v) {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果
                                self.getList(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入设备ID").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getList(self.code)
                            }
                        });
                    }
                });
        },
        getList(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/app/device/list?id=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result.records[0].deviceName);
                        console.log(res.data.result.records[0]);
                        this.$set(self.formData, "machineName", res.data.result.records[0].deviceName)
                        this.$set(self.formData, "machineId", res.data.result.records[0].id)
                        this.$set(self.formData, "machineNo", res.data.result.records[0].deviceNo)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        bookNameConfirm(value) {
            this.formData.book = value;
            this.formData.workshop = '';
            this.formData.jobCenter = '';
            localStorage.setItem('feedingCheckBook', this.bookName)
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value }
                })
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.workshopColumns = []
                        res.data.result.forEach(item => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.formData.workshop = value;
            this.formData.mitosome = '';
            this.showworkshopPicker = false;
            // 查找工作中心
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
                    params: { book: this.bookName, workshop: value }
                })
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach(item => {
                            this.jobCenterColumns.push(item.jobCenter);
                        });
                    } else {
                    }
                });
        },
        jobCenterConfirm(value) {
            this.formData.mitosome = value;
            this.showjobCenterPicker = false;
        },
        typeConfirm(value) {
            this.formData.type = value;
            this.showTypePicker = false;
        },
        onSubmit() {
            console.log(this.formData);
            console.log(this.detailList);
            console.log(this.pictureList);
            let param = {
                ...this.formData,
                detailList: this.detailList,
                pictureList: this.pictureList,
                userCode: localStorage.getItem('userCode'),
                userName: localStorage.getItem('userName')
            }
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .put(`/jeecg-boot/app/improve/editImproveMain`, param)
                .then(res => {
                    if (res.data.code == 200) {
                        Indicator.close();
                        this.$router.go(-1);
                    } else {
                        Indicator.close();
                        Toast({
                            message: "操作失败",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios.post(`/jeecg-boot/app/improve/uploadPic`, param).then(res => {
                if (res.data.code == 200) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            console.log(file, name);
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then((res) => {
                    console.log(res);
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/app/mac/deletePic?id=${file.id}&picUrl=${file.url}`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                            } else {
                                Indicator.close();
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });

                })
                .catch(() => {
                    Indicator.close();
                    this.formData.uploader.splice(name.index, 1);
                    this.pictureList.splice(name.index, 1);
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
    }
};
</script>

<style>
:deep(.ant-select-dropdown){
    z-index: 2050;
}

/* 添加自定义样式使 select option 能够固定宽度并换行 */
.custom-option {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100% !important;
  display: block !important;
  line-height: 1.5;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 专门针对Ant Design的样式 */
.ant-select-dropdown-menu-item, 
.ant-select-dropdown-menu-item-selected {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow: hidden !important;
  padding: 5px 8px !important;
  height: auto !important;
  min-height: 32px !important;
}

.ant-select-dropdown {
  width: 300px !important;
}

/* 禁用水平滚动 */
.ant-select-dropdown-menu {
  overflow-x: hidden !important;
}

/* 控制下拉选项的样式 */
.ant-select-dropdown-menu-item-content,
.ant-select-dropdown-menu-item-content span {
  white-space: normal !important;
  word-break: break-all !important;
  display: inline-block !important;
  width: 100% !important;
}

/* 修改a-select-option的样式 */
.ant-select-dropdown-menu-item div {
  white-space: normal !important;
  word-break: break-all !important;
}

/* 针对 Ant Design Vue 1.x 的特殊处理 */
.ant-select-dropdown-menu-item {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: normal !important;
}

/* 处理实际文本内容 */
.ant-select-dropdown ul li {
  white-space: normal !important;
}
</style>