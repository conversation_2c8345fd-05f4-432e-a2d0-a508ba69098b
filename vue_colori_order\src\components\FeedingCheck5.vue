<template>
  <div style="text-align:left;background-color:#fff;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="单据状态" right-text="筛选" @click-right="onClickRight" left-arrow @click-left="onClickLeft" />
    </van-sticky>



    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
      <!-- 时间 -->
      <van-field readonly clickable label="选择时间" :value="queryParams.date" @click="showDate = true" />
      <van-calendar v-model="showDate" type="range" @confirm="onConfirm" :min-date="new Date(2022)" color="#1989fa" />

      <!-- 账套 -->
      <van-field readonly clickable :clearable="true" name="picker" v-model="queryParams.book" label="账套："
        placeholder="点击选择账套" @click="showbookNamePicker = true" />
      <van-popup v-model="showbookNamePicker" position="bottom">
        <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
          @cancel="showbookNamePicker = false" />
      </van-popup>

      <!-- 车间 -->
      <van-field readonly clickable :clearable="true" name="picker" v-model="queryParams.workshop" label="车间："
        placeholder="点击选择车间" @click="showworkshopPicker = true" />
      <van-popup v-model="showworkshopPicker" position="bottom">
        <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
          @cancel="showworkshopPicker = false" />
      </van-popup>


      <van-field readonly clickable :clearable="true" name="picker" v-model="queryParams.jobCenter" label="工作中心："
        placeholder="点击选择工作中心" @click="showjobCenterPicker = true" />
      <van-popup v-model="showjobCenterPicker" position="bottom">
        <van-picker show-toolbar :columns="jobCenterColumns" @confirm="jobCenterConfirm"
          @cancel="showjobCenterPicker = false" />
      </van-popup>

      <van-field v-model="queryParams.moId" clearable label="MO单：" placeholder="请输入MO单号" />
      <van-field v-model="queryParams.glue" clearable label="胶体：" placeholder="请输入胶体名称或编码" />

      <van-field readonly clickable :clearable="true" name="picker" v-model="queryParams.statusText" label="状态："
        placeholder="点击选择状态" @click="showstatusPicker = true" />
      <van-popup v-model="showstatusPicker" position="bottom">
        <van-picker show-toolbar :columns="statusColumns" @confirm="statusConfirm" @cancel="showstatusPicker = false" />
      </van-popup>

      <van-button type="info" @click="search" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>

    <div v-for="(item, index) in dataArr1" :key="index" @click='goDetail(item)'
      style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;" >
        <van-row>
          <van-col span="16">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{ item.remark }}
            </span>
          </van-col>
          <van-col span="8" style="text-align: right;">
            <span v-if="item.status==1" style="font-size:18px;font-weight: 700;color: #000;">待指派</span>
            <span v-if="item.status==2" style="font-size:18px;font-weight: 700;color: #000;">已指派</span>
            <span v-if="item.status==3" style="font-size:18px;font-weight: 700;color: #000;">已称重</span>
            <!-- <span v-if="item.status==4" style="font-size:18px;font-weight: 700;color: #000;">复核员已复核</span>
            <span v-if="item.status==5" style="font-size:18px;font-weight: 700;color: #000;">拉料工已复核</span> -->
            <span v-if="item.status==6" style="font-size:18px;font-weight: 700;color: #000;">已复核</span>
            <span v-if="item.status==11" style="font-size:18px;font-weight: 700;color: #000;">已投产</span>
            <span v-if="item.status==12" style="font-size:18px;font-weight: 700;color: #000;">完成投料复核</span>
          </van-col>
        </van-row>
      </div>

      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体编码：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体名称：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueName }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 工作中心：</van-col>
            <van-col span="18">
              <span style="color:black;">
                {{ item.jobCenter }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体批次号：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{item.glueBatchCode}}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 原料数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.materialNumber }}种 </span>
            </van-col>
          </van-row>
        </van-col>

      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 标签数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.signNumber }}张 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { Indicator } from "mint-ui";
  export default {
    data() {
      return {
        active: 0,
        userCode: localStorage.getItem("userCode"),
        //   拉料工校验
        dataArr1: [],
        //   操作工校验
        dataArr2: [],
        //   已完成
        dataArr3: [],


        show: false,

        showDate: false,

        showbookNamePicker: false,
        bookName: "",
        bookNameColumns: [],

        showworkshopPicker: false,
        workshop: "",
        workshopColumns: [],

        showjobCenterPicker: false,
        jobCenter: "",
        jobCenterColumns: [],


        showstatusPicker: false,
        status: "",
        statusColumns: ['全部', '待指派', '已指派', '已称重', '复核员已复核', '拉料工已复核', '操作工已复核','已投产','完成投料复核'],

        moId: '',
        glue: '',

        queryParams: {
          startDate: this.getDate(0),
          date: this.getDate(0) + ' ~ ' + this.getDate(0),
          endDate: this.getDate(0),
          book: '',
          workshop: '',
          jobCenter: '',
          moId: '',
          glue: '',
          status: '',
        }

      };
    },
    created() {
      if (this.userCode == null || this.userCode == "") {
        Toast({
          message: "请先登录",
          position: "bottom",
          duration: 2000
        });
        this.$router.push({
          name: "LoginIndex"
        });
      } else {
        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            res.data.result.forEach(item => {
              this.bookNameColumns.push(item.name);
            });
          } else {
          }
        });
      }
      this.bookName = localStorage.getItem('feedingCheckBook')
      this.bookNameConfirm(this.bookName)
      this.search();
    },
    methods: {
      bookNameConfirm(value) {
        this.queryParams.book = value;
        this.queryParams.workshop = '';
        this.queryParams.jobCenter = '';
        localStorage.setItem('feedingCheckBook', this.bookName)
        this.showbookNamePicker = false;
        //查找车间
        this.$axios.get('/jeecg-boot/app/warehouse/getFactoryInfoByCode', { params: { code: value } }).then(res => {
          if (res.data.code = 200) {
            this.workshopColumns = []
            res.data.result.forEach(item => {
              this.workshopColumns.push(item.name);
            });
          } else {

          }
        })
      },
      workshopConfirm(value) {
        this.queryParams.workshop = value;
        this.showworkshopPicker = false;
        // 查找工作中心
        this.$axios.get(`/jeecg-boot/ncApp/molds/getJobCenter`, { params: { book: this.bookName, workshop: value } }).then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            res.data.result.forEach(item => {
              this.jobCenterColumns.push(item.jobCenter);
            });
          } else {
          }
        });
      },
      jobCenterConfirm(value) {
        this.queryParams.jobCenter = value;
        this.showjobCenterPicker = false;
      },
      statusConfirm(value) {
        this.queryParams.statusText = value
        if (value == '全部') {
          this.queryParams.status = ''
        } else if (value == '待指派') {
          this.queryParams.status = '1'
        } else if (value == '已指派') {
          this.queryParams.status = '2'
        } else if (value == '已称重') {
          this.queryParams.status = '3'
        } else if (value == '复核员已复核') {
          this.queryParams.status = '4'
        } else if (value == '拉料工已复核') {
          this.queryParams.status = '5'
        } else if (value == '已复核') {
          this.queryParams.status = '6'
        } else if (value == '已投产') {
          this.queryParams.status = '11'
        } else if (value == '完成投料复核') {
          this.queryParams.status = '12'
        }
        this.showstatusPicker = false;
      },
      onClickRight() {
        this.show = true;
      },
      onConfirm(date) {
        const [start, end] = date;
        this.queryParams.startDate = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
        this.queryParams.endDate = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
        this.queryParams.date = `${this.queryParams.startDate}~${this.queryParams.endDate}`
        this.showDate = false;
      },
      search() {
        Indicator.open({
          text: "正在加载中，请稍后……",
          spinnerType: "fading-circle"
        });
        this.$axios
          .get(
            `/jeecg-boot/app/gcMix/getMixMainList?startDate=${this.queryParams.startDate}&endDate=${this.queryParams.endDate}&moId=${this.queryParams.moId}&glue=${this.queryParams.glue}&book=${this.queryParams.book}&workshop=${this.queryParams.workshop}&jobCenter=${this.queryParams.jobCenter}&userCode=${this.userCode}&status=${this.queryParams.status}`
          )
          .then(res => {
            this.show = false
            if (res.data.code == 200) {
              console.log(res.data.result.records);
              this.dataArr1 = res.data.result.records;
              console.log(this.dataArr1);
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          }).finally(() => {
            Indicator.close();
          })
      },
      goDetail(item) {
        console.log(item);
        this.$router.push({
          name: "FeedingCheckDetail5",
          params: item
        });
      },
      onClickLeft() {
        this.$router.go(-1);
      },
      onClick(name, title) {
        console.log(name, title);
      },
      detail(item, num) {
        if (num == 1) {
          this.$router.push({
            name: "FeedingCheckDetail1",
            params: item
          });
        } else if (num == 2) {
          this.$router.push({
            name: "FeedingCheckDetail2",
            params: item
          });
        } else if (num == 3) {
          this.$router.push({
            name: "FeedingCheckDetail3",
            params: item
          });
        } else if (num == 4) {
          this.$router.push({
            name: "FeedingCheckDetail4",
            params: item
          });
        }
      },
      getDate(day) {
        var date1 = new Date(),
          time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
        var date2 = new Date(date1);
        date2.setDate(date1.getDate() + day);
        return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
      },
    }
  };
</script>

<style scoped>

</style>