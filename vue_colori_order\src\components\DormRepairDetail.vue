<template>
<div class="repair">
    <van-nav-bar
        title="维修详情"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
        <van-cell-group>
            <van-cell class="vanCellClass" title="登记时间" :value="repair.createTime" />
            <van-cell class="vanCellClass" title="登记人" :value="repair.creator" />
            <van-cell class="vanCellClass" title="登记工号" :value="repair.userCode" />
            <van-cell class="vanCellClass" title="联系电话" :value="repair.phone" />
            <van-cell class="vanCellClass" title="宿舍信息" :value="`${repair.areaName}-${repair.buildingName}-${repair.roomNumber}`" />
            <van-cell class="vanCellClass" title="维修状态" :value="repair.maintainStatus|fMStatus" />
            <van-cell class="vanCellClass" title="维修类型" :value="repair.type" />
            <van-cell class="vanCellClass" title="维修内容" value=" ">
                <template #label>
                    <div v-html="repair.content"></div>
                </template>
            </van-cell>
            <van-cell class="vanCellClass" title="固定设施" :value="repair.articleName" />
            <van-cell class="vanCellClass" title="关闭原因" value=" " v-if="repair.closeReason && repair.closeReason!=''">
                <template #label>
                    <div v-html="repair.closeReason" ></div>
                </template>
            </van-cell>
            <van-cell class="vanCellClass" title="照片" value=" " v-if="repair.pictureUrl && repair.pictureUrl!=''">
                <template #label>
                    <div v-for="img,index in repair.pictureUrl.split(',')" :key="index">
                        <van-image :src="`/dormApi/${img}`" @click="onImagePreview(img)"></van-image>
                    </div>
                </template>
            </van-cell>
            
            <!-- <van-cell class="vanCellClass" title="操作">
                <template #default>
                    <van-button type="warning" size="small" @click="onRollBack">撤销</van-button>
                </template>
            </van-cell> -->
        </van-cell-group>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            repairId: "",
            repair: {},
        }
    },
    methods:{
        onRollBack(){
            const _THIS=this
            Dialog.confirm({
                title: '提示',
                message: '确认撤销？',
            }).then(()=>{
                let rParams={
                    id: this.repairId,
                    userCodeRequest:localStorage.getItem('userCode'),
                }
                _THIS.$axios.get("/dormApi/maintain/app/rollBackRecord",{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            this.$router.go(0)
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    }else{
                        Toast({
                            message: '发生错误',
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            })
        },
        getRepair(){
            const rParams={
                id: this.repairId
            }
            this.$axios.get("/dormApi/maintain/app/getMaintainRecordOne", { params: rParams }).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        let repair=res.result
                        if(repair.content && repair.content!=""){
                            repair.content=repair.content.replace(/\n/g,'<br>')
                        }
                        if(repair.closeReason && repair.closeReason!=""){
                            repair.closeReason=repair.closeReason.replace(/\n/g,'<br>')
                        }
                        this.repair=repair
                    }else{
                        Toast.fail(res.message);
                    }
                }else{
                    Toast.fail('发生错误');
                }
            })
        },
        onImagePreview(pImg){
            ImagePreview({
                images: [`/dormApi/${pImg}`],
                closeable: true
            })
        },
        onClickLeft(){
            this.$router.go(-1)
        },
    },
    created(){
        this.repairId= this.$route.query.rId
        this.getRepair()
    },
    filters: {
        fStatus(status){
            switch(status){
                case "0": return "正常";
                case "1": return "失效";
                default: return status;
            }
        },
        fMStatus(mStatus){
            switch(mStatus){
                case "1": return "待处理";
                case "2": return "已报修";
                case "3": return "已完成";
                case "4": return "已撤销";
                case "5": return "已关闭";
                default: return mStatus;
            }
        }
    }
}
</script>
<style scoped>
.vanCellClass{
    color: #646566;
    text-align: left;
}
.repairItem{
    width: 92%;
    padding: 10px;
    margin: 0 auto;
    border-radius: 8px;
    background: #eee;
    text-align: left;
}
</style>