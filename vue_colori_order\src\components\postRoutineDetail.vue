<template>
    <div style="text-align:left;padding-bottom: 1%;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="例事明细"  left-arrow  />
        </van-sticky>
        <van-popup v-model="show" position="bottom" :style="{ height: '35%' }">
            <van-field v-model="userName" clearable label="姓名：" placeholder="请输入姓名" />
            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>
        <div v-for="(item, index) in list" :key="index"
            style="text-align: left;margin-bottom: 3%;background-color: #fbf8fb; padding: 3%; border-radius: 5px;">
            <div style="display: flex">
                <p style="width: 80%; word-wrap:break-word;">
                    <span style="font-weight: 700; font-size: 18px">
                        {{ item.content }}
                    </span>

                </p>
                <p style="flex: 1; text-align: right">
                    <span v-if="item.status == 0" style="color: red">失效</span>
                    <span v-if="item.status == 1" style="color: green">生效</span>
                </p>
            </div>

            <div style="display: flex">
                <p style="width: 20%">预完日:</p>
                <p style="width: 80%; text-align: right" v-if="item.type == 1">每日{{ item.completionTime }}</p>
                <p style="width: 80%; text-align: right" v-if="item.type == 2">
                    每
                    <span v-if="item.completionDay == 1">周日</span>
                    <span v-if="item.completionDay == 2">周一</span>
                    <span v-if="item.completionDay == 3">周二</span>
                    <span v-if="item.completionDay == 4">周三</span>
                    <span v-if="item.completionDay == 5">周四</span>
                    <span v-if="item.completionDay == 6">周五</span>
                    <span v-if="item.completionDay == 7">周六</span>
                    {{ item.completionTime }}
                </p>
                <p style="width: 80%; text-align: right" v-if="item.type == 3">每月 {{ item.completionDay * 1 + 1 }} 日 {{ item.completionTime }} </p>
                <p style="width: 80%; text-align: right" v-if="item.type == 4">每年 {{ item.completionDay }}-{{ item.completionTime }} </p>
            </div>

        </div>
    </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
    data() {
        return {
            // 搜索弹出层是否显示
            show: false,
            userName: '',
            list: [],
            info: {},
        };
    },
    created() {
        this.info = this.$route.query;
        console.log(this.info);
        this.search();
    },
    methods: {
        search() {
            this.$axios.get(`/jeecg-boot/app/example/getStaffExampleList?userCode=${this.info.userCode}&department=${this.info.department}&ncWork=${this.info.ncWork}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.list = res.data.result.filter(item=>item.status!=0);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.replace({
                name: "postRoutine"
            });
        },
        onClickRight() {
            this.show = true;
        },
    }
};
</script>

<style scoped></style>