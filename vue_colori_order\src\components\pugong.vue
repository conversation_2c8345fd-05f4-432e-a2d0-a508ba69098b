<template>
  <div>
    <H3 style="text-align:center">普工入职登记表</H3>
    <van-form @submit="onSubmit" ref="pugong">

      <h2 class="jc">基本信息</h2>

      <van-field name="gzdd" label="工作地点" required>
        <template #input>
          <van-radio-group v-model="info.gzdd" direction="horizontal">
            <van-radio name="1">常熟总部</van-radio>
            <van-radio name="2">南通栟茶</van-radio>
            <van-radio name="3">成都工厂</van-radio>
          </van-radio-group>
        </template>
      </van-field>



      <van-field
        v-model="info.username"
        name="username"
        label="姓名"
        placeholder="请输入姓名" 
        :rules="[{ required:true, message: '请输入姓名' }]"
        required 
        clearable 
      />

      <van-field name="hunfou" label="性别" required >
        <template #input>
          <van-radio-group v-model="info.sex" direction="horizontal">
            <van-radio name="男">男</van-radio>
            <van-radio name="女">女</van-radio>
          </van-radio-group>
        </template>
      </van-field>

      <van-field
        v-model="info.shengao"
        type="number"
        maxlength="3"
        required
        clearable
        label="身高(CM)"
        placeholder="请输入身高(cm)"
      />

      <van-field
        v-model="info.tizhong"
        type="number"
        maxlength="5"
        required
        clearable
        label="体重(KG)"
        placeholder="请输入体重(Kg)"
      />

      <van-field
        v-model="info.papernum"
        name="papernum"
        label="身份证号"
        placeholder="请输入身份证号" 
        :rules="papernumRule"
        required 
        clearable
      />

      <van-field
        v-model="info.mz"
        label="民族"
        name="mz"
        placeholder="请输入民族"
        :rules="[{ required:true, message: '请输入民族' }]"
        required 
        clearable 
      />


      <van-field
        required 
        readonly
        clickable
        name="xueli"
        :value="info.xueli"
        label="学历"
        :rules="[{ required:true, message: '请选择学历' }]"
        placeholder="请选择学历"
        @click="showPicker = true"
      />
      <van-popup v-model="showPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="columns"
          @confirm="onConfirm"
          @cancel="showPicker = false"
        />
      </van-popup>

      <van-field-Pick
        v-model="info.rxfs"
        label="入学方式"
        placeholder="请选择"
        required
        :columns="['全日制','自考']"
      />

      <van-field-Pick
            v-model="info.zzmm"
            label="政治面貌"
            placeholder="请选择"
            required
            :columns="['中共党员','中共预备党员','共青团员','群众']"
          />


      <van-field
        v-model="info.gw"
        name="gw"
        label="应聘岗位"
        placeholder="请输入应聘岗位" 
        :rules="[{ required:true, message: '请输入应聘岗位' }]"
        required 
        clearable 
      />



      <van-field
        v-model="info.mobile"
        name="mobile"
        type="tel"
        label="联系电话"
        placeholder="请输入联系电话"
        :rules="telRules" 
        required 
        clearable 
      />


      <van-field
        v-model="info.jg"
        name="jg"
        label="籍贯"
        placeholder="请输入籍贯"
        :rules="jgRules"
        required 
        clearable 
      />


    <van-field name="hunfou" label="婚否" required >
        <template #input>
          <van-radio-group v-model="info.hunfou" direction="horizontal">
            <van-radio name="1">未婚</van-radio>
            <van-radio name="2">已婚未育</van-radio>
            <van-radio name="3">已婚已育</van-radio>
            <van-radio name="4">离异</van-radio>
          </van-radio-group>
        </template>
      </van-field>


      <van-field
        required 
        readonly
        clickable
        name="zpqd"
        :value="info.zpqd"
        label="招聘渠道"
        :rules="[{ required:true, message: '请选择招聘渠道' }]"
        placeholder="请选择招聘渠道"
        @click="showZpPicker = true"
      />
      <van-popup v-model="showZpPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="Zpcolumns"
          @confirm="onZpConfirm"
          @cancel="showZpPicker = false"
        />
      </van-popup>

      <van-field
        v-model="info.address"
        name="address"
        type="textarea" 
        rows="2"
        label="身份证地址"
        placeholder="请输入身份证地址"
        :rules="[{ required:true, message: '请输入身份证地址' }]"
        required 
        clearable 
      />


      <van-field
        v-model="info.now_address"
        name="now_address"
        type="textarea" 
        rows="2"
        label="现住地址"
        placeholder="请输入现住地址"
        :rules="[{ required:true, message: '请输入现住地址' }]"
        required 
        clearable 
      />



      <van-field
        v-model="info.lzyy"
        name="lzyy"
        type="textarea" 
        rows="2"
        label="上份工作离职原因"
        placeholder="请输入上份工作离职原因"
        :rules="[{ required:true, message: '请输入上份工作离职原因' }]"
        required 
        clearable 
      />


      <van-field name="bgjl" label="是否有我司办公经历" required >
        <template #input>
          <van-radio-group v-model="info.bgjl" direction="horizontal">
            <van-radio name="是">是</van-radio>
            <van-radio name="否">否</van-radio>
          </van-radio-group>
        </template>
      </van-field>


      <van-field name="nbjs" label="是否有我司内部介绍" required >
        <template #input>
          <van-radio-group v-model="info.nbjs" direction="horizontal">
            <van-radio name="Y">是</van-radio>
            <van-radio name="N">否</van-radio>
          </van-radio-group>
        </template>
      </van-field>


      <h2 class="jc" v-if="info.nbjs=='Y'">介绍人</h2>

      <van-field
        v-model="info.jsr.xm"
        name="jsrxm"
        label="姓名"
        placeholder="请输入姓名"
        v-if="info.nbjs=='Y'"
        clearable 
      />


      <van-field
        readonly
        clickable
        name="jsrgx"
        :value="info.jsr.gx"
        label="关系"
        placeholder="请选择关系"
        v-if="info.nbjs=='Y'"
        @click="showJGxPicker = true"
      />
      <van-popup v-model="showJGxPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="gx_columns"
          @confirm="onJGxConfirm"
          @cancel="showJGxPicker = false"
        />
      </van-popup>


      <van-field
        v-model="info.jsr.bm"
        name="jsrbm"
        label="部门"
        v-if="info.nbjs=='Y'"
        placeholder="请输入部门"
        clearable 
      />


      <van-field
        v-model="info.jsr.zw"
        name="jsrzw"
        label="职务"
        v-if="info.nbjs=='Y'"
        placeholder="请输入职务"
        clearable 
      />


      <van-field
        v-model="info.jsr.mobi"
        name="jsrmobi"
        type="tel"
        label="联系电话"
        v-if="info.nbjs=='Y'"
        placeholder="请输入联系电话"
        clearable 
      />




      <!-- <h2 class="jc">紧急联系人</h2>

      <van-field
        v-model="info.jjlx.xm"
        name="jjlxxm"
        label="姓名"
        placeholder="请输入姓名"
        :rules="[{ required:true, message: '请输入紧急联系人姓名' }]"
        required 
        clearable 
      />


      <van-field
        required 
        readonly
        clickable
        name="jjlxgx"
        :value="info.jjlx.gx"
        :rules="[{ required:true, message: '请选择紧急联系人关系' }]"
        label="关系"
        placeholder="请选择关系"
        @click="showGxPicker = true"
      />
      <van-popup v-model="showGxPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="gx_columns"
          @confirm="onGxConfirm"
          @cancel="showGxPicker = false"
        />
      </van-popup>


      <van-field
        v-model="info.jjlx.gzdd"
        name="jjlxgzdd"
        label="工作单位"
        placeholder="请输入工作单位"
        clearable 
      />


      <van-field
        v-model="info.jjlx.mobi"
        label="联系电话"
        type="tel"
        name="jjlxmobi"
        placeholder="请输入联系电话"
        :rules="telRules" 
        required 
        clearable 
      /> -->



      


      <div v-for="(item,index) in info.xxjl" :key="index">

      <h2 class="jc">学习经历{{index+1}}</h2>


      <month-year-picker
          v-model="info.xxjl[index].bdate"
          label="起始时间"
          placeholder="请选择起始年月"
          required
          :rules="[{ required:true, message: '请选择起始年月' }]"
      />


      <month-year-picker
          v-model="info.xxjl[index].edate"
          label="结束时间"
          placeholder="请选择结束时间"
          required
          :rules="[{ required:true, message: '请选择结束时间' }]"
      />

      <van-field
        v-model="info.xxjl[index].xxmc"
        name="xxjlxxmc"
        label="学校名称"
        placeholder="请输入学校名称"
        required
        :rules="[{ required:true, message: '请输入学校名称' }]"
        clearable 
      />

      <van-field
        v-model="info.xxjl[index].zy"
        name="xxjlzy"
        label="专业"
        placeholder="请输入专业"
        required
        :rules="[{ required:true, message: '请输入专业' }]"
        clearable 
      />

      </div>


      <div style="padding:20px;">
        <div class="cbtn" style="color:blue;" @click="addXxjl" v-if="info.xxjl.length<2">新增</div>
        <div class="cbtn" style="color:red;" @click="removeXxjl" v-if="info.xxjl.length>1">删除</div>
        <div style="clear:both;"></div>
      </div>


      <div v-for="(item,index) in info.gzjl" :key="index">
      <h2 class="jc">工作经历{{index+1}}</h2>


      <month-year-picker
          v-model="info.gzjl[index].bdate"
          label="开始时间"
          placeholder="请选择开始时间"
          required
          :rules="[{ required:true, message: '请选择开始时间' }]"
      />


      <month-year-picker
          v-model="info.gzjl[index].edate"
          label="结束时间"
          placeholder="请选择结束时间"
          required
          :rules="[{ required:true, message: '请选择结束时间' }]"
      />


      <van-field
        v-model="info.gzjl[index].gzdw"
        name="gzjlgzdw"
        label="工作单位"
        placeholder="请输入工作单位"
        required
        :rules="[{ required:true, message: '请输入工作单位' }]"
        clearable 
      />



      <van-field
        v-model="info.gzjl[index].zw"
        name="gzjlzw"
        label="职务"
        placeholder="请输入职务"
        required
        :rules="[{ required:true, message: '请输入职务' }]"
        clearable 
      />


      <van-field
        v-model="info.gzjl[index].zmr"
        name="gzjlzmr"
        label="证明人"
        placeholder="请输入证明人"
        required
        :rules="[{ required:true, message: '请输入证明人' }]"
        clearable 
      />

      <van-field
        v-model="info.gzjl[index].lxdh"
        name="gzjllxdh"
        label="联系电话"
        placeholder="请输入联系电话"
        required
        :rules="[{ required:true, message: '请输入联系电话' }]"
        clearable 
      />


      </div>


      <div style="padding:20px;">
        <div class="cbtn" style="color:blue;" @click="addGzjl" v-if="info.gzjl.length<3">新增</div>
        <div class="cbtn" style="color:red;" @click="removeGzjl" v-if="info.gzjl.length>1">删除</div>
        <div style="clear:both;"></div>
      </div>


      <div v-for="(item,index) in info.jtcy" :key="index">
      <h2 class="jc">家庭成员{{index+1}}</h2>


      <van-field
        v-model="info.jtcy[index].xm"
        name="jtcyxm"
        label="姓名"
        placeholder="请输入姓名"
        :rules="[{ required:true, message: '请输入家庭成员姓名' }]"
        required 
        clearable 
      />


      <van-field-Pick required  v-model="info.jtcy[index].gx" label="关系" placeholder="请选择与成员的关系"
          :columns="['父亲','母亲','夫妻','儿女','哥哥','姐姐','舅舅','舅妈','姑父','姑姑','爷爷','奶奶','外公','外婆','其他']" />



      <van-field
        v-model="info.jtcy[index].age"
        name="jtcyage"
        label="年龄"
        placeholder="请输入年龄"
        :rules="[{ required:true, message: '请输入家庭成员年龄' }]"
        required 
        clearable 
      />


      <van-field
        v-model="info.jtcy[index].gzdw"
        name="jtcygzdw"
        label="工作单位"
        placeholder="请输入工作单位"
        :rules="[{ required:true, message: '请输入工作单位' }]"
        required 
        clearable 
      />


      <van-field
        v-model="info.jtcy[index].zw"
        name="jtcyzw"
        label="职务"
        placeholder="请输入职务"
        :rules="[{ required:true, message: '请输入职务' }]"
        required 
        clearable 
      />


      <van-field
        v-model="info.jtcy[index].lxdh"
        name="jtcylxdh"
        label="联系电话"
        placeholder="请输入联系电话"
        :rules="[{ required:true, message: '请输入联系电话' }]"
        required 
        clearable 
      />


      </div>


      <div style="padding:20px;">
        <div class="cbtn" style="color:blue;" @click="addJtcy" v-if="info.jtcy.length<3">新增</div>
        <div class="cbtn" style="color:red;" @click="removeJtcy" v-if="info.jtcy.length>1">删除</div>
        <div style="clear:both;"></div>
      </div>




      <h3 style="text-align:center">员工声明</h3>

      <van-field
        v-model="ygsm"
        type="textarea" 
        rows="16"
        readonly 
        clearable 
      />

      <van-checkbox v-model="checked" icon-size="24px" style="padding-left:4%;">本人已知悉并同意此协议</van-checkbox>




      <van-button round type="info" size="normal" :disabled="disabled" native-type="submit" style="margin:10%;width:80%;text-align:center;">提交</van-button>
      

    </van-form>

  </div>
</template>


<script>
import { Notify,Toast } from 'vant';
export default {
  components: {
    [Notify.Component.name]: Notify.Component,
  },
  name: '普工入职申请表',
  data () {
    return {
      showPicker: false,
      showZpPicker: false,
      showGxPicker:false,
      showJGxPicker:false,
      showjtGxPicker:false,
      showBDatePicker:false,
      showDDatePicker:false,
      showGzBDatePicker:false,
      showGzDDatePicker:false,
      checked:true,
      disabled:false,
      minDate:new Date(1950, 0, 1),
      maxDate:new Date(2035, 11, 1),
      ygsm:'1、员工知悉入职之日起三十日内必须跟本公司人事部签订劳动合同，不签合同者公司有权不予录用。\n2、公司已经告知员工本人工作内容、工作条件、工作地点、职业危害、安全生产状况、劳动报酬以及岗位职责，并随时接受公司岗位职责考核录用条件等内容。\n3、本人保证，提供的所有资料全部属实。如有虚假，公司可以立即解除劳动关系，并不给予补偿。如有这种情况出现，我愿意承担由此引起的一切法律后果。\n4、本人保证与其他单位没有劳动合同关系，未有承担竞业限制义务。\n5、本人已收到公司的各项规章制度，并同意试用期间保证遵守公司各项规章制度。\n6、本人在正常工作时间之外根据自己的工作任务自愿延长工作时间，在周末或法定节假日根据自身的工作需要自愿留下完成工作任务。',
      columns: ['小学','初中','高中','中专','大专','本科','硕士','博士','博士后','MBA'],
      Zpcolumns:['直播平台','网络招聘','现场招聘','内部介绍'],
      gx_columns: ['父亲','母亲','夫妻','儿女','哥哥','姐姐','舅舅','舅妈','姑父','姑姑','爷爷','奶奶','外公','外婆','其他'],
      telRules: [{
                    required: true,
                    message: '联系方式不能为空',
                    trigger: 'onBlur'
                }, {
                	// 自定义校验规则
                    validator: value => {
                        return /^(0|86|17951)?(13[0-9]|15[0-9]|16[3456]|17[0-9]|18[0-9]|14[57]|19[0-9])[0-9]{8}$/
                            .test(value)
                    },
                    message: '请输入正确格式的联系方式',
                    trigger: 'onBlur'
                }],
      jgRules:[{
        required: true,
        message: '籍贯不能为空',
        trigger: 'onBlur'
      },{
        validator: value => {
          return value.length>=4
        },
        message: '不得少于四个汉字，例：江苏常熟',
        trigger: 'onBlur'
      }],
      papernumRule:[{
        required: true,
        message: '身份证号不能为空',
        trigger: 'onBlur'
      }, {
        // 自定义校验规则
        validator: value => {
            return /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/
            .test(value)
        },
        message: '请输入正确格式的身份证号',
        trigger: 'onBlur'
      }],
      info: {
        gzdd:'1',
        sex:'男',
        shengao:'',
        bgjl:'否',
        gw:'',
        tizhong:'',
        username:'',
        papernum:'',
        zzmm:'群众',
        mz:'汉',
        rxfs:'',
        nbjs:'N',
        xueli:'',
        birthday:'',
        mobile:'',
        jg:'',
        hunfou:'1',
        address:'',
        now_address:'',
        jjlx:{
          xm:'',
          gx:'',
          gzdd:'',
          mobi:''
        },
        jsr:{
          xm:'',
          gx:'',
          bm:'',
          zw:'',
          mobi:''
        },
        xxjl:[{
          bdate:'',
          edate:'',
          xxmc:'',
          zy:''
        }],
        gzjl:[{
          bdate:'',
          edate:'',
          gzdw:'',
          zw:'',
          zmr:'',
          lxdh:''
        }],
        jtcy:[{
          xm:'',
          gx:'',
          age:'',
          gzdw:'',
          zw:'',
          lxdh:''
        }]
      },
    }
  },
  methods: {
    onSubmit(values) {
      const toast = Toast.loading({
         duration: 0, // 持续展示 toast
         forbidClick: true,
         message: "上传中..."
      });
      this.disabled=true;
      console.log(this.info.papernum)

      var birthdays="";
      if(this.info.papernum != null && this.info.papernum!=''){
        birthdays = this.info.papernum.substring(6,14);
      }
      console.log("birthdays:"+birthdays)

      var aData = new Date();
      var month =aData.getMonth() < 9 ? "0" + (aData.getMonth() + 1) : aData.getMonth() + 1;
      var date = aData.getDate() <= 9 ? "0" + aData.getDate() : aData.getDate();
      const today=aData.getFullYear()+""+month+""+date;
      let diff=today-birthdays;
      console.log(diff)
      diff=(diff+"").substring(0,2);
      console.log(diff)
      if(diff<18){
        this.disabled=false;
        toast.clear()//清除加载效果
        Notify({ type: 'danger', message: "年龄不满18周岁，不予录用！" });
        return;
      }
      this.$refs.pugong.validate().then(()=>{
        // 验证通过
        console.log('submit', values);
        this.$axios.post('/zhaopin/saveZhaopinInfo',this.info).then(res=>{
          console.log('res', res);
          if(res.data.code=1000){
            toast.clear()//清除加载效果
            this.disabled=false;
            Notify({ type: 'success', message: res.data.msg.msg });
            this.$router.push('/success');
          }else{
            toast.clear()//清除加载效果
            this.disabled=false;
            Notify({ type: 'danger', message: res.data.msg.msg });
          }
        });
      }).catch((e)=>{
        //验证失败
        console.log('submit', e);
        this.disabled=false;
        toast.clear()//清除加载效果
        Notify({ type: 'danger', message: '数据有误，请检查' });
      })
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      }
      return val;
    },
    onjtGxConfirm(value){
      this.info.jtcy.gx=value;
      this.showjtGxPicker = false;
    },
    onGxConfirm(value){
      this.info.jjlx.gx=value;
      this.showGxPicker = false;
    },
    onJGxConfirm(value){
      this.info.jsr.gx=value;
      this.showJGxPicker = false;
    },
    onXxjlBConfirm(value,index){

      console.log(value)

      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl[index].bdate=yy+"年"+mm+"月";
      this.showBDatePicker = false;
    },
    onXxjlEConfirm(value,index){

      console.log("index:"+index)

      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl[index].edate=yy+"年"+mm+"月";
      this.showDDatePicker = false;
    },
    onGzDDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.edate=yy+"年"+mm+"月";
      this.showGzDDatePicker = false;
    },
    onGzBDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.bdate=yy+"年"+mm+"月";
      this.showGzBDatePicker = false;
    },
    onConfirm(value){
      this.info.xueli=value;
      this.showPicker=false;
    },
    onZpConfirm(value){
      this.info.zpqd=value;
      this.showZpPicker=false;
    },
    addXxjl(){
      let param = {
        bdate:'',
        edate:'',
        xxmc:'',
        zy:''
      }
      this.info.xxjl.push(param)
    },
    removeXxjl(){
      this.info.xxjl.splice(this.info.xxjl,1);
    },
    addGzjl(){
      this.info.gzjl.push({
        bdate:'',
        edate:'',
        gzdw:'',
        zw:'',
        zmr:'',
        lxdh:''
      })
    },
    removeGzjl(){
      this.info.gzjl.splice(this.info.gzjl,1);
    },
    addJtcy(){
      this.info.jtcy.push({
        xm:'',
        gx:'',
        age:'',
        gzdw:'',
        zw:'',
        lxdh:''
      })
    },
    removeJtcy(){
      this.info.jtcy.splice(this.info.jtcy,1);
    }
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.jc {
  margin: 0;
  color: rgba(69, 90, 100, 0.6);
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  padding-left: 4%;
  padding-top: 2%;
  padding-bottom: 2%;
  background: #f0f0f0;
}
h1, h2 {
  font-weight: normal;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
.cbtn{
  text-align:center;
  float:left;
  width:50%;
}
.van-radio{
  padding-bottom: 5px;
}
</style>
