<template>
  <div style="text-align:left;background-color:#fff;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="操作工校验" right-text="筛选" @click-right="onClickRight" left-arrow @click-left="onClickLeft" />
    </van-sticky>


    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">


      <!-- 时间 -->
      <van-field readonly clickable label="选择时间" :value="queryParams.date" @click="showDate = true" />
      <van-calendar v-model="showDate" type="range" @confirm="onConfirm" :min-date="new Date(2022)" color="#1989fa" />


      <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
        @click="showbookNamePicker = true" />
      <van-popup v-model="showbookNamePicker" position="bottom">
        <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
          @cancel="showbookNamePicker = false" />
      </van-popup>

      <van-field readonly clickable name="picker" v-model="workshop" label="车间：" placeholder="点击选择车间"
        @click="showworkshopPicker = true" />
      <van-popup v-model="showworkshopPicker" position="bottom">
        <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
          @cancel="showworkshopPicker = false" />
      </van-popup>

      <van-field readonly clickable name="picker" v-model="jobCenter" label="工作中心：" placeholder="点击选择工作中心"
        @click="showjobCenterPicker = true" />
      <van-popup v-model="showjobCenterPicker" position="bottom">
        <van-picker show-toolbar :columns="jobCenterColumns" @confirm="jobCenterConfirm"
          @cancel="showjobCenterPicker = false" />
      </van-popup>
      <van-field v-model="moId" clearable label="MO单：" placeholder="请输入MO单号" />
      <van-field v-model="glue" clearable label="胶体：" placeholder="请输入胶体名称或编码" />


      <van-button type="info" @click="search" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>

    <div v-for="(item, index) in dataArr1" :key="index" @click="detail(item, 2)"
      style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="24">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{item.remark}}-{{item.glueBatchCode}}
            </span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 账套：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.book }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 车间：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.workshop }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 所属MO单：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.moId }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> id：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.id }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体编码：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体名称：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueName }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 工作中心：</van-col>
            <van-col span="18">
              <span style="color:black;">
                {{ item.jobCenter }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 重量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.expectWeight }}KG </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 原料数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.materialNumber }}种 </span>
            </van-col>
          </van-row>
        </van-col>

      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 标签数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.signNumber }}张 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <!-- <van-row>
        <van-col span="16"> </van-col>
        <van-col span="4">
          <van-button type="info" @click="editCode" style="width: 100%;" round>
            修改批号
          </van-button>
        </van-col>
        <van-col span="4">
          <van-button type="info"  style="width: 100%;" round>
            复核
          </van-button>
        </van-col>
      </van-row> -->
    </div>

    <van-dialog v-model="dialogshow" title="请确认胶体批号和工作中心(批次号为9位)" show-cancel-button showConfirmButton
      @confirm="confirm" @cancel="cancel">
      <van-field v-model="glueBatchCode" label="胶体批号" />

      <van-field readonly :clickable='canClick' name="picker" v-model="jobCenterValue" label="工作中心：" placeholder="点击选择工作中心"
        @click="jobClick" />
    </van-dialog>
    <van-popup v-model="showjobCenterValuePicker" position="bottom">
      <van-picker show-toolbar :columns="jobCenterValueColumns" @confirm="jobCenterValueConfirm"
        @cancel="showjobCenterValuePicker = false" />
    </van-popup>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { Indicator } from "mint-ui";
  export default {
    data() {
      return {
        active: 0,
        userCode: localStorage.getItem("userCode"),
        //   拉料工校验
        dataArr1: [],
        //   操作工校验
        dataArr2: [],
        //   已完成
        dataArr3: [],
        show: false,

        showbookNamePicker: false,
        bookName: "",
        bookNameColumns: [],

        showworkshopPicker: false,
        workshop: "",
        workshopColumns: [],

        showjobCenterPicker: false,
        jobCenter: "",
        jobCenterColumns: [],
        moId: '',
        glue: '',
        glueBatchCode: '',//批次号确认
        dialogshow: false,//批次号确认弹窗
        checkObj: {},

        jobCenterValue: '',//工作中心确认
        jobCenterValueColumns: [],
        showjobCenterValuePicker: false,//工作中心 选择弹框

        showDate:false,
        
        queryParams: {
          startDate: this.getDate(-1),
          date: this.getDate(0) + ' ~ ' + this.getDate(0),
          endDate: this.getDate(0),
        },
        canClick:false,
      };
    },
    created() {
      this.canClick=false
      if (this.userCode == null || this.userCode == "") {
        Toast({
          message: "请先登录",
          position: "bottom",
          duration: 2000
        });
        this.$router.push({
          name: "LoginIndex"
        });
      } else {
        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            res.data.result.forEach(item => {
              this.bookNameColumns.push(item.name);
            });
          } else {
          }
        });
        this.bookName = localStorage.getItem('feedingCheckBook')
        this.bookNameConfirm(this.bookName)
        this.search();
      }
    },
    methods: {
      jobClick(){
        console.log(!this.canClick);
        if(!this.canClick){
          this.showjobCenterValuePicker = true
        }else{
          Toast('绑定批后记录不能修改工作中心');
        }
      },
      onConfirm(date) {
        const [start, end] = date;
        this.queryParams.startDate = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
        this.queryParams.endDate = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
        this.queryParams.date = `${this.queryParams.startDate}~${this.queryParams.endDate}`
        this.showDate = false;
      },
      getDate(day) {
        var date1 = new Date(),
          time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
        var date2 = new Date(date1);
        date2.setDate(date1.getDate() + day);
        return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
      },
      jobCenterValueConfirm(value) {
        this.jobCenterValue = value;
        this.showjobCenterValuePicker = false;
      },
      confirm() {
        if (this.glueBatchCode.trim().length < 6) {
          Toast.fail({
            message: '批次号长度不能小于6位',
            position: "bottom",
            duration: 2000
          });
          return
        }
        this.checkObj.glueBatchCode = this.glueBatchCode
        this.checkObj.jobCenter = this.jobCenterValue
        this.$axios
          .put(`/jeecg-boot/app/gcMix/editMixMain`, this.checkObj)
          .then(res => {
            if (res.data.code == 200) {
              this.search()
              Toast.success({
            message: res.data.message,
            position: "bottom",
            duration: 2000
          });
              // 取消跳转详情

              this.$router.push({
                name: "FeedingCheckDetail2",
                params: this.checkObj
              });
            } else {
              Toast.fail({
            message: res.data.message,
            position: "bottom",
            duration: 2000
          });
            }
          });
      },
      cancel() {
        this.dialogshow = false
      },
      bookNameConfirm(value) {
        this.bookName = value;
        this.workshop = '';
        this.jobCenter = '';
        localStorage.setItem('feedingCheckBook', this.bookName)
        this.showbookNamePicker = false;
        //查找车间
        this.$axios.get('/jeecg-boot/app/warehouse/getFactoryInfoByCode', { params: { code: value } }).then(res => {
          if (res.data.code = 200) {
            this.workshopColumns = []
            res.data.result.forEach(item => {
              this.workshopColumns.push(item.name);
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        })
      },
      workshopConfirm(value) {
        this.workshop = value;
        this.jobCenter = '';
        this.showworkshopPicker = false;
        // 查找工作中心
        this.$axios.get(`/jeecg-boot/ncApp/molds/getJobCenter`, { params: { book: this.bookName, workshop: value } }).then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            res.data.result.forEach(item => {
              this.jobCenterColumns.push(item.jobCenter);
            });
          } else {
          }
        });
      },
      jobCenterConfirm(value) {
        this.jobCenter = value;
        this.showjobCenterPicker = false;
      },
      onClickRight() {
        this.show = true;
      },
      search() {
        Indicator.open({
          text: "正在加载中，请稍后……",
          spinnerType: "fading-circle"
        });
        this.$axios
          .get(
            `/jeecg-boot/app/gcMix/getMixMainList?startDate=${this.queryParams.startDate}&endDate=${this.queryParams.endDate}&moId=${this.moId}&glue=${this.glue}&book=${this.bookName}&workshop=${this.workshop}&jobCenter=${this.jobCenter}&userCode=${this.userCode}&status=5`
          )
          .then(res => {
            this.show = false
            if (res.data.code == 200) {
              console.log(res.data.result.records);
              // res.data.result.records.forEach(item => {
              //   if (item.status == 5) {
              //     // 完成
              //     this.dataArr3.push(item);
              //   } else if (item.status == 4) {
              //     // 操作工校验
              //     this.dataArr2.push(item);
              //   } else if (item.status == 3) {
              //     // 拉料工校验
              //     this.dataArr1.push(item);
              //   }
              // });
              this.dataArr1 = res.data.result.records;
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          }).finally(() => {
            Indicator.close();
          })
      },
      onClickLeft() {
        this.$router.go(-1);
      },
      onClick(name, title) {
        console.log(name, title);
      },
      detail(item, num) {
        this.$axios
          .get(`/jeecg-boot/app/gcMixError/getMixErrorInfo?mainId=${item.id}`)
          .then((res) => {
            this.show = false;
            if (res.data.code == 200) {
              if (res.data.result.status == 1) {
                this.$router.push({
                  name: "FeedingCheckErrorPage",
                  params: res.data.result,
                });
              } else {
                if (item.status == 5) {
                  this.checkObj = JSON.parse(JSON.stringify(item))
                  this.glueBatchCode = item.glueBatchCode
                  this.jobCenterValue = item.jobCenter
                  this.$axios.get(`/jeecg-boot/app/mix/getTecRouting`, { params: { book: item.book, workshop: item.workshop, code: item.glueCode, workType: '4' } }).then(res => {
                    if (res.data.code == 200) {
                      res.data.result.forEach(item => {
                        this.jobCenterValueColumns.push(item.jobCenter);
                      });
                      this.dialogshow = true
                      console.log(item.checkStatus > 1&&item.checkStatus!=99);
                      if(item.checkStatus > 1&&item.checkStatus!=99){
                        this.canClick = true
                      }
                    } else {
                      Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                      });
                    }
                  });
                } else {
                  if (num == 1) {
                    this.$router.push({
                      name: "FeedingCheckDetail1",
                      params: item,
                    });
                  } else if (num == 2) {
                    this.$router.push({
                      name: "FeedingCheckDetail2",
                      params: item,
                    });
                  } else if (num == 3) {
                    this.$router.push({
                      name: "FeedingCheckDetail3",
                      params: item,
                    });
                  } else if (num == 4) {
                    this.$router.push({
                      name: "FeedingCheckDetail4",
                      params: item,
                    });
                  }
                }
              }
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000,
              });
            }
          });
      }
    }
  };
</script>

<style scoped></style>