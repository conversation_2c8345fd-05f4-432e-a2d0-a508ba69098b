<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_hour" width="100%"/>

        <vue-horizontal-calendar
            style="width:400px;margin: 0 auto;"
            swipeSpace="7"
            sundayText="天"
            :choosedDate="getThisMondayDate"
            :showBorderTop="false"
            :resizeable="false"
            v-on:change="dateChange2"
        ></vue-horizontal-calendar>


        <div style="margin-top:20%;margin-left:5%;margin-right:5%;padding: 5%;background:white;border-radius: 10px;" v-if="reports.length>0">
            <div class="hour_item">
                <div class="hour_left">员工姓名</div>
                <div class="hour_right">{{ userName }}</div>
            </div>
            <div class="hour_item">
                <div class="hour_left">工时日期</div>
                <div class="hour_right">{{ date }}</div>
            </div>
            <div class="hour_item">
                <div class="hour_left">当日班次</div>
                <div class="hour_right" v-if="reports[0].category">{{ reports[0].category }}</div>
                <div class="hour_right" v-else>-</div>
            </div>
            <div class="hour_item">
                <div class="hour_left">今日当班工时</div>
                <div class="hour_right" v-if="reports[0].standardHour">{{reports[0].standardHour}}</div>
                <div class="hour_right" v-else>0</div>
            </div>
            <div class="hour_item">
                <div class="hour_left">今日加班工时</div>
                <div class="hour_right" v-if="reports[0].extraHour">{{reports[0].extraHour}}</div>
                <div class="hour_right" v-else>0</div>
            </div>
            <div class="hour_item" v-if="reports[0].enCompany.indexOf('人力')==-1 && reports[0].enCompany.indexOf('服务')==-1 && reports[0].enCompany.indexOf('企业管理')==-1">
                <div class="hour_left">今日加班补贴</div>
                <div class="hour_right" v-if="reports[0].extraMoney">{{reports[0].extraMoney}}</div>
                <div class="hour_right" v-else>0</div>
            </div>
            <!-- <div class="hour_item">
                <div class="hour_left">当月累计工时</div>
                <div class="hour_right" v-if="reports[0].total">{{reports[0].total}}</div>
                <div class="hour_right" v-else>0</div>
                <div class="hour_right">敬请期待</div>
            </div> -->
        </div>
        <div class="hour" @click="showDetail" style="margin-top:20%;" v-else>
            <div class="attence_item4" style="padding-top:10px;">
                <div class="attence_circle">
                    <div class="item_circle4"></div>
                </div>
                
                <div class="item_top" style="height:100%;">
                    <div style="font-weight:800;font-size: 16px;">{{date}}未排班，无工时记录！</div>
                </div>
            </div>
            <!-- <div class="hour_item">
                <div class="hour_left">当月累计工时</div>
                <div class="hour_right" v-if="reports[0].total">{{reports[0].total}}</div>
                <div class="hour_right" v-else>0</div>
            </div> -->
        </div>
        <!-- <div style="color:#f00;font-size:14px;padding:2%">总部仓库/IPQC工时待人事部具体通知后生效</div> -->

        <van-popup
            v-model="popup_show"
            round
            closeable
            close-icon="close"
            style="height:75%;width:90%;"
        >
            <div style="height:30px;"></div>
            <div class="hour_item" v-for="(item,index) in reports.operationList" :key="index">
                <div class="hour_left">{{item.createTime}}</div>
                <div class="hour_right">{{item.type}}</div>
            </div>
        </van-popup>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import VueHorizontalCalendar from 'vue-horizontal-calendar';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            currentFirstDay: {
                dateFormat: "",
                year: "",
                month: "",
                date: "",
                day: "",
                timestamp: ""
            },
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_hour:require('../../static/images/my_hour.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            reports:{},
            pwd:"",
        }
    },
    components:{
        DatetimePicker,
        VueHorizontalCalendar
    },
    created:function(){
        let nowDate = new Date();
        let self=this;
        self.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        self.maxDate = nowDate
        self.date=self.formatDate(new Date)
        self.userCode=localStorage.getItem('userCode')
        self.userName=localStorage.getItem('userName')

        // self.writePassword();
        self.getDailyReportInfo()
    },
    computed:{
        // 获取当前日期所在的周‘周一’的日期
        getThisMondayDate(){
            let today = new Date();
            let today_weekCode = today.getDay() == 0? 7: today.getDay();
            let monday_timestamp = today.getTime() - (today_weekCode - 1) * 1000*3600*24;
            let monday = new Date(monday_timestamp);
            return today.getFullYear() + "/" + (today.getMonth() + 1) + "/" + today.getDate();
        }
    },
    methods: {
        writePassword(){
            let self = this;
            MessageBox.prompt('请输入您的密码!初始密码为身份证后6位').then(({ value, action }) => {
                if(action=="confirm"){
                    self.pwd = value;
                    self.getDailyReportInfo()
                }else{
                    self.$router.go(-1);
                }
            }).catch((res)=>{
                if(res=="cancel"){
                    self.$router.go(-1);
                }
            });
        },
        dateChange2(day) {
            this.currentFirstDay = day;
            this.date=this.formatDate(this.currentFirstDay.dateFormat)
            this.getDailyReportInfo();
        },
        firstDayChange(day) {
            this.currentFirstDay = day;
            this.date=this.formatDate(this.currentFirstDay.dateFormat)
            this.getDailyReportInfo();
        },
        showDetail(){
            // this.popup_show=true
        },
        getDailyReportInfo(){
            let self=this
            // if(self.pwd=="" || self.pwd == null){
            //     Toast({
            //         message: "请输入密码！",
            //         position: 'bottom',
            //         duration: 5000
            //     });
            //     self.writePassword();
            //     return;
            // }
            Indicator.open({
                text: '正在获取工时，请稍候……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcAttendance/getDailyReportInfo',{params:{userCode:this.userCode,workDay:this.date,password:'320581'}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.reports=res.data.result
                }else{
                    Indicator.close();
                    self.reports={};
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.writePassword();
                }
            })
        },
        getDetail(item){
            console.log("item:"+item)
            sessionStorage.setItem('item',JSON.stringify(item))
            this.$router.push({path:'/schedualDetail'})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getDailyReportInfo()
        },
        
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    text-align: left;
    width: 45%;
}
.hour_right{
    float: left;
    text-align: right;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
</style>