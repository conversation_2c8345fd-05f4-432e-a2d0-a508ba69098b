<template>
    <div>
        <van-cell-group style="text-align:center;">
            <div style="margin:10px;text-align:left">MO单号数据</div>
            <van-field label="MO单号" placeholder="请选择MO单号" :value="itemParams.moId" readonly />
            <van-field label="生产车间" :value="itemParams.workshop" readonly />
            <van-field label="流水线" :value="itemParams.mitosome" readonly />
            <van-field label="线体组长" :value="itemParams.leaderNo" readonly />
            <van-field label="灌装设备" :value="itemParams.machine" readonly />
            <van-field label="产品编号" :value="itemParams.code" readonly />
            <van-field label="产品名称" autosize type="textarea" :value="itemParams.name" readonly />
            <div style="margin:10px;height: 30px;">
                <div
                    style="text-align:left;width:30%;height: 100%;float: left;font-weight: 800;display: flex;align-items: center;">
                    过程检查</div>
                <div style="text-align:left;width:30%;height: 100%;float: left;display: flex;align-items: center;">
                    <!-- <van-button type="primary" style="height:25px;" @click="handleChangeNa">一键NA</van-button> -->
                </div>
                <div style="clear:both;"></div>
            </div>

            <div v-if="tableType == '2'">
                <div v-for="(item, index) in modelList" :key="item.id">
                    <div class="co-middle" v-if="item.moldType == '数字' || item.moldType == '文本'">
                        <div style="width: 46%;align-self: center;">{{ item.moldName }}</div>

                        <div style="width: 27%;" v-if="item.split == 2">
                            <van-field style="background-color: #CCFFCC;" v-model="item.results"
                                @change="inputChange(item, index)" :disabled="item.disabled" />
                        </div>
                        <div style="width: 27%;" v-if="item.split == 2">
                            <van-field style="background-color: #99CCCC;" v-model="item.results2"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                        </div>

                        <div style="width: 54%;" v-if="item.split == 1">
                            <van-field style="background-color: #FFFFCC;" v-model="item.results"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                        </div>
                    </div>
                    <van-field :label="item.moldName" rows="3" v-if="item.moldType == '多行文本'" autosize type="textarea"
                        maxlength="100" v-model="item.results" @change="inputChange(item, index)" :disabled="item.disabled"/>
                    <template v-if="item.moldType.indexOf('选择') != -1">
                        <van-field-Pick v-model="item.results" v-if="!item.disabled"
                            :label="item.moldName" required :columns="['正常', '异常', 'N/A']" :disabled="item.disabled"/>
                        <van-field v-else style="background-color: #99CCCC;" v-model="item.results2"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                    </template>

                    <template v-if="item.moldName == '不合格类别'">
                        <div>
                            <van-field-Pick v-if="!item.disabled" v-model="item.results" v-show="showx" :label="item.moldName" required
                                :columns="[...item.moldValue.split(';'), 'N/A']" :disabled="item.disabled"/>
                                <van-field v-else style="background-color: #99CCCC;" v-model="item.results2"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                        </div>
                    </template>

                    <div v-if="item.moldType == '动态文本'">
                        <van-cell :title="item.moldName" @click="addField(item)" v-show="!item.disabled">
                            <template #right-icon>
                                <van-icon name="plus" />
                            </template>
                        </van-cell>
                        <div v-for="x, xi in item.fieldList" :key="xi">
                            <van-row>
                                <van-col span="22">
                                    <!-- @blur="(e) => fieldBlur(e, item)"  -->
                                    <van-field label-width="8em" v-model="x.result" :label="''" placeholder="请输入"
                                        :readonly="false" :disabled="item.disabled"/>
                                </van-col>
                                <van-col span="2">
                                    <div style="width: 100%;height: 100%;">
                                        <van-icon name="minus" @click="delField(x, xi)" v-show="!item.disabled"/>
                                    </div>
                                </van-col>
                            </van-row>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <div v-for="(item, index) in modelList" :key="index">
                    <van-field :label="item.moldName" type="number" required v-if="item.moldType == '数字'"
                        v-model="item.results" @change="inputChange(item, index)" :disabled="item.disabled"/>
                    <van-field :label="item.moldName" required v-if="item.moldType == '文本'" v-model="item.results"
                        @change="inputChange(item, index)" :disabled="item.disabled"/>
                    <van-field :label="item.moldName" rows="3" v-if="item.moldType == '多行文本'" autosize type="textarea"
                        maxlength="100" v-model="item.results" @change="inputChange(item, index)" :disabled="item.disabled"/>

                    <template v-if="item.moldType.indexOf('选择') != -1">
                        <van-field-Pick v-model="item.results" v-if="!item.disabled"
                            :label="item.moldName" required :columns="[...item.moldId.split(';'), 'N/A']" :disabled="item.disabled" />
                        <van-field v-else :label="item.moldName" v-model="item.results"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                    </template>
                    <template v-if="item.moldName == '不合格类别'">
                        <div>
                            <van-field-Pick v-if="!item.disabled" v-model="item.results" v-show="showx" :label="item.moldName" required
                                :columns="[...item.moldId.split(';'), 'N/A']" @change="inputChange(item, index)" :disabled="item.disabled"/>
                            <van-field v-else :label="item.moldName"  v-model="item.results"
                                @change="inputChange(item, index)" :disabled="item.disabled"/>
                        </div>
                    </template>
                    <div v-if="item.moldType == '动态文本'" style="text-align: left ;">
                        <van-cell :title="item.moldName" @click="addField(item)" v-show="!item.disabled">
                            <template #right-icon>
                                <van-icon name="plus" />
                            </template>
                        </van-cell>
                        <div v-for="x, xi in item.fieldList" :key="xi">
                            <van-row>
                                <van-col span="22">
                                    <!-- @blur="(e) => fieldBlur(e, item)"  -->
                                    <van-field label-width="8em" v-model="x.result" :label="''" placeholder="请输入"
                                        :readonly="false" @change="e => dynamicTextChange(e, item)" :disabled="item.disabled"/>
                                </van-col>
                                <van-col span="2">
                                    <div style="width: 100%;height: 100%;">
                                        <van-icon name="minus" @click="delField(item, xi)" v-show="!item.disabled"/>
                                    </div>
                                </van-col>
                            </van-row>
                        </div>
                    </div>
                </div>
            </div>
        </van-cell-group>


        <!-- <van-field name="uploader" label="">
            <template #input>
                <van-uploader v-model="uploader" :after-read="afterRead" :before-delete="beforeDel" multiple
                        :max-size="10000 * 1024" @oversize="onOversize" />
            </template>
        </van-field> -->


        <!-- <div class="add_upload_imgBox float_left all_width">
            <div class="add_upload_imgDiv float_left" v-for="(item, index) in imgs" :key="item.id + index">
                <img @click="showImg(item.base64)" :src="item.base64" />
                <p class="add_upload_close" @click="handleDeleteImage(item.id)" v-if="item.flag">
                    <img :src="'../../static/images/delete.png'" />
                </p>
            </div>

            <div class="add_upload float_left add_width">
                <button class="add_upload_button">
                    <img class="add_upload_icon" :src="'../../static/images/add_pic.png'" />
                    <input id="upfile" type="file" accept="image/*" class="add_upload_file" @change="fileUpload" />
                </button>
            </div>
        </div> -->



        <!-- <div class="sudoku_row"  >
            <div class="sudoku_item " :class="opacity" v-for="(sudoku,index) in urls" :key="index" >
                <img :src="sudoku" :key="sudoku" width="90%" height="100vw" @click="previewImg(index)" >
            </div>
            <div class="j-upload-btn" @click="chooseImg()">
            <span class="j-upload-add">+</span>
            </div>
        </div> -->


        <van-field label="备注" rows="3" autosize type="textarea" v-model="remarks"  />
        <van-button type="primary" v-show="show" @click="submit()" style="margin:10px;width:80%;border-radius:10px;">{{
            button_name }}</van-button>

        <pic-modal ref="modalForm"></pic-modal>
    </div>
</template>

<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from 'mint-ui';
import { Dialog } from 'vant'
import PicModal from './list/PicModal.vue'
import Vue from 'vue'
export default ({
    components: { PicModal },
    data() {
        return {
            itemParams: {},
            modelId: '',
            mitosome: '',
            detailInfoList: {},
            show: true,
            modelList: [],
            modelType: '',
            button_name: "提交",
            submitFlag: false,
            itemId: '',
            childId: '',
            remarks:'',
            tableType: '',
            urls: [],
            imgs: [],
            uploader: [],
            pictureList: [],
            imageParams: {
                id: '',
                localIds: [],
                serverIds: []
            },
            isUpdate: false,
            workDetaiList: [],
            showx: false,
            restrictFlag: false,//是否不在限制范围内

            editId:'',
            errorId:'',
        }
    },
    watch: {
        modelList: {
            deep: true,
            handler(newV, oldV) {
                const a = newV.find(item => item.moldName == '不合格数')
                if (a.results > 0) {
                    this.showx = true
                } else {
                    let self = this
                    for (var i = 0; i < self.modelList.length; i++) {
                        if (self.modelList[i].moldName == "不合格类别") {
                            Vue.set(self.modelList[i], 'results', 'N/A')
                        }
                    }
                    this.showx = false

                }
            }
        }
    },
    created: async function () {
        this.remarks=''
        this.editId = this.$route.query.id
        this.errorId = this.$route.query.errorId
        let res = await this.$axios.get('/jeecg-boot/app/appQuality/getErrorDetailById?id=' + this.$route.query.id)
        this.modelList = res.data.result

        let resa = await this.$axios.get('/jeecg-boot/app/appQuality/getMainInfo', { params: {id: this.modelList[0].mainId } })
        console.log('@s', resa.data.result);
        this.itemParams = resa.data.result
        let self = this;
        // self.itemId = self.$route.params.itemId
        // self.modelId = self.$route.params.id
        // self.modelType = self.$route.params.type
        // self.childId = self.$route.params.childId
        // self.tableType = self.$route.params.tableType
        // self.itemParams = JSON.parse(localStorage.getItem("controlItem"))
        // self.modelList=JSON.parse(self.$route.params.item)
        // console.log("itemId:" + self.itemId);
        // console.log("childId:" + self.childId);

        // console.log("tableType:" + self.tableType)


        console.log("🚀 ~ this.modelList:", this.modelList)
        self.modelList.forEach(item => {
            if(item.id==this.editId){
                item.disabled=false
            }else{
                item.disabled=true
            }
            if (item.moldType == "动态文本") {
                console.log("🚀 ~ item:", item)
                let arr = item.results.split(',').map(item => {
                    return {
                        result: item
                    }
                })
                self.$set(item, 'fieldList', arr)
            }
        })
        self.isUpdate = true
        self.button_name = "修改"
        console.log("🚀 ~ self.modelList:", self.modelList)

        // self.imageParams.id = self.itemParams.itemId;

        // self.getImages();

        // if(self.$route.params.item!=null){
        //     self.detailInfoList=JSON.parse(self.$route.params.item);
        // }

        // self.detailInfoList.mainId=self.itemParams.id
        // self.detailInfoList.type=self.$route.params.type

        // if(self.detailInfoList.id==null || self.detailInfoList.id==''){
        //     self.show=true
        // }else{
        //     self.show=false
        // }
        self.getImages();

    },
    methods: {
        dynamicTextChange(e, item) {
            let downLimit = this.modelList[item.downLimit - 1].results;
            let upLimit = this.modelList[item.upLimit - 1].results;
            if (downLimit != 'N/A' && e.target.value < downLimit*1) {
                Dialog.confirm({
                    title: '警告',
                    message:
                        '不能小于下限值',
                }).then(() => {
                    this.restrictFlag = true
                }).catch(() => {});
            } else if (upLimit != 'N/A' && e.target.value > upLimit*1) {
                Dialog.confirm({
                    title: '警告',
                    message:
                        '不能大于上限值',
                }).then(() => {
                    this.restrictFlag = true
                }).catch(() => {});
            } else {
                this.restrictFlag = false
            }
        },
        addField(item) {
            console.log(item);
            if (item.fieldList && item.fieldList.length > 0) {
                item.fieldList.push({})
            } else {
                this.$set(item, 'fieldList', [])
                item.fieldList.push({})
            }
        },
        delField(x, xi) {
            console.log(x);
            x.fieldList.splice(xi, 1)
        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: '文件大小不能超过 10M',
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            let that = this
            const param = new FormData()
            console.log(file)
            console.log(name)

            file.forEach(item => {
                item.status = 'uploading'
                item.message = '上传中...'
                param.append("file", item.file)
            })
            param.append('id', that.itemParams.id)
            param.append('childId', that.childId)
            param.append('description', "巡检喷码信息")
            param.append('type', "1")
            that.$axios
                .post(`jeecg-boot/app/gcWorkshop/uploadPic`, param)
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res)
                        let tmpImgs = res.data.message.split(";")
                        for (var i = 0; i < tmpImgs.length; i++) {
                            let img = tmpImgs[i];
                            console.log(file)
                            for (var j = 0; j < that.uploader.length; j++) {
                                let item = that.uploader[j]
                                let tmpName = item.file.name
                                let fileName = tmpName.substring(0, tmpName.lastIndexOf('.'))
                                console.log(fileName);
                                if (img.indexOf(fileName) != -1) {// indexOf()返回-1则不包含该文件名
                                    item.status = ''
                                    item.message = ''
                                    item.url = that.getFullURL(img)
                                    console.log(item.url)
                                    that.pictureList.push({ picUrl: item.url })
                                }
                            }
                        }
                    } else {
                        this.uploader.splice(name.index, 1)
                        Toast({
                            message: '上传失败,请选择图片上传',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        getFullURL(img) {
            return "http://service.colori.com/jeecg-boot/sys/common/static/images/" + this.itemParams.id + "/" + img;
        },
        //删除图片
        beforeDel(file, name) {
            let self = this
            Dialog.confirm({
                message: '确定删除吗?',
                theme: 'round-button',
                confirmButtonColor: '#1989fa',
                cancelButtonColor: '#CCCCCC',
            }).then(() => {
                Indicator.open({
                    text: '处理中，请稍后……',
                    spinnerType: 'fading-circle'
                });

                console.log(file.url)

                let formData = new window.FormData()
                formData.append('path', file.url)
                formData.append('id', self.itemParams.id)
                formData.append('childId', self.childId)
                formData.append('type', "1")
                this.$axios
                    .post('/jeecg-boot/app/gcWorkshop/deletePic', formData)
                    .then(res => {
                        if (res.data.code == 200) {
                            Indicator.close();
                            Toast({
                                message: '删除成功',
                                position: "bottom",
                                duration: 2000
                            });
                        } else {
                            Indicator.close();
                            // this.uploader.splice(name.index, 1)
                            Toast({
                                message: '删除失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
                this.uploader.splice(name.index, 1)
                this.pictureList.splice(name.index, 1)
            }).catch((e) => {
                Toast({
                    message: e,
                    position: "bottom",
                    duration: 1000
                });
            });
        },
        handleChangeNa() {
            for (var i = 0; i < this.modelList.length; i++) {
                this.modelList[i].results = "N/A";
            }
            console.log(this.modelList)
            this.$forceUpdate()
        },
        inputChange(item, index) {
            let self = this
            console.log(item)
            console.log(index)
            Vue.set(self.modelList, index, item)
            // this.$set(this.modelList,index,item)
            const a = self.modelList.find(item => item.moldName == '不合格数')
            if (a.results > 0) {
                this.showx = true
            } else {
                let self = this
                for (var i = 0; i < self.modelList.length; i++) {
                    if (self.modelList[i].moldName == "不合格类别") {
                        Vue.set(self.modelList[i], 'results', 'N/A')
                    }
                }
                this.showx = false
            }
            console.log(this.showx);


            this.$forceUpdate()
        },
        showImg(url) {
            this.$refs.modalForm.show(url);
            this.$refs.modalForm.disableSubmit = true;
        },
        handleDeleteImage(id) {
            let that = this;
            MessageBox.confirm('确定删除该图片吗?').then(action => {
                console.log(action)
                if (action == 'confirm') {
                    deleteImage()
                }
            });
            function deleteImage() {
                for (let i = 0; i < that.imgs.length; i += 1) {
                    if (that.imgs[i].id === id) {
                        console.log(that.imgs[i])
                        that.deleteImageToService(that.imgs[i].base64, i)
                        break;
                    }
                }
            }
        },
        deleteImageToService(path, i) {
            let self = this
            console.log(path)
            let formData = new window.FormData()
            formData.append('path', path)
            formData.append('id', self.itemParams.id)
            formData.append('childId', self.childId)
            formData.append('type', "1")
            self.$axios.post('/jeecg-boot/app/gcWorkshop/deletePic', formData)
                .then(res => {
                    if (res.data.code == '200') {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        self.imgs.splice(i, 1);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
        },
        getHistory(id) {
            let self = this
            let hisrecords = []
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/appQuality/getBaseInfo', { params: { mainId: id } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    hisrecords = res.data.result;
                    for (var i = 0; i < hisrecords.length; i++) {
                        for (var j = 0; j < self.modelList.length; j++) {
                            if (self.modelList[j].id == hisrecords[i].moldId) {
                                self.modelList[j].results = hisrecords[i].results
                                Vue.set(self.modelList, j, self.modelList[j])
                            }
                        }
                    }
                    console.log(self.modelList)
                }
            });
        },
        getworkInfo() {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this;
            self.$axios.get('/jeecg-boot/app/appQuality/getDetailList', { params: { id: JSON.parse(localStorage.getItem("controlItem")).id } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.workDetaiList = res.data.result.filter(arr => arr.length > 0);
                    if (self.workDetaiList.length >= 2) {
                        for (var i = 0; i < self.modelList.length; i++) {
                            if (self.modelList[i].autoFlag == 'Y') {
                                self.workDetaiList[self.workDetaiList.length - 1].forEach(item => {
                                    if (self.modelList[i].id == item.moldId) {
                                        self.$set(self.modelList[i], 'results', item.results)
                                        self.$set(self.modelList[i], 'results2', item.results2)
                                    }
                                })
                            }
                        }
                    }
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getModelDetail() {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this;
            self.$axios.get('/jeecg-boot/app/appQuality/getModelInfoById', { params: { id: self.itemParams.id, tableId: self.modelId } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.modelList = res.data.result

                    for (var i = 0; i < self.modelList.length; i++) {
                        if (self.modelList[i].autoFlag == 'Y' && self.modelType == '1' && self.modelList[i].moldType == '数字') {
                            self.modelList[i].results = 0
                            if (self.tableType == '2' && self.modelList[i].split == '2') {
                                self.modelList[i].results2 = 0
                            }
                        }
                        if (self.modelList[i].autoFlag == 'Y' && self.modelType == '2' && self.modelList[i].moldType == '数字') {
                            self.modelList[i].results = 0
                            if (self.tableType == '2' && self.modelList[i].split == '2') {
                                self.modelList[i].results2 = 0
                            }
                        }
                        if (self.modelList[i].moldType.indexOf('选择') != -1) {
                            self.modelList[i].results = '正常'
                        }
                        if (self.modelList[i].autoFlag == 'Y' && self.modelList[i].moldType == '文本') {
                            if (self.modelList[i].sign == '是') {
                                self.modelList[i].results = 0
                                if (self.tableType == '2' && self.modelList[i].split == '2') {
                                    self.modelList[i].results2 = 0
                                }
                            } else {
                                self.modelList[i].results = self.modelList[i].moldValue == null ? 0 : self.modelList[i].moldValue
                                if (self.tableType == '2' && self.modelList[i].split == '2') {
                                    self.modelList[i].results2 = 0
                                }
                            }

                        }
                    }
                    self.getHistory(self.itemId);
                    self.getworkInfo();
                }
            });
        },
        submit() {
            let self = this;

            console.log(self.modelList)
            let flag = false
            let flag1 = false
            for (var i = 0; i < self.modelList.length; i++) {
                if (self.modelList[i].moldType == "规则") {
                    Vue.set(self.modelList[i], 'results', 'M')
                    Vue.set(self.modelList[i], 'results2', 'U')
                }
                if (self.modelList[i].moldType == "N/A") {
                    Vue.set(self.modelList[i], 'results', 'N/A')
                }
                if (self.modelList[i].moldType == '动态文本') {
                    if (self.modelList[i].moldName.indexOf('净含量') > -1) {
                        if(self.modelList[i].upLimit == null||self.modelList[i].downLimit == null){
                            Toast({
                                message:  "请去管制表模板设置动态文本上限和下限",
                                position: 'bottom',
                                duration: 1000
                            });
                            return;
                        }
                        if (!self.modelList[i].fieldList || self.modelList[i].fieldList.length < 5) {
                            if (self.modelList[self.modelList[i].downLimit - 1].results != 'N/A' || self.modelList[self.modelList[i].upLimit - 1].results != 'N/A') {
                                Toast({
                                    message: self.modelList[i].moldName + "不得为空,至少填写五条！",
                                    position: 'bottom',
                                    duration: 1000
                                });
                                return;
                            }
                        }
                    }
                    for (let ax = 0; ax < self.modelList[i].fieldList.length; ax++) {
                        let downLimit = this.modelList[self.modelList[i].downLimit - 1].results;
                        let upLimit = this.modelList[self.modelList[i].upLimit - 1].results;
                        if (downLimit != 'N/A' && self.modelList[i].fieldList[ax].result*1 < downLimit*1) {
                            this.restrictFlag = true
                            break;
                        } else if (upLimit != 'N/A' && self.modelList[i].fieldList[ax].result*1 > upLimit*1) {
                            this.restrictFlag = true
                            break;
                        } else {
                            this.restrictFlag = false
                        }
                    }
                    

                    self.modelList[i].results=self.modelList[i].fieldList.map(item=>{
                        return item.result
                    }).join(',');
                }
                if (self.modelList[i].results == null || self.modelList[i].results == undefined ||
                    self.modelList[i].results == '') {
                    if (self.modelList[i].moldName != '异常状况描述' && self.modelList[i].moldName != '喷码信息') {
                        console.log(self.modelList[i]);
                        Toast({
                            message: self.modelList[i].moldName + "不得为空！",
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                }

                if (self.modelList[i].moldType == '数字') {
                    if (parseInt(self.modelList[i].results) > parseInt(self.modelList[i].moldValue)) {
                        Toast({
                            message: self.modelList[i].moldName + "值过大！不得超过" + self.modelList[i].moldValue,
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                }

                if (self.modelList[i].moldType == '数字') {
                    if (!(/(^[0-9]\d*$)/.test(self.modelList[i].results * 1))) {
                        Toast({
                            message: self.modelList[i].moldName + "值不正确,请输入大于0的整数",
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                    if (self.tableType == '2' && self.modelList[i].split == '2') {
                        if (!(/(^[0-9]\d*$)/.test(self.modelList[i].results2 * 1))) {
                            Toast({
                                message: self.modelList[i].moldName + "值不正确,请输入大于0的整数",
                                position: 'bottom',
                                duration: 1000
                            });
                            return;
                        }
                    }
                }

                // if(self.modelType=='2' && self.modelList[i].moldType=='数字'){
                //     if(parseInt(self.modelList[i].results)>parseInt(self.modelList[i].moldValue2)){
                //         Toast({
                //             message: self.modelList[i].moldName+"值过大！不得超过"+self.modelList[i].moldValue2,
                //             position: 'bottom',
                //             duration: 1000
                //         });
                //         return;
                //     }
                // }

                if(self.modelList[i].moldName=='不合格数'&&self.modelList[i].results==0){
                    flag=true
                }else if(self.modelList[i].moldName=='不合格数'&&self.modelList[i].results>0){
                    flag1=true
                }
            }
                for (var i = 0; i < self.modelList.length; i++) {

                    if (flag&&self.modelList[i].moldName == "不合格类别") {
                        Vue.set(self.modelList[i], 'results', 'N/A')
                    }

                    if (flag1&&self.modelList[i].moldName == "不合格类别" && self.modelList[i].results == 'N/A') {
                        console.log(self.modelList[i].results)
                        Toast({
                            message: "不合格数大于 0 时需要选择不合格类别,不能是N/A",
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                    if (self.modelList[i].moldName == "异常状况描述" && this.restrictFlag && self.modelList[i].results=='') {
                        Toast({
                            message: "存在不在范围内的数据需要填写异常状况描述",
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                    if(self.modelList[i].moldName == "不合格数"&& this.restrictFlag&&(self.modelList[i].results==0||self.modelList[i].results=='')){
                        Toast({
                            message: "存在不在范围内的数据时不合格数不得小于等于0",    
                            position: 'bottom',
                            duration: 1000
                        });
                        return;
                    }
                }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            console.log(self.itemId)
            let params_type = '1'
            if (self.isUpdate) {
                params_type = '2'
            }

            // for(var i=0;i<self.modelList.length;i++){
            //     if(self.modelType=='1' && self.modelList[i].moldType=='数字' && self.modelList[i].moldValue != null){
            //         self.modelList[i].results=self.modelList[i].results+"/"+self.modelList[i].moldValue
            //     }
            //     if(self.modelType=='2' && self.modelList[i].moldType=='数字' && self.modelList[i].moldValue2 != null){
            //         self.modelList[i].results=self.modelList[i].results+"/"+self.modelList[i].moldValue2
            //     }
            // }

            // for(var i=0;i<self.modelList.length;i++){
            //     console.log("modelList:"+self.modelList[i].results)
            // }

            if (self.submitFlag) {
                Toast({
                    message: "请勿重复提交！",
                    position: 'bottom',
                    duration: 1000
                });
            }
            self.submitFlag = true;

            // let params = {
            //     detailInfoList: self.modelList,
            //     creator: localStorage.getItem('userCode'),
            //     id: self.itemParams.id,
            //     childId: self.childId,
            //     type: params_type
            // }
            console.log(self.modelList)
            let paramsObj = self.modelList.filter(item=>item.id==self.editId)
            console.log("🚀 ~ submit ~ paramsObj:", paramsObj)
            let params = {
                id: self.editId,
                results: paramsObj[0].results,
                results2 :paramsObj[0].results2,
                errorId:self.errorId,
                remarks:self.remarks,
                userCode:localStorage.getItem('userCode')
            }

            self.$axios.get('/jeecg-boot/app/appQuality/editDetail', {params:{...params}}).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                } else {
                    Indicator.close();
                    self.submitFlag = false;
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getImages() {
            let self = this
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getImagesBySxp', { params: { id: self.itemParams.id, childId: self.childId, type: '1', status: '1' } }).then(res => {
                if (res.data.code == 200) {
                    self.imageUrls = res.data.result.images
                    for (var i = 0; i < self.imageUrls.length; i++) {
                        self.imgs.push({ id: i, base64: self.imageUrls[i], flag: true })
                    }
                }
            })
        },
        chooseImg() {
            let self = this;
            wx.chooseImage({
                count: 1, // 默认9
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                defaultCameraMode: "batch", //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
                isSaveToAlbum: 1, //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
                success: function (res) {
                    var localIds = res.localIds; // 返回选定照片的本地ID列表，
                    // andriod中localId可以作为img标签的src属性显示图片；
                    // iOS应当使用 getLocalImgData 获取图片base64数据，从而用于img标签的显示（在img标签内使用 wx.chooseImage 的 localid 显示可能会不成功）
                    for (var n = 0; n < localIds.length; n++) {
                        wx.getLocalImgData({
                            localId: localIds[n], // 图片的localID
                            success: function (res) {
                                let localData = res.localData; // localData是图片的base64数据，可以用img标签显示
                                if (localData.indexOf('data:image') != 0) {
                                    //判断是否有这样的头部
                                    localData = 'data:image/jpeg;base64,' + localData
                                }
                                localData = localData.replace(/\r|\n/g, '').replace('data:image/jgp', 'data:image/jpeg')
                                self.urls.push(localData);

                                console.log("localData:" + localData)
                                console.log("urls:" + self.urls.length)
                            }
                        });
                    }

                    self.uploadImgToWx(localIds);
                }
            });

        },
        uploadPic() {
            let self = this;
            self.imageParams.description = "巡检喷码信息";
            self.imageParams.type = "1"
            self.$axios.post('/jeecg-boot/app/wx/uploadPic', self.imageParams)
                .then(res => {
                    if (res.data.code == '200') {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        self.$router.go(-1);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }).catch(res => {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                });
        },
        fileUpload() {
            let that = this;
            let file = document.getElementById('upfile');
            let fileName = file.value;
            let files = file.files;
            console.log(files[0])
            if (fileName == null || fileName == "") {
                alert("请选择文件");
            } else {
                let fileType = fileName.substr(fileName.length - 4, fileName.length);
                console.log("fileType:" + fileType)
                if (fileType == ".jpg" || fileType == ".png") {
                    if (files[0]) {
                        let formData = new window.FormData()
                        formData.append('file', files[0])
                        formData.append('id', that.itemParams.id)
                        formData.append('childId', that.childId)
                        formData.append('description', "巡检喷码信息")
                        formData.append('type', "1")

                        that.$axios.post('jeecg-boot/app/gcWorkshop/uploadPic', formData).then(res => {
                            if (res.data.code == 200) {
                                let timestamp = (new Date()).valueOf();
                                that.imgs.push({ id: timestamp, base64: res.data.message, flag: true })
                            } else {
                                Toast({
                                    message: "上传失败，请稍候再试！",
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    } else {
                        alert("请选择要上传的图片");
                    }
                } else {
                    alert("上传文件类型错误！");
                }
            }
        },
        uploadImgToWx(localIds) {
            let self = this;
            var localId = localIds.pop();
            console.log("localId:" + localId);
            wx.uploadImage({
                localId: localId,
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                    var serverId = res.serverId;
                    console.log("serverId:" + serverId);
                    self.imageParams.serverIds.push(serverId);
                },
                fail: err => {
                    alert("上传失败");
                }
            });
        },
        previewImg(index) {
            let that = this;
            console.log("index:" + index)
            wx.previewImage({
                current: that.urls[index],
                urls: that.urls
            });
        },
    }
})
</script>


<style scoped>
.order {
    background-color: #ebecf7;
    min-height: 100%;
}

.top_order_title {
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}

.top_msg {
    float: right;
}

.items_d {
    padding: 5%;
    height: 6rem;
}

.item_bg {
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}

.item_add {
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}

.itemTitle {
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sign {
    text-align: center;
}

.plotName {
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}

.plotCode {
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}

.plotCard {
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}

.addControl {
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}

.plotFactory {
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}

.plotWorkshop {
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}

.plotMitosome {
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}

.plotTime {
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.orderType {
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}

#peopleChorseT {
    position: absolute;
    width: 100%;
    top: 1.17rem;
    height: 0.6rem;
}

/**问题类型弹框样式 */
.picker-toolbar-title {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    background-color: #eee;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
}

.usi-btn-cancel,
.usi-btn-sure {
    color: #26a2ff;
    font-size: 16px;
}

.popup-div {
    width: 100%;
}

.pro-report {
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}

.sc_date {
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width: 64%;
    border-radius: 10px;
}

.rq_date {
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}

.date_work {
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}

.right_jt {
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}

.pool {
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom: 5%;
    font-size: 1rem;
    width: 90%;
}

.zbPool {
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.ybPool {
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.mid {
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}

.midPool {
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}

.sudoku_row {
    display: flex;
    align-items: center;
    width: 90%;
    flex-wrap: wrap;
    margin-left: 5%;
    margin-top: 5vh;
    margin-bottom: 5vh;
}

.sudoku_item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 33%;
    padding-top: 10px;
    padding-bottom: 10px;
}

.opacity {
    opacity: 0.4;
    background: #e5e5e5;
}

.sudoku_item img {
    margin-bottom: 3px;
    display: block;
}

.j-pic-upload {
    margin-top: 2vh;
    margin-bottom: 1vh;
    padding: 10rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}

.j-upload-btn {
    border: 1px dashed #ddd;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    width: 32%;
    height: 30vw;
}

.j-upload-add {
    font-size: 80rpx;
    font-weight: 500;
    color: #C9C9C9;
}

/deep/ .van-field__label {
    width: 10em;
}
</style>
<style scoped>
.float_left {
    float: left;
}

.all_width {
    width: 100%;
}

.add_width {
    width: 29%;
}

.add_upload .add_upload_button {
    position: relative;
    width: 100%;
    height: 8rem;
    border: none;
    background: rgb(236, 236, 236);
    margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.add_upload .add_upload_icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.add_upload .add_upload_file {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    font-size: 0;
}

.co-middle {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.add_upload_imgBox .add_upload_imgDiv {
    position: relative;
    width: 29%;
    height: 8rem;
    margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.add_upload_imgBox .add_upload_imgDiv img {
    width: 100%;
    height: 100%;
}

.add_upload_imgBox .add_upload_close {
    position: absolute;
    top: 0;
    left: 0;
    width: 30%;
    height: 30%;
}

.add_upload_imgBox .add_upload_close img {
    width: 100%;
    height: 100%;
    vertical-align: top;
}
</style>