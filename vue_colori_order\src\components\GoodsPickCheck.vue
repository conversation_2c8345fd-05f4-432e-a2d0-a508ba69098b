<template>
  <div style="text-align:left;padding-bottom: 1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="复核" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-tabs
      v-model="activeName"
      color="#1989fa"
      sticky
      offset-top="46px"
      @click="onClick"
    >
      <van-tab title="未复核" name="1">
        <!-- 复核员可以操作 -->
        <div v-if="type == 1">
          <!-- <van-checkbox-group v-model="result" ref="checkboxGroup">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in dataArr1"
                clickable
                :key="index"
              >
                <template #default>
                  <div
                    class="van-hairline--bottom"
                    style="margin-bottom:0.3rem;"
                  >
                    <van-row>
                      <van-col
                        span="24"
                        style="overflow-x: auto;white-space:nowrap;"
                      >
                        <span
                          style="font-size:18px;font-weight: 700;color: #000;"
                        >
                          {{ item.name }}</span
                        >
                      </van-col>
                    </van-row>
                  </div>

                  <van-row>
                    <van-col span="12">
                      <span style="color:gary"
                        >编&emsp;码:
                        <span style="color:black;">{{ item.code }}</span></span
                      >
                    </van-col>
                    <van-col span="12">
                      <span style="color:gary"
                        >货位号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.rackName }}</span
                        ></span
                      >
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col
                      span="24"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        批次号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.customer }}
                        </span>
                      </span>
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col
                      span="8"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        应发:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.count }}
                        </span>
                      </span>
                    </van-col>
                    <van-col
                      span="8"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        实发:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.actualCount }}
                        </span>
                      </span>
                    </van-col>
                    <van-col
                      span="8"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        缺发:
                        <span style="color:red;font-size:16px;font-weight: 700;"
                          >{{ item.defectCount }}
                        </span>
                      </span>
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col
                      span="24"
                      v-if="item.defectCount != 0"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        缺发原因:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.defectRemark }}
                        </span>
                      </span>
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="24" class="van-hairline--bottom">
                      <van-field
                        style="margin-left:-1rem;"
                        v-model="item.checkDefectCount"
                        type="digit"
                        label="复检缺数："
                      />
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="24" class="van-hairline--bottom">
                      <van-field
                        style="margin-left:-1rem;"
                        v-model="item.checkRemark"
                        label="缺少原因："
                      />
                    </van-col>
                  </van-row>
                </template>
                <template #icon>
                  <van-checkbox
                    :name="item"
                    ref="checkboxes"
                    style="margin-right: 1rem;"
                  />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
          <div style="height: 2rem; width: 100%">&nbsp;</div>
          <van-row
            style="
        background-color:#F1F1F1;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;"
            gutter="30"
          >
            <van-col span="4">
            </van-col>
            <van-col span="11"></van-col>
            <van-col span="8">
              <van-button
                round
                style="height: 25px"
                type="info"
                size="large"
                @click="submit"
                loading-type="spinner"
              >
                确认
              </van-button>
            </van-col>
          </van-row> -->

          <div
            v-for="(item, index) in dataArr1"
            clickable
            :key="index"
            style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;overflow: hidden;"
          >
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span
                  >
                </van-col>
              </van-row>
            </div>

            <van-row>
              <van-col span="12">
                <span style="color:gary"
                  >编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span
                >
              </van-col>
              <van-col span="12">
                <span style="color:gary"
                  >货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                    item.rackName
                  }}</span></span
                >
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary"
                  >托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span
                >
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  辅数量:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.respondFcount }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  应发:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.respondCount }}
                  </span>
                </span>
              </van-col>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  实发:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.actualCount }}
                  </span>
                </span>
              </van-col>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  缺发:
                  <span style="color:red;font-size:16px;font-weight: 700;"
                    >{{ item.defectCount }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col
                span="24"
                v-if="item.defectCount != 0"
                style="overflow-x: auto;white-space:nowrap;"
              >
                <span style="color:gary">
                  缺发原因:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.defectRemark }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="24" class="van-hairline--bottom">
                <van-field
                  style="margin-left:-1rem;"
                  v-model="item.backCount"
                  type="digit"
                  label="复核缺数："
                />
              </van-col>
            </van-row>
            <van-row v-show="item.backCount > 0">
              <van-col span="24" class="van-hairline--bottom">
                <van-field
                  style="margin-left:-1rem;"
                  v-model="item.remark"
                  label="缺少原因："
                />
              </van-col>
            </van-row>
            <van-row>
              <van-col span="20"> </van-col>
              <van-col span="4">
                <van-button
                  size="mini"
                  icon="success"
                  round
                  hairline
                  type="info"
                  @click="check(item)"
                  >确认
                </van-button>
              </van-col>
            </van-row>
          </div>
        </div>
        <!-- 非复核员只能看 -->
        <div v-if="type == 2">
          <div
            v-for="(item, index) in dataArr1"
            clickable
            :key="index"
            style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
          >
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span
                  >
                </van-col>
              </van-row>
            </div>

            <van-row>
              <van-col span="12">
                <span style="color:gary"
                  >编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span
                >
              </van-col>
              <van-col span="12">
                <span style="color:gary"
                  >货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                    item.rackName
                  }}</span></span
                >
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary"
                  >托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span
                >
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  辅数量:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.respondFcount }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  应发:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.respondCount }}
                  </span>
                </span>
              </van-col>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  实发:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.actualCount }}
                  </span>
                </span>
              </van-col>
              <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  缺发:
                  <span style="color:red;font-size:16px;font-weight: 700;"
                    >{{ item.defectCount }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  缺发原因:
                  <span style="color:black;font-size:16px;font-weight: 700;"
                    >{{ item.defectRemark }}
                  </span>
                </span>
              </van-col>
            </van-row>
          </div>
        </div>
      </van-tab>
      <van-tab title="已复核" name="2">
        <div
          v-for="(item, index) in dataArr2"
          clickable
          :key="index"
          style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
        >
          <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
            <van-row>
              <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                <span style="font-size:18px;font-weight: 700;color: #000;">
                  {{ item.name }}</span
                >
              </van-col>
            </van-row>
          </div>

          <van-row>
            <van-col span="12">
              <span style="color:gary"
                >编&emsp;码:
                <span style="color:black;">{{ item.code }}</span></span
              >
            </van-col>
            <van-col span="12">
              <span style="color:gary"
                >货位号:
                <span style="color:black;font-size:16px;font-weight: 700;">{{
                  item.rackName
                }}</span></span
              >
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12">
              <span style="color:gary"
                >托&emsp;码:
                <span style="color:black;">{{ item.stickerId }}</span></span
              >
            </van-col>
            <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                批次号:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.customer }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                辅数量:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.respondFcount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                应发:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.respondCount }}
                </span>
              </span>
            </van-col>
            <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                实发:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.actualCount }}
                </span>
              </span>
            </van-col>
            <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                缺发:
                <span style="color:red;font-size:16px;font-weight: 700;"
                  >{{ item.defectCount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                复核缺数:
                <span style="color:red;font-size:16px;font-weight: 700;"
                  >{{ item.checkDefectCount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row v-if="item.defectCount > 0">
            <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                缺发原因:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.defectRemark }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row v-if="item.checkDefectCount > 0">
            <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                复核缺少原因:
                <span style="color:black;font-size:16px;font-weight: 700;"
                  >{{ item.checkDefectRemark }}
                </span>
              </span>
            </van-col>
          </van-row>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { Toast } from "vant";
import { Indicator } from "mint-ui";
export default {
  data() {
    return {
      type: 1,
      activeName: "1",
      info: {},
      dataArr1: [],
      dataArr2: [],
      userCode: localStorage.getItem("userCode"),
      result: [],
      plain: true
    };
  },
  created() {
    this.info = this.$route.params;
    this.info.userCode = localStorage.getItem("userCode");
    this.$axios
      .get(`/jeecg-boot/app/warehousePick/getPickMainSun?id=${this.info.id}`)
      .then(res => {
        if (res.data.code == 200) {
          res.data.result.forEach(item => {
            item.backCount = 0;
            if (item.status == "2") {
              this.dataArr1.push(item);
            } else if (item.status == "3") {
              this.dataArr2.push(item);
            }
          });
          // this.dataArr1.forEach(item=>{
          //   item.backCount=0
          // })
        } else {
          Toast({
            message: res.data.message,
            position: "bottom",
            duration: 2000
          });
        }
      });
    if (this.info.checkerNo != localStorage.getItem("userCode")) {
      this.type = 2;
    }
  },
  methods: {
    search() {
      this.dataArr1 = [];
      this.dataArr2 = [];
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });
      this.$axios
        .get(`/jeecg-boot/app/warehousePick/getPickMainSun?id=${this.info.id}`)
        .then(res => {
          Indicator.close();
          if (res.data.code == 200) {
            if (res.data.result.length == 0) {
              this.$router.replace({
                name: "GoodsCheck"
              });
            }
            res.data.result.forEach(item => {
              item.backCount = 0;
              if (item.status == "2") {
                this.dataArr1.push(item);
              } else if (item.status == "3") {
                this.dataArr2.push(item);
              }
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
      if (this.info.checkerNo != localStorage.getItem("userCode")) {
        this.type = 2;
      }
    },
    check(item) {
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });
      if (item.checkDefectCount > 0 && item.remark == "") {
        Indicator.close();
        Toast({
          message: "请填写复核缺少原因",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      if (item.checkDefectCount * 1 > item.actualCount * 1) {
        Indicator.close();
        Toast({
          message: "复核缺数不能大于已拣数量",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      item.userCode = localStorage.getItem("userCode");
      this.$axios
        .post(`/jeecg-boot/app/warehousePick/getPickSunCheck`, item)
        .then(res => {
          if (res.data.code == 200) {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            this.search();
          } else {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    submit() {
      this.info.detailList = this.result;
      for (let i = 0; i < this.result.length; i++) {
        if (
          this.result[i].checkDefectCount * 1 >
          this.result[i].actualCount * 1
        ) {
          Toast({
            message: `复核缺数大于实发数量`,
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (
          this.result[i].checkDefectCount != 0 &&
          this.result[i].checkRemark == ""
        ) {
          Toast({
            message: `复核缺少但未填写备注`,
            position: "bottom",
            duration: 2000
          });
          return;
        }
      }
      this.$axios
        .post(`/jeecg-boot/app/warehousePick/getPickSunCheck`, this.info)
        .then(res => {
          if (res.data.code == 200) {
            this.$router.go(-1);
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    toggleAll() {
      if (this.activeName == 1) {
        if (this.result.length != this.dataArr1.length) {
          this.plain = false;
          this.$refs.checkboxGroup.toggleAll(true);
        } else {
          this.plain = true;
          this.$refs.checkboxGroup.toggleAll();
        }
      } else if (this.activeName == 2) {
        if (this.result.length != this.dataArr2.length) {
          this.plain = false;
          this.$refs.checkboxGroup.toggleAll(true);
        } else {
          this.plain = true;
          this.$refs.checkboxGroup.toggleAll();
        }
      }
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onClick(name, title) {
      this.activeName = name;
      this.result = [];
    }
  }
};
</script>

<style scoped></style>
