<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div v-for="(item,index) in materialList" :key="index" style="padding:8px;text-align:left;background:white">
            <van-row>
                <van-col span="24">
                    <span style="font-weight:800;font-size:18px;float:left;width:80%">{{item.malName}}</span>
                </van-col>
            </van-row>
            <div style="width:100%;height:1px;background:#333"></div>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">物料编码：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.malCode}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">理论用量：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.reportMaterialCount}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">实际用量：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.mainQuantity}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">理论损耗：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{parseFloat(item.ndissipatioNum).toFixed(6)}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">实际损耗：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{((item.mainQuantity-item.reportMaterialCount)/item.reportMaterialCount).toFixed(6)}}</span>
                </van-col>
            </van-row>
            <van-row v-if="((item.mainQuantity-item.reportMaterialCount)/item.reportMaterialCount).toFixed(6)>parseFloat(item.ndissipatioNum).toFixed(6)">
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">损耗原因：</span>
                </van-col>
                <van-col span="18">
                    <van-field v-model="item.remark" rows="2" type="textarea" maxlength="50" autosize placeholder="请输入损耗原因" show-word-limit @change="inputChange(item,index)"/>
                </van-col>
            </van-row>
        </div>

        <van-button type="info" :loading="loading" @click="submit()" style="width:100%;">提交</van-button>
    </div>
</template>
<script>
import { Toast,MessageBox  } from 'mint-ui';
import Vue from 'vue'
export default {
    data(){
        return{
            tpId:"",
            moId:"",
            code:"",
            plot:"",
            lpId:"",
            worker:"",
            workType:"",
            mainUnit:"",
            preCategory:"",
            loading:false,
            itemParams:{},
            sendParams:{},
            personList:[],
            materialList:[]
        }
    },
    created:function(){
        let self = this;
        self.tpId = self.$route.params.tpId
        self.lpId = self.$route.params.lpId
        self.moId = self.$route.params.moId
        self.code = self.$route.params.code
        self.plot = self.$route.params.plot
        self.worker = self.$route.params.worker
        self.mainUnit = self.$route.params.mainUnit
        self.workType = self.$route.params.workType
        self.preCategory = self.$route.params.preCategory
        self.personList = self.$route.params.personList
        console.log(self.personList)
        self.getProductBomInfo()
    },
    methods: {
        inputChange(item,index){
            let self = this
            console.log(item)
            Vue.set(self.materialList, index, item)
        },
        submit(){
            let self = this
            self.loading = true;
            var warnNum =0;
            for(var i=0;i<this.materialList.length;i++){
                let item = this.materialList[i]
                if(((item.mainQuantity-item.reportMaterialCount)/item.reportMaterialCount).toFixed(6)>parseFloat(item.ndissipatioNum).toFixed(6) && (item.remark==null || item.remark=='')){
                    Toast({
                        message: item.malCode+item.malName+"的损耗原因必填！",
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading = false;
                    return;
                }
                if((item.mainQuantity-item.reportMaterialCount)/item.reportMaterialCount<=item.ndissipatioNum){
                    continue;
                }
                
                this.materialList[i].parentId = self.tpId;
                this.materialList[i].standardQuantity = this.materialList[i].reportMaterialCount
                this.materialList[i].bomLossPercent = this.materialList[i].ndissipatioNum
                this.materialList[i].actualLossPercent = ((item.mainQuantity-item.reportMaterialCount)/item.reportMaterialCount).toFixed(6)
                this.materialList[i].creator = localStorage.getItem("userCode")

                console.log(this.materialList[i])
                warnNum++;
            }
            if(warnNum>0){
                self.$axios.post('/jeecg-boot/app/gcWorkshop/addWmsUsageLoss',this.materialList).then(res=>{
                    if(res.data.success){
                        // self.loading = false;
                        self.startMode()
                    }else{
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        self.loading = false;
                    }
                });
            }else{
                self.startMode()
            }
            
        },
        startMode(){
            let self=this;
            let params={
                lpId:self.lpId,
                gcWorkOperationList:self.personList,
                type:'4',
                preCategory:self.preCategory,
                workType:self.workType,
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName'),
                repCreator:self.worker
            }
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getOutputRecords',{params:{lpId:self.lpId}}).then(res=>{
                if(res.data.success){
                    MessageBox.confirm('当前报工数量为'+res.data.result.output+self.mainUnit+',是否确认完工？').then(action => {
                        if(action=="confirm"){
                            self.sendFinishMode(params)
                        }
                    })
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendFinishMode(params){
            let self = this
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew',params).then(res=>{
                if(res.data.code==200){
                    self.loading = false;
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    // self.getInfo(self.itemParams)
                    self.$router.go(-1);
                }else{
                    self.loading = false;
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getProductBomInfo(){
            let self = this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMoBomInfo',{params:{tpId:self.tpId}}).then(res=>{
                if(res.data.success){
                    this.materialList = res.data.result
                    for(var i=0;i<this.materialList.length;i++){
                        this.materialList[i].remark="";
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 22rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.ycl-style{
    color: crimson;
}
</style>