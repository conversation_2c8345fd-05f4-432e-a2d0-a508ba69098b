diff a/vue_colori_order/src/components/OrderOther.vue b/vue_colori_order/src/components/OrderOther.vue	(rejected hunks)
@@ -177,50 +177,53 @@
         }
 
     },
     methods: {
         addMnt() {
             this.$router.push({
                 name: "Repair",
                 params: {
                     moId: this.item.moId,
                     lpId: this.item.id,
                     type: 11,
                     createNo: localStorage.getItem('userCode'),
                     creator: localStorage.getItem('userName')
                 }
             });
         },
         startToNext() {
 
         },
         sendMode(mode) {
             let self = this;
             let num = 0;
             if (mode == "mnt") {
                 if (self.status == '') {
                     num = '11'
+                    self.mntEndVis = false
+                    self.mntdisable = false
+                    self.status = '21'
                     this.$router.push({
                         name: "Repair",
                         params: {
                             moId: self.item.moId,
                             lpId: self.item.id,
                             type: num,
                             createNo: localStorage.getItem('userCode'),
                             creator: localStorage.getItem('userName')
                         }
                     });
                 } else {
                     num = '21'
                 }
             } else if (mode == "change") {
                 if (self.status == '') {
                     num = '12'
                 } else {
                     num = '22'
                 }
             } else if (mode == "wait") {
                 if (self.status == '') {
                     num = '13'
                 } else {
                     num = '23'
                 }
