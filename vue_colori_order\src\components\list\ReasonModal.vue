<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入离开原因" v-decorator="['reason',{rules:[{required:true,message:'请输入离开原因'}]}]"></a-input>
              <!-- <a-select placeholder="请输入离开原因" v-decorator="['reason',{rules:[{required:true,message:'请选择离开原因'}]}]">
                <a-select-option value="上厕所">上厕所</a-select-option>
                <a-select-option value="吃饭">吃饭</a-select-option>
                <a-select-option value="抽烟">抽烟</a-select-option>
              </a-select> -->
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "ReasonModal",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: "/gc/gcTechnologyPlan/addGcPlan",
          edit: "/test/order/edit",
          quota: "/gc/gcLead/getTechnology",
          getMoPlot: "/gc/gcTechnologyPlan/getRestCount",
          mode: "/gc/gcLead/getWorkmode",
          order: "/gc/gcLead/getOrderInfo",
          worker: "/gc/gcLead/getWorker",
          material: "/gc/gcLead/getMaterial",
          preplot:"/gc/gcLead/getPrePlot",
          restcount:"/gc/gcLead/getRestCount",
          getOrderNo: "/gc/gcTechnologyPlan/getOrderNo",
          orderCustomerList: "/test/order/listOrderCustomerByMainId",
          orderTicketList: "/test/order/listOrderTicketByMainId",
          toSelectEmp:"/gc/gcPeople/getPeopleInfo"
        },
        moIds:[],
        expandedRowKeys: [],
        id: ' ',
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },

      edit(item) {  
        this.visible = true;
        this.item=item;
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        let self=this
        this.form.validateFields((err, values) => {
          if (!err) {
            self.confirmLoading = true;
            for(var i=0;i<self.item.length;i++){
              self.item[i].reason=self.form.getFieldValue("reason")
            }
            let params={
              lpId:self.item[0].lpId,
              gcWorkOperationList:self.item,
              type:'5',
              createNo:localStorage.getItem('userCode'),
              creator:localStorage.getItem('userName')
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                this.visible=false;
                this.$emit('ok');
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            }).finally(() => {
                  self.confirmLoading = false;
                  self.close();
            });
          }
        });
        
      },
      handleCancel() {
        this.close()
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>