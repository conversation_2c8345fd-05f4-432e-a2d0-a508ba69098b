<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">拉料数据</div>
        <van-field label="所属账套" :value="book"  readonly @click="handleBook"/>
        <van-field label="灌包车间" :value="workshop"  readonly @click="handleWorkshop"/>
        <van-field label="工作中心" :value="jobCenter" readonly @click="handleJobCenter"/>
        <van-field label="储罐编号" v-model="tankNo"/>

        <van-button type="info" @click="searchTank()"  style="margin:10px;width:40%;border-radius:10px;">查询</van-button>
        <van-button type="primary" @click="scanTank()"  style="margin:10px;width:40%;border-radius:10px;">扫码拉料</van-button>

        <van-popup  v-model="showBook" position="bottom">
            <van-picker :columns="bookList"  @cancel="onBookCancel" @confirm="onBookConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showWorkshop" position="bottom">
            <van-picker :columns="workShopList" @cancel="onWorkShopCancel" @confirm="onWorkShopConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showJobCenter" position="bottom">
            <van-picker :columns="jobCenterList" @cancel="onJobCenterCancel" @confirm="onJobCenterConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px" v-if="tankList.length>0">储罐查询清单</div>

        <div v-for="(item,index) in tankList" :key="index" style="text-align:left;padding:5px;margin:5px;background:#FFF;" @click="handleTankPull(item)">
            <div style="width:100%">
                <div style="float:left;font-size:16px;font-weight:800;width:70%;color:green;">{{item.tankNo}}</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:orange" v-if="item.realProductStatus=='1'">待检</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:green" v-if="item.realProductStatus=='2'">合格</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:red" v-if="item.realProductStatus=='3'">不合格</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:gray" v-if="item.realProductStatus=='0'">锁定</div>
                <div style="clear:both"></div>
            </div>
            
            <div style="background:#f0f0f0;height:1px;width:100%"></div>

            <div style="width:100%;font-size:14px;color:#888888">胶体编号：{{item.realProductNo}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体名称：{{item.realProductName}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体批次：{{item.realCode}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体净重：{{item.tankVolume-item.realVolume}}KG</div>


            <div style="clear:both"></div>
        </div>


        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px" v-if="pullList.length>0">已拉料清单</div>

        <div v-for="(item,index) in pullList" :key="index" style="text-align:left;padding:5px;margin:5px;background:#FFF;">
            <div style="width:100%">
                <div style="float:left;font-size:16px;font-weight:800;width:70%;color:green;">{{item.tankNo}}</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:blue" v-if="item.status=='1'">待灌包</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:orange" v-if="item.status=='2'">灌包中</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:gray" v-if="item.status=='0'">锁定中</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:pink" v-if="item.status=='3'">已完成</div>
                <div style="clear:both"></div>
            </div>
            
            <div style="background:#f0f0f0;height:1px;width:100%"></div>

            <div style="width:100%;font-size:14px;color:#888888">胶体编号：{{item.code}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体名称：{{item.name}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体批次：{{item.customer}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体净重：{{item.volume}}KG</div>
            <div style="width:100%;font-size:14px;color:#888888">拉入产线：{{item.jobCenter}}</div>


            <div style="clear:both"></div>
        </div>

        
    </div>
</template>
<script>
import { Indicator   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            bookList:[],
            workShopList:[],
            jobCenterList:[],
            tankList:[],
            pullList:[],
            showBook:false,
            showWorkshop:false,
            showJobCenter:false,
            book:"",
            workshop:"",
            jobCenter:"",
            tankNo:"",
        }
    },
    created:function(){

        this.book = localStorage.getItem('book');
        this.workshop = localStorage.getItem('gbWorkshop');
        this.jobCenter = localStorage.getItem('gbJobCenter');
        
        this.getBook();

        if(this.book!=null && this.book !=''){
            this.getWorkShop(this.book);
        }
        
        if(this.workshop!=null && this.workshop !=''){
            this.getJobCenter(this.workshop);
        }

        if(this.jobCenter!=null && this.jobCenter !=''){
            this.getPullInfo(this.jobCenter);
        }
        
    },
    methods:{
        getBook(){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/warehouse/getFactoryInfo',null).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    let books=res.data.result
                    for(var i=0;i<books.length;i++){
                        self.bookList.push(books[i].name)
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        getWorkShop(book){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/tank/check/getWorkShopByBook',{params:{book:book}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.workShopList=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        getJobCenter(value){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getJobCenterByShop',{params:{workshop:value}}).then(res=>{
                if(res.data.code==200){
                    self.jobCenterList=res.data.result
                }
            })
        },
        searchTank(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getIdByTankNo',{params:{tankNo:self.tankNo}}).then(res=>{
                if(res.data.code==200){
                    self.tankList=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        handleTankPull(item){
            let self=this;
            if(self.book==null || self.book ==''){
                Toast({
                    message: "请先选择账套",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.workshop==null || self.workshop ==''){
                Toast({
                    message: "请先选择车间",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.jobCenter==null || self.jobCenter ==''){
                Toast({
                    message: "请先选择工作中心",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }


            let result=item.id+'&'+item.realProductNo+'&'+item.realCode

            self.$router.push({name:"ScanColloidInfo",params:{result:result,type:'2',book:self.book,workshop:self.workshop,jobCenter:self.jobCenter}})
        },
        handleBook(){
            this.showBook=true
        },
        handleWorkshop(){
            if(this.book==null || this.book==''){
                Toast({
                    message: "请先选择账套",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                this.showWorkshop=true
            }
            
        },
        handleJobCenter(){
            if(this.workshop==null || this.workshop==''){
                Toast({
                    message: "请先选择灌包车间",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                this.showJobCenter=true
            }
        },
        onBookConfirm(value){
            this.book = value
            localStorage.setItem('book',value)
            localStorage.setItem('workshop','')
            this.workshop = '';
            this.jobCenter = '';
            this.showBook = false
            this.getWorkShop(value);
        },
        onWorkShopConfirm(value){
            this.workshop = value
            localStorage.setItem('gbWorkshop',value)
            this.jobCenter = '';
            this.showWorkshop = false
            this.getJobCenter(value);
        },
        onJobCenterConfirm(value){
            this.jobCenter = value
            localStorage.setItem('gbJobCenter',value);
            this.showJobCenter = false
            this.getPullInfo(this.jobCenter);
        },
        onBookCancel(){
            this.showBook=false;
        },
        onWorkShopCancel(){
            this.showWorkshop = false
        },
        onJobCenterCancel(){
            this.showJobCenter = false
        },
        scanTank(){
            let self=this

            if(self.book==null || self.book ==''){
                Toast({
                    message: "请先选择账套",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.workshop==null || self.workshop ==''){
                Toast({
                    message: "请先选择车间",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.jobCenter==null || self.jobCenter ==''){
                Toast({
                    message: "请先选择工作中心",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

             self.$router.push({name:"ScanColloidInfo",params:{result:'1739450352310910978',type:'2',book:self.book,workshop:self.workshop,jobCenter:self.jobCenter}})

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$router.push({name:"ScanColloidInfo",params:{result:result,type:'2',book:self.book,workshop:self.workshop,jobCenter:self.jobCenter}})
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });

        },
        getPullInfo(jobCenter){
            let self=this;

            self.$axios.get('/jeecg-boot/app/tank/check/getPullInfo',{params:{jobCenter:jobCenter}}).then(res=>{
                if(res.data.code==200){
                    self.pullList=res.data.result;
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}

</style>