<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="胶体操作记录填写" left-arrow @click-left="onClickLeft" right-text="复核"
                @click-right="checkAll" />
        </van-sticky>
        <van-collapse v-model="active" accordion @change="onChange">
            <van-collapse-item v-for="(item,index) in data" :key="index" :name="index">
                <template #title>
                    <div v-if="item.arr[0][0].status==1" style="background-color: #79bcff;">
                        {{item.label+'：'+item.content}}
                    </div>
                    <div v-else-if="item.arr[0][0].status==2" style="background-color: #8cf175">
                        {{item.label+'：'+item.content}}</div>
                    <div v-else>{{item.label+'：'+item.content}}</div>
                </template>
                <van-tabs v-model="activeName" color="#1989fa">
                    <van-tab title="制胶内容">
                        <van-button
                            v-if="item.arr[0][0].status != 2 && item.fillCount > 1 && item.fillCount != item.arr.length"
                            @click="AccordingToFillCount(item)" size="mini"
                            style="width: 100%;height:2rem;">+</van-button>
                        <div v-for="(a, ai) in item.arr" :key="ai">
                            <van-cell :style="{ backgroundColor: getRandomColor() }"> 第{{ ai + 1 }} 组</van-cell>
                            <van-button v-if="a[ai].status == null && ai > 0" @click="deleteArr(item, ai)" size="mini"
                                style="width: 100%;height:2rem;">删除</van-button>
                            <div v-for="(v, vi) in a" :key="vi">
                                <van-field v-if="v.headType == '数字'" labelWidth='10rem' placeholder="请填写"
                                    v-model="v.result" type="number"
                                    :label="`${v.headContent}${v.headValue == null ? '' : '(' + v.headValue + ')：'}`"
                                    @blur="(e) => lastProductfieldBlur(e, v)" :readonly="v.status == 2" />
                                <van-cell v-else-if="v.headType == '时间'">
                                    <template #title>
                                        <span class="custom-title">{{ v.headContent }}&nbsp;:&emsp;&emsp;</span>
                                        <!-- @change="onChange" @ok="onOk" -->
                                        <a-date-picker :locale="locale" v-model="v.result" show-time placeholder="请选择"
                                            @change="(e) => dateChage(e, v)" :disabled="v.status == 2" />
                                    </template>
                                </van-cell>
                                <van-field v-else-if="v.headType == '文本'" labelWidth='10rem' placeholder="请填写"
                                    v-model="v.result" type="text"
                                    :label="`${v.headContent}${v.headValue == null ? '' : '(' + v.headValue + ')：'}`"
                                    @blur="(e) => lastProductfieldBlur(e, v)" :readonly="v.status == 2" />
                            </div>
                            <van-row gutter="20">
                                <van-col span="24">
                                    <van-button v-if="ai>0&&a[0].status == null" size="small" type="info"
                                        @click="submit(item)" style="width:100%;">提交</van-button>
                                </van-col>
                            </van-row>
                        </div>
                        <van-row gutter="20">
                                <van-col span="24">
                                    <van-button v-if="item.arr[0][0].status == null" size="small" type="info"
                                        @click="submit(item)" style="width:100%;">提交</van-button>
                                    <van-button v-if="item.arr[0][0].status == 1" size="small" type="primary"
                                        @click="check(item)" style="width:100%;">复核</van-button>
                                </van-col>
                            </van-row>
                    </van-tab>
                    <van-tab title="步骤内容" v-if="item.labelArr">
                        <div v-for="(x, xi) in item.labelArr" :key="xi">
                            <van-field labelWidth='10rem' placeholder="请填写" v-model="x.value" :label="x.label" />
                        </div>
                        <van-row gutter="20">
                            <van-col span="24">
                                <van-button size="small" type="info" @click="editContent(item)"
                                    style="width:100%;">提交</van-button>
                            </van-col>
                        </van-row>
                    </van-tab>
                </van-tabs>
            </van-collapse-item>
        </van-collapse>
        <!-- <van-tabs v-model="active" sticky color="#1989fa">
            <van-tab v-for="(item,index) in data" :title="item.label" :key="index">
                <van-cell :title="item.content" />
                <div v-for="v in item.arr">
                    <van-field v-if="v.headType=='数字'" placeholder="请填写" v-model="v.result" type="number"
                        :label="v.headContent+':'" @blur="(e)=>lastProductfieldBlur(e,v)" />
                    <van-cell v-else-if="v.headType=='时间'">
                        <template #title>
                            <span class="custom-title">{{v.headContent}}&nbsp;:&emsp;&emsp;</span>
                            <a-date-picker :locale="locale" v-model="v.result" show-time placeholder="请选择"
                                @change=" (e)=>dateChage(e,v)" />
                        </template>
                    </van-cell>
                    <van-field v-else-if="v.headType=='文本'" placeholder="请填写" v-model="v.result" type="text"
                        :label="v.headContent+':'" @blur="(e)=>lastProductfieldBlur(e,v)" />
                </div>
            </van-tab>
        </van-tabs> -->

    </div>
</template>

<script>
    import { Indicator, MessageBox, Toast } from "mint-ui";
    import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
    export default {
        data() {
            return {
                locale,
                userCode: localStorage.getItem("userCode"),
                data: [],
                active: 0,
                activeName: '1'
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                this.search();
            }
        },
        methods: {
            getRandomColor() {
                var letters = '0123456789ABCDEF';
                var color = '#';
                do {
                    for (var i = 0; i < 6; i++) {
                        color += letters[Math.floor(Math.random() * 16)];
                    }
                } while (color === '#FFFFFF' || color === '#000000');
                return color;
            },
            AccordingToFillCount(item) {
                if(item.fillCount==item.arr.length)return
                item.arr.push([...[...item.arr[0]].map(q => {
                    return {
                        ...q,
                        status:null,
                        id: null,
                        groupCode: item.arr.length
                    }
                })])
                console.log(item);
            },
            deleteArr(item,i){
                item.arr.splice(i, 1)
            },
            onChange(e) {
                this.activeName = '1'
            },
            editContent(item) {


                item.labelArr.forEach(v => {
                    item.text = item.text.replace(v.label, v.label + v.value)
                })
                let param = {
                    id: item.childId,
                    mixId: item.arr[0].mixId,
                    batchId: item.arr[0].batchId,
                    zjbzId: item.zjbzId,
                    result: item.text,
                    childResult: JSON.stringify(item.labelArr),
                    creator: localStorage.getItem('userCode'),
                }
                this.$axios.post('/jeecg-boot/app/batchRecord/editZjbz', param).then(res => {
                    item.text = item.content
                    if (res.data.success) {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            },
            groupByProperty(arr, prop) {
                return Object.values(arr.reduce((acc, item) => {
                    // 初始化对应属性的数组（如果尚未存在）
                    acc[item[prop]] = acc[item[prop]] || [];
                    // 将当前对象推入对应的属性数组中
                    acc[item[prop]].push(item);
                    return acc;
                }, {}));
            },
            groupByProperty(arr, prop) {
                return Object.values(arr.reduce((acc, item) => {
                    // 初始化对应属性的数组（如果尚未存在）
                    acc[item[prop]] = acc[item[prop]] || [];
                    // 将当前对象推入对应的属性数组中
                    acc[item[prop]].push(item);
                    return acc;
                }, {}));
            },
            search() {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${this.info.id}`).then(res => {
                    if (res.data.code == 200) {
                        this.data = []
                        this.groupByDifferentIds(res.data.result.zjnrList).forEach(item => {
                            this.data.push({
                                arr: this.groupByProperty(item,'groupCode'),
                                label: item[0].label,
                                zjbzId: item[0].zjbzId,
                                content: item[0].content,
                                text: item[0].content,
                                childId: item[0].childId,
                                childResult: item[0].childResult,
                                fillCount: item[0].fillCount,
                                arrLength: item.length,
                            })
                        });
                        console.log(this.data);
                        this.active = 0
                        let bzArr = []//步骤
                        let yclArr = []//预处理
                        this.data.forEach(item => {
                            if (item.label.startsWith("步骤")) {
                                bzArr.push(item)
                            } else {
                                yclArr.push(item)
                            }
                            if (item.childId == null) {
                                let labelArr = []
                                let atArr = item.content.match(/@([^@]+)@/g);
                                this.$set(item, 'atArr', atArr);
                                let noatArr = []
                                if (atArr) {
                                    atArr.forEach(x => {
                                        labelArr.push({
                                            label: x.slice(1, -1),
                                            value: '',
                                        })
                                        noatArr.push(x.slice(1, -1))
                                    })
                                    this.$set(item, 'labelArr', labelArr);
                                }
                            }
                            else {
                                this.$set(item, 'labelArr', JSON.parse(item.childResult));
                            }
                            if (item.arr[0][0].status == 1 || item.arr[0][0].status == 2) {
                                this.active++
                            }
                        });
                        bzArr.sort((a, b) => {
                            return a.label.slice(2) * 1 - b.label.slice(2) * 1;
                        });
                        yclArr.sort((a, b) => {
                            return a.label.slice(3) * 1 - b.label.slice(3) * 1;
                        });
                        this.data = [...yclArr, ...bzArr]
                    }
                });
            },
            check(item) {
                MessageBox.confirm('是否确认复核？').then(action => {
                    if (action == "confirm") {
                        let str = ''
                        item.arr.flat().forEach(v => {
                            if(v.id){
                                str += v.id + ','
                            }
                        })
                        let param = {
                            id: str,
                            checker: localStorage.getItem('userCode'),
                            checkerName: localStorage.getItem('userName')
                        }
                        this.$axios.put('/jeecg-boot/app/batchRecord/approveZjnr', param).then(res => {
                            if (res.data.success) {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                this.search()
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                })
            },
            checkAll() {
                MessageBox.confirm('是否确认复核所有？').then(action => {
                    if (action == "confirm") {
                        let str = ''
                        let checkFlag = false
                        this.data.forEach(item => {
                            console.log(item);
                            item.arr.forEach(a=>{
                                a.forEach(v => {
                                    if (v.status == 1) {
                                        checkFlag = true
                                        if(v.id){
                                            str += v.id + ','
                                        }
                                    }
                                })
                            })
                        })
                        if (!checkFlag) {
                            Toast({
                                message: '暂无需要复核的步骤',
                                position: 'bottom',
                                duration: 2000
                            });
                            return
                        }
                        let param = {
                            id: str,
                            checker: localStorage.getItem('userCode'),
                            checkerName: localStorage.getItem('userName')
                        }
                        this.$axios.put('/jeecg-boot/app/batchRecord/approveZjnr', param).then(res => {
                            if (res.data.success) {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                this.search()
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                })
            },
            editItem(item) {
                if (item.status == null) {
                    return
                }
                if (item.status == 2) {
                    return
                }
                if (item.headValue != null) {
                    item.min = item.headValue.split('~')[0]
                    item.max = item.headValue.split('~')[1]
                    if (item.result * 1 > item.max * 1 || item.result * 1 < item.min * 1) {
                        Toast({
                            message: '数值不在范围内',
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                        return
                    }
                }
                this.$axios.put(`/jeecg-boot/app/batchRecord/editZjnr`, item).then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            },
            dateChage(e, item) {
                console.log(item);
                this.editItem(item)
            },
            lastProductfieldBlur(e, item) {
                console.log(item);
                this.editItem(item)
            },
            submit(item) {
                if (item.id != null) {
                    Toast({
                        message: '已经提交过了',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                let beginTime = ''
                let endTime = ''
                let flag = false
                item.arr.forEach(a => {
                    a.forEach(v => {
                        if (v.headContent == '开始时间') {
                            beginTime = v.result
                        }
                        if (v.headContent == '结束时间') {
                            endTime = v.result
                        }
                        if (v.headValue != null) {
                            v.min = v.headValue.split('~')[0]
                            v.max = v.headValue.split('~')[1]
                            if (v.result * 1 > v.max * 1 || v.result * 1 < v.min * 1) {
                                flag = true
                            }
                        }
                    })
                })
                if (flag) {
                    Toast({
                        message: '数值不在范围内',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                // if (!beginTime||!endTime) {
                //     Toast({
                //         message: '开始时间结束时间不能为空',
                //         position: "bottom",
                //         duration: 2000
                //     });
                //     return
                // }
                if (new Date(endTime) < new Date(beginTime)) {
                    Toast({
                        message: '结束时间不能小于开始时间',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                let dataArr = [...item.arr.flat().filter(v=>v.id==null)]
                console.log(dataArr);
                let params = {
                    userCode: localStorage.getItem('userCode'),
                    creator: localStorage.getItem('userCode'),
                    userName: localStorage.getItem('userName'),
                    mixId: this.info.id,
                    checkStatus: 3,
                    resultList: dataArr
                }
                console.log(params);
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios.post(`/jeecg-boot/app/batchRecord/addZjnr`, params).then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            },
            groupByDifferentIds(arr) {
                const groupedArrays = {};

                for (const item of arr) {
                    const zjbzId = item.zjbzId;
                    if(item.status==null){
                        item.result = item.defaultValue
                    }
                    if (!groupedArrays[zjbzId]) {
                        groupedArrays[zjbzId] = [];
                    }
                    groupedArrays[zjbzId].push(item);
                }

                const result = Object.values(groupedArrays);
                return result;
            },
            onClickLeft() {
                this.$router.go(-1);
            },

        }
    };
</script>

<style scoped></style>