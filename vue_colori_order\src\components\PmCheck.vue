<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div v-for="(item,index) in checkInfo" :key="index">

            <div style="text-align:left;font-weight:800;padding:10px;" v-if="item.show">{{item.deviceName}}</div>

            <div v-for="(temp,key) in item.checkInfo" :key="key" v-if="item.show">

                <van-field-Pick
                    v-model="temp.result"
                    :label="temp.content"
                    required
                    :columns="['正常','异常']"
                />

                <van-field label="异常描述" rows="3" v-if="temp.result=='异常'" autosize type="textarea" maxlength="200" v-model="temp.remarks"/>

            </div>

            <van-button v-if="item.show && index>0" type="primary" @click="up(index)" style="margin-left:5%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">{{upBtn}}</van-button>
            <van-button v-if="item.show" type="primary" @click="submit(index)" style="margin-left:10%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">{{nextBtn}}</van-button>

        </div>

        
        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            checkInfo:[],
            userCode:'',
            startTime:'',
            upBtn:'上一步',
            nextBtn:'下一步'
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.checkInfo=JSON.parse(localStorage.getItem("pmCheckInfo"));
        this.startTime=localStorage.getItem("pmStartTime");
        // console.log("checkInfo:"+JSON.stringify(this.checkInfo))
        this.userCode=localStorage.getItem('userCode')

        console.log(this.startTime)
    },
    methods: {
        up(index){
            let self=this;
            self.$set(this.checkInfo[index],"show",false)
            self.$set(this.checkInfo[index-1],"show",true)
            self.nextBtn="下一步"
        },
        submit(index){
            let self=this;

            if(index==self.checkInfo.length-1){
                let params={
                    result:this.checkInfo,
                    userCode:this.userCode,
                    startTime:this.startTime,
                    department:localStorage.getItem("pmDepartment")
                }
                self.$axios.post('/jeecg-boot/app/device/sendPmCheckInfo',params).then(res=>{
                    if(res.data.code==200){
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        this.$router.go(-1);
                    }else{
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            }else if(index+1==self.checkInfo.length-1){
                self.$set(this.checkInfo[index],"show",false)
                self.$set(this.checkInfo[index+1],"show",true)
                self.nextBtn="提交"
            }else{
                self.$set(this.checkInfo[index],"show",false)
                self.$set(this.checkInfo[index+1],"show",true)
            }


            


          
            
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
/deep/ .van-field__label{
    width: 16em;
}
/deep/ .van-field__control{
    text-align: center;
    color: #00f;
}
</style>