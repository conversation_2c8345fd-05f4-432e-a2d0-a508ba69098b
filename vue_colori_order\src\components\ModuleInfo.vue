<template>
  <div>
    <van-sticky :offset-top="0">
      <van-nav-bar title="模具信息" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
        @click-right="onClickRight" />
    </van-sticky>
    <van-popup v-model="show" position="right" :style="{ height: '100%', width: '90%' }">
      <van-field v-model="product" clearable label="模具：" placeholder="请输入模具" />
      <van-field v-model="workshop" clearable label="车间：" placeholder="请输入车间" />
      <van-button type="info" @click="search1" style="width: 90%; height: 5%">
        确定
      </van-button>
    </van-popup>

    <van-checkbox-group v-model="result" ref="checkboxGroup">
      <van-cell-group>
        <van-list v-model="load" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <van-cell v-for="(item, index) in moduleList" clickable :key="index">
            <template #default>
              <div style="text-align: left; margin-left: 5%">
                <van-row>
                  <van-col span="24" style="display: flex; color: rgb(38 37 37 / 85%)">
                    <span style="font-size: large; font-weight: 700; text-align: left">
                      {{ item.name }}
                    </span>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24">模具位置: {{ item.location }}</van-col>
                </van-row>
                <van-row>
                  <van-col span="24">模具编号: {{ item.code }}</van-col>
                </van-row>
                <van-row>
                  <van-col span="24">模具规格: {{ item.spec }}</van-col>
                </van-row>
                <van-row>
                  <van-col span="24">
                    总&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;量:
                    {{ item.totalNumber }}
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24">
                    可用数量: {{ item.availableNumber }}
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="20">领用数量: {{ item.lendNumber }}</van-col>
                  <van-button plain hairline type="info" size="small" @click="goDetail(item)">
                    详情
                  </van-button>
                </van-row>
                <!-- <van-row>
                <van-col span="4">
                  <van-button type="default" size="mini"  plain @click="Storage(item)">
                    入库
                  </van-button>
                </van-col>
                <van-col span="1"> </van-col>
                <van-col span="4">
                  <van-button type="default" size="mini"  plain @click="Receive(item)">
                    领用
                  </van-button>
                </van-col>
                <van-col span="1"> </van-col>
                <van-col span="4">
                  <van-button type="default" size="mini"  plain @click="Return(item)">
                    归还
                  </van-button>
                </van-col>
                <van-col span="1"> </van-col>
                <van-col span="4">
                  <van-button type="default" size="mini"  plain @click="Destroy(item)">
                    报损
                  </van-button>
                </van-col>
                <van-col span="1"> </van-col>
                <van-col span="4">
                  <van-button type="default" size="mini"  plain @click="goDetail(item)">
                    详情
                  </van-button>
                </van-col>
              </van-row> -->
              </div>
            </template>
            <template #icon>
              <van-checkbox :name="item" ref="checkboxes" />
            </template>
          </van-cell>
        </van-list>
      </van-cell-group>
    </van-checkbox-group>
    <div style="height: 10%; width: 100%">&nbsp;</div>
    <van-row style="
        background-color: #fff;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;
      " gutter="30">
      <van-col span="4">
        <van-button :plain="plain" icon="success" type="info" round size="mini" @click="toggleAll">
        </van-button>
      </van-col>
      <van-col span="11"></van-col>
      <van-col span="8">
        <van-button round style="height: 25px" type="info" size="large" @click="batchReceive" loading-type="spinner">
          批量领用
        </van-button>
      </van-col>
    </van-row>
  </div>
</template>

<script>
import { Toast } from "mint-ui";

export default {
  data() {
    return {
      show: false,
      filter: {},
      product: "",
      workshop: "",
      moduleList: [],
      result: [],
      plain: true,
      finished: false,
      load: false,
      pageNo: 1,
      total: 0,
    };
  },

  methods: {
    search1() {
      this.result = []
      this.pageNo = 1
      this.moduleList = []
      this.$axios
        .get(
          `/jeecg-boot/ncApp/molds/getMoldsList?product=${this.product
          }&pageNo=${this.pageNo}&workshop=${this.workshop}&userCode=${localStorage.getItem(
            "userCode"
          )}`
        )
        .then((res) => {
          if (res.data.code == 200) {
            this.finished = false
            let len = res.data.result.records.length;
            if (len == 0) {
              this.moduleList = []; // 清空数组
              this.finished = true; // 停止加载
            }
            this.total = res.data.result.total;  //总数
            this.moduleList.push(...res.data.result.records);
            this.load = false;
            this.pageNo++; // 分页数加一
            if (this.moduleList.length >= res.data.result.total) {
              this.finished = true; // 结束加载状态
            }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000,
            });
          }
        });
      this.show = false;
    },
    onLoad() {
      this.search(); // 调用上面方法,请求数据
    },
    isAllEqual(array) {
      if (array.length > 0) {
        return !array.some((value, index) => {
          return value.workshop !== array[0].workshop;
        });
      } else {
        return true;
      }
    },
    batchReceive() {
      if (this.result.length > 0) {
        if (this.isAllEqual(this.result)) {
          this.$router.replace({
            name: "ModuleBatchReceive",
            params: { arr: this.result },
          });
        } else {
          Toast({
            message: "请选择同一个车间",
            position: "bottom",
            duration: 2000,
          });
        }
      } else {
        Toast({
          message: "请选择至少一个",
          position: "bottom",
          duration: 2000,
        });
      }
    },
    goDetail(item) {
      // SpareListDetail
      localStorage.setItem("moduleListItem", JSON.stringify(item));
      this.$router.replace({
        name: "ModuleInfoDetail",
      });
    },
    search() {
      this.$axios
        .get(
          `/jeecg-boot/ncApp/molds/getMoldsList?product=${this.product
          }&pageNo=${this.pageNo}&workshop=${this.workshop}&userCode=${localStorage.getItem(
            "userCode"
          )}`
        )
        .then((res) => {
          if (res.data.code == 200) {
            this.finished = false
            let len = res.data.result.records.length;
            if (len == 0) {
              this.moduleList = []; // 清空数组
              this.finished = true; // 停止加载
            }
            this.total = res.data.result.total;  //总数
            this.moduleList.push(...res.data.result.records);
            this.load = false;
            this.pageNo++; // 分页数加一
            if (this.moduleList.length >= res.data.result.total) {
              this.finished = true; // 结束加载状态
            }
          } else {
            Toast({
              message: res.data.msg,
              position: "bottom",
              duration: 2000,
            });
          }
        });
      this.show = false;
    },
    onClickLeft() {
      this.$router.push({
        name: "ModuleOperation",
      });
    },
    onClickRight() {
      this.pageNo = 1
      this.show = true;
    },
    toggleAll() {
      if (this.result.length != this.moduleList.length) {
        this.plain = false;
        this.$refs.checkboxGroup.toggleAll(true);
      } else {
        this.plain = true;
        this.$refs.checkboxGroup.toggleAll();
      }
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    Storage(item) {
      this.$router.replace({
        name: "ModuleStorage",
        params: item,
      });
    },
    Receive(item) {
      this.$router.replace({
        name: "ModuleReceive",
        params: item,
      });
    },
    Return(item) {
      this.$router.replace({
        name: "ModuleReturn",
        params: item,
      });
    },
    Destroy(item) {
      this.$router.replace({
        name: "ModuleDestroy",
        params: item,
      });
    },
  },
};
</script>

<style  scoped>

</style>