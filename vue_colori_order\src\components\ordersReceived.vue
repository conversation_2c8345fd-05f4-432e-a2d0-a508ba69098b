<template>
  <div style="text-align: left; background-color: #fff">
    <van-sticky :offset-top="0">
      <van-nav-bar
        title="生产计划列表"
        right-text="筛选"
        @click-right="onClickRight"
        left-arrow
        @click-left="onClickLeft"
      />
    </van-sticky>

    <van-popup v-model="show" position="bottom" :style="{ height: '60%' }">
      <div style="padding: 16px;">
        <van-field
          readonly
          clickable
          name="workDayPicker"
          v-model="workDay"
          label="工作日："
          placeholder="点击选择工作日"
          @click="showWorkDayPicker = true"
        />
        <van-popup v-model="showWorkDayPicker" position="bottom">
          <van-datetime-picker
            v-model="workDayDate"
            type="date"
            title="选择工作日"
            @confirm="workDayConfirm"
            @cancel="showWorkDayPicker = false"
          />
        </van-popup>

        <van-field
          readonly
          clickable
          name="picker"
          v-model="bookName"
          label="账套："
          placeholder="点击选择账套"
          @click="showbookNamePicker = true"
        />
        <van-popup v-model="showbookNamePicker" position="bottom">
          <van-picker
            show-toolbar
            :columns="bookNameColumns"
            @confirm="bookNameConfirm"
            @cancel="showbookNamePicker = false"
          />
        </van-popup>

        <van-field
          readonly
          clickable
          name="picker"
          v-model="workshop"
          label="车间："
          placeholder="点击选择车间"
          @click="showworkshopPicker = true"
        />
        <van-popup v-model="showworkshopPicker" position="bottom">
          <van-picker
            show-toolbar
            :columns="workshopColumns"
            @confirm="workshopConfirm"
            @cancel="showworkshopPicker = false"
          />
        </van-popup>

        <van-field
          readonly
          clickable
          name="picker"
          v-model="jobCenter"
          label="工作中心："
          placeholder="点击选择工作中心"
          @click="showjobCenterPicker = true"
        />
        <van-popup v-model="showjobCenterPicker" position="bottom">
          <van-picker
            show-toolbar
            :columns="jobCenterColumns"
            @confirm="jobCenterConfirm"
            @cancel="showjobCenterPicker = false"
          />
        </van-popup>

        <van-button type="info" @click="search" style="width: 100%; margin-top: 16px;" round>
          确定
        </van-button>
      </div>
    </van-popup>

    <!-- 数据列表 -->
    <div style="padding: 16px;">
      <div
        v-for="(item, index) in planList"
        :key="index"
        @click="detail(item)"
        style="
          background-color: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          border-left: 4px solid #1989fa;
        "
      >
        <!-- 产品名称 - 最上面显示 -->
        <div style="margin-bottom: 12px;">
          <div style="font-size: 16px; font-weight: bold; color: #323233; margin-bottom: 8px;">
            {{ item.name || '未知产品' }}
          </div>
        </div>

        <van-row gutter="8" style="margin-bottom: 8px;">
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">id</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.id || '-' }}</div>
          </van-col>
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">MO单号</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.moId || '-' }}</div>
          </van-col>
        </van-row>
        <van-row gutter="8" style="margin-bottom: 8px;">
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">OG单号</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.ogId || '-' }}</div>
          </van-col>
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">账套</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.bookName || '-' }}</div>
          </van-col>
        </van-row>

        <van-row gutter="8" style="margin-bottom: 8px;">
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">车间</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.workshop || '-' }}</div>
          </van-col>
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">工作中心</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.jobCenter || '-' }}</div>
          </van-col>
        </van-row>

        <van-row gutter="8" style="margin-bottom: 8px;">
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">产品编码</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.code || '-' }}</div>
          </van-col>
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">生产班次</div>
            <div style="font-size: 14px; color: #323233; font-weight: 500;">{{ item.category || '-' }}</div>
          </van-col>
        </van-row>

        <van-row gutter="8" style="margin-bottom: 8px;">
          <van-col span="8">
            <div style="font-size: 12px; color: #646566;">计划产量</div>
            <div style="font-size: 14px; color: #1989fa; font-weight: bold;">{{ item.plot || 0 }}</div>
          </van-col>
          <van-col span="8">
            <div style="font-size: 12px; color: #646566;">预计时间(H)</div>
            <div style="font-size: 14px; color: #07c160; font-weight: bold;">{{ item.planHour || 0 }}</div>
          </van-col>
          <van-col span="8">
            <div style="font-size: 12px; color: #646566;">预计人数</div>
            <div style="font-size: 14px; color: #ff976a; font-weight: bold;">{{ item.tpnum || 0 }}</div>
          </van-col>
        </van-row>

        <van-row gutter="8" style="margin-bottom: 12px;">
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">开始时间</div>
            <div style="font-size: 13px; color: #323233;">{{ formatDateTime(item.beginTime) }}</div>
          </van-col>
          <van-col span="12">
            <div style="font-size: 12px; color: #646566;">结束时间</div>
            <div style="font-size: 13px; color: #323233;">{{ formatDateTime(item.endTime) }}</div>
          </van-col>
        </van-row>

        <!-- 操作按钮区域 -->
        <div style="border-top: 1px solid #ebedf0; padding-top: 12px; display: flex; justify-content: space-around;">
          <van-button
            v-if="item.status == 1"
            type="primary"
            size="small"
            @click.stop="handleAccept(item)"
          >
            接单
          </van-button>

          <van-button
            v-if="item.status == 1 || item.status == 2 || item.status == 3"
            type="warning"
            size="small"
            @click.stop="showHangModal(item)"
          >
            挂起
          </van-button>

          <van-button
            v-if="item.status == 4"
            type="info"
            size="small"
            @click.stop="showOverTurnModal(item)"
          >
            结转
          </van-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" style="text-align: center; padding: 16px;">
        <van-loading size="16">加载中...</van-loading>
      </div>

      <!-- 暂无数据 -->
      <div v-if="!loading && planList.length === 0" style="text-align: center; padding: 32px; color: #969799;">
        暂无数据
      </div>
    </div>

    <!-- 挂起弹窗 -->
    <van-popup v-model="hangModalVisible" position="bottom" :style="{ height: '40%' }">
      <div style="padding: 16px;">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 16px; text-align: center;">
          挂起原因
        </div>
        <van-field
          v-model="hangReason"
          type="textarea"
          placeholder="请输入挂起原因"
          rows="4"
          maxlength="200"
          show-word-limit
        />
        <div style="display: flex; gap: 12px; margin-top: 16px;">
          <van-button style="flex: 1;" @click="handleHangCancel">取消</van-button>
          <van-button style="flex: 1;" type="primary" @click="handleHangConfirm" :loading="hangLoading">确定</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 结转弹窗 -->
    <van-popup v-model="overTurnModalVisible" position="bottom" :style="{ height: '40%' }">
      <div style="padding: 16px;">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 16px; text-align: center;">
          结转原因
        </div>
        <van-field
          v-model="overTurnReason"
          type="textarea"
          placeholder="请输入结转原因"
          rows="4"
          maxlength="200"
          show-word-limit
        />
        <div style="display: flex; gap: 12px; margin-top: 16px;">
          <van-button style="flex: 1;" @click="handleOverTurnCancel">取消</van-button>
          <van-button style="flex: 1;" type="primary" @click="handleOverTurnConfirm" :loading="overTurnLoading">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  data() {
    return {
      active: 0,
      userCode: localStorage.getItem("userCode"),

      // 列表数据
      planList: [],

      // 加载状态
      loading: false,

      show: false,

      // 工作日选择
      showWorkDayPicker: false,
      workDay: "",
      workDayDate: new Date(),

      showbookNamePicker: false,
      bookName: "",
      bookNameColumns: [],

      showworkshopPicker: false,
      workshop: "",
      workshopColumns: [],

      showjobCenterPicker: false,
      jobCenter: "",
      jobCenterColumns: [],

      // 挂起弹窗相关
      hangModalVisible: false,
      hangReason: '',
      hangLoading: false,
      currentHangRecord: null,

      // 结转弹窗相关
      overTurnModalVisible: false,
      overTurnReason: '',
      overTurnLoading: false,
      currentOverTurnRecord: null,
    };
  },
  created() {
    if (this.userCode == null || this.userCode == "") {
      Toast({
        message: "请先登录",
        position: "bottom",
        duration: 2000,
      });
      this.$router.push({
        name: "LoginIndex",
      });
    } else {
      this.initData();
    }
  },
  methods: {
    // 初始化数据
    initData() {
      // 设置工作日默认值为今天
      this.workDay = this.formatDate(new Date());

      this.$axios
        .get(`/jeecg-boot/app/warehouse/getFactoryInfo`)
        .then((res) => {
          if (res.data.code == 200) {
            console.log(res.data.result);
            res.data.result.forEach((item) => {
              this.bookNameColumns.push(item.name);
            });
          }
        });
      this.bookName = localStorage.getItem("feedingCheckBook");
      if (this.bookName) {
        this.bookNameConfirm(this.bookName);
      }
      this.loadData();
    },

    // 工作日确认
    workDayConfirm(value) {
      this.workDayDate = value;
      this.workDay = this.formatDate(value);
      this.showWorkDayPicker = false;
    },

    bookNameConfirm(value) {
      this.bookName = value;
      this.workshop = "";
      this.jobCenter = "";
      localStorage.setItem("feedingCheckBook", this.bookName);
      this.showbookNamePicker = false;
      //查找车间
      this.$axios
        .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
          params: { code: value },
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.workshopColumns = [];
            res.data.result.forEach((item) => {
              this.workshopColumns.push(item.name);
            });
          }
        });
    },

    workshopConfirm(value) {
      this.workshop = value;
      this.jobCenter = "";
      this.showworkshopPicker = false;
      // 查找工作中心
      this.$axios
        .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
          params: { book: this.bookName, workshop: value },
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.jobCenterColumns = [];
            res.data.result.forEach((item) => {
              this.jobCenterColumns.push(item.jobCenter);
            });
          }
        });
    },

    jobCenterConfirm(value) {
      this.jobCenter = value;
      this.showjobCenterPicker = false;
    },
    onClickRight() {
      this.show = true;
    },

    // 搜索
    search() {
      this.planList = [];
      this.show = false;
      this.loadData();
    },

    // 加载数据
    loadData() {
      if (this.loading) return;

      this.loading = true;

      const params = {
        workDay: this.workDay,
        book: this.bookName,
        workshop: this.workshop,
        jobCenter: this.jobCenter
      };

      this.$axios
        .get('/jeecg-boot/app/gcAps/getApsPlanList', { params })
        .then((res) => {
          if (res.data.code == 200) {
            const result = res.data.result;
            this.planList = result.records || result || [];

            console.log('加载数据成功:', result);
          } else {
            Toast({
              message: res.data.message || '加载失败',
              position: "bottom",
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          console.error('加载数据失败:', error);
          Toast({
            message: '网络错误，请重试',
            position: "bottom",
            duration: 2000,
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },


    onClickLeft() {
      this.$router.go(-1);
    },

    // 查看详情
    detail(item) {
      console.log('查看详情:', item);
      // 这里可以根据需要跳转到详情页面
      // this.$router.push({
      //   name: "PlanDetail",
      //   params: item,
      // });
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      const d = new Date(dateTime);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 接单操作
    handleAccept(record) {
      const userCode = localStorage.getItem('userCode');
      const userName = localStorage.getItem('userName');
      this.$axios
        .get(`/jeecg-boot/app/gcAps/acceptApsPlan?workDay=${record.workDay}&category=${record.category}&jobCenter=${record.jobCenter}&userCode=${userCode}&userName=${encodeURIComponent(userName)}`)
        .then((res) => {
          if (res.data.success) {
            Toast({
              message: res.data.message || '接单成功',
              position: "bottom",
              duration: 2000,
            });
            this.loadData();
          } else {
            Toast({
              message: res.data.message || '接单失败',
              position: "bottom",
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          console.error('接单失败:', error);
          Toast({
            message: '网络错误，请重试',
            position: "bottom",
            duration: 2000,
          });
        });
    },

    // 显示挂起弹窗
    showHangModal(record) {
      this.currentHangRecord = record;
      this.hangModalVisible = true;
      this.hangReason = '';
    },

    // 确认挂起
    handleHangConfirm() {
      if (!this.hangReason.trim()) {
        Toast({
          message: '请输入挂起原因',
          position: "bottom",
          duration: 2000,
        });
        return;
      }

      this.hangLoading = true;
      const record = this.currentHangRecord;
      const userCode = localStorage.getItem('userCode');
      const userName = localStorage.getItem('userName');
      this.$axios
        .get(`/jeecg-boot/app/gcAps/hangApsPlan?id=${record.id}&reason=${encodeURIComponent(this.hangReason)}&userCode=${userCode}&userName=${encodeURIComponent(userName)}`)
        .then((res) => {
          this.hangLoading = false;
          if (res.data.success) {
            Toast({
              message: res.data.message || '挂起成功',
              position: "bottom",
              duration: 2000,
            });
            this.hangModalVisible = false;
            this.loadData();
          } else {
            Toast({
              message: res.data.message || '挂起失败',
              position: "bottom",
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          this.hangLoading = false;
          console.error('挂起失败:', error);
          Toast({
            message: '网络错误，请重试',
            position: "bottom",
            duration: 2000,
          });
        });
    },

    // 取消挂起
    handleHangCancel() {
      this.hangModalVisible = false;
      this.hangReason = '';
      this.currentHangRecord = null;
    },

    // 显示结转弹窗
    showOverTurnModal(record) {
      this.currentOverTurnRecord = record;
      this.overTurnModalVisible = true;
      this.overTurnReason = '';
    },

    // 确认结转
    handleOverTurnConfirm() {
      if (!this.overTurnReason.trim()) {
        Toast({
          message: '请输入结转原因',
          position: "bottom",
          duration: 2000,
        });
        return;
      }

      this.overTurnLoading = true;
      const record = this.currentOverTurnRecord;
      const userCode = localStorage.getItem('userCode');
      const userName = localStorage.getItem('userName');
      this.$axios
        .get(`/jeecg-boot/app/gcAps/carryOverApsPlan?id=${record.id}&reason=${encodeURIComponent(this.overTurnReason)}&userCode=${userCode}&userName=${encodeURIComponent(userName)}`)
        .then((res) => {
          this.overTurnLoading = false;
          if (res.data.success) {
            Toast({
              message: res.data.message || '结转成功',
              position: "bottom",
              duration: 2000,
            });
            this.overTurnModalVisible = false;
            this.loadData();
          } else {
            Toast({
              message: res.data.message || '结转失败',
              position: "bottom",
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          this.overTurnLoading = false;
          console.error('结转失败:', error);
          Toast({
            message: '网络错误，请重试',
            position: "bottom",
            duration: 2000,
          });
        });
    },

    // 取消结转
    handleOverTurnCancel() {
      this.overTurnModalVisible = false;
      this.overTurnReason = '';
      this.currentOverTurnRecord = null;
    },
  },
};
</script>

<style scoped>
</style>