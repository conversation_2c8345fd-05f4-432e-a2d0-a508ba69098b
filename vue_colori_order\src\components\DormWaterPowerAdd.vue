<template>
<div class="pageWPAdd">
    <van-nav-bar fixed
        title="水电表登记"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <van-form @submit="recordSubmit">
        <van-field readonly label="年月" name="年月" required :disabled="isModify"
            v-model="rForm.yearMonth"  placeholder="请选择年月" @focus="onShowPicker('showDatePicker')" />
        <van-field readonly  label="区域" name="区域" required :disabled="isModify"
            v-model="selectedArea.label" placeholder="请选择区域" @focus="onShowPicker('showAreaPicker')" />
        <van-field readonly  label="楼栋" name="楼栋" required :disabled="isModify"
            v-model="selectedBuilding.label" placeholder="请选择楼栋" @focus="onShowPicker('showBuildingPicker')" />
        <van-field readonly  label="房间" name="房间" required :disabled="isModify"
            v-model="selectedRoom.label" placeholder="请选择房间" @focus="onShowPicker('showRoomPicker')" />
        <van-field readonly  label="类型" name="类型" required :disabled="isModify"
            v-model="selectedType.label" placeholder="请选择类型" @focus="onShowPicker('showTypePicker')" />
        <van-field v-model="rForm.waterScale" type="digit" label="水表刻度" name="水表刻度" required />
        <van-field v-model="rForm.powerScale" type="digit" label="电表刻度" name="电表刻度" required />
        <van-field v-model="rForm.newWaterScale" type="digit" label="新水表刻度" name="新水表刻度" v-if="selectedType.value=='3'" />
        <van-field v-model="rForm.newPowerScale" type="digit" label="新电表刻度" name="新电表刻度" v-if="selectedType.value=='3'" />
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
        </div>
    </van-form>

<!-- 类型picker -->
<van-popup v-model="showTypePicker" position="bottom" :style="{ height: '36%' }">
    <van-picker show-toolbar title="类型"
        :columns="typeOptions.map(item=>item.label)"
        @confirm="onTypeConfirm"
        @cancel="onTypeCancel"
        />
</van-popup>
<!-- 年月Picker -->
<van-popup v-model="showDatePicker" position="bottom" :style="{ height: '36%' }">
    <van-picker show-toolbar title="年月"
        :columns="dateOptions"
        @confirm="onDateConfirm"
        @cancel="onDateCancel"
        />
</van-popup>
<!-- 区域、楼栋、房间Picker -->
<van-popup v-model="showAreaPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择区域"
        :columns="areaOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'区域')"
        @cancel="(value,index)=>onPickerCancel(value,index,'区域')" />
</van-popup>
<van-popup v-model="showBuildingPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择楼栋"
        :columns="buildingOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'楼栋')"
        @cancel="(value,index)=>onPickerCancel(value,index,'楼栋')" />
</van-popup>
<van-popup v-model="showRoomPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择房间"
        :columns="roomOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'房间')"
        @cancel="(value,index)=>onPickerCancel(value,index,'房间')" />
</van-popup> 

</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            confirmLoading: false,
            isModify: false,
            localModal: {},
            rForm: { 
                yearMonth: '',
                waterScale: '',
                powerScale: '',
                newWaterScale: '',
                newPowerScale: '',
            },
            showDatePicker: false, 
            dateOptions: [
                { values: Array(300).fill(0).map((item,index) => `${index+2020}年`), defaultValue: `${new Date().getFullYear()}年` },
                { values: Array(12).fill(0).map((item,index) => `${index+1}月`), defaultValue: `${new Date().getMonth()+1}月` },
            ],
            selectedYearMonth: "",
            showAreaPicker: false,
            areaOptions: [],
            selectedArea: {},
            showBuildingPicker: false,
            buildingOptions: [],
            selectedBuilding: {},
            showRoomPicker: false,
            roomOptions: [],
            selectedRoom: {},
            showTypePicker: false,
            typeOptions: [
                { value: '1', label: '正常' },
                { value: '2', label: '表重置' },
                { value: '3', label: '表更换' },
            ],
            selectedType: {},
        }
    },
    methods:{
        recordSubmit(){
            const that=this
            that.confirmLoading=true
            const params=Object.assign(that.rForm, {
                type: that.selectedType.value,
                areaId: that.selectedArea.value,
                buildingId: that.selectedBuilding.value,
                roomId: that.selectedRoom.value,
            })
            if(!params.yearMonth||params.yearMonth==""){
                that.confirmLoading=false
                Toast.fail("年月必选")
                return false
            }
            if(!params.areaId||params.areaId==""){
                that.confirmLoading=false
                Toast.fail("宿舍必选")
                return false
            }
            if(!params.buildingId||params.buildingId==""){
                that.confirmLoading=false
                Toast.fail("楼栋必选")
                return false
            }
            if(!params.roomId||params.roomId==""){
                that.confirmLoading=false
                Toast.fail("房间必选")
                return false
            }
            if(!params.type||params.type==""){
                that.confirmLoading=false
                Toast.fail("类型必选")
                return false
            }
            if(!params.waterScale||params.waterScale==""){
                that.confirmLoading=false
                Toast.fail("水表刻度必填")
                return false
            }
            if(!params.powerScale||params.powerScale==""){
                that.confirmLoading=false
                Toast.fail("电表刻度必填")
                return false
            }
            let requestURL=""
            if(this.isModify){
                requestURL="/dormApi/dm/dmHydropowerUsage/app/edit"
            }else{
                requestURL="/dormApi/dm/dmHydropowerUsage/app/add"
            }
            console.log(requestURL,params)
            this.$axios.post(requestURL, params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.$router.push("/waterPowerManage")
                    }else{
                        Toast.fail(res.message)
                        console.log("ERROR", res)
                    }
                }else{
                    Toast.fail("发生错误")
                    console.log("error", rtn)
                }
            }).finally(()=>{
                setTimeout(() => {
                    this.confirmLoading=false
                }, 1500);
            })
        },
        onDateConfirm(pValue,pIndex){
            const yyyy=`${pIndex[0]+2020}`
            const MM=pIndex[1]+1
            this.rForm.yearMonth=`${yyyy}-${MM>9?MM:'0'+MM}`
            this.showDatePicker=false
        },
        onDateCancel(){
            this.showDatePicker=false
        },
        onTypeConfirm(pValue,pIndex){
            this.selectedType={
                label: pValue,
                value: pIndex+1+""
            }
            this.showTypePicker=false
        },
        onTypeCancel(){
            this.showTypePicker=false
        },
        onShowPicker(pType){
            this[pType]=true
        },
        onPickerConfirm(pValue,pIndex,pType){// 区域/楼栋/房间 选择确认
            if(pType=="区域"){
                // 清空楼栋&房间
                this.selectedBuilding=this.selectedRoom={}
                this.buildingOptions=this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedArea=this.areaOptions[pIndex]
                this.getBuildingList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                // 清空房间
                this.selectedRoom={}
                this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedBuilding=this.buildingOptions[pIndex]
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom=this.roomOptions[pIndex]
                this.showRoomPicker=false
            }
        },
        onPickerCancel(pValue,pIndex,pType){// 区域/楼栋/房间 选择取消
            if(pType=="区域"){
                this.selectedArea=this.selectedBuilding=this.selectedRoom={}
                this.getBuildingList()
                this.getRoomList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                this.selectedBuilding=this.selectedRoom={}
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom={}
                this.showRoomPicker=false
            }
        },
        onClickLeft(){// 返回
            this.$router.go(-1)
        },
        getAreaList(){// 获取宿舍区域
            let params = {userCodeRequest:localStorage.getItem('userCode')};
            this.$axios.get("/dormApi/dormitory/app/getAreaListAll",params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.areaOptions=res.result.map(item=>{
                            return {
                                label: item.areaName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getBuildingList(){// 获取宿舍楼栋
            const params={ areaId: this.selectedArea.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getBuildingListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.buildingOptions=res.result.map(item=>{
                            return {
                                label: item.buildingName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getRoomList(){// 获取宿舍楼栋
            const params={ buildingId: this.selectedBuilding.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getRoomInfoListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.roomOptions=res.result.map(item=>{
                            return {
                                label: item.roomNumber,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        typeFormat(pValue){
            switch(pValue){
                case "1": return "正常";
                case "2": return "表重置";
                case "3": return "表更换";
                default: return pValue;
            }
        }
    },
    created(){ 
        const mModal=this.$route.params.wpModal
        if(mModal && mModal.id){
            this.localModal=mModal
            this.isModify=true
            this.rForm={
                id: mModal.id,
                yearMonth: mModal.yearMonth,
                waterScale: mModal.waterScale,
                powerScale: mModal.powerScale,
                newWaterScale: mModal.newWaterScale,
                newPowerScale: mModal.newPowerScale,
            }
            this.selectedArea={
                label: mModal.areaId_dictText,
                value: mModal.areaId
            }
            this.selectedBuilding={
                label: mModal.buildingId_dictText,
                value: mModal.buildingId
            }
            this.selectedRoom={
                label: mModal.roomId_dictText,
                value: mModal.roomId
            }
            this.selectedType={
                label: this.typeFormat(mModal.type),
                value: mModal.type
            }
        }
        this.getAreaList()
        this.getBuildingList()
        this.getRoomList()
    },
    filters: { }
}
</script>
<style scoped>
.pageWPAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>