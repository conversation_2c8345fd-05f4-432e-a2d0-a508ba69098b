<template>
<div class="pageHygieneManage">
    <van-nav-bar fixed
        title="宿舍卫生"
        left-text="返回"
        left-arrow
        @click-left="onPageBack">
        <template #right v-if="queryParams.sign==''">
            <van-popover
                v-model="popoverShow"
                trigger="click"
                placement="bottom-end"
                :actions="popoverActions"
                @select="onPopoverSelect">
                <template #reference>
                    <van-button icon="more-o"></van-button>
                </template>
            </van-popover>
        </template>
    </van-nav-bar>
    <van-tabs v-model="queryParams.sign" @click="onTabClick">
        <van-tab name="" title="全部"></van-tab>
        <van-tab name="good" title="最优排名"></van-tab>
        <van-tab name="bad" title="最差排名"></van-tab>
    </van-tabs>
    <!-- 列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getSSWSList">
            <div v-for="(ws, index) in wsList" :key="index" style="margin-top: 15px;">
                <van-cell-group inset>
                    <van-cell class="vanCellClass" title="id" :value="ws.id" />
                    <van-cell class="vanCellClass" title="楼栋" :value="ws.buildingId_dictText" />
                    <van-cell class="vanCellClass" title="房间" :value="ws.roomNo_dictText" />
                    <van-cell class="vanCellClass" title="分数" :value="ws.score">
                        <template #default>
                            <van-tag plain type="success" v-if="ws.score*1>=80">优</van-tag>
                            <van-tag plain type="warning" v-else-if="ws.score*1<80 && ws.score*1>=60">良</van-tag>
                            <van-tag plain type="danger" v-else>差</van-tag>
                            {{ ws.score }}
                        </template>
                    </van-cell>
                    <van-cell class="vanCellClass" title="检查时间" :value="ws.checkTime" />
                    <van-cell class="vanCellClass" title="检查人" :value="ws.checkUser_dictText" />
                    <van-cell v-if="ws.groupNumber && ws.groupNumber!=''"
                        class="vanCellClass" title="组别" :value="`第${ws.groupNumber}组`" />
                    <van-cell class="vanCellClass" title="是否推送心声社区" :value="ws.unionCommunity=='1'?'是':'否'" />
                    <van-cell class="vanCellClass" title="照片" is-link @click="onPreviewImage(ws)">
                        <template #default>
                            <van-image :src="`/dormApi/${ws.pictureUrl}`" width="100" height="100" fit="cover"></van-image>
                        </template>
                    </van-cell>
                </van-cell-group>
            </div>
        </van-list>
    </van-pull-refresh>


<!-- 筛选条件 -->
<van-popup v-model="popFilterShow" position="top" :style="{ height: '38%' }" :close-on-click-overlay="false">
    <h2>筛选条件</h2>
    <van-cell-group>
        <van-field readonly  v-model="selectedArea.label" label="区域" placeholder="请选择区域" @focus="showAreaPicker=true;" />
        <van-field readonly  v-model="selectedBuilding.label" label="楼栋" placeholder="请选择楼栋" @focus="showBuildingPicker=true;" />
        <van-field readonly  v-model="selectedRoom.label" label="房间" placeholder="请选择房间" @focus="showRoomPicker=true;" />
        <van-field readonly v-model="queryParams.startTime" label="开始时间" placeholder="请输入开始时间" @focus="onSelectDate" />
        <van-field readonly v-model="queryParams.endTime" label="结束时间" placeholder="请输入结束时间" @focus="onSelectDate" />
    </van-cell-group>
    <div style="margin: 0 auto; padding-top: 10px;">
        <van-button round type="default" size="small" style="width: 30%;" @click="searchReset">重置</van-button>
        <van-button round type="primary" size="small" style="width: 30%;margin-left: 2rem;" @click="searchQuery">查询</van-button>
    </div>
</van-popup>
<!-- 日期 -->
<van-calendar v-model="showDate" type="range" @confirm="onDateConfirm" 
    :min-date="new Date('2023-01-01')" />

<!-- 区域、楼栋、房间Picker -->
<van-popup v-model="showAreaPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择区域"
        :columns="areaOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'区域')"
        @cancel="(value,index)=>onPickerCancel(value,index,'区域')" />
</van-popup>
<van-popup v-model="showBuildingPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择楼栋"
        :columns="buildingOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'楼栋')"
        @cancel="(value,index)=>onPickerCancel(value,index,'楼栋')" />
</van-popup>
<van-popup v-model="showRoomPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择房间"
        :columns="roomOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'房间')"
        @cancel="(value,index)=>onPickerCancel(value,index,'房间')" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
import { MessageBox } from 'mint-ui';
export default {
    data(){
        return{
            queryParams:{
                sign: ''
            },
            iPage: {
                current: 1,
                size: 10,
                total: 1,
            },
            refreshing: false, // 是否下拉刷新
            wsList: [],   // 宿舍卫生列表
            loading: false, // 是否加载
            finished: false, // 是否完成  停止触底加载
            popoverShow: false,
            popoverActions: [
                { text: '筛选', icon: 'filter-o' },
                { text: '新增', icon: 'add-o' },
                { text: '发布', icon: 'bullhorn-o' },
            ],
            popFilterShow: false, //是否显示筛选
            showDate: false,  // 是否显示日期选择
            showAreaPicker: false,
            areaOptions: [],
            selectedArea: {},
            showBuildingPicker: false,
            buildingOptions: [],
            selectedBuilding: {},
            showRoomPicker: false,
            roomOptions: [],
            selectedRoom: {},
        }
    },
    methods:{
        onPickerConfirm(pValue,pIndex,pType){
            if(pType=="区域"){
                // 清空楼栋&房间
                this.selectedBuilding=this.selectedRoom={}
                this.buildingOptions=this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedArea=this.areaOptions[pIndex]
                this.getBuildingList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                // 清空房间
                this.selectedRoom={}
                this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedBuilding=this.buildingOptions[pIndex]
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom=this.roomOptions[pIndex]
                this.showRoomPicker=false
            }
        },
        onPickerCancel(pValue,pIndex,pType){
            if(pType=="区域"){
                this.selectedArea=this.selectedBuilding=this.selectedRoom={}
                this.getBuildingList()
                this.getRoomList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                this.selectedBuilding=this.selectedRoom={}
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom={}
                this.showRoomPicker=false
            }
        },
        onDateConfirm(pDate){// 选择日期确认
            console.log("onDateConfirm",pDate)
            const [start, end] = pDate;
            this.queryParams.startTime=this.formatDate(start)
            this.queryParams.endTime=this.formatDate(end)
            console.log(this.queryParams)
            this.showDate=false
        },
        onSelectDate(){// 显示选择日期
            this.showDate=true
        },
        searchQuery(){// 筛选查询
            this.wsList=[]
            this.iPage.current=1
            this.popFilterShow=false
            this.onTabClick('', '全部')
            this.onRefresh()
        },
        searchReset(){// 筛选重置
            this.wsList=[]
            this.iPage.current=1
            this.queryParams={}
            this.popFilterShow=false
            this.onPickerCancel('','','区域')
            this.onTabClick('', '全部')
            this.onRefresh()
        },
        onPopoverSelect(pAction, pIndex){//下拉菜单选择
            if(pIndex===0){
                this.popFilterShow=true
            }else if(pIndex===1){
                this.$router.push({ path: "/hygieneAdd"})
            }else if(pIndex===2){
                // this.$router.push({ path: "/hygieneBulletin"})
                const self=this
                MessageBox.confirm('是否确认发布?').then(action => {
                    if(action=='confirm'){
                        self.$axios.get("/dormApi/dm/dmDailyCsInfo/app/unionCommunityChange",{params:{userCodeRequest:localStorage.getItem('userCode')}}).then(rtn=>{
                            if(rtn.status===200){
                                const res=rtn.data
                                if(res.success){
                                    Toast(res.message)
                                }else{
                                    Toast(res.message)
                                    console.log("ERROR", res)
                                }
                            }else{
                                Toast("发生错误")
                                console.error("ERROR", rtn)
                            }
                        }).catch(err=>{
                            Toast("发生错误")
                            console.error(err)
                        })
                    }
                });
            }
        },
        onRefresh(){// 列表刷新
            // 处于刷新状态
            this.refreshing=true;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            // 加载数据
            this.getSSWSList();
        },
        getSSWSList(){// 获取宿舍卫生列表
            if (this.refreshing) {
                this.wsList = [];
                this.iPage.current = 1;
                this.refreshing = false;
                this.finished=false
            }
            let rParams=JSON.parse(JSON.stringify(this.queryParams))
            Object.assign(rParams,{
                areaId: this.selectedArea.value,
                buildingId: this.selectedBuilding.value,
                roomNo: this.selectedRoom.value,
                pageNo: this.iPage.current,
                pageSize: this.iPage.size
            })
            console.log("/dormApi/dm/dmDailyCsInfo/app/getHealthRecordList",rParams)
            this.$axios.get("/dormApi/dm/dmDailyCsInfo/app/getHealthRecordList", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.wsList.push(...res.result.records);
                        this.iPage={
                            current: res.result.current,
                            size: res.result.size,
                            total: res.result.pages,
                        }
                        // 关闭loading状态
                        this.loading = false;
                        // 判断是否到底了
                        if(this.iPage.current*1>=this.iPage.total*1){
                            this.finished = true;
                        }else{
                            this.iPage.current++;
                        }
                    }else{
                        Toast({
                            message: res.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: '发生错误',
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onTabClick(pName,pTitle){ //Tabs点击
            this.iPage.current=1
            this.wsList=[]
            this.onRefresh()
        },
        onPageBack(){ // 返回
            this.$router.go(-1)
        },
        formatDate (secs) {// date format
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        },
        debounce(fn, delay = 100) {// debounce
            let timer = null
            return function () {
                let args = arguments
                if (timer) {
                    clearTimeout(timer)
                }
                timer = setTimeout(() => {
                    fn.apply(this, args)
                }, delay)
            }
        },
        getAreaList(){// 获取宿舍区域
            let params = {userCodeRequest:localStorage.getItem('userCode')};
            this.$axios.get("/dormApi/dormitory/app/getAreaListAll",params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.areaOptions=res.result.map(item=>{
                            return {
                                label: item.areaName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getBuildingList(){// 获取宿舍楼栋
            const params={ areaId: this.selectedArea.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getBuildingListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.buildingOptions=res.result.map(item=>{
                            return {
                                label: item.buildingName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getRoomList(){// 获取宿舍楼栋
            const params={ buildingId: this.selectedBuilding.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getRoomInfoListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.roomOptions=res.result.map(item=>{
                            return {
                                label: item.roomNumber,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        onPreviewImage(pRow){
            ImagePreview({
                images: [`/dormApi/${pRow.pictureUrl}`],
                closeable: true
            })
        }
    },
    created(){
        this.getAreaList()
        this.getBuildingList()
        this.getRoomList()
    },
    filters: {
        fStatus(status){
            switch(status){
                case "0": return "正常";
                case "1": return "失效";
                default: return status;
            }
        },
    }
}
</script>
<style scoped>
.pageHygieneManage{
    background: #eee;
    padding-top: 50px;
}
.vanCellClass{
    text-align: left;
}
</style>