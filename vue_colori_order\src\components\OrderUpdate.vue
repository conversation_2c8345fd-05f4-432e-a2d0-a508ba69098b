<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_order_title" @click="hiddenToUpdateOrder">订单修改</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div>
        </div>
        <van-cell title="员工编号" :value="worker" @click="sendWorker" style="margin:5%;width:90%;border-radius:10px;"/>
        <!-- <div class="plotTime" @click="selectData">{{newSelectedValue}}</div> -->
        <van-cell title="生产日期" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />
        


        <div>
            <div class="orderType" v-if="newOrderList.length>0">新任务</div>
            <div v-for="(item,index) in newOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="orderType" v-if="workOrderList.length>0">处理中</div>
            <div v-for="(item,index) in workOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>


        <div>
            <div class="orderType" v-if="errorOrderList.length>0">异常调整</div>
            <div v-for="(item,index) in errorOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>


        <div>
            <div class="orderType" v-if="finishOrderList.length>0">已完结</div>
            <div v-for="(item,index) in finishOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>

        <update-modal ref="modalForms" @ok="modalFormOk"></update-modal>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import UpdateModal from './list/UpdateModal.vue';
export default {
  components: { UpdateModal },
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal:'',
            minDate:'',
            maxDate:'',
            date:'',
            category:'',
            userName:'',
            userCode:'',
            popupVisible:false,
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            questionType:'',
            c_show:false,
            questionTypeVal:'',
            worker:'',
            popupVisible:false,
            leadAppInfo:[],
            popupSlots:[
                {
                    values:[
                        '全部','白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 100 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.userCode=localStorage.getItem('userCode')
        this.worker=this.userCode;
        this.userName=localStorage.getItem('userName')
        this.getOrderInfo()
    },
    methods:{
        getOrderInfo(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo',{params:{userCode:self.worker,workDay:self.date,preCategory:self.questionType}}).then(res=>{
                console.log(res)
                if(res.data.code==200){
                    console.log(res.data.peopleInfo)
                    self.peopleInfo=res.data.result.peopleInfo
                    self.newOrderList=res.data.result.newOrderList
                    self.workOrderList=res.data.result.workOrderList
                    self.errorOrderList=res.data.result.errorOrderList
                    self.finishOrderList=res.data.result.finishOrderList
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendWorker(){
            let self=this
            MessageBox.prompt('请输入员工编号').then(({ value, action }) => {
                if(action=="confirm"){
                    self.worker=value
                    this.getOrderInfo()
                }
            });
        },
        startToNext(item){
            let self=this;
            let userCode=self.worker
            let params={
                id:item.id,
                workDay:self.date,
                userCode:userCode,
                preCategory:self.questionType
            }
            localStorage.setItem('params',JSON.stringify(params));
            this.$router.push({name:"OrderStart"})
        },
        hiddenToUpdateOrder(){
            this.$router.push({name:"OrderUpdate"})
        },
        modalFormOk(){
            this.getLeadAppInfo()
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getOrderInfo()
        },
        updateCount(item){
            let self=this;
            self.$refs.modalForms.edit(item);
            self.$refs.modalForms.title="产量更改";
            self.$refs.modalForms.disableSubmit = true;
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType(){
            this.popupVisible = true;
        },
        dateConfirm(value){
            this.newSelectedValue=this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getOrderInfo()
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getOrderInfo()
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    padding: 5%;
    font-size: 1rem;
    width: 50%;
    background: #fff;
    margin: 0 auto;
    margin-top: 5%;
    border-radius: 10px;
}
.report_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.report_line{
    background: #cfcfcf;
    height: 1px;
}
.report_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.report_item{
    width: 90%;
    border-radius: 10px;
    margin: 0 auto;
    margin-bottom: 20px;
    background: #fff;
}
.bottom_button{
    padding: 5%;
    color: #ff0000;
    font-size: 1rem;
    font-weight: 600;
}
.bottom_button2{
    padding: 5%;
    color: chartreuse;
    font-size: 1rem;
    font-weight: 600;
}
.ycl-style{
    color: crimson;
}
</style>