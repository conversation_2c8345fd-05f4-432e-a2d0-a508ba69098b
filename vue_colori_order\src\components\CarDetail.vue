<template>
    <div style="background:#f3f4f6;">
        <img :src="car_top" width="100%"/>
        <div class="top_title">派车事由</div>
        <div class="top_hint">在这里您可以查看车辆调度情况</div>
        <div class="line"></div>
        <div style="text-align:left;margin-left:5%">
            <span class="car_title">车牌号：</span><span class="car_card">{{carNum}}</span>
            <span class="car_d_status">{{status}}</span>
        </div>
        <div class="line"></div>
        <div class="first_item">
            <span>用车联系人：</span><span>{{contractName}}</span>
        </div>
        <div class="other_item">
            <span>联系电话：</span><span>{{contractMobi}}</span>
        </div>
        <div class="other_item">
            <span>乘坐人数：</span><span>{{takeNumber}}</span>
        </div>
        <div class="other_item">
            <span>用车单位：</span><span>{{userCompany}}</span>
        </div>
        <div class="other_item">
            <span>用车事由：</span><span>{{reason}}</span>
        </div>
        <div class="other_item">
            <span>用车开始时间：</span><span>{{startTime}}</span>
        </div>
        <div class="other_item">
            <span>用车结束时间：</span><span>{{endTime}}</span>
        </div>
        <div class="other_item">
            <span>行程：</span><span>{{trip}}</span>
        </div>
        <div class="other_item">
            <span>调度要求：</span><span>{{demand}}</span>
        </div>
        <div class="other_item">
            <span>出发地点：</span><span>{{startPlace}}</span>
        </div>
        <div class="other_item">
            <span>到达地点：</span><span>{{endPlace}}</span>
        </div>
        <div class="other_item">
            <span>驾驶员：</span><span>{{actName}}</span>
        </div>
        <mt-checklist
            style="margin-top:2rem;margin-bottom:1.5rem"
            v-model="abvoad"
            @change="accept"
            :options="['允许异地归队']">
        </mt-checklist>

        <mt-button type="default" class="back_button" @click="back">返回</mt-button>
        <mt-button type="primary" class="back_button" @click="back">关闭</mt-button>
    </div>
</template>
<script>
import { DatetimePicker } from 'mint-ui';
export default {
    data(){
        return{
            driverName:'',
            actName:'',
            contractMobi:'',
            startPlace:'',
            userCompany:'',
            endPlace:'',
            reason:'',
            userName:'',
            carNum:'',
            status:'',
            spNo:'',
            abvoad:'',
            takeNumber:'',
            startTime:'',
            endTime:'',
            demand:'',
            trip:'',
            car_top:require('../../static/images/car_top.png'),
        }
    },
    components:{
        DatetimePicker
    },
    created(){
        this.carNum = this.$route.query.carNum;
        this.status = this.$route.query.status;
        this.userCompany=this.$route.query.userCompany;
        this.contractMobi=this.$route.query.contractMobi;
        this.userName=this.$route.query.userName;
        this.contractName=this.$route.query.contractName;
        this.startPlace=this.$route.query.startPlace;
        this.endPlace=this.$route.query.endPlace;
        this.driverName=this.$route.query.driverName;
        this.actName=this.$route.query.actName;
        this.takeNumber=this.$route.query.takeNumber;
        this.startTime=this.$route.query.startTime;
        this.endTime=this.$route.query.endTime;
        this.spNo=this.$route.query.spNo;
        this.abvoad=this.$route.query.abvoad;
        this.reason=this.$route.query.reason;
        this.demand=this.$route.query.demand;
        this.trip=this.$route.query.trip;

        if(this.abvoad==='false'){
            console.log("run here 1")
            this.abvoad=false
        }else{
            console.log("run here 2")
            this.abvoad=true
        }

        console.log(this.abvoad)
    },
    methods: {
        back(){
            this.$router.go(-1);//返回上一层
        },
        accept(){
            const self=this;
            let params={
                id:self.spNo,
                selected:self.abvoad
            }
            self.$axios.post('/jeecg-boot/car/changeAbvoad',params).then(res=>{
                if(res.code=200){
                    
                }
            })
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
    margin-bottom: 3rem;
}
.car_title{
    font-size: 1.2rem;
    color: #455a64;
    font-weight: 500;
}
.car_card{
    font-size: 1.5rem;
    margin-left: 5%;
    color: #000;
    font-weight: 550;
}
.line{
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    background: #bfbfbf;
    height: 1px;
    width: 100%;
}
.first_item{
    margin-top: 2rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.other_item{
    margin-top: 0.8rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.mint-cell{
    background-color: transparent;
}
.mint-cell-wrapper{
    font-size: 1.2rem;
}
.back_button{
    width: 80%;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    height: 4rem;
    border-radius: 10px;
    border: 1px solid #bfbfbf;
}
.car_d_status{
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
    text-align: center;
    float: right;
    margin-right: 10%;
}
</style>