<template>
    <div class="order">

        <div class="top-title">
            <div class="title_order_user">
                <span>{{title}}</span>
            </div>
            <div class="top-message">
                <img :src="message" width="70%" />
            </div>
        </div>

        <div v-for="(item,index) in userInfo" :key="index">
            <div class="userItem">
                <span class="userName-st">{{item.userCode}}_{{item.userName}}</span>
                <div class="userName-add" @click="delPerson(item.id)">
                    <img :src="sub" width="25%"/>
                </div>
            </div>
        </div>

        <div style="height:5rem;"></div>

        
        <div class="selectBottom">
            <div class="user_List">
                <span style="margin-left:5%;font-size: 1.2rem;">
                    当前{{title}}人数：{{userInfo.length}}
                </span>
            </div>
            <div class="addBtn" @click="addUserToServe">{{$t('add')}}</div>
        </div> 

        
    </div>
</template>
<script>
import { Notify,Toast } from 'vant';
import { MessageBox } from 'mint-ui';
export default {
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            search:require('../../static/images/search.png'),
            sub:require('../../static/images/userSub.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            title:'值班池',
            value:false,
            type:'',
            personList:[],
            addUserInfo:[],
            searchKey:'',
            preCategory:'',
            userInfo:[],
            item:[],
            questionType:'',
            questionTypeVal:'',
            status:'',
            lpId:'',
            createTime:'',
            popupVisible:false,
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let self=this;
        self.type=localStorage.getItem('type')
        if(self.type=='1'){
            self.title='值班池'
        }else{
            self.title='延班池'
        }
        self.userCode=localStorage.getItem('userCode')
        self.getPool();
    },
    methods:{
        getPool(){
            let self=this
            let workDay=this.formatDate(new Date())
            let type=localStorage.getItem('type')
            self.$axios.get('/jeecg-boot/app/gcAttendance/getSpecialInfo',{params:{type:type,userCode:self.userCode,workDay:workDay}}).then(res=>{
                if(res.data.code==200){
                    self.userInfo=res.data.result
                }else{
                    Notify({ type: "warning", message: res.data.message });
                }
            })
        },
        delPerson(id){
            let self=this;
            MessageBox.confirm('是否确认将其移除').then(action => {
                if(action=="confirm"){
                    self.delSpecialInfo(id)
                }
            });
        },
        delSpecialInfo(id){
            let self=this
            self.$axios.get('/jeecg-boot/app/gcAttendance/delSpecialInfo',{params:{id:id,userCode:localStorage.getItem('userCode')}}).then(res=>{
                if(res.data.code==200){
                    Notify({ type: "success", message: res.data.message });
                    self.getPool()
                }else{
                    Notify({ type: "warning", message: res.data.message });
                }
            })
        },
        addUserToServe(){
            let self=this;
            self.$router.push({name:"PoolUser",params:{type:self.item.type}})
        },
        searchUser(){
            let self=this;
            let url="";
            if(self.status=='0'){
                //未开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreeWorker";
            }else{
                //开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreePeopleInfo";
            }
            const toast = Toast.loading({
                duration: 0, // 持续展示 toast
                forbidClick: true,
                message: "正在查询员工数据..."
            });

            console.log(self.preCategory)

            self.$axios.get(url,{params:{secinfo:self.searchKey,createTime:self.createTime,lpId:self.lpId,preCategory:self.preCategory}}).then(res=>{
                toast.clear()
                if(res.data.code==200){
                    Notify({ type: "success", message: res.data.message });
                    self.userInfo=res.data.result
                    console.log(self.userInfo)
                }else{
                    Notify({ type: "warning", message: res.data.message });
                }
            })

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year +'-'+ month +'-'+ date
        },
        openQuestionType(){
            this.popupVisible = true;
        },
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        addPerson(item){
            let self=this;
            item.lpId=self.lpId;
            // item.pid=item.id;
            console.log(item)
            this.addUserInfo.push(item);
            this.userInfo.splice(this.userInfo.indexOf(item),1);
        },
        selectData(){
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.title_order_user{
    font-size: 1.6rem;
    font-weight: 600;
    float: left;
    width: 60%;
    text-align:left;
    padding:8%;
}
.top-message{
    float: left;
    display:flex;
    align-items:center;
    width:20%;
    margin:0 auto;
}
.searchField{
    width: 70%;
    margin-left: 8%;
    margin-top:5%;
    float: left;
}
.searchBtn{
    margin-top:5%;
    float: left;
}
.selectBottom{
    height: 4rem;
    width: 100%;
    position:fixed;
    bottom:0;
    background-color: #fff;
}
.top-title{
    height:fit-content;
    width:100%;
    display:flex;
}
.userItem{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 2rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 5%;
    font-size: 1.2rem;
}
.userName-st{
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}
.userName-add{
    float: left;
    width: 28%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
.user_List{
    float: left;
    width: 60%;
    height: 100%;
    display: flex;
    align-items: center;
}
.addBtn{
    float: left;
    width: 40%;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    justify-content: center;
    height: 100%;
    background: #5032f2;
    color: #fff;
}
</style>