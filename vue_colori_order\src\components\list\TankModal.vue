<template>
  <a-modal :title="title" :width="500" :visible="visible" okText="确定" cancelText="取消" :confirmLoading="confirmLoading"
    @ok="handleOk" @cancel="handleCancel">



    <div class="shopContent">
      <a-input-search v-model="searchValue" placeholder="请输入" @search="addSearch" />

      <ul>
        <li v-for="(item, i) in goodsInfo" :key="i">
          <div class="shopmain">
            <van-checkbox v-model="item.checked" @click="signchecked(item, i)"></van-checkbox>
            <div class="shops" @click="todetail(item, i)">
              <div class="shopsright">
                <h4>{{ item.tankNo }}</h4>
                <div class="shoprightbot">
                  <span>{{ item.tankName }}</span>
                  <div class="shopradd">
                    <span>{{ item.realVolume }}KG</span>
                  </div>
                </div>
                <h4>{{ item.realProductNo }}</h4>
                <h4>{{ item.realProductName }}</h4>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="!goodsInfo.length" class="shopping">
      <div><img src="../../../static/images/nothing.png" alt=""></div>
      <p>暂无数据</p>
    </div>


  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import Vue from 'vue'
import { DatetimePicker, Toast, MessageBox } from 'mint-ui';
import { Checkbox, SubmitBar, Card, Field, Cell, Dialog } from 'vant';
export default {
  name: "TankModal",
  components: {
    [SubmitBar.name]: SubmitBar,
    [Checkbox.name]: Checkbox,
    [Card.name]: Card,
    [Field.name]: Field,
    [Cell.name]: Cell,
  },
  data() {
    return {
      title: "操作",
      visible: false,
      ischecked: false,
      orderMainModel: {
        jeecgOrderCustomerList: [{}],
        jeecgOrderTicketList: [{}]
      },
      rl: '',
      workshop: '',
      code:'',
      department: '',
      selectedItems: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      moIds: [],
      expandedRowKeys: [],
      id: ' ',
      item: [],
      depart: [],
      goodsInfo: [],
      customerId: '',
      workshop: '',
      item: [],
      searchValue: '',
      type: '',
    }
  },
  methods: {
    addSearch() {
      let self = this;
      self.$axios
        .get('/jeecg-boot/app/gcWorkshop/getTankInfo', {
          params: {
            tank: self.searchValue,
            customerId: self.customerId,
            workshop: self.workshop,
            code:this.code,
            type: '1',
          }
        })
        .then(res => {
          if (res.data.code = 200) {
            if (self.item.length > 0 && res.data.result.length > 0) {
              for (var i = 0; i < res.data.result.length; i++) {
                var temp = 1;
                for (var j = 0; j < self.item.length; j++) {
                  if (res.data.result[i].tankNo == self.item[j].tankNo) {
                    temp = 2
                    break;
                  }
                }
                if (temp == 1) {
                  self.goodsInfo.push(res.data.result[i]);
                }
              }
            } else {
              self.goodsInfo = res.data.result;
            }
          } else {
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        });
    },
    todetail(item, i) {
      let self = this;
      console.log("rl:" + this.rl)
      if (item.checked) {
        item.checked = false
        this.rl = parseInt(this.rl) + parseInt(item.output)
        item.output = 0
      } else {
        item.checked = true
        if (parseInt(item.realVolume) >= parseInt(this.rl)) {
          item.output = parseInt(this.rl)
          this.rl = 0
        } else {
          item.output = parseInt(item.realVolume)
          this.rl = parseInt(this.rl) - parseInt(item.realVolume)
        }
      }

      console.log(JSON.stringify(item))

      Vue.set(this.goodsInfo, i, item)
    },
    edit(customer, workshop, rl, type, item, code) {
      this.customerId = customer
      this.workshop = workshop
      this.item = item
      this.code = code
      this.type = type
      let self = this;
      self.visible = true;

      this.rl = rl
      this.selectedItems = item



      //坦克type=1
      //青罐type=2
      self.$axios.get('/jeecg-boot/app/gcWorkshop/getTankInfo', { params: { customerId: customer, workshop: workshop, type: type, code: code } }).then(res => {
        if (res.data.code = 200) {

          if (item.length > 0 && res.data.result.length > 0) {
            for (var i = 0; i < res.data.result.length; i++) {
              var temp = 1;
              for (var j = 0; j < item.length; j++) {
                if (res.data.result[i].tankNo == item[j].tankNo) {
                  temp = 2
                  break;
                }
              }
              if (temp == 1) {
                self.goodsInfo.push(res.data.result[i]);
              }
            }
          } else {
            self.goodsInfo = res.data.result;
          }




        } else {
          Toast({
            message: res.data.message,
            position: 'bottom',
            duration: 2000
          });
        }
      })
    },
    close() {
      this.$emit('close');
      this.visible = false;
      this.goodsInfo = []
      this.flag = 0
      this.saveCode = ''
      this.saveStation = ''
    },
    handleOk() {
      let self = this
      let selectInfo = []
      for (var i = 0; i < self.goodsInfo.length; i++) {
        if (self.goodsInfo[i].checked) {
          selectInfo.push(self.goodsInfo[i]);
        }
      }
      if (selectInfo.length <= 0) {
        Toast({
          message: "请选择一条记录",
          position: 'bottom',
          duration: 2000
        });
        return;
      } else {
        self.visible = false
        this.close()
        self.$emit('ok', selectInfo, this.type);
        // 锁定tank
        let ids = ''
        selectInfo.forEach(item => {
          ids += item.id + ','
        })
        console.log('ids',ids);
        this.$axios.get(`/jeecg-boot/app/gcWorkshop/addTank?ids=${ids}`).then(res => {
        })
      }

    },
    handleCancel() {
      this.close()
    },
    signchecked(item, i) {
      let self = this;

      if (item.checked) {
        if (parseInt(item.realVolume) >= parseInt(this.rl)) {
          item.output = this.rl
          this.rl = 0
        } else {
          item.output = item.realVolume
          this.rl = parseInt(this.rl) - parseInt(item.realVolume)
        }
      } else {
        this.rl = parseInt(this.rl) + parseInt(item.output)
        item.output = 0
      }

      Vue.set(this.goodsInfo, i, item)
      // Vue.set(this.goodsInfo, i, val)
    },

    modalFormOk() {

    },
  }
}
</script>

<style scoped>
.ant-btn {
  padding: 0 10px;
  margin-left: 3px;
}

.ant-form-item-control {
  line-height: 0px;
}

/** 主表单行间距 */
.ant-form .ant-form-item {
  margin-bottom: 10px;
}

/** Tab页面行间距 */
.ant-tabs-content .ant-form-item {
  margin-bottom: 0px;
}

.fontColor {
  color: black;
}
</style>
<style lang="less" scoped>
.van-submit-bar {
  bottom: 49px;
  padding-left: 20px;

}

.shopContent {
  margin-top: 10px;
  padding-bottom: 20px;
  height: 400px;
  overflow: auto;
}

.shopping {
  text-align: center;
  padding-top: 99px;

  img {
    width: 96px;
    height: 96px;
    margin-bottom: 25px;
  }
}

li {
  padding: 0 15px;
  background: #ffffff;
  margin-bottom: 10px;
  position: relative;
  height: 103px;

  .shopmain {
    display: flex;
    padding: 10px 8px 10px 10px;
    position: relative;

    .shops {
      display: flex;
      margin-left: 5%;

      .shopImg {
        width: 103px;
        height: 83px;
        margin: 0 7px 0 11px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .shopsright {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        h4 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-align: left;
        }

        .shoprightbot {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 260px;

          span {
            font-size: 17px;
            color: #F74022;
          }
        }
      }
    }

    .van-checkbox__icon--checked .van-icon {
      background: red !important;
    }
  }

  button {
    width: 24px;
    height: 26px;
    font-size: 20px;
    background: #F74022;
    color: #ffffff;
    border: none;
  }

  input {
    width: 48px;
  }
}

.shopradd {
  width: 98px;
  display: flex;

  .van-field__control {
    text-align: center !important;
  }
}

.van-cell {
  padding: 0;
  line-height: 26px
}

.van-field__control {
  height: 26px;
}

/deep/.ant-modal-body {
  padding: 0px;
}
</style>