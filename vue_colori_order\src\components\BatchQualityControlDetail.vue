<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="胶体理化指标自检填写" left-arrow @click-left="onClickLeft" right-text="复核" @click-right="check" />
        </van-sticky>
        <div v-for="(item, index) in dataArr" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <van-cell :title="'项目  ：  '+item.kzzb" @click="add(item)">
                <template #right-icon>
                    <van-icon name="plus" />
                </template>
            </van-cell>
            <div v-if="item.type==1">
                <van-cell :title="'标准值   ：  '+item.zbz" />
                <van-cell>
                    <template #title>
                        <span class="custom-title">检测原始值</span>
                    </template>
                    <template #default>
                        <div class="custom-title">
                            <van-radio-group v-model="item.result1" direction="horizontal"
                                @change="(e)=>radioChage(e,item)" :disabled="item.status==2">
                                <van-radio name="符合" value="符合">符合</van-radio>
                                <van-radio name="不符合" value="不符合">不符合</van-radio>
                            </van-radio-group>
                        </div>
                    </template>
                </van-cell>

            </div>
            <div v-if="item.type==2">
                <van-cell :title="'标准值  ：  '+item.zbz" @click="addzbz(item)">
                    <template #right-icon>
                        <van-icon name="plus" />
                    </template>
                </van-cell>
                <div v-for="x,xi in item.zbzList">
                    <van-row v-if="xi==0">
                        <van-col span="22">
                            <van-field label-width="8em" v-model="item.result1" :label="`检测原始值(上层)`" placeholder="请输入"
                                @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        </van-col>
                        <van-col span="2">
                            <div style="width: 100%;height: 100%;">
                                <van-icon name="minus" @click="delzbzList(item,xi)" />
                            </div>
                        </van-col>
                    </van-row>
                    <van-row v-if="xi==1">
                        <van-col span="22">
                            <van-field label-width="8em" v-model="item.result3" :label="`检测原始值(下层)`" placeholder="请输入"
                                @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        </van-col>
                        <van-col span="2">
                            <div style="width: 100%;height: 100%;">
                                <van-icon name="minus" @click="delzbzList(item,xi)" />
                            </div>
                        </van-col>
                    </van-row>
                    <van-row v-if="xi==2">
                        <van-col span="22">
                            <van-field label-width="8em" v-model="item.result2" :label="`检测原始值(中层)`" placeholder="请输入"
                                @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        </van-col>
                        <van-col span="2">
                            <div style="width: 100%;height: 100%;">
                                <van-icon name="minus" @click="delzbzList(item,xi)" />
                            </div>
                        </van-col>
                    </van-row>
                </div>
            </div>
            <div v-for="(v,i) in item.arr">
                <van-row v-if="i==0">
                    <van-col span="22">
                        <van-field label-width="8em" v-model="item.firstTimes" :label="`第${i+1}次`" placeholder="请输入"
                            @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        <van-field label-width="8em" v-model="item.firstReason" :label="`第${i+1}次调整原因`"
                            placeholder="请输入" @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                    </van-col>
                    <van-col span="2">
                        <div style="width: 100%;height: 100%;">
                            <van-icon name="minus" @click="del(item,i)" />
                        </div>
                    </van-col>
                </van-row>
                <van-row v-if="i==1">
                    <van-col span="22">
                        <van-field label-width="8em" v-model="item.secondTimes" :label="`第${i+1}次`" placeholder="请输入"
                            @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        <van-field label-width="8em" v-model="item.secondReason" :label="`第${i+1}次调整原因`"
                            placeholder="请输入" @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                    </van-col>
                    <van-col span="2">
                        <div style="width: 100%;height: 100%;">
                            <van-icon name="minus" @click="del(item,i)" />
                        </div>
                    </van-col>
                </van-row>
                <van-row v-if="i==2">
                    <van-col span="22">
                        <van-field label-width="8em" v-model="item.thirdTimes" :label="`第${i+1}次`" placeholder="请输入"
                            @blur="(e)=>fieldBlur(e,item)" :readonly="item.status==2" />
                        <van-field label-width="8em" v-model="item.thirdReason" :label="`第${i+1}次调整原因`"
                            placeholder="请输入" @blur="(e)=>fieldBlur(e,item)" :disabled="item.status==2" />
                    </van-col>
                    <van-col span="2">
                        <div style="width: 100%;height: 100%;">
                            <van-icon name="minus" @click="del(item,i)" />
                        </div>
                    </van-col>
                </van-row>
            </div>
        </div>
        <div style="height: 3rem;"></div>
        <!-- v-if="submitBtn" -->
        <van-button  type="info" @click="submit" style="width:100%;position: fixed;bottom: 0;">提交</van-button>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator, MessageBox } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                data: '',
                showDate: false,
                isEdit: '1',
                
                zjnrList: [],//制胶步骤
                gjdList: [],//HACCP关键点
                submitBtn: true,
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                this.search(this.$route.params.id)

            }
        },
        methods: {
            check(item) {
                // 判断是否报工,未报工不能复核 
                this.$axios.get(`/jeecg-boot/app/gcMix/getMixQrCodeCount?mixId=${this.info.id}`).then(res => {
                    if (res.data.code == 200) {
                        if (res.data.result > 0) {
                            let flag = false
                            let flag1 = false
                            this.dataArr.forEach(item => {
                                if (item.id == null) {
                                    flag = true
                                }
                                if (item.status == 2) {
                                    flag1 = true
                                }
                            })
                            if (flag) {
                                Toast({
                                    message: '未提交不能复核',
                                    position: "bottom",
                                    duration: 2000
                                });
                                return
                            }
                            if (flag1) {
                                Toast({
                                    message: '已复核请勿重复提交',
                                    position: "bottom",
                                    duration: 2000
                                });
                                return
                            }
                            MessageBox.confirm('是否确认复核？').then(action => {
                                if (action == "confirm") {
                                    let str = ''
                                    this.dataArr.forEach(item => {
                                        str += item.id + ','
                                    })
                                    let param = {
                                        id: str,
                                        mixId: this.info.id,
                                        checkeNo: localStorage.getItem('userCode'),
                                        checker: localStorage.getItem('userName')
                                    }
                                    console.log(param);
                                    this.$axios.put('/jeecg-boot/app/batchRecord/approveKzzb', param).then(res => {
                                        if (res.data.success) {
                                            Toast({
                                                message: res.data.message,
                                                position: 'bottom',
                                                duration: 2000
                                            });
                                        } else {
                                            Toast({
                                                message: res.data.message,
                                                position: 'bottom',
                                                duration: 2000
                                            });
                                        }
                                    })
                                }
                            })
                        } else {
                            Toast({
                                message: '未报工不能复核',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                })

            },
            editItem(item) {
                if (item.id == null) {
                    return
                }
                if (item.type == '1' && item.result1 == '符合') {
                    item.conclusion = '合格'
                } else if (item.type == '1' && item.result1 == '不符合') {
                    item.conclusion = '不合格'
                }
                let num0 = item.zbz.split('-')[0]
                let num1 = item.zbz.split('-')[1]
                if (item.type == '2') {
                    if (item.zbzList.length == 1) {
                        console.log(item.result1);
                        console.log(item.result1 >= num0);
                        console.log(item.result1 <= num1);
                        console.log((item.result1 >= num0 * 1 && item.result1 <= num1 * 1));
                        if (item.result1 >= num0 * 1 && item.result1 <= num1 * 1) {
                            item.conclusion = '合格';
                        } else {
                            item.conclusion = '不合格';
                        }
                    } else if (item.zbzList.length == 2) {
                        if (item.result1 >= num0 * 1 && item.result1 <= num1 * 1 && item.result3 >= num0 * 1 && item.result3 <= num1 * 1) {
                            item.conclusion = '合格';
                        } else {
                            item.conclusion = '不合格';
                        }
                    } else if (item.zbzList.length == 3) {
                        if (item.result1 >= num0 * 1 && item.result1 <= num1 * 1 && item.result2 >= num0 * 1 && item.result2 <= num1 * 1
                            && item.result3 >= num0 * 1 && item.result3 <= num1 * 1) {
                            item.conclusion = '合格';
                        } else {
                            item.conclusion = '不合格';
                        }
                    }
                }
                console.log(item);
                this.$axios.put(`/jeecg-boot/app/batchRecord/editKzzb`, item).then(res => {
                    if (res.data.success) {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        this.search(this.info.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                });
            },
            radioChage(e, item) {
                console.log(item);
                this.editItem(item)

            },
            fieldBlur(e, item) {
                console.log(item);
                console.log(item.result);
                this.editItem(item)
            },
            submit() {
                // 制胶步骤未完成不能填写控制指标
                let zjnrFlag=false
                this.zjnrList.forEach(item => {
                    if (item.id == null) {
                        zjnrFlag = true
                    }
                })
                if(zjnrFlag){
                    Toast({
                        message: '制胶步骤未全部完成,请检查',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                let gjdFlag=false
                if(this.gjdList.length>0){
                    this.gjdList.forEach(item => {
                        if (item.status != 2) {
                            gjdFlag = true
                        }
                    })
                }
                if(gjdFlag){
                    Toast({
                        message: 'HACCP关键点未复核,请检查',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }

                let resultArr = []

                let flag = false
                this.dataArr.forEach(item => {
                    if (item.id != null) {
                        flag = true
                    }
                    let obj = {}
                    obj = Object.assign(item, obj)

                    if (item.type == '1' && item.result1 == '符合') {
                        obj.conclusion = '合格'
                    } else if (item.type == '1' && item.result1 == '不符合') {
                        obj.conclusion = '不合格'
                    }
                    let num0 = item.zbz.split('-')[0]
                    let num1 = item.zbz.split('-')[1]
                    if (item.type == '2') {
                        num1 = parseFloat(num1) 
                        if (item.zbzList.length == 1) {
                            if (obj.result1 >= num0 * 1 && obj.result1 <= num1 * 1) {
                                obj.conclusion = '合格';
                            } else {
                                obj.conclusion = '不合格';
                            }
                        } else if (item.zbzList.length == 2) {
                            if (obj.result1 >= num0 * 1 && obj.result1 <= num1 * 1 && obj.result3 >= num0 * 1 && obj.result3 <= num1 * 1) {
                                obj.conclusion = '合格';
                            } else {
                                obj.conclusion = '不合格';
                            }
                        } else if (item.zbzList.length == 3) {
                            if (obj.result1 >= num0 * 1 && obj.result1 <= num1 * 1 && obj.result2 >= num0 * 1 && obj.result2 <= num1 * 1
                                && obj.result3 >= num0 * 1 && obj.result3 <= num1 * 1) {
                                obj.conclusion = '合格';
                            } else {
                                obj.conclusion = '不合格';
                            }
                        }
                    }
                    obj.firstTimes == null ? 'N/A' : obj.firstTimes
                    obj.firstReason == null ? 'N/A' : obj.firstTimes
                    obj.secondTimes == null ? 'N/A' : obj.firstTimes
                    obj.secondReason == null ? 'N/A' : obj.firstTimes
                    obj.thirdTimes == null ? 'N/A' : obj.firstTimes
                    obj.thirdReason == null ? 'N/A' : obj.firstTimes
                    resultArr.push(obj)
                })
                console.log(resultArr);
                if (flag) {
                    Toast({
                        message: '已经提交过了',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                let params = {
                    userCode: localStorage.getItem('userCode'),
                    userName: localStorage.getItem('userName'),
                    mixId: this.info.id,
                    resultList: resultArr
                }
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios.post(`/jeecg-boot/app/batchRecord/addKzzb`, params).then(res => {
                    Indicator.close();
                    if (res.data.success) {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        this.search(this.info.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                });
            },
            add(item) {
                if (item.status == 2) {
                    return
                }
                if (item.arr.length == 3) {
                    Toast({
                        message: "最多只能三个",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                item.arr.push({})
            },
            addzbz(item) {
                if (item.status == 2) {
                    return
                }
                if (item.zbzList.length == 3) {
                    Toast({
                        message: "最多只能三个",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }

                item.zbzList.push({
                    result: '',
                })
            },
            del(item, i) {
                item.arr.splice(i, 1)
            },
            delzbzList(item, xi) {
                if (xi == 0) {
                    Toast({
                        message: "检测原始值(上层)不能删除",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                item.zbzList.splice(xi, 1)
            },
            search(id) {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${id}`).then(res => {
                    if (res.data.code == 200) {
                        this.isEdit = res.data.result.checkStatus
                        this.dataArr = res.data.result.kzzbList
                        this.zjnrList = res.data.result.zjnrList
                        this.gjdList = res.data.result.gjdList
                        this.dataArr.forEach((item,index) => {
                            if(item.id!=null){
                                this.submitBtn = false
                            }
                            item.mixId = this.info.id
                            item.batchId = this.info.batchId
                            this.$set(item, 'arr', []);
                            this.$set(item, 'zbzList', [{}]);
                            if (item.zbz.indexOf("-") == -1) {
                                item.type = 1
                            } else {
                                item.type = 2
                                if (item.result3 != null) {
                                    item.zbzList.push({})
                                }
                                if (item.result3 != null && item.result2 != null) {
                                    item.zbzList.push({})
                                }
                            }
                            if (item.firstTimes != null) {
                                console.log(item);
                                item.arr.push({})
                            }
                            if (item.firstTimes != null && item.secondTimes != null) {
                                item.arr.push({})
                            }
                            if (item.firstTimes != null && item.secondTimes != null && item.thirdTimes != null) {
                                item.arr.push({})
                            }
                        });
                    }
                });
            },
            confirm(item) {
                if (item.confirmer == null) {
                    let params = {
                        userCode: localStorage.getItem('userCode'),
                        userName: localStorage.getItem('userName'),
                        mixId: this.info.id,
                        resultList: [{
                            ...item,
                            confirmer: localStorage.getItem('userCode')
                        }]
                    }
                    this.$axios.post(`/jeecg-boot/app/batchRecord/addGjd`, params).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                            this.search(this.info.id)
                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
                } else {
                    item.confirmer = localStorage.getItem('userCode')
                    this.$axios.put(`/jeecg-boot/app/batchRecord/editGjd`, item).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                            this.search(this.info.id)

                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
                }
            },

            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>