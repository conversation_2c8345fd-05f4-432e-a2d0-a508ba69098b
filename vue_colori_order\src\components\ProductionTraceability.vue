<template>
  <div style="background-color: #ebecf7;height: 100%;">
    <div style="height:32%">
      <div style="font-size:16px;text-align: left;font-weight: 700;margin-left:1rem;padding-top:0.5rem">
        查询条件
      </div>
      <van-cell-group style="margin-top:0.5rem">
        <van-field v-model="queryParam.code" label="产品编码" placeholder="请输入产品编码"
          @change="(e) => { debounce(searchProductName(e), 500) }" />
        <van-field v-model="queryParam.name" label="产品名称" placeholder="请输入产品名称" />
        <van-field v-model="queryParam.customerId" label="批次号" placeholder="请输入批次号" />
        <van-field name="radio" label="类型">
          <template #input>
            <van-radio-group v-model="type" direction="horizontal">
              <van-radio name="1">按成品</van-radio>
              <van-radio name="2">按胶体</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-button round block type="info" @click="search" style="width:90%;margin:1rem auto;">查询</van-button>
      </van-cell-group>
    </div>
    <div style="height:67%;margin-top:20px;overflow: auto;">
      <div v-for="(item, index) in dataSource" :key="index"
        style="text-align: left;background-color: #fff;border-radius: 10px;padding:2%;width: 95%;margin: 0rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
        <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
          <van-row>
            <van-col span="20">
              <span style="font-size:18px;font-weight: 700;color: #000;">
                {{ item.moId }}
              </span>
            </van-col>
          </van-row>
        </div>
        <van-row>
          <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
            <span style="color:gary">产品名称：
              <span style="color:black;">
                {{ item.name }}
              </span>
            </span>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="12">
            <span style="color:gary">产品编号：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.code }}
              </span>
            </span>
          </van-col>
          <van-col span="12">
            <span style="color:gary">批&emsp;次&emsp;号：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.customer }}
              </span>
            </span>
          </van-col>
        </van-row>

        <van-row>
          <van-col span="12">
            <span style="color:gary">生产时间：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.createTime }}
              </span>
            </span>
          </van-col>
          <van-col span="12">
            <span style="color:gary">主单位数量：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.mainQuantity }}
              </span>
            </span>
          </van-col>

        </van-row>

        <van-row>
          <van-col span="12">
            <span style="color:gary">工作中心：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.mitosome }}
              </span>
            </span>
          </van-col>
          <van-col span="12">
            <span style="color:gary">辅单位数量：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.quantity }}
              </span>
            </span>
          </van-col>
        </van-row>

        <van-row>
          <van-col span="20">
            <span style="color:gary">生产组长：
              <span style="color:black;overflow-x: auto;white-space:nowrap;">
                {{ item.leaderNo }}
              </span>
            </span>
          </van-col>
          <van-col span="4">
            <van-button size="mini" icon="share-o" round hairline type="primary" @click="detail(item)">追溯
            </van-button>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="16"></van-col>
          <van-col span="4"></van-col>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { Indicator } from 'mint-ui';
import { Toast } from 'vant';



export default {
  data() {
    return {
      queryParam: {
        code: '',
        name: '',
        customerId: '',
      },
      type: '1',
      dataSource: [],
    }
  },
  methods: {
    debounce(fn, delay = 100) {
      let timer = null
      return function () {
        let args = arguments
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    },
    searchProductName(e) {
      this.$axios
        .get(`/jeecg-boot/ncApp/molds/getGoodsInfo?code=${e.target.value}`)
        .then(res => {
          if (res.data.code == 200) {
            if (res.data.result) {
              console.log(res.data.result.name);
              this.queryParam.name=res.data.result.name
            } else {
              Toast({
                message: '请重新输入产品编码',
                position: "bottom",
                duration: 2000
              });
              this.queryParam.name=''
            }
          }
        });
    },

    search() {
      Indicator.open({
        text: '正在加载中，请稍后……',
        spinnerType: 'fading-circle'
      });
      this.$axios.get('/jeecg-boot/app/tank/check/colloidReview', { params: { code: this.queryParam.code, name: this.queryParam.name, customerId: this.queryParam.customerId } }).then(res => {
        if (res.data.code == 200) {
          console.log(res);
          this.dataSource = res.data.result
        } else {
          Toast({
            message: res.data.message,
            position: 'bottom',
            duration: 2000
          });
        }
        Indicator.close();
      })
    },
    detail(item) {
      this.$router.push({
        name: "ProductionTraceabilityDetail",
        params: { item: item, type: this.type }
      });
    },
  },
}
</script>

<style>

</style>