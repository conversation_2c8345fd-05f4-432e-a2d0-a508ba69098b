{"name": "vue_colori_order", "version": "1.0.0", "description": "A Vue.js project", "author": "qianbin <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "build": "node build/build.js"}, "dependencies": {"@tarojs/taro": "^3.3.15", "ant-design-vue": "^1.7.5", "axios": "^0.21.1", "buffer": "^6.0.3", "decimal.js": "^10.4.3", "echarts": "^5.5.1", "html2canvas": "^1.0.0-rc.4", "js-cookie": "^3.0.1", "lodash.pick": "^4.4.0", "mint-ui": "^2.2.13", "moment": "^2.29.1", "qrcodejs2": "0.0.2", "to-array-buffer": "^3.2.0", "vant": "^2.12.21", "vconsole": "^3.9.5", "vue": "^2.5.2", "vue-horizontal-calendar": "^1.0.0", "vue-pdf": "^4.3.0", "vue-qr": "^4.0.9", "vue-qrcode-reader": "^3.0.4", "vue-router": "^3.0.1"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^21.0.2", "babel-loader": "^7.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "chromedriver": "^2.27.2", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.11", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jest": "^22.0.4", "jest-serializer-vue": "^0.3.0", "less": "^3.12.2", "less-loader": "^4.1.0", "nightwatch": "^0.9.12", "node-notifier": "^5.4.5", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "^3.0.0", "stylus": "^0.54.8", "stylus-loader": "^6.1.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-directive-image-previewer": "^2.2.2", "vue-i18n": "^8.7.0", "vue-jest": "^1.0.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "volta": {"node": "16.18.0"}}