<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div style="padding:10px">BOS订单每日执行情况表</div>

        <div>

            <div style="width:100%;">

                <div class="bg_item" @click="getOrderDetail('99')">
                    <p>BOS订单总量<br/>&nbsp;</p>
                    <p>{{reports[8].quantity}}</p>
                </div>

                <div class="bg_item" style="background-color:green" @click="getOrderDetail('61')">
                    <p>正常执行订单<br/>（X≤1周）</p>
                    <p>{{reports[4].quantity}}</p>
                </div>

            </div>

            <div style="width:100%;">

                <div class="bg_item" style="background-color:orange" @click="getOrderDetail('62')">
                    <p>警戒订单<br/>(1周&lt;X≤3周)</p>
                    <p>{{reports[5].quantity}}</p>
                </div>

                <div class="bg_item" style="background-color:red" @click="getOrderDetail('63')">
                    <p>逾期订单<br/>(X&gt;3周)</p>
                    <p>{{reports[6].quantity}}</p>
                </div>

            </div>


            <div style="width:100%;">

                <div class="bg_item" @click="getOrderDetail('2')">
                    <p>工艺阶段订单<br/>&nbsp;</p>
                    <p>{{reports[1].quantity}}</p>
                </div>

                <div class="bg_item" @click="getOrderDetail('3')">
                    <p>财务核价阶段订单<br/>&nbsp;</p>
                    <p>{{reports[2].quantity}}</p>
                </div>

            </div>


            <div style="width:100%;">

                <div class="bg_item" @click="getOrderDetail('4')">
                    <p>计划审核阶段订单<br/>&nbsp;</p>
                    <p>{{reports[3].quantity}}</p>
                </div>

                <div class="bg_item" @click="getOrderDetail('0')">
                    <p>审批阶段订单<br/>&nbsp;</p>
                    <p>{{reports[0].quantity}}</p>
                </div>

            </div>



            <div style="width:100%;">

                <div class="bg_item" @click="getOrderDetail('9')">
                    <p>已关闭订单<br/>&nbsp;</p>
                    <p>{{reports[7].quantity}}</p>
                </div>

                <div class="bg_item">
                    
                </div>

            </div>

        </div>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            reports:[],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.getOrderSummary()
    },
    methods: {
        getOrderSummary(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/report/getOrderSummary',null).then(res=>{
                if(res.data.code==200){
                    self.reports=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getOrderDetail(status){
            let self=this
            localStorage.setItem('orderStatus',status)
            self.$router.push({name:"OrderInfoDetail",params:{status:status}})
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png') no-repeat;
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
.menu_order2{
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
</style>