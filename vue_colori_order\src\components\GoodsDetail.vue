<template>
  <div style="text-align:left;padding-bottom: 1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar
        title="详情"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
    </van-sticky>
    <div
      v-for="(item, index) in dataArr"
      :key="index"
      style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;overflow: hidden;"
    >
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{ item.name }}</span
            >
          </van-col>
        </van-row>
      </div>

      <van-row>
        <van-col span="12">
          <span style="color:gary"
            >编&emsp;码: <span style="color:black;">{{ item.code }}</span></span
          >
        </van-col>
        <van-col span="12">
          <span style="color:gary">
            状态:
            <span style="color:red;font-weight: 700;" v-if="item.status == '1'"
              >待拣货</span
            >
            <span
              style="color:#CCCC00;font-weight: 700;"
              v-if="item.status == '2'"
              >待复核</span
            >
            <span
              style="color:green;font-weight: 700;"
              v-if="item.status == '3'"
              >已复核</span
            >
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            批次号:
            <span style="color:black">{{ item.customer }} </span>
          </span>
        </van-col>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            应发辅数量:
            <span style="color:black">{{ item.respondFcount }} </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            应发数量:
            <span style="color:black">{{ item.respondCount }} </span>
          </span>
        </van-col>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            实发数量:
            <span style="color:black">{{ item.actualCount }} </span>
          </span>
        </van-col>
      </van-row>

      <van-row>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            拣货缺数:
            <span style="color:black">{{ item.defectCount }} </span>
          </span>
        </van-col>
        <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            复核缺数:
            <span style="color:black">{{ item.checkDefectCount }} </span>
          </span>
        </van-col>
      </van-row>

      <van-row v-if="item.defectCount > 0">
        <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            拣货缺少原因:
            <span style="color:black">{{ item.defectRemark }} </span>
          </span>
        </van-col>
      </van-row>
      <van-row v-if="item.checkDefectCount > 0">
        <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            复核缺少原因:
            <span style="color:black">{{ item.checkRemark }} </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
          <span style="color:gary">
            保管员&emsp;:
            <span style="color:black">{{ item.pickerName }} </span>
          </span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      info: {},
      dataArr: [],
      userCode: localStorage.getItem("userCode")
    };
  },
  created() {
    if (this.$route.params) {
      this.info = this.$route.params;
      this.$axios
        .get(
          `/jeecg-boot/app/warehousePick/getPickMainSun?id=${
            this.info.id
          }&userCode=${localStorage.getItem("userCode")}`
        )
        .then(res => {
          if (res.data.code == 200) {
            this.dataArr = res.data.result;
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    } else {
      this.$router.go(-1);
    }
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped></style>
