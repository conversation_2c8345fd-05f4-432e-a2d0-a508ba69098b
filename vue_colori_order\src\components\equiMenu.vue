<template>
    <div>
        <template v-if="active === 0">
            <img src="../../static/images/equiMenuPng.png" width="100%" />
            <div class="menu_order_more">
                <div class="menu_order_more_item" @click="goDetail('1')">
                    <img src="../../static/images/PositionAdjustment.png"
                        style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                    <p>点检</p>
                </div>

                <div class="menu_order_more_item" @click="goDetail('2')">
                    <img src="../../static/images/picking.png"
                        style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                    <p>维保</p>
                </div>
                <div class="menu_order_more_item" @click="goDetail('3')">
                    <img src="../../static/images/pickingCheck.png"
                        style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                    <p>维修</p>
                </div>
            </div>
        </template>

        <template v-if="active === 1">
            <div class="profile-container">
                <div class="profile-card">
                    <div class="avatar-section">
                        <div class="avatar">
                            <van-icon name="contact" size="50" color="#1989fa" />
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="info-item">
                            <label>账号</label>
                            <span>{{ userCode }}</span>
                        </div>
                        <div class="info-item">
                            <label>姓名</label>
                            <span>{{ userName }}</span>
                        </div>
                    </div>

                    <div class="action-section" v-if="type == '1'">
                        <van-button type="primary" block round icon="lock" @click="showPasswordDialog = true"
                            style="margin-bottom: 16px;">
                            修改密码
                        </van-button>

                        <van-button type="danger" block round icon="close" @click="handleLogout">
                            退出登录
                        </van-button>
                    </div>
                </div>
            </div>

            <!-- 修改密码弹窗 -->
            <van-dialog v-model="showPasswordDialog" title="修改密码" show-cancel-button :before-close="handleBeforeClose">
                <van-form>
                    <van-field v-model="passwordForm.newPassword" type="password" label="新密码" placeholder="请输入新密码"
                        :rules="[{ required: true, message: '请输入新密码' }]" />
                    <van-field v-model="passwordForm.confirmPassword" type="password" label="确认密码"
                        placeholder="请再次输入新密码" :rules="[{ required: true, message: '请确认新密码' }]" />
                </van-form>
            </van-dialog>
        </template>

        <van-tabbar v-model="active" fixed>
            <van-tabbar-item icon="home-o">首页</van-tabbar-item>
            <van-tabbar-item icon="manager">个人中心</van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            active: 0,
            userCode: '',
            userName: '',
            showPasswordDialog: false,
            passwordForm: {
                newPassword: '',
                confirmPassword: ''
            },
            type: ''
        }
    },
    created() {
        const { id, type } = this.$route.query
        this.type = type
        if (type == '1') {
            this.userCode = localStorage.getItem("equiUserCode")
            this.userName = localStorage.getItem("equiUserName")
        } else if (type == '2') {
            this.userCode = localStorage.getItem("userCode")
            this.userName = localStorage.getItem("userName")
        }
        if ((type == '1' && (!localStorage.getItem("equiUserCode") || localStorage.getItem("equiUserCode") == "")) ||
            (type == '2' && (!this.userCode || this.userCode == ""))) {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000
            });
            if (type == '2') {
                this.$router.push({
                    name: "Login",
                    query: {
                        route: 'equiMenu',
                        id: id,
                        type: type
                    }
                });
            } else if (type == '1') {
                this.$router.push({
                    name: "equiSpotLogin",
                    query: {
                        route: 'equiSpotCheck',
                        id: id,
                        type: type
                    }
                });
            }
        }
    },
    methods: {
        goDetail(i) {
            if (i == 1) {
                this.$router.push({ path: "/equiDianjian", query: { id: this.$route.query.id, type: this.$route.query.type } })
            } else if (i == 2) {
                this.$router.push({ path: "/equiSpotCheck", query: { id: this.$route.query.id, type: this.$route.query.type } })
            } else if (i == 3) {
            }
        },
        handleBeforeClose(action, done) {
            if (action === 'confirm') {
                if (!this.passwordForm.newPassword || !this.passwordForm.confirmPassword) {
                    Toast('请填写完整密码信息');
                    done(false);
                    return;
                }

                if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
                    Toast('两次输入的新密码不一致');
                    done(false);
                    return;
                }

                this.$axios.get(`/jeecg-boot/app/external/changePassword?password=${this.passwordForm.newPassword}&id=${localStorage.getItem('equiUserId')}&username=${this.userCode}`).then(res => {
                    if (res.data.code === 200) {
                        Toast.success('密码修改成功');
                        this.passwordForm = {
                            newPassword: '',
                            confirmPassword: ''
                        };
                        done();
                    } else {
                        Toast.fail(res.data.message || '修改失败');
                        done(false);
                    }
                }).catch(() => {
                    Toast.fail('修改失败');
                    done(false);
                });
            } else {
                done();
            }
        },
        handleLogout() {
            Dialog.confirm({
                title: '提示',
                message: '确定要退出登录吗？',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonColor: '#ee0a24'
            }).then(() => {
                localStorage.removeItem('equiUserCode');
                localStorage.removeItem('equiUserId');
                localStorage.removeItem('equiUserName');

                this.$router.replace({
                    name: 'equiSpotLogin'
                });

                Toast.success('已退出登录');
            }).catch(() => {
                // 取消退出
            });
        }
    },
}
</script>

<style scoped>
.menu_order_more_item {
    float: left;
    height: 100%;
    width: 33.33%;
}

.menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}

.menu_order_more_item p {
    text-align: center;
    margin-top: 8px;
    color: #323233;
    font-size: 14px;
}

.profile-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 50px);
}

.profile-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.avatar-section {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
}

.avatar {
    width: 80px;
    height: 80px;
    background: #f5f7fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f5f7fa;
}

.info-item label {
    color: #969799;
    width: 60px;
}

.info-item span {
    color: #323233;
    flex: 1;
}

.action-section {
    margin-top: 32px;
}

.action-section .van-button {
    margin-bottom: 12px;
}

.action-section .van-button:last-child {
    margin-bottom: 0;
}
</style>