<template>
    <div style="background:#f0f0f0;min-height:100%;text-align:left;">
        
        <van-button type="primary" @click="handleScan" style="margin:10%;width:80%;border-radius:10px;">扫码入库确认</van-button>

        <!-- <van-field label="扫码内容" v-model="sticker" ref='sticker'  @keyup.enter.native="handleContent" style="margin:5%;width:90%;border-radius:10px;"/> -->

        <van-cell title="托码编号" v-model="id" @click="handleSearch" style="margin:5%;width:90%;border-radius:10px;"/>

        <div v-if="size!=null && size !=''" style="margin-left:3%;">该工单共有托码数量：<span style="font-size:20px;">{{size}}</span>，未入库托码数量：
            <span style="font-size:20px;color:red">{{notWareHouseSize}}</span>
        </div>

        <div style="margin:3%;background:white;padding:2%;" v-for="(item,index) in stickerList" :key="index">
            <div style="width:100%;">
                <div style="float:left;width:60%;font-size:18px;">
                    <span>
                        <strong>{{item.id}}</strong>
                    </span>
                </div>
                <div style="float:left;width:40%;text-align:right;">
                    <span>
                        {{item.createTime}}
                    </span>
                </div>
                <div style="clear:both;"></div>
            </div>
            <div style="background:#f0f0f0;width:100%;height:2px;"></div>
            <div style="width:100%;">
                <div style="float:left;width:30%;">
                    <span>
                        {{item.code}}
                    </span>
                </div>
                <div style="float:left;width:70%;">
                    <span>
                        {{item.name}}
                    </span>
                </div>
                <div style="clear:both;"></div>
            </div>
            <div style="width:100%;">
                <div style="float:left;width:30%;">
                    <span>
                        {{item.customer}}
                    </span>
                </div>
                <div style="float:left;width:70%;">
                    <span v-if="item.qcStatus=='1'" style="color:green;">
                        √
                    </span>
                    <span v-if="item.qcStatus=='2'" style="color:red;">
                        ×
                    </span>
                    <span v-if="item.qcStatus=='0'" style="color:orange;">
                        N/A
                    </span>
                    &emsp;
                    <span v-if="item.cxStatus=='1'" style="color:green;">
                        √
                    </span>
                    <span v-if="item.cxStatus=='0'" style="color:orange;">
                        ×
                    </span>
                    &emsp;
                    <span v-if="item.ckStatus=='1'" style="color:green;">
                        √
                    </span>
                    <span v-if="item.ckStatus=='0'" style="color:orange;">
                        ×
                    </span>
                    &emsp;
                    <span v-if="item.warehouse">
                        {{item.warehouse}}
                    </span>
                    -
                    <span v-if="item.hwh">
                        {{item.hwh}}
                    </span>
                </div>
                <div style="clear:both;"></div>
            </div>
            <div style="clear:both;"></div>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            id:'',
            size:'',
            sticker:'',
            inWareHouseSize:'',
            notWareHouseSize:'',
            stickerList:[],
        }
    },
    mounted () {
        this.$refs.sticker.focus();
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')
        // this.getWxInfo()
        // this.getCodeInfo("2112090001","1")
        // this.getStickerList()
    },
    methods: {
        handleScan(){
            let self=this;

            // self.$router.push({name:"ScanResult",params:{result:'T24090900069',type:'3'}})

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    if(result.indexOf('T')!=-1){
                        self.$router.push({name:"ScanResult",params:{result:result,type:'3'}})
                    }else{
                        self.$router.push({name:"ScanWareInResult",params:{result:result}})
                    }
                    
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
        },
        handleContent(e){
            let self=this
            var content=e.target.value

            // self.$router.push({name:"ScanResult",params:{result:'D23010600349',type:'3'}})

            // self.$router.push({name:"ScanResult",params:{result:content,type:'3'}})
            if(content.indexOf('T')!=-1){
                self.$router.push({name:"ScanResult",params:{result:content,type:'3'}})
            }
            // else{
            //     self.$router.push({name:"ScanWareInResult",params:{result:result}})
            // }
        },
        handleSearch(){
            let self=this
            MessageBox.confirm('',{
                message: '请选择扫码或者录入',
                title: '提示',
                confirmButtonText: '扫码',
                cancelButtonText: '录入'
                }).then(action => {
                    if(action=="confirm"){
                        wx.scanQRCode({
                            desc: 'scanQRCode desc',
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function(res) {
                                // 回调
                                var result = res.resultStr;//当needResult为1时返回处理结果
                                self.id=result
                                self.getStickerList(result)
                            },
                            error: function(res) {
                                if (res.errMsg.indexOf('function_not_exist') > 0) {
                                    alert('版本过低请升级')
                                }
                            }
                        });
                    }
                }).catch((res)=>{
                    if(res=="cancel"){
                        MessageBox.prompt('请录入托码编号').then(({ value, action }) => {
                            if(action=="confirm"){
                                this.id=value
                                this.getStickerList(value)
                            }
                        });
                    }        
                });
        },
        getStickerList(id){
            let self=this;
            self.$axios.get('/jeecg-boot/app/sticker/getStickerListByIdToStore',{params:{id:id}}).then(res=>{
                if(res.data.code==200){
                    this.stickerList=res.data.result;
                    this.size=this.stickerList.length;
                    this.inWareHouseSize=0;

                    for(var i=0;i<this.stickerList.length;i++){
                        if(this.stickerList[i].ckStatus=='1'){
                            this.inWareHouseSize++
                        }
                    }

                    this.notWareHouseSize=this.size-this.inWareHouseSize

                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
