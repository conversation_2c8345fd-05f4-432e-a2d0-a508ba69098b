<template>
    <div>
        <van-form validate-first @submit="onSubmit">
            <!-- 日期 -->
            <van-field readonly clickable name="workDay" :value="workDay" label="日期" placeholder="点击选择日期"
                @click="showCalendar = true" :rules="[{ required: true, message: '请选择日期' }]" />
            <van-calendar v-model="showCalendar" @confirm="workDayConfirm" color="#1989fa" />
            <!-- 账套 -->
            <van-field readonly clickable name="book" :value="book" label="账套" placeholder="点击选择账套"
                @click="showBookPicker = true" :rules="[{ required: true, message: '请选择账套' }]" />
            <van-popup v-model="showBookPicker" position="bottom">
                <van-picker show-toolbar :columns="bookColumns" @confirm="onBookConfirm"
                    @cancel="showBookPicker = false" />
            </van-popup>

            <!-- 车间 -->
            <van-field name="shop" label="车间" :rules="[{ required: true, message: '请选择车间' }]">
                <template #input>
                    <van-checkbox-group v-model="checkboxGroup" direction="horizontal">
                        <van-row>
                            <van-col span="24">
                                <van-checkbox style="margin-top:5%;" v-for="(item, index) in workshopArr" :key="index"
                                    :name="item" shape="square">
                                    {{ item }}
                                </van-checkbox>
                            </van-col>
                        </van-row>
                    </van-checkbox-group>
                </template>
            </van-field>

            <!-- 班次 -->
            <van-field readonly clickable name="category" :value="category" label="班次" placeholder="点击选择班次"
                @click="showCategoryPicker = true" :rules="[{ required: true, message: '请选择班次' }]" />
            <van-popup v-model="showCategoryPicker" position="bottom">
                <van-picker show-toolbar :columns="categoryColumns" @confirm="onCategoryConfirm"
                    @cancel="showCategoryPicker = false" />
            </van-popup>
            <!-- 类别 -->
            <van-field readonly clickable name="type" :value="type" label="类别" placeholder="点击选择类别"
                @click="showTypePicker = true" :rules="[{ required: true, message: '请选择类别' }]" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="onTypeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>

            <!-- 操作人名字 编码 -->
            <van-button type="default" native-type="submit" icon="plus" size="mini" @click="add"></van-button>
            <div v-for="(item, index) in arr" :key="index">
                <van-field name="leader" v-model.trim="item.leader" label="机修工编码" placeholder="请输入机修工员编号" :rules="[
                    { validator: value => validator(value, item), message: '必填' }
                ]" @change="e=>leaderChange(item,e)"/>
                <van-field name="leaderNo" v-model="item.leaderNo" label="机修工员姓名" readonly
                    :rules="[{ required: true, message: '必填' }]" />
                <van-button type="default" native-type="submit" icon="minus" size="mini" @click="minus(item, index)">
                </van-button>
            </div>

            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            // 日期选择框显隐
            showCalendar: false,
            // 账套选择框显隐
            showBookPicker: false,
            // 班次选择框显隐
            showCategoryPicker: false,
            // 类别选择框显隐
            showTypePicker: false,
            // 账套数据
            bookColumns: [],
            // 机修工名字
            leaderNo: "",
            // 机修工编码
            leader: "",
            // 日期
            workDay: "",
            // 账套
            book: "",
            // 车间复选框
            checkbox: false,
            checkboxGroup: [],
            workshopArr: [],
            // 班次数据
            categoryColumns: ["白班", "晚班"],
            // 班次
            category: "",
            // 类别
            type: "",
            // 类别数据
            typeColumns: ["设备", "通用"],
            arr: []
        };
    },
    created() {
        // 获取账套
        this.$axios
            .get(
                `/jeecg-boot/app/device/getClassDepartMent?userCode=${localStorage.getItem(
                    "userCode"
                )}`
            )
            .then(res => {
                if (res.data.code == 200) {
                    this.bookColumns = res.data.result;
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
    },
    methods: {
        leaderChange(item,e){
            if(item.leaderNo!=''||item.leaderNo!=null||item.leaderNo!=undefined){
                item.leaderNo=''
            }
        },
        add() {
            this.arr.push({ leader: "", leaderNo: "" });
        },
        minus(item, index) {
            this.arr.splice(index, 1);
        },
        // 通过编号搜索名字
        validator(value, item) {
            console.log(value, item);
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        item.leaderNo = res.data.message;
                        return true;
                    } else {
                        item.leaderNo = "";
                        Toast({
                            message: "请检查机修工编码是否正确",
                            position: "bottom",
                            duration: 2000
                        });
                        return false;
                    }
                });
        },
        // 日期选择确认
        workDayConfirm(date) {
            this.workDay = `${date.getFullYear()}-${date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1
                }-${date.getDate() < 10 ? "0" + date.getDate() : date.getDate()}`;
            this.showCalendar = false;
        },
        // 账套确认
        onBookConfirm(value) {
            this.book = value;
            this.showBookPicker = false;
            // 获取车间
            this.$axios
                .get(
                    `/jeecg-boot/app/device/getClassWorkshop?userCode=${localStorage.getItem(
                        "userCode"
                    )}&dept=${value}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res);
                        this.workshopArr = res.data.result;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        //班次确认
        onCategoryConfirm(value) {
            this.category = value;
            this.showCategoryPicker = false;
        },
        //类别确认
        onTypeConfirm(value) {
            this.type = value;
            this.showTypePicker = false;
        },
        // 提交
        onSubmit(values) {
            if (this.arr.length < 1) {
                Toast({
                    message: '至少添加一个机修工',
                    position: "bottom",
                    duration: 1000
                });
                return;
            }
            let str = ''
            this.arr.forEach(item => {
                str += item.leader + ','
            })
            values.leaderNo = str
            let workshop = "";
            values.shop.forEach(item => {
                workshop += item + ",";
            });
            delete values.leader
            values.workshop = workshop;
            values.creator = localStorage.getItem("userCode");
            console.log("请求参数", values);
            this.$axios.post(`/jeecg-boot/app/device/add`, values).then(res => {
                if (res.data.code == 200) {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                    this.$router.replace({
                        name: "EquipmentScheduling"
                    });
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        }
    }
};
</script>

<style scoped>

</style>
