<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <a-form-item
              :labelCol="labelCol"
              label="客户序列号"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入客户序列号" :disabled="workType=='1'" v-decorator="['customer',{rules:[{required:true,message:'请输入客户序列号'}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="reportMainName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入报工产量" v-decorator="['output',{rules:[{required:true,message:'请输入产量'},{validator: this.validateNum}]}]" ></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="reportName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入报工产量" v-decorator="['zoutput',{rules:[{required:true,message:'请输入产量'},{validator: this.validateNum1}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="换算率"
              :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['exchange',{}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="规格"
              :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['spec',{}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import pick from 'lodash.pick'
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "PostRecordModal",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        moIds:[],
        expandedRowKeys: [],
        id: '',
        unit:'',
        type:'',
        isOk:false,
        flag:false,
        saveFlag: true,
        reportName:'',
        exchange:'',
        workType:'',
        reportMainName:'',
        updateItem:{},
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },

      edit(id,item) {  
        this.visible = true;
        this.updateItem={}
        this.id=id;
        this.unit=item.unit
        this.workType=item.workType
        console.log("workType:"+this.workType)
        this.reportMainName="报工产量("+item.mainUnit+")"
        this.reportName="报工产量("+item.unit+")"
        this.orderMainModel = Object.assign({}, item);
        this.exchange=item.exchange
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.orderMainModel, 'exchange','spec'))
        });
        this.getCustomer(this.id)
      },
      update(item,items){
        let self=this;

        console.log("item:"+item)
        console.log("items:"+items)

        this.visible = true;
        this.reportMainName="报工产量("+items.mainUnit+")"
        this.reportName="报工产量("+items.unit+")"
        this.updateItem=item;
        this.workType=item.workType
        this.exchange=items.exchange
        this.flag=true
        this.unit=items.mainUnit
        console.log("output:"+item.output)
        this.orderMainModel = Object.assign({}, items);
        this.exchange=items.exchange
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.orderMainModel,'zoutput','exchange','spec'))
        });
        this.getCustomer(item.lpId)
      },
      close() {
        this.$emit('close');
        this.item=[]
        this.exchange="";
        this.unit="";
        this.visible = false;
        this.updateItem={}
        this.flag=false;
        this.isOk=false;
      },
      getCustomer(id){
        let self=this
        self.$axios.get('/jeecg-boot/app/gcWorkshop/getCustomerInfoNew',{params:{lpId:id}}).then(res=>{
          if(res.data.success){
            self.form.setFieldsValue({customer: res.data.message ? res.data.message : null})
            if(this.updateItem.output != null && this.updateItem.output !=undefined && this.updateItem.output !=''){
              self.form.setFieldsValue({output: this.updateItem.output ? this.updateItem.output : null})
              var zoutput=this.updateItem.output/this.exchange
              try{
                zoutput=parseFloat(zoutput).toFixed(2);
              }catch(e){
                zoutput='0'
              }
              self.form.setFieldsValue({zoutput: zoutput ? zoutput : null})
            }
            
          }
        })
      },
      handleOk() {
        let self=this
        if(!self.saveFlag){
          return;
        }
        self.saveFlag=false
        this.form.validateFields((err, values) => {
          if (!err) {
            self.confirmLoading = true;
            this.isOk=true;
            let params="";
            let urls="/jeecg-boot/app/gcWorkshop/addLeadOutput";

            console.log(this.flag)

            if(this.flag){
              params=this.updateItem
              params.customer=self.form.getFieldValue("customer")
              params.output=self.form.getFieldValue("output")
              urls="/jeecg-boot/app/gcWorkshop/changeOutput";
            }else{
              console.log("flag"+this.flag)
              params={
                customer:self.form.getFieldValue("customer"),
                output:self.form.getFieldValue("output"),
                lpId:self.id,
                type:'1',
                creator:localStorage.getItem('userCode')
              }
            }

            console.log("urls:"+urls)
            console.log("params:"+params)
            self.$axios.post(urls,params).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                if(!this.flag){
                  this.visible=false;
                  self.saveFlag=true
                  this.$emit('ok');
                }else{
                  this.visible=false;
                  self.saveFlag=true
                  this.$emit('ok');
                }
                
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                self.saveFlag=true
              }
            }).finally(() => {
                  self.confirmLoading = false;
                  self.close();
            });
          }
        });
        
      },
      endWork(){
        let self=this;
        let params={
          lpId:self.id,
          type:'2',
          creator:localStorage.getItem('userCode'),
        }
        self.$axios.post('/jeecg-boot/app/gcWorkshop/addGlueOperation',params).then(res=>{
          if(res.data.code==200){
            self.visible=false;
            self.saveFlag=true
            self.$emit('ok');
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        })
      },
      handleCancel() {
        this.close()
      },
      // 验证数字
      validateNum(rule, value, callback) {
        if (!value || new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/).test(value)) {
          callback();
          var changeOutput=value/this.exchange
          try{
            changeOutput=parseFloat(changeOutput).toFixed(2);
          }catch(e){
            changeOutput='0'
          }
          this.form.setFieldsValue({zoutput: changeOutput ? changeOutput : null})
        } else {
          callback("请输入数字，小数请保留2位!");
        }
      },
      validateNum1(rule, value, callback) {
        if (!value || new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/).test(value)) {
          callback();
          var changeOutput=value*this.exchange
          this.form.setFieldsValue({output: changeOutput ? changeOutput : null})
        } else {
          callback("请输入数字，小数请保留2位!");
        }
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>