<template>
    <div style="background:#f3f4f6;min-height:100%">

        <van-tabs v-model="active">
            <van-tab title="信息">
                <div>
                    <div v-if="result!='' && result!=null">
                        <div class="sign">
                            <div class="sign-type" style="background:gray;" v-if="itemParams.productNo==null">无产品信息
                            </div>
                            <div class="sign-type" style="background:gray;"
                                v-else-if="itemParams.realProductStatus=='0'">锁定</div>
                            <div class="sign-type" style="background:orange;"
                                v-else-if="itemParams.realProductStatus=='1'">待检</div>
                            <div class="sign-type" style="background:green;"
                                v-else-if="itemParams.realProductStatus=='2'">质检合格</div>
                            <div class="sign-type" style="background:red;"
                                v-else-if="itemParams.realProductStatus=='3'">质检不合格</div>
                            <div class="sign-type" style="background:blue;"
                                v-else-if="itemParams.realProductStatus=='9'">工艺已核->质量待检</div>
                            <div style="clear:both;"></div>
                        </div>

                        <div v-if="itemParams.productNo!=null && itemParams.productNo!=''">
                            <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">
                                产品信息</div>
                            <van-field label="胶体编号" :value="itemParams.productNo" readonly />
                            <van-field label="胶体名称" :value="itemParams.productName" readonly />
                            <van-field label="客户批次号" :value="itemParams.customerId" readonly />
                            <van-field label="胶体容量" :value="itemParams.volume+'KG'" readonly />
                            <van-field label="生产日期" :value="itemParams.productiveTime" readonly />
                            <van-field label="复检周期" :value="itemParams.cyclecheck+'天'" readonly />
                            <van-field label="归属区域" :value="itemParams.area" readonly />
                            <van-field label="有效期截止" :value="itemParams.endTime" readonly />
                            <van-field label="客户名称" :value="itemParams.productCust" readonly />
                            <van-field label="备注" :value="itemParams.remarks" readonly />

                        </div>


                        <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">坦克信息
                        </div>
                        <van-field label="扫描编号" :value="info[0]" readonly />
                        <van-field label="坦克编号" :value="itemParams.tankNo" readonly />
                        <van-field label="坦克类型" value="坦克" v-if="itemParams.tankType=='1'" readonly />
                        <van-field label="坦克类型" value="青桶" v-if="itemParams.tankType=='2'" readonly />
                        <van-field label="坦克类型" value="储罐" v-if="itemParams.tankType=='3'" readonly />
                        <van-field label="归属账套" :value="itemParams.tankBook" readonly />
                        <van-field label="胶体车间" :value="itemParams.tankWorkshop" readonly />
                        <van-field label="坦克净重" :value="itemParams.tankWeight+'KG'" readonly />
                        <van-field label="坦克容量" :value="itemParams.tankVolume+'KG'" readonly />


                        <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">质检信息
                        </div>
                        <van-field label="校验次数" :value="itemParams.checkTimes" readonly />
                        <van-field label="上次校验时间" :value="itemParams.lastCheckTime" readonly />
                        <van-field label="上次检验人" :value="itemParams.lastChecker" readonly />
                        <van-field label="上次检验状态" value="锁定" v-if="itemParams.lastStatus=='0'" readonly />
                        <van-field label="上次检验状态" value="质检合格" v-else-if="itemParams.lastStatus=='2'" readonly />
                        <van-field label="上次检验状态" value="质检不合格" v-else-if="itemParams.lastStatus=='3'" readonly />
                        <van-field label="上次检验状态" value="工艺已核->质量待检" v-else-if="itemParams.lastStatus=='9'" readonly />
                        <!-- <van-field label="上次检验状态" value="待检" v-else readonly /> -->


                        <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0"
                            v-if="itemParams.infoList.length>0">拉灌信息</div>
                        <div v-if="itemParams.infoList.length>0">
                            <van-field label="拉料工" :value="itemParams.pullUser" v-if="itemParams.pullTimes>0"
                                readonly />
                            <van-field label="拉料时间" :value="itemParams.pullTime" v-if="itemParams.pullTimes>0"
                                readonly />
                            <van-field label="灌包车间" :value="itemParams.gbWorkshop" v-if="itemParams.pullTimes>0"
                                readonly />
                            <van-field label="灌包工作中心" :value="itemParams.gbJobCenter" readonly />
                            <van-field label="灌装单号" :value="itemParams.infoList[0].moId" readonly />
                            <van-field label="灌装组长" :value="itemParams.infoList[0].gbLeaderNo" readonly />
                            <van-field label="加料工" :value="itemParams.infoList[0].acceptUser" readonly />
                            <van-field label="灌装时间" :value="itemParams.infoList[0].acceptTime" readonly />
                        </div>

                        <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0"
                            v-if="itemParams.gcTankUsageInfos.length>0">近10次消耗情况</div>
                        <div v-for="(item,index) in itemParams.gcTankUsageInfos" :key="index"
                            style="margin-bottom:15px">
                            <van-field label="产品编号" :value="item.productNo" readonly />
                            <van-field label="产品名称" :value="item.productName" readonly />
                            <van-field label="产品批次" :value="item.customerId" readonly />
                            <van-field label="MO单号" :value="item.reason" readonly />
                            <van-field label="消耗时间" :value="item.createTime" readonly />
                            <van-field label="消耗重量" :value="item.output+'KG'" readonly />
                        </div>

                        <!-- <van-popup  v-model="showJobCenter" position="bottom">
                <van-picker :columns="jobCenterList" @cancel="onJobCenterCancel" @confirm="onJobCenterConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
            </van-popup>

            <van-popup  v-model="showInfoList" position="bottom">
                <van-picker :columns="infoCenter" @cancel="onInfoCancel" @confirm="onInfoConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
            </van-popup> -->

                        <!-- <van-field label="加料工" :value="itemParams.productiveTime" v-if="type=='3'" readonly/>
            <van-field label="灌装时间" :value="itemParams.productiveTime" v-if="type=='3'" readonly/> -->

                        <!-- <van-field label="使用情况" :value="itemParams.gbLeader" v-if="type=='2'" readonly/>
            <van-field label="结余重量(KG)" :value="itemParams.gbLeader" v-if="type=='2'" readonly/>
            <van-field label="是否留用" :value="itemParams.gbLeader" v-if="type=='2'" readonly/>
            <van-field label="工作中心" :value="itemParams.gbLeader" v-if="type=='2'" readonly/> -->

                        <!-- <div style="width:100%;margin-top:50px;">
                <van-button type="primary" @click="submit()" :loading="loading"  v-if="itemParams.tankType!='3' && itemParams.pullTimes=='0' && itemParams.realProductStatus=='2' && clickFlag"  style="margin:5px;width:40%;border-radius:10px;">拉料</van-button>
                <van-button type="primary" @click="submit()" :loading="loading"  v-if="itemParams.tankType=='3' && itemParams.realProductStatus=='2' && clickFlag"  style="margin:5px;width:40%;border-radius:10px;">拉料</van-button>
                <van-button type="danger" @click="updateToMitosome()"  v-if="itemParams.pullTimes>0 && itemParams.infoList[0].status=='1'"  style="margin:5px;width:40%;border-radius:10px;">修改</van-button>
                <van-button type="info" @click="reback()"  v-if="itemParams.pullTimes>0" style="margin:5px;width:40%;border-radius:10px;">退桶</van-button>
            </div>

            <div style="width:100%;margin-top:50px;" v-if="type=='3' && itemParams.realProductStatus=='2' && itemParams.pullTimes!='0' && itemParams.acceptUser==null">
                <van-button type="primary" @click="pushColloid()" :loading="pushLoading"   style="margin:5px;width:40%;border-radius:10px;">确认加料</van-button>
            </div> -->


                        <van-popup v-model="popup_show" round close-icon="close" style="height:75%;width:90%;">
                            <div style="margin-top:10%;width:90%;margin-left:5%" v-for="(item,index) in infoCenter"
                                :key="index" @click="selectPullItem(index)">
                                <div class="step">
                                    <h3 class="step_h">{{item.pullTime}}_{{item.pullUser}}</h3>
                                    <p class="step_p" v-if="item.id">{{item.id}}</p>
                                    <p class="step_p" v-if="item.jobCenter">{{item.jobCenter}}</p>
                                </div>
                            </div>
                        </van-popup>



                    </div>
                    <div v-else>
                        二维码已失效，请退出后重新扫描！
                    </div>
                </div>
            </van-tab>
            <van-tab title="移动储罐洗消状态标识卡" v-if="itemParams.tankType == '1'">
                <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0"></div>
                <van-field label="清洗消毒时间" :value="deviceInfo.recCleanTime" readonly />
                <van-field label="空置有效期" :value="deviceInfo.recFreeTime" readonly />
                <van-field label="第一次装料日期" :value="deviceInfo.recChargeTime" readonly />
                <van-field label="连续使用有效期" :value="deviceInfo.recUseTime" readonly />
                <van-field label="操作人" :value="deviceInfo.recCleanName" readonly />
                <van-field label="复核人" :value="deviceInfo.recCheckName" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 1" value="闲置" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 2" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 3" value="使用中" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 4" value="待维修" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 5" value="待清洗" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 6" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 7" value="待复核" readonly />
            </van-tab>

        </van-tabs>

    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx=window.wx
let moItem={}
let previousRouterName = "";
let result = "";
let cgIndex = "";
let type = "";
let back="";
export default {
    data(){
        return{
            result:'',
            info:{},
            type:'',
            clickFlag:true,
            itemParams:{},
            infoCenter:[],
            workShopList:[],
            jobCenterList:[],
            gcTankUsageInfos:[],
            showWorkshop:false,
            showInfoList:false,
            showJobCenter:false,
            loading:false,
            pushLoading:false,
            popup_show:false,
            back:'',
            title1:'',
            title2:'',
            title3:'',
            mitosomeColloidStatus:'',
            llBook:'',
            llWorkshop:'',
            llJobcenter:'',
            cgIndex:'',
            mitosomeColloidId:'',
            moItem:{},
            
            active:'',
            deviceInfo:{},
        }
    },
    components:{
        DatetimePicker
    },
    beforeRouteEnter(to, from, next) {
        previousRouterName = from.name;
        if (from.name === "MoSelectInfo") {
            moItem = from.params.moItem;
            result = from.params.result;
            type = from.params.type;
            cgIndex=from.params.cgIndex;
        }
        if (from.name === "RebackColloidInfo") {
            back=from.params.back;
        }
        next();
    },
    created:function(){
        this.result=this.$route.params.id

        if (previousRouterName === "RebackColloidInfo") {
            this.back=back
        }

        if(this.back=='1'){
            this.$router.back();
        }

    
        if (previousRouterName === "MoSelectInfo") {
            this.moItem=moItem
            this.result=result
            this.type=type
            this.cgIndex=cgIndex
        }

        this.info=this.result.split("&");


        this.getDeviceInfo(this.info[0]);
        this.getColloidInfo();
    },
    methods: {
        onJobCenterCancel(){
            this.showJobCenter=false
        },
        onJobCenterConfirm(value){
            this.itemParams.newJobCenter=value
            this.onJobCenterCancel();
        },
        onInfoConfirm(value,index){
            console.log(value)
            console.log(index)
            this.itemParams.pullUser=this.itemParams.infoList[index].pullUser
            this.itemParams.pullTime=this.itemParams.infoList[index].pullTime
            this.itemParams.gbWorkshop=this.itemParams.infoList[index].workshop
            this.itemParams.gbJobCenter=this.itemParams.infoList[index].jobCenter
            this.mitosomeColloidStatus=this.itemParams.infoList[index].status
            this.mitosomeColloidId=this.itemParams.infoList[index].id
            this.cgIndex=index;
            console.log(this.itemParams)
            this.onInfoCancel();
            // this.$forceUpdate();
        },
        onInfoCancel(){
            this.showInfoList=false
        },
        inputOutput(e){
            let self=this;
            self.itemParams.cxOutput=0
        },
        changeOutput(value){
            let self=this;
            var vcxMinOutput=value*self.itemParams.exchange;
            self.itemParams.cxMinOutput=parseInt(vcxMinOutput)
        },
        changeMinOutput(value){
            let self=this;
            var vcxOutput=value/self.itemParams.exchange;
            if(vcxOutput==parseInt(vcxOutput)){
                vcxOutput = parseInt(vcxOutput);
            }else{
                vcxOutput = vcxOutput.toFixed(3)
            }
            self.itemParams.cxOutput=vcxOutput
        },
        getMoInfo(){
            let self=this;
            self.$router.push({name:"MoSelectInfo",params:{jobCenter:self.itemParams.gbJobCenter,code:self.itemParams.productNo,result:self.result,type:self.type,volume:self.itemParams.volume,cgIndex:self.cgIndex}})
        },
        selectPullItem(index){
            let self = this
            self.itemParams.pullUser=self.itemParams.infoList[index].pullUser
            self.itemParams.pullTime=self.itemParams.infoList[index].pullTime
            self.itemParams.gbWorkshop=self.itemParams.infoList[index].workshop
            self.itemParams.gbJobCenter=self.itemParams.infoList[index].jobCenter
            self.mitosomeColloidStatus=self.itemParams.infoList[index].status
            self.mitosomeColloidId=self.itemParams.infoList[index].id
            self.itemParams.acceptUser=self.itemParams.infoList[index].acceptUser
            self.cgIndex=index;

            self.popup_show = false;
        },
        cancelWareHouseIn(){
            let self=this;

            MessageBox.confirm('',{
                message: '请确认是否撤销入库？',
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        
                        self.$axios.get('/jeecg-boot/app/sticker/cancelWareHouseIn',{params:{id:self.result,userCode:localStorage.getItem('userCode')}}).then(res=>{
                            if(res.data.code==200){
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                        position: 'bottom',
                                        duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });


            
        },
        pushColloid(){
            let self=this;
            let moId=this.moItem.moId?this.moItem.moId:this.itemParams.infoList[0].moId;
            console.log(moId)

            if(moId==null || moId==''){
                Toast({
                    message: "mo单号不得为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }

            MessageBox.confirm('',{
                message: '请确认是否将此胶体加入至'+moId+'中？',
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.addColloidToMo();
                    }
            }).catch((res)=>{
                            
            });
        },
        addColloidToMo(){
            let self=this
            
            let params={
                acceptUser:localStorage.getItem('userCode'),
                gbLpId:self.moItem.lpId,
                moId:self.moItem.moId,
                gbLeaderNo:self.moItem.leaderNo,
                colloidStatus:self.itemParams.realProductStatus
            }
            if(self.itemParams.tankType=='3'){
                params.id=self.mitosomeColloidId
            }else{
                params.id=self.itemParams.infoList[0].id
            }

            self.pushLoading=true
            self.$axios.post('/jeecg-boot/app/tank/check/pushColloid',params).then(res=>{
                if(res.data.code==200){
                    self.pushLoading=false
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    self.pushLoading=false
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })


        },
        submit(){
            //拉料提交
            let self=this
            self.loading=true
            //判断是否有记录
            self.$axios.get('/jeecg-boot/app/tank/check/checkTankMitosome',{params:{tankId:self.itemParams.tankId,jobCenter:self.llJobcenter}}).then(res=>{
                if(res.data.code==200){
                    let result=res.data.result;

                    if(result>0){
                        //存在记录
                        Toast({
                            message: "此数据已存在，请勿重复拉取！",
                            position: 'bottom',
                            duration: 2000
                        });
                        self.loading=false
                        return;
                    }else{
                        //不存在记录
                        self.sendToMitosome();
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false
                }
            })
        },
        sendToMitosome(){
            let self=this;
            let params={
                tankId:self.itemParams.tankId,
                tankNo:self.itemParams.tankNo,
                tankType:self.itemParams.tankType,
                colloidLpId:self.itemParams.lpId,
                code:self.itemParams.productNo,
                name:self.itemParams.productName,
                volume:self.itemParams.volume,
                receiver:localStorage.getItem('userCode'),
                customer:self.itemParams.customerId,
                workshop:self.llWorkshop,
                jobCenter:self.llJobcenter,
            }
            self.$axios.post('/jeecg-boot/app/tank/check/sendToMitosome',params).then(res=>{
                if(res.data.code==200){
                    self.loading=false
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.clickFlag=true;
                    self.loading=false;
                    self.$router.back()
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.clickFlag=true;
                    self.loading=false;
                }
            })
        },
        updateToMitosome(){
            let self=this;
            let params={
                id:this.itemParams.infoList[0].id,
                jobCenter:self.itemParams.gbJobCenter,
                newJobCenter:self.itemParams.newJobCenter,
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/tank/check/updateToMitosome',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.back()
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        saveOrUpdateMsg(tankType){
            let self=this;
            let msg="";
            if(tankType=='3'){
                msg="该储罐已存在一条记录，是否继续添加？";
                MessageBox.confirm('',{
                    message: msg,
                    title: '提示',
                    confirmButtonText: '添加',
                    cancelButtonText: '更改'
                })
                .then(action => {
                    if(action=="confirm"){
                        //添加
                        self.sendToMitosome();
                    }
                }).catch((res)=>{
                    if(res=="cancel"){
                        //更改
                        self.updateToMitosome();
                    }
                });

            }else{
                msg="该青桶/坦克已存在一条记录，是否修改线体？";
                MessageBox.confirm(msg).then(action => {
                    if(action=="confirm"){
                        self.updateToMitosome();
                    }
                })
            }

        },
        reback(){
            let self=this;
            //拉料退桶
            if(self.itemParams.tankType=='3'){
                // params.id=self.mitosomeColloidId
                self.$router.push({name:"RebackColloidInfo",params:{result:self.result,workshop:self.itemParams.gbWorkshop,id:self.mitosomeColloidId,status:self.mitosomeColloidStatus}})
            }else{
                self.$router.push({name:"RebackColloidInfo",params:{result:self.result,workshop:self.itemParams.gbWorkshop,id:self.itemParams.infoList[0].id,status:self.mitosomeColloidStatus}})
            }
            
        },
        check(num){
            let self=this;

            let params={
                tankId:this.info[0],
                code:self.itemParams.productNo,
                name:self.itemParams.productName,
                customer:self.itemParams.customerId,
                status:num,
                checker:localStorage.getItem('userCode')
            }

            var message="是否确认将此胶体设为合格？";
            if(num=='3'){
                message="是否确认将此胶体设为不合格？";
            }else if(num=='0'){
                message="是否确认将此胶体锁定？";
            }

            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/tank/check/checkColloidInfo',params).then(res=>{
                            if(res.data.code==200){
                                self.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });
        },
        getJobCenter(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getJobCenterByShop',{params:{workshop:self.itemParams.gbWorkshop}}).then(res=>{
                if(res.data.code==200){
                    self.jobCenterList=res.data.result
                }
            })
        },
        getDeviceInfo(id){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcTankInfo/getDeviceInfo',{params:{id:id}}).then(res=>{
                if(res.data.code==200){
                    self.deviceInfo=res.data.result
                }
            })
        },
        getColloidInfo(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getColloidInfo',{params:{id:self.info[0],type:'4'}}).then(res=>{
                if(res.data.code==200){
                    self.itemParams=res.data.result
                    console.log(self.itemParams)
                    if(self.type=='2'){
                        self.getJobCenter();
                    }
                    if(self.itemParams.infoList.length>1){
                        // for(var i=0;i<self.itemParams.infoList.length;i++){
                        //     self.infoCenter.push(self.itemParams.infoList[i].jobCenter)
                        // }

                        self.infoCenter=self.itemParams.infoList

                        if(self.itemParams.tankType==="3"){
                            if(self.cgIndex === ''){
                                self.popup_show = true;
                            }else{
                                self.itemParams.pullUser=self.itemParams.infoList[self.cgIndex].pullUser
                                self.itemParams.pullTime=self.itemParams.infoList[self.cgIndex].pullTime
                                self.itemParams.gbWorkshop=self.itemParams.infoList[self.cgIndex].workshop
                                self.itemParams.gbJobCenter=self.itemParams.infoList[self.cgIndex].jobCenter
                                self.mitosomeColloidStatus=self.itemParams.infoList[self.cgIndex].status
                                self.mitosomeColloidId=self.itemParams.infoList[self.cgIndex].id
                                self.itemParams.acceptUser=self.itemParams.infoList[self.cgIndex].acceptUser
                            }
                            
                        }

                    }else if(self.itemParams.infoList.length==1){
                        self.mitosomeColloidStatus=self.itemParams.infoList[0].status
                        self.mitosomeColloidId=self.itemParams.infoList[0].id
                    }
                }else{
                     Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sign{
    width:100%;
    height:5rem;
}
.sign-type{
    height:100%;
    width:100%;
    color:white;
    display: flex;
    align-items: center;
    justify-content: center;
}
.step {
  background: #39b54a;
  width: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  height: auto;
  color: #fff;
  border-radius: 5px;
}
.step_h{
    padding: 10px;
    margin: 0;
}
.step_p{
    margin: 0;
    padding: 10px;
    padding-top: 0;
}
/deep/.van-field__control{
    color: black;
}
/deep/.van-field__label{
    color: #777777;
}
</style>