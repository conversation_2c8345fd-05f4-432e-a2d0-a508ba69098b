<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入备注" v-decorator="['comments',{}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import pick from 'lodash.pick'
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "CloseNewModal",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        moIds:[],
        expandedRowKeys: [],
        id: ' ',
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },

      edit(item) {  
        this.visible = true;
        item.completionRate="100"
        this.item=item
        this.orderMainModel = Object.assign({}, item);
        console.log("orderMainModel:"+this.orderMainModel)
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.orderMainModel, 'completionRate'))
        });
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        let self=this
        this.form.validateFields((err, values) => {
          if (!err) {
            self.confirmLoading = true;
            self.item.comments=self.form.getFieldValue("comments")
            console.log(self.item)
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew',self.item).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                this.visible=false;
                this.$emit('ok');
                // this.$router.push({name:"OrderClose",params:res.data.result});
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            }).finally(() => {
                  self.confirmLoading = false;
                  self.close();
            });
          }
        });
        
      },
      handleCancel() {
        this.close()
      },
      // 验证数字
      validateNum(rule, value, callback) {
        if (!value || new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/).test(value)) {
          callback();
        } else {
          callback("请输入数字，小数请保留2位!");
        }
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>