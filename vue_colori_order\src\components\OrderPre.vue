<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_order_title">预备工作单</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div>
        </div>

        <div v-for="(item,index) in leadAppInfo" :key="index" class="report_item">
            <div class="report_mo" >{{item.lpId}}</div>
            <div class="report_line"></div>
            <div class="report_item_text">
                <div class="item_left">开线时间</div>
                <div class="item_right">{{item.begin}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">关线时间</div>
                <div class="item_right">{{item.end}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">人数</div>
                <div class="item_right">{{item.pnum}}</div>
            </div>
            <div class="report_line"></div>
            <!-- <div class="bottom_button2" v-if="item.spNo">已推送</div> -->
            <div class="bottom_button" @click="checkSelected(item)">选择</div>
        </div>

        <update-modal ref="modalForms" @ok="modalFormOk"></update-modal>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import UpdateModal from './list/UpdateModal.vue';
export default {
  components: { UpdateModal },
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal:'',
            minDate:'',
            maxDate:'',
            date:'',
            category:'',
            userName:'',
            userCode:'',
            popupVisible:false,
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            questionType:'',
            item:{},
            id:'',
            c_show:false,
            questionTypeVal:'',
            popupVisible:false,
            leadAppInfo:[],
            popupSlots:[
                {
                    values:[
                        '全部','白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 15 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.item=this.$route.params
        this.id=this.item.id
        this.getPrepareOrder()
    },
    methods:{
        getPrepareOrder(){
            let self=this;
            // let workDay='2021-07-31'
            let workDay=this.formatDate(new Date())
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getPrepareOrder',{params:{workDay:workDay}}).then(res=>{
                if(res.data.code==200){
                    self.leadAppInfo=res.data.result
                }
            })
        },
        checkSelected(item){
            let self=this
            MessageBox.confirm('是否确认选择此单关联？').then(action => {
                if(action=="confirm"){
                    self.selectYbd(item)
                }
            })
        },
        selectYbd(item){
            let self=this;
            let num='9';
            if(self.item.id=='' || self.item.id==null){
                Toast({
                    message: '切勿刷新，请退回上一页后重新进入',
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let params={
                lpId:self.item.id,
                laId:item.laId,
                type:num,
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName')
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeploy',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })

        },
        modalFormOk(){
            this.getPrepareOrder()
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getPrepareOrder()
        },
        updateCount(item){
            let self=this;
            self.$refs.modalForms.edit(item);
            self.$refs.modalForms.title="产量更改";
            self.$refs.modalForms.disableSubmit = true;
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType(){
            this.popupVisible = true;
        },
        dateConfirm(value){
            this.newSelectedValue=this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getPrepareOrder()
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getLeadAppInfo()
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    padding: 5%;
    font-size: 1rem;
    width: 50%;
    background: #fff;
    margin: 0 auto;
    margin-top: 5%;
    border-radius: 10px;
}
.report_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.report_line{
    background: #cfcfcf;
    height: 1px;
}
.report_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.report_item{
    width: 90%;
    border-radius: 10px;
    margin: 0 auto;
    margin-bottom: 20px;
    background: #fff;
}
.bottom_button{
    padding: 5%;
    color: #ff0000;
    font-size: 1rem;
    font-weight: 600;
}
.bottom_button2{
    padding: 5%;
    color: chartreuse;
    font-size: 1rem;
    font-weight: 600;
}
.ycl-style{
    color: crimson;
}
</style>