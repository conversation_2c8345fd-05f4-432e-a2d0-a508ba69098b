import Vue from "vue";
import Router from "vue-router";
import Login from "@/components/Login";
import Home from "@/components/Home";
import Car from "@/components/Car";
import CarDetail from "@/components/CarDetail";
import CarStart from "@/components/CarStart";
import CarFinish from "@/components/CarFinish";
import OrderPlat from "@/components/OrderPlat";
import AfricaOrderPlat from "@/components/AfricaOrderPlat";
import AfricaOrderReport from "@/components/AfricaOrderReport";
import OrderStart from "@/components/OrderStart";
import OrderOther from "@/components/OrderOther";
import OrderUser from "@/components/OrderUser";
import AfricaOrderStart from "@/components/AfricaOrderStart";
import OrderClose from "@/components/OrderClose";
import CarOdo from "@/components/CarOdo";
import Schedual from "@/components/Schedual";
import SchedualDetail from "@/components/SchedualDetail";
import SchedualUser from "@/components/SchedualUser";
import Attence from "@/components/Attence";
import AddPl from "@/components/AddPl";
import ProductData from "@/components/ProductData";
import OrderReport from "@/components/OrderReport";
import MyHour from "@/components/MyHour";
import PoolList from "@/components/PoolList";
import PoolUser from "@/components/PoolUser";
import OrderPre from "@/components/OrderPre";
import OrderUpdate from "@/components/OrderUpdate";
import CheckSpNo from "@/components/CheckSpNo";
import ScanInCheck from "@/components/ScanInCheck";
import ProductControl from "@/components/ProductControl";
import WorkBegin from "@/components/WorkBegin";
import WorkDuring from "@/components/WorkDuring";
import WorkPhoto from "@/components/WorkPhoto";
import WorkStart from "@/components/WorkStart";
import EvalSuccess from "@/components/EvalSuccess";
import EvalSuccess2 from "@/components/EvalSuccess2";
import EvalFail from "@/components/EvalFail";
import EvalResult from "@/components/EvalResult";
import WorkScan from "@/components/WorkScan";
import BottledWater from "@/components/BottledWater";
import SelectModel from "@/components/SelectModel";
import SelectModelForQa from "@/components/SelectModelForQa";
import OrderMenu from "@/components/OrderMenu";
import CheckCodeInfo from "@/components/CheckCodeInfo";
import GcodeManage from "@/components/GcodeManage";
import WorkBlueTootch from "@/components/WorkBlueTootch";
import CargoPerch from "@/components/CargoPerch";
import WarehouseIn from "@/components/WarehouseIn";
import Scgzb from "@/components/Scgzb";
import BeginRecord from "@/components/BeginRecord";
import HourReport from "@/components/HourReport";
import HourReportUp from "@/components/HourReportUp";
import WorkForQa from "@/components/WorkForQa";
import WorkDuringForQa from "@/components/WorkDuringForQa";
import UpdateForQa from "@/components/UpdateForQa";
import CheckForQa from "@/components/CheckForQa";
import LoginIndex from "@/components/LoginIndex";
import OrderInfo from "@/components/OrderInfo";
import Binding from "@/components/Binding";
import OrderInfoDetail from "@/components/OrderInfoDetail";
import WorkInfo from "@/components/WorkInfo";
import WorkInfoDetail from "@/components/WorkInfoDetail";
import JobCenterInfo from "@/components/JobCenterInfo";
import MaterialPurchase from "@/components/MaterialPurchase";
import OrderInfoMaterial from "@/components/OrderInfoMaterial";
import PmMain from "@/components/PmMain";
import PmCheck from "@/components/PmCheck";
import SpotMain from "@/components/SpotMain";
import SpotCheck from "@/components/SpotCheck";
import StoreSticker from "@/components/StoreSticker";
import General from "@/components/General";
import GeneralMain from "@/components/GeneralMain";
import Success from "@/components/Success";
import HourTank from "@/components/HourTank";
import Jlbmb from "@/components/Jlbmb";
import EmpMenu from "@/components/EmpMenu";
import FinishOrder from "@/components/FinishOrder";
import rosterStaff from "@/components/rosterStaff";
import rosterStaffDetail from "@/components/rosterStaffDetail";
import ScanResult from "@/components/ScanResult";
import RepairOrder from "@/components/RepairOrder";
import Repair from "@/components/Repair";
import RepairDetail1 from "@/components/RepairDetail1";
import RepairAdd from "@/components/RepairAdd";
import RepairDetail2 from "@/components/RepairDetail2";
import OverTimeReport from "@/components/OverTimeReport";
import OverTimeDetail from "@/components/OverTimeDetail";
import StickerList from "@/components/StickerList";
import TankConsume from "@/components/TankConsume";
import RepairOrderSee from "@/components/RepairOrderSee";
import RepairDetail1See from "@/components/RepairDetail1See";
import RepairDetail2See from "@/components/RepairDetail2See";
import ScanWareInResult from "@/components/ScanWareInResult";
import SparePart from "@/components/SparePart";
import SpareList from "@/components/SpareList";
import SpareListDetail from "@/components/SpareListDetail";
import SpareOut from "@/components/SpareOut";
import SpareOutScan from "@/components/SpareOutScan";
import SpareSetup from "@/components/SpareSetup";
import SpareSetupScan from "@/components/SpareSetupScan";
import SpareExit from "@/components/SpareExit";
import SpareExitScan from "@/components/SpareExitScan";
import SpareCheck from "@/components/SpareCheck";
import SpareCheckDetail from "@/components/SpareCheckDetail";
import SpareCheckDetailEdit from "@/components/SpareCheckDetailEdit";
import ModuleInfo from "@/components/ModuleInfo";
import ModuleInfoDetail from "@/components/ModuleInfoDetail";
import ModuleReceive from "@/components/ModuleReceive";
import ModuleBatchReceive from "@/components/ModuleBatchReceive";
import ModuleReturn from "@/components/ModuleReturn";
import ModuleBorrow from "@/components/ModuleBorrow";
import ModuleBorrowList from "@/components/ModuleBorrowList";
import ModuleStorage from "@/components/ModuleStorage";
import ModuleOperation from "@/components/ModuleOperation";
import ModuleStorageList from "@/components/ModuleStorageList";
import ModuleReturnList from "@/components/ModuleReturnList";
import ModuleReceiveList from "@/components/ModuleReceiveList";
import ModuleCheck from "@/components/ModuleCheck";
import ModuleCheckDetail from "@/components/ModuleCheckDetail";
import ModuleCheckDetailEdit from "@/components/ModuleCheckDetailEdit";
import ModuleReceiveReturn from "@/components/ModuleReceiveReturn";
import ModuleDestroy from "@/components/ModuleDestroy";
import ModuleDestroyList from "@/components/ModuleDestroyList";
import EquipmentScheduling from "@/components/EquipmentScheduling";
import EquipmentSchedulingAdd from "@/components/EquipmentSchedulingAdd";
import EquipmentSchedulingEdit from "@/components/EquipmentSchedulingEdit";
import GoodsOutbound from "@/components/GoodsOutbound";
import GoodsLocation from "@/components/GoodsLocation";
import GoodsPick from "@/components/GoodsPick";
import GoodsLocationDetail from "@/components/GoodsLocationDetail";
import GoodsLocationAdjust from "@/components/GoodsLocationAdjust";
import GoodsPickDetail from "@/components/GoodsPickDetail";
import GoodsPickDetailNo from "@/components/GoodsPickDetailNo";
import GoodsPickDetailYes from "@/components/GoodsPickDetailYes";
import GoodsPickCheck from "@/components/GoodsPickCheck";
import GoodsPickOverDetail from "@/components/GoodsPickOverDetail";
import GoodsAssigned from "@/components/GoodsAssigned";
import GoodsCheck from "@/components/GoodsCheck";
import GoodsPickOver from "@/components/GoodsPickOver";
import GoodsAssignedDetail from "@/components/GoodsAssignedDetail";
import GoodsDetail from "@/components/GoodsDetail";
import GoodsPickTurn from "@/components/GoodsPickTurn";
import Feeding from "@/components/Feeding";
import FeedingCheck from "@/components/FeedingCheck";
import FeedingCheck1 from "@/components/FeedingCheck1";
import FeedingCheck2 from "@/components/FeedingCheck2";
import FeedingCheck3 from "@/components/FeedingCheck3";
import FeedingCheck4 from "@/components/FeedingCheck4";
import FeedingCheck5 from "@/components/FeedingCheck5";
import FeedingCheck6 from "@/components/FeedingCheck6";
import FeedingCheck7 from "@/components/FeedingCheck7";
import FeedingCheck8 from "@/components/FeedingCheck8";
import FeedingCheck9 from "@/components/FeedingCheck9";
import FeedingCheckDetail1 from "@/components/FeedingCheckDetail1";
import FeedingCheckDetail2 from "@/components/FeedingCheckDetail2";
import FeedingCheckDetail3 from "@/components/FeedingCheckDetail3";
import FeedingCheckDetail4 from "@/components/FeedingCheckDetail4";
import FeedingCheckDetail5 from "@/components/FeedingCheckDetail5";
import FeedingCheckDetail9 from "@/components/FeedingCheckDetail9";
import ScanColloidInfo from "@/components/ScanColloidInfo";
import CheckEvaluationInfo from "@/components/CheckEvaluationInfo"
import ColloidUsageInfo from "@/components/ColloidUsageInfo";
import ColloidPull from "@/components/ColloidPull";
import ColloidPush from "@/components/ColloidPush";
import ColloidCheck from "@/components/ColloidCheck";
import MoSelectInfo from "@/components/MoSelectInfo";
import RebackColloidInfo from "@/components/RebackColloidInfo";
import GbUsageInfo from "@/components/GbUsageInfo";
import MoUsageSelect from "@/components/MoUsageSelect";
import ColloidInfo from "@/components/ColloidInfo";
import ProductionTraceability from "@/components/ProductionTraceability";
import ProductionTraceabilityDetail from "@/components/ProductionTraceabilityDetail";
import PowerSetting from "@/components/PowerSetting";
import AttenceRecord from "@/components/AttenceRecord";
import KeyInfo from "@/components/KeyInfo";
import MaterialsWeighing from "@/components/MaterialsWeighing";
import MaterialsWeighingForm from "@/components/MaterialsWeighingForm";
import MaterialsWeighingLabel from "@/components/MaterialsWeighingLabel";
import UpdatePwd from "@/components/UpdatePwd";
import MixtureSelect from "@/components/MixtureSelect";
import OrderMixture from "@/components/OrderMixture";
import IdIcInfo from "@/components/IdIcInfo";
import BatchNoToCode from "@/components/BatchNoToCode";
import batchRecord from "@/components/batchRecord";
import batchRecordcheck1 from "@/components/batchRecordcheck1";
import batchRecordcheck1detail from "@/components/batchRecordcheck1detail";
import batchOptionrecord from "@/components/batchOptionrecord";
import batchOptionrecordDetail from "@/components/batchOptionrecordDetail";
import batchHACCP from "@/components/batchHACCP";
import batchHACCPdetail from "@/components/batchHACCPdetail";
import BatchQualityControl from "@/components/BatchQualityControl";
import BatchQualityControlDetail from "@/components/BatchQualityControlDetail";
import batchBeforeCheck from "@/components/batchBeforeCheck";
import batchBeforeCheckDetail from "@/components/batchBeforeCheckDetail";
import DormRepairManage from '@/components/DormRepairManage'
import DormRepair from '@/components/DormRepair'
import AttenceInfo from '@/components/AttenceInfo'
import DormRepairDetail from '@/components/DormRepairDetail'
import DormRepairAdd from '@/components/DormRepairAdd'
import DormAdvice from '@/components/DormAdvice'
import DormAdviceReply from '@/components/DormAdviceReply'
import DormHygieneManage from '@/components/DormHygieneManage'
import DormHygieneAdd from '@/components/DormHygieneAdd'
import DormHygieneBulletin from '@/components/DormHygieneBulletin'
import DormWaterPowerManage from '@/components/DormWaterPowerManage'
import DormWaterPowerAdd from '@/components/DormWaterPowerAdd'
import DmBill from '@/components/DmBill'
import CheckBeforeProduction from '@/components/checkBeforeProduction'
import batchAbnormalRecord from "@/components/batchAbnormalRecord";
import batchAbnormalRecordDetail from "@/components/batchAbnormalRecordDetail";
import TankInfo from "@/components/TankInfo"
import ScanTankOptInfo from "@/components/ScanTankOptInfo"
import ApplyPrintPermission from "@/components/ApplyPrintPermission"
import DmChooseEqu from "@/components/DmChooseEqu"
import DormRepairComplate from "@/components/DormRepairComplate"
import pugong from "@/components/pugong"
import EvaluationCheck from "@/components/EvaluationCheck"
import success from "@/components/Success"
import SuitFeedback from "@/components/SuitFeedback"
import PostRoutine from "@/components/postRoutine"
import postRoutineDetail from "@/components/postRoutineDetail"
import postRoutineDetailSee from "@/components/postRoutineDetailSee"
import eqImprovement from "@/components/eqImprovement"
import eqImprovementDetail from "@/components/eqImprovementDetail"
import eqImprovementAdd from "@/components/eqImprovementAdd"
import eqImprovementSetup from "@/components/eqImprovementSetup"
import FeedingCheckErrorPage from "@/components/FeedingCheckErrorPage"
import FeedingCheckErrorList from "@/components/FeedingCheckErrorList"
import emd016List from "@/components/emd016List"
import emd021List from "@/components/emd021List"
import emd016add from "@/components/emd016add"
import emd021Add from "@/components/emd021Add"
import emd016ToEmd021 from "@/components/emd016ToEmd021"
import emd021Detail from "@/components/emd021Detail"
import packagingMaterialCheck from "@/components/packagingMaterialCheck"
import packagingMaterialCheckDetail from "@/components/packagingMaterialCheckDetail"
import packaging from "@/components/packaging"
import packagingMaterialBackApply from "@/components/packagingMaterialBackApply"
import packagingMaterialBackApplyDetail from "@/components/packagingMaterialBackApplyDetail"
import InductionTrain from "@/components/InductionTrain"
import DeptTrain from "@/components/DeptTrain"
import deviceClean from "@/components/deviceClean"
import WorkDuringEdit from "@/components/WorkDuringEdit"
import equiSpotCheck from "@/components/equiSpotCheck"
import Communication from "@/components/Communication"
import equiSpotLogin from "@/components/equiSpotLogin"
import emd022List from "@/components/emd022List"
import emd022Add from "@/components/emd022Add"
import emd022Detail from "@/components/emd022Detail"
import AttenceFzInfo from "@/components/AttenceFzInfo"
import LeadBatch from "@/components/LeadBatch"
import kpiMettle from "@/components/kpiMettle"
import equiMenu from "@/components/equiMenu"
import ProcessSpnoInfo from "@/components/ProcessSpnoInfo"
import WorkLineInfo from "@/components/WorkLineInfo"
import WorklineSuccess from "@/components/WorklineSuccess"
import StopLineInfo from "@/components/StopLineInfo"
import packagingMaterialBack from "@/components/packagingMaterialBack"
import packagingMaterialBackDetail from "@/components/packagingMaterialBackDetail"
import pceForm from "@/components/pceForm"
import equiDianjian from "@/components/equiDianjian"
import powerSettingMenu from "@/components/powerSettingMenu"
import equipmentInspectionList from "@/components/equipmentInspectionList"
import equipmentInspectionAdd from "@/components/equipmentInspectionAdd"
import WmsUsageLoss from "@/components/WmsUsageLoss"
import OrderPlatNew from "@/components/OrderPlatNew"
import groupLeaderScheduling from "@/components/groupLeaderScheduling"
import ordersReceived from "@/components/ordersReceived"


const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

Vue.use(Router);

export default new Router({
  routes: [
    {
      path: "/",
      name: "Login",
      component: Login
    },
    {
      path: "/home",
      name: "Home",
      component: Home
    },
    {
      path: "/car",
      name: "Car",
      component: Car
    },
    {
      path: "/carDetail",
      name: "CarDetail",
      component: CarDetail
    },
    {
      path: "/carStart",
      name: "CarStart",
      component: CarStart
    },
    {
      path: "/wmsUsageLoss",
      name: "WmsUsageLoss",
      component: WmsUsageLoss
    },
    {
      path: "/carFinish",
      name: "CarFinish",
      component: CarFinish
    },
    {
      path: "/orderPlat",
      name: "OrderPlat",
      component: OrderPlat
    },
    {
      path: "/africaOrderPlat",
      name: "AfricaOrderPlat",
      component: AfricaOrderPlat
    },
    {
      path: "/orderStart",
      name: "OrderStart",
      component: OrderStart
    },
    {
      path: "/orderOther",
      name: "OrderOther",
      component: OrderOther
    },
    {
      path: "/scanTankOptInfo",
      name: "ScanTankOptInfo",
      component: ScanTankOptInfo
    },
    {
      path: "/applyPrintPermission",
      name: "ApplyPrintPermission",
      component: ApplyPrintPermission
    },
    {
      path: "/africaOrderReport",
      name: "AfricaOrderReport",
      component: AfricaOrderReport
    },
    {
      path: "/orderUser",
      name: "OrderUser",
      component: OrderUser
    },
    {
      path: "/africaOrderStart",
      name: "AfricaOrderStart",
      component: AfricaOrderStart
    },
    {
      path: "/orderMenu",
      name: "OrderMenu",
      component: OrderMenu
    },
    {
      path: "/orderInfo",
      name: "OrderInfo",
      component: OrderInfo
    },
    {
      path: "/orderInfoDetail",
      name: "OrderInfoDetail",
      component: OrderInfoDetail
    },
    {
      path: "/carOdo",
      name: "CarOdo",
      component: CarOdo
    },
    {
      path: "/checkCodeInfo",
      name: "CheckCodeInfo",
      component: CheckCodeInfo
    },
    {
      path: "/colloidInfo",
      name: "ColloidInfo",
      component: ColloidInfo
    },
    {
      path: "/orderClose",
      name: "OrderClose",
      component: OrderClose
    },
    {
      path:"/worklineSuccess",
      name:"WorklineSuccess",
      component:WorklineSuccess
    },
    {
      path: "/schedual",
      name: "Schedual",
      component: Schedual
    },
    {
      path: "/schedualDetail",
      name: "SchedualDetail",
      component: SchedualDetail
    },
    {
      path: "/schedualUser",
      name: "SchedualUser",
      component: SchedualUser
    },
    {
      path:"/stopLineInfo",
      name:"StopLineInfo",
      component:StopLineInfo
    },
    {
      path: "/attence",
      name: "Attence",
      component: Attence
    },
    {
      path: "/addPl",
      name: "AddPl",
      component: AddPl
    },
    {
      path: "/productData",
      name: "ProductData",
      component: ProductData
    },
    {
      path: "/orderUpdate",
      name: "OrderUpdate",
      component: OrderUpdate
    },
    {
      path: "/orderReport",
      name: "OrderReport",
      component: OrderReport
    },
    {
      path: "/myHour",
      name: "MyHour",
      component: MyHour
    },
    {
      path: "/poolList",
      name: "PoolList",
      component: PoolList
    },
    {
      path: "/poolUser",
      name: "PoolUser",
      component: PoolUser
    },
    {
      path: "/orderPre",
      name: "OrderPre",
      component: OrderPre
    },
    {
      path: "/leadBatch",
      name: "LeadBatch",
      component: LeadBatch
    },
    {
      path: "/checkSpNo",
      name: "CheckSpNo",
      component: CheckSpNo
    },
    {
      path: "/scanInCheck",
      name: "ScanInCheck",
      component: ScanInCheck
    },
    {
      path: "/productControl",
      name: "ProductControl",
      component: ProductControl
    },
    {
      path: "/workBegin",
      name: "WorkBegin",
      component: WorkBegin
    },
    {
      path: "/workScan",
      name: "WorkScan",
      component: WorkScan
    },
    {
      path: "/workDuring",
      name: "WorkDuring",
      component: WorkDuring
    },
    {
      path: "/workDuringForQa",
      name: "WorkDuringForQa",
      component: WorkDuringForQa
    },
    {
      path: "/updateForQa",
      name: "UpdateForQa",
      component: UpdateForQa
    },
    {
      path: "/workStart",
      name: "WorkStart",
      component: WorkStart
    },
    {
      path: "/workPhoto",
      name: "WorkPhoto",
      component: WorkPhoto
    },
    {
      path: "/bottledWater",
      name: "BottledWater",
      component: BottledWater
    },
    {
      path: "/selectModel",
      name: "SelectModel",
      component: SelectModel
    },
    {
      path: "/selectModelForQa",
      name: "SelectModelForQa",
      component: SelectModelForQa
    },
    {
      path: "/gcodeManage",
      name: "GcodeManage",
      component: GcodeManage
    },
    {
      path: "/workBlueTootch",
      name: "WorkBlueTootch",
      component: WorkBlueTootch
    },
    {
      path: "/cargoPerch",
      name: "CargoPerch",
      component: CargoPerch
    },
    {
      path: "/warehouseIn",
      name: "WarehouseIn",
      component: WarehouseIn
    },
    {
      path: "/scgzb",
      name: "Scgzb",
      component: Scgzb
    },
    {
      path: "/beginRecord",
      name: "BeginRecord",
      component: BeginRecord
    },
    {
      path: "/hourReport",
      name: "HourReport",
      component: HourReport
    },
    {
      path: "/hourReportUp",
      name: "HourReportUp",
      component: HourReportUp
    },
    {
      path: "/workForQa",
      name: "WorkForQa",
      component: WorkForQa
    },
    {
      path: "/checkForQa",
      name: "CheckForQa",
      component: CheckForQa
    },
    {
      path: "/loginIndex",
      name: "LoginIndex",
      component: LoginIndex
    },
    {
      path: "/workInfo",
      name: "WorkInfo",
      component: WorkInfo
    },
    {
      path: "/attenceInfo",
      name: "AttenceInfo",
      component: AttenceInfo
    },
    {
      path: "/workInfoDetail",
      name: "WorkInfoDetail",
      component: WorkInfoDetail
    },
    {
      path: "/jobCenterInfo",
      name: "JobCenterInfo",
      component: JobCenterInfo
    },
    {
      path: "/binding",
      name: "Binding",
      component: Binding
    },
    {
      path: "/materialPurchase",
      name: "MaterialPurchase",
      component: MaterialPurchase
    },
    {
      path: "/orderInfoMaterial",
      name: "OrderInfoMaterial",
      component: OrderInfoMaterial
    },
    {
      path: "/pmMain",
      name: "PmMain",
      component: PmMain
    },
    {
      path: "/pmCheck",
      name: "PmCheck",
      component: PmCheck
    },
    {
      path: "/spotMain",
      name: "SpotMain",
      component: SpotMain
    },
    {
      path: "/spotCheck",
      name: "SpotCheck",
      component: SpotCheck
    },
    {
      path: "/general",
      name: "General",
      component: General
    },
    {
      path: "/generalMain",
      name: "GeneralMain",
      component: GeneralMain
    },
    {
      path: "/success",
      name: "Success",
      component: Success
    },
    {
      path: "/hourTank",
      name: "HourTank",
      component: HourTank
    },
    {
      path: "/jlbmb",
      name: "Jlbmb",
      component: Jlbmb
    },
    {
      path: "/empMenu",
      name: "EmpMenu",
      component: EmpMenu
    },
    {
      path: "/finishOrder",
      name: "FinishOrder",
      component: FinishOrder
    },
    {
      path: "/rosterStaff",
      name: "rosterStaff",
      component: rosterStaff,
      meta: {
        keepAlive: true //此页面需要缓存
      }
    },
    {
      path:"/processSpnoInfo",
      name:"processSpnoInfo",
      component:ProcessSpnoInfo
    },
    {
      path: "/rosterStaffDetail",
      name: "rosterStaffDetail",
      component: rosterStaffDetail
    },
    {
      path:"/workLineInfo",
      name:"WorkLineInfo",
      component:WorkLineInfo
    },
    {
      path: "/scanResult",
      name: "ScanResult",
      component: ScanResult
    },
    {
      path: "/pugong",
      name: "pugong",
      component: pugong
    },
    {
      path: "/success",
      name: "success",
      component: success
    },
    {
      path: "/stickerList",
      name: "StickerList",
      component: StickerList
    },
    {
      path: "/storeSticker",
      name: "StoreSticker",
      component: StoreSticker
    },
    {
      path: "/repairOrder",
      name: "RepairOrder",
      component: RepairOrder
    },
    {
      path: "/repair",
      name: "Repair",
      component: Repair
    },
    {
      path: "/repairDetail1",
      name: "RepairDetail1",
      component: RepairDetail1
    },
    {
      path: "/repairAdd",
      name: "RepairAdd",
      component: RepairAdd
    },
    {
      path: "/repairDetail2",
      name: "RepairDetail2",
      component: RepairDetail2
    },
    {
      path: "/TankConsume",
      name: "TankConsume",
      component: TankConsume
    },
    {
      path: "/RepairOrderSee",
      name: "RepairOrderSee",
      component: RepairOrderSee
    },
    {
      path: "/RepairDetail1See",
      name: "RepairDetail1See",
      component: RepairDetail1See
    },
    {
      path: "/RepairDetail2See",
      name: "RepairDetail2See",
      component: RepairDetail2See
    },
    {
      path: "/overTimeReport",
      name: "OverTimeReport",
      component: OverTimeReport
    },
    {
      path: "/overTimeDetail",
      name: "OverTimeDetail",
      component: OverTimeDetail
    },
    {
      path: "/scanWareInResult",
      name: "ScanWareInResult",
      component: ScanWareInResult
    },
    {
      path: "/SparePart",
      name: "SparePart",
      component: SparePart
    },
    {
      path: "/SpareList",
      name: "SpareList",
      component: SpareList
    },
    {
      path: "/SpareListDetail",
      name: "SpareListDetail",
      component: SpareListDetail
    },
    {
      path: "/SpareOut",
      name: "SpareOut",
      component: SpareOut
    },
    {
      path: "/SpareOutScan",
      name: "SpareOutScan",
      component: SpareOutScan
    },
    {
      path: "/SpareSetup",
      name: "SpareSetup",
      component: SpareSetup
    },
    {
      path: "/SpareSetupScan",
      name: "SpareSetupScan",
      component: SpareSetupScan
    },
    {
      path: "/SpareExit",
      name: "SpareExit",
      component: SpareExit
    },
    {
      path: "/SpareExitScan",
      name: "SpareExitScan",
      component: SpareExitScan
    },
    {
      path: "/SpareCheck",
      name: "SpareCheck",
      component: SpareCheck
    },
    {
      path: "/SpareCheckDetail",
      name: "SpareCheckDetail",
      component: SpareCheckDetail
    },
    {
      path: "/SpareCheckDetailEdit",
      name: "SpareCheckDetailEdit",
      component: SpareCheckDetailEdit
    },
    {
      path: "/ModuleInfo",
      name: "ModuleInfo",
      component: ModuleInfo
    },
    {
      path: "/ModuleInfoDetail",
      name: "ModuleInfoDetail",
      component: ModuleInfoDetail
    },
    {
      path: "/ModuleReceive",
      name: "ModuleReceive",
      component: ModuleReceive
    },
    {
      path: "/ModuleBorrow",
      name: "ModuleBorrow",
      component: ModuleBorrow
    },
    {
      path: "/ModuleBorrowList",
      name: "ModuleBorrowList",
      component: ModuleBorrowList
    },
    {
      path: "/ModuleBatchReceive",
      name: "ModuleBatchReceive",
      component: ModuleBatchReceive
    },
    {
      path: "/ModuleReturn",
      name: "ModuleReturn",
      component: ModuleReturn
    },
    {
      path: "/ModuleStorage",
      name: "ModuleStorage",
      component: ModuleStorage
    },
    {
      path: "/ModuleOperation",
      name: "ModuleOperation",
      component: ModuleOperation
    },
    {
      path: "/ModuleReceiveList",
      name: "ModuleReceiveList",
      component: ModuleReceiveList
    },
    {
      path: "/ModuleReturnList",
      name: "ModuleReturnList",
      component: ModuleReturnList
    },
    {
      path: "/ModuleStorageList",
      name: "ModuleStorageList",
      component: ModuleStorageList
    },
    {
      path: "/ModuleCheck",
      name: "ModuleCheck",
      component: ModuleCheck
    },
    {
      path: "/ModuleCheckDetail",
      name: "ModuleCheckDetail",
      component: ModuleCheckDetail
    },
    {
      path: "/ModuleCheckDetailEdit",
      name: "ModuleCheckDetailEdit",
      component: ModuleCheckDetailEdit
    },
    {
      path: "/ModuleReceiveReturn",
      name: "ModuleReceiveReturn",
      component: ModuleReceiveReturn
    },
    {
      path: "/ModuleDestroy",
      name: "ModuleDestroy",
      component: ModuleDestroy
    },
    {
      path: "/ModuleDestroyList",
      name: "ModuleDestroyList",
      component: ModuleDestroyList
    },
    {
      path: "/EquipmentScheduling",
      name: "EquipmentScheduling",
      component: EquipmentScheduling
    },
    {
      path: "/EquipmentSchedulingAdd",
      name: "EquipmentSchedulingAdd",
      component: EquipmentSchedulingAdd
    },
    {
      path: "/EquipmentSchedulingEdit",
      name: "EquipmentSchedulingEdit",
      component: EquipmentSchedulingEdit
    },
    {
      path: "/GoodsOutbound",
      name: "GoodsOutbound",
      component: GoodsOutbound
    },
    {
      path: "/GoodsLocation",
      name: "GoodsLocation",
      component: GoodsLocation
    },
    {
      path: "/GoodsPick",
      name: "GoodsPick",
      component: GoodsPick
    },
    {
      path: "/GoodsLocationDetail",
      name: "GoodsLocationDetail",
      component: GoodsLocationDetail
    },
    {
      path: "/GoodsLocationAdjust",
      name: "GoodsLocationAdjust",
      component: GoodsLocationAdjust
    },
    {
      path: "/GoodsPickDetail",
      name: "GoodsPickDetail",
      component: GoodsPickDetail
    },
    {
      path: "/GoodsPickDetailNo",
      name: "GoodsPickDetailNo",
      component: GoodsPickDetailNo
    },
    {
      path: "/GoodsPickDetailYes",
      name: "GoodsPickDetailYes",
      component: GoodsPickDetailYes
    },
    {
      path: "/GoodsPickCheck",
      name: "GoodsPickCheck",
      component: GoodsPickCheck
    },
    {
      path: "/GoodsPickOverDetail",
      name: "GoodsPickOverDetail",
      component: GoodsPickOverDetail
    },
    {
      path: "/GoodsAssigned",
      name: "GoodsAssigned",
      component: GoodsAssigned
    },
    {
      path: "/GoodsAssignedDetail",
      name: "GoodsAssignedDetail",
      component: GoodsAssignedDetail
    },
    {
      path: "/GoodsCheck",
      name: "GoodsCheck",
      component: GoodsCheck
    },
    {
      path: "/GoodsPickOver",
      name: "GoodsPickOver",
      component: GoodsPickOver
    },
    {
      path: "/GoodsDetail",
      name: "GoodsDetail",
      component: GoodsDetail
    },
    {
      path: "/GoodsPickTurn",
      name: "GoodsPickTurn",
      component: GoodsPickTurn
    },
    {
      path: "/FeedingCheck",
      name: "FeedingCheck",
      component: FeedingCheck
    },
    {
      path: "/FeedingCheck1",
      name: "FeedingCheck1",
      component: FeedingCheck1
    },
    {
      path: "/FeedingCheck2",
      name: "FeedingCheck2",
      component: FeedingCheck2
    },
    {
      path: "/FeedingCheck3",
      name: "FeedingCheck3",
      component: FeedingCheck3
    },
    {
      path: "/FeedingCheck4",
      name: "FeedingCheck4",
      component: FeedingCheck4
    },
    {
      path: "/FeedingCheck5",
      name: "FeedingCheck5",
      component: FeedingCheck5
    },
    {
      path: "/FeedingCheck6",
      name: "FeedingCheck6",
      component: FeedingCheck6
    },
    {
      path: "/FeedingCheck7",
      name: "FeedingCheck7",
      component: FeedingCheck7
    },
    {
      path: "/FeedingCheck8",
      name: "FeedingCheck8",
      component: FeedingCheck8
    },
    {
      path: "/FeedingCheck9",
      name: "FeedingCheck9",
      component: FeedingCheck9
    },
    {
      path: "/Feeding",
      name: "Feeding",
      component: Feeding
    },
    {
      path: "/FeedingCheckDetail1",
      name: "FeedingCheckDetail1",
      component: FeedingCheckDetail1
    },
    {
      path: "/FeedingCheckDetail2",
      name: "FeedingCheckDetail2",
      component: FeedingCheckDetail2
    },
    {
      path: "/FeedingCheckDetail3",
      name: "FeedingCheckDetail3",
      component: FeedingCheckDetail3
    },
    {
      path: "/FeedingCheckDetail4",
      name: "FeedingCheckDetail4",
      component: FeedingCheckDetail4
    },
    {
      path: "/FeedingCheckDetail5",
      name: "FeedingCheckDetail5",
      component: FeedingCheckDetail5
    },
    {
      path: "/FeedingCheckDetail9",
      name: "FeedingCheckDetail9",
      component: FeedingCheckDetail9
    },
    {
      path: "/FeedingCheckErrorList",
      name: "FeedingCheckErrorList",
      component: FeedingCheckErrorList
    },
    {
      path: "/scanColloidInfo",
      name: "ScanColloidInfo",
      component: ScanColloidInfo
    },
    {
      path: "/colloidUsageInfo",
      name: "ColloidUsageInfo",
      component: ColloidUsageInfo
    },
    {
      path: "/colloidPull",
      name: "ColloidPull",
      component: ColloidPull
    },
    {
      path: "/colloidPush",
      name: "ColloidPush",
      component: ColloidPush
    },
    {
      path: "/moSelectInfo",
      name: "MoSelectInfo",
      component: MoSelectInfo
    },
    {
      path: "/colloidCheck",
      name: "ColloidCheck",
      component: ColloidCheck
    },
    {
      path: "/rebackColloidInfo",
      name: "RebackColloidInfo",
      component: RebackColloidInfo
    },
    {
      path: "/gbUsageInfo",
      name: "GbUsageInfo",
      component: GbUsageInfo
    },
    {
      path: "/moUsageSelect",
      name: "MoUsageSelect",
      component: MoUsageSelect
    },
    {
      path: "/productionTraceability",
      name: "ProductionTraceability",
      component: ProductionTraceability
    },
    {
      path: "/productionTraceabilityDetail",
      name: "ProductionTraceabilityDetail",
      component: ProductionTraceabilityDetail
    },
    {
      path: "/powerSetting",
      name: "PowerSetting",
      component: PowerSetting
    },
    {
      path: "/attenceRecord",
      name: "AttenceRecord",
      component: AttenceRecord
    },
    {
      path: "/keyInfo",
      name: "KeyInfo",
      component: KeyInfo
    },
    {
      path: "/materialsWeighing",
      name: "MaterialsWeighing",
      component: MaterialsWeighing
    },
    {
      path: "/materialsWeighingForm",
      name: "MaterialsWeighingForm",
      component: MaterialsWeighingForm
    },
    {
      path: "/materialsWeighingLabel",
      name: "MaterialsWeighingLabel",
      component: MaterialsWeighingLabel
    },
    {
      path: "/updatePwd",
      name: "UpdatePwd",
      component: UpdatePwd
    },
    {
      path: "/mixtureSelect",
      name: "MixtureSelect",
      component: MixtureSelect
    },
    {
      path: "/idIcInfo",
      name: "IdIcInfo",
      component: IdIcInfo
    },
    {
      path: "/orderMixture",
      name: "OrderMixture",
      component: OrderMixture
    },
    {
      path: "/batchNoToCode",
      name: "BatchNoToCode",
      component: BatchNoToCode
    },
    {
      path: "/batchRecord",
      name: "batchRecord",
      component: batchRecord
    },
    {
      path: "/batchRecordcheck1",
      name: "batchRecordcheck1",
      component: batchRecordcheck1
    },
    {
      path: "/batchRecordcheck1detail",
      name: "batchRecordcheck1detail",
      component: batchRecordcheck1detail
    },
    {
      path: "/batchOptionrecord",
      name: "batchOptionrecord",
      component: batchOptionrecord
    },
    {
      path: "/batchOptionrecordDetail",
      name: "batchOptionrecordDetail",
      component: batchOptionrecordDetail
    },
    {
      path: "/batchHACCP",
      name: "batchHACCP",
      component: batchHACCP
    },
    {
      path: "/batchHACCPdetail",
      name: "batchHACCPdetail",
      component: batchHACCPdetail
    },
    {
      path: "/BatchQualityControl",
      name: "BatchQualityControl",
      component: BatchQualityControl
    },
    {
      path: "/BatchQualityControlDetail",
      name: "BatchQualityControlDetail",
      component: BatchQualityControlDetail
    },
    {
      path: "/batchBeforeCheck",
      name: "batchBeforeCheck",
      component: batchBeforeCheck
    },
    {
      path: "/batchBeforeCheckDetail",
      name: "batchBeforeCheckDetail",
      component: batchBeforeCheckDetail
    },
    {
      path: "/repairManage",
      name: "repairManage",
      component: DormRepairManage
    },
    {
      path: "/ssRepair",
      name: "ssRepair",
      component: DormRepair
    },
    {
      path: "/ssRepairDetail",
      name: "ssRepairDetail",
      component: DormRepairDetail
    },
    {
      path: "/addRepair",
      name: "addRepair",
      component: DormRepairAdd
    },
    {
      path: "/advice",
      name: "advice",
      component: DormAdvice
    },
    {
      path: "/adviceReply",
      name: "adviceReply",
      component: DormAdviceReply
    },
    {
      path: "/hygieneManage",
      name: "hygieneManage",
      component: DormHygieneManage
    },
    {
      path: "/hygieneAdd",
      name: "hygieneAdd",
      component: DormHygieneAdd
    },
    {
      path: "/hygieneBulletin",
      name: "hygieneBulletin",
      component: DormHygieneBulletin
    },
    {
      path: "/waterPowerManage",
      name: "waterPowerManage",
      component: DormWaterPowerManage
    },
    {
      path: "/waterPowerAdd",
      name: "waterPowerAdd",
      component: DormWaterPowerAdd
    },
    {
      path: "/dmBill",
      name: "dmBill",
      component: DmBill
    },
    {
      path: "/tankInfo",
      name: "TankInfo",
      component: TankInfo
    },
    {
      path: "/checkBeforeProduction",
      name: "checkBeforeProduction",
      component: CheckBeforeProduction
    },
    {
      path: "/batchAbnormalRecord",
      name: "batchAbnormalRecord",
      component: batchAbnormalRecord
    },
    {
      path: "/batchAbnormalRecordDetail",
      name: "batchAbnormalRecordDetail",
      component: batchAbnormalRecordDetail
    },
    {
      path: "/dmChooseEqu",
      name: "dmChooseEqu",
      component: DmChooseEqu
    },
    {
      path: "/dormRepairComplate",
      name: "dormRepairComplate",
      component: DormRepairComplate
    },
    {
      path: "/suitFeedback",
      name: "suitFeedback",
      component: SuitFeedback
    },
    {
      path: "/postRoutine",
      name: "postRoutine",
      component: PostRoutine
    },
    {
      path: "/postRoutineDetail",
      name: "postRoutineDetail",
      component: postRoutineDetail
    },
    {
      path: "/postRoutineDetailSee",
      name: "postRoutineDetailSee",
      component: postRoutineDetailSee
    },
    {
      path: "/eqImprovement",
      name: "eqImprovement",
      component: eqImprovement
    },
    {
      path: "/eqImprovementDetail",
      name: "eqImprovementDetail",
      component: eqImprovementDetail
    },
    {
      path: "/eqImprovementAdd",
      name: "eqImprovementAdd",
      component: eqImprovementAdd
    },
    {
      path: "/eqImprovementSetup",
      name: "eqImprovementSetup",
      component: eqImprovementSetup
    },
    {
      path: "/FeedingCheckErrorPage",
      name: "FeedingCheckErrorPage",
      component: FeedingCheckErrorPage
    },
    {
      path: "/emd016List",
      name: "emd016List",
      component: emd016List
    },
    {
      path: "/emd021List",
      name: "emd021List",
      component: emd021List
    },
    {
      path: "/emd016add",
      name: "emd016add",
      component: emd016add
    },
    {
      path: "/emd021Add",
      name: "emd021Add",
      component: emd021Add
    },
    {
      path: "/emd016ToEmd021",
      name: "emd016ToEmd021",
      component: emd016ToEmd021
    },
    {
      path: "/emd021Detail",
      name: "emd021Detail",
      component: emd021Detail
    },
    {
      path: "/packagingMaterialCheck",
      name: "packagingMaterialCheck",
      component: packagingMaterialCheck
    },
    {
      path: "/packagingMaterialCheckDetail",
      name: "packagingMaterialCheckDetail",
      component: packagingMaterialCheckDetail
    },
    {
      path: "/packaging",
      name: "packaging",
      component: packaging
    },
    {
      path: "/packagingMaterialBackApply",
      name: "packagingMaterialBackApply",
      component: packagingMaterialBackApply
    },
    {
      path: "/packagingMaterialBackApplyDetail",
      name: "packagingMaterialBackApplyDetail",
      component: packagingMaterialBackApplyDetail
    },
    {
      path: "/inductionTrain",
      name: "InductionTrain",
      component: InductionTrain
    },
    {
      path: "/deptTrain",
      name: "DeptTrain",
      component: DeptTrain
    },
    {
      path: "/evaluationCheck",
      name: "EvaluationCheck",
      component: EvaluationCheck
    },
    {
      path: "/evalSuccess",
      name: "EvalSuccess",
      component: EvalSuccess
    },
    {
      path: "/evalSuccess2",
      name: "EvalSuccess2",
      component: EvalSuccess2
    },
    {
      path: "/evalFail",
      name: "EvalFail",
      component: EvalFail
    },
    {
      path:"/evalResult",
      name:"EvalResult",
      component: EvalResult
    },
    {
      path: "/checkEvaluationInfo",
      name: "CheckEvaluationInfo",
      component: CheckEvaluationInfo
    },
    {
      path: "/deviceClean",
      name: "deviceClean",
      component: deviceClean
    },
    {
      path: "/WorkDuringEdit",
      name: "WorkDuringEdit",
      component: WorkDuringEdit
    },
    {
      path: "/equiSpotCheck",
      name: "equiSpotCheck",
      component: equiSpotCheck
    },
    {
      path: "/communication",
      name: "Communication",
      component: Communication
    },{
      path: "/equiSpotLogin",
      name: "equiSpotLogin",
      component: equiSpotLogin
    },
    {
      path: "/emd022List",
      name: "emd022List",
      component: emd022List
    },
    {
      path: "/emd022Detail",
      name: "emd022Detail",
      component: emd022Detail
    },
    {
      path: "/emd022Add",
      name: "emd022Add",
    },{
      path:"/attenceFzInfo",
      name: "AttenceFzInfo",
      component: AttenceFzInfo
    },{
      path: "/kpiMettle",
      name: "kpiMettle",
      component: kpiMettle
    },
    {
      path: "/equiMenu",
      name: "equiMenu",
      component: equiMenu
    },
    {
      path: "/packagingMaterialBack",
      name: "packagingMaterialBack",
      component: packagingMaterialBack
    },
    {
      path: "/packagingMaterialBackDetail",
      name: "packagingMaterialBackDetail",
      component: packagingMaterialBackDetail
    },
    {
      path: "/pceForm",
      name: "pceForm",
      component: pceForm
    },
    {
      path: "/equiDianjian",
      name: "equiDianjian",
      component: equiDianjian
    },
    {
      path: "/powerSettingMenu",
      name: "powerSettingMenu",
      component: powerSettingMenu
    },
    {
      path: "/equipmentInspectionList",
      name: "equipmentInspectionList",
      component: equipmentInspectionList
    },
    {
      path: "/equipmentInspectionAdd",
      name: "equipmentInspectionAdd",
      component: equipmentInspectionAdd
    },
    {
      path: "/OrderPlatNew",
      name: "OrderPlatNew",
      component: OrderPlatNew
    },
    {
      path: "/groupLeaderScheduling",
      name: "groupLeaderScheduling",
      component: groupLeaderScheduling
    },
    {
      path: "/ordersReceived",
      name: "ordersReceived",
      component: ordersReceived
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    // return 期望滚动到哪个的位置
    if (savedPosition) {
      return savedPosition;
    } else {
      return {
        x: 0,
        y: 0
      };
    }
  }
});
