<template>
    <div>
        <!-- 报损 -->
        <van-tabs v-model="active" color="#1989fa" sticky @click="tabChange">
            <van-tab title="已领用" name="1">
                <van-form @submit="onSubmit">
                    <van-field label="报损数量" v-model="info.count" placeholder="请输入"
                        :rules="[{ required: true, message: '请填写报损数量' }]" />
                    <van-field label="备注" v-model="info.remarks" placeholder="请输入" />

                    <van-field name="uploader" label="上传文件">
                        <template #input>
                            <van-uploader v-model="uploader" :after-read="afterRead" :before-delete="beforeDel"
                                :max-size="10000 * 1024" @oversize="onOversize" />
                        </template>
                    </van-field>

                    <van-radio-group v-model="radio" style="height: 450px;overflow:auto ;">
                        <van-cell-group v-for="item in receiveArr" :key="item.id">
                            <van-cell clickable @click="radio = item.id">
                                <p style="font-size:large;font-weight:700;">{{ item.name }}</p>
                                <p>数量:{{ item.count }}</p>
                                <p>已归还数:{{ item.backNumber }}</p>
                                <p>备注:{{ item.remarks }}</p>
                                <template #right-icon>
                                    <van-radio :name="item.id" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>

                    <div style="margin: 16px;">
                        <van-button round block type="info" native-type="submit">提交</van-button>
                    </div>
                </van-form>
            </van-tab>
            <van-tab title="未领用" name="2">
                <van-form @submit="onSubmit">
                    <van-field label="报损数量" v-model="info.count" placeholder="请输入"
                        :rules="[{ required: true, message: '请填写报损数量' }]" />
                    <van-field label="备注" v-model="info.remarks" placeholder="请输入" />

                    <van-field name="uploader" label="上传文件">
                        <template #input>
                            <van-uploader v-model="uploader" :after-read="afterRead" :before-delete="beforeDel"
                                :max-size="10000 * 1024" :max-count="1" @oversize="onOversize" />
                        </template>
                    </van-field>
                    <div style="margin: 16px;">
                        <van-button round block type="info" native-type="submit">提交</van-button>
                    </div>
                </van-form>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator } from "mint-ui";
export default {
    data() {
        return {
            active: "1",
            info: {},
            // 展示图片
            pictureList: [],
            // 上传图片
            uploader: [],

            // 领用的
            receiveArr: [],
            radio: ""
        };
    },
    created() {
        this.info = this.$route.params;
        if (!this.info.id) {
            this.$router.replace({
                name: "ModuleInfoDetail"
            });
        } else {
            this.info.moldsId = this.info.id;
            this.info.remarks = "";
            this.info.pictureList = [];

            this.$axios
                .get(
                    `/jeecg-boot/ncApp/moldsUsage/getUsageList?product=${this.info.code
                    }&workshop=${this.info.workshop}&type=${2}&flag=${1}&id=${this.info.id
                    }&userCode=${localStorage.getItem("userCode")}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.receiveArr = res.data.result.records;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
    },
    methods: {
        tabChange() {
            this.radio = "";
        },
        // 限制大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            if (file.file.type != "application/pdf") {
                this.uploader.pop()
                Toast({
                    message: '请上传PDF格式的文件',
                    position: "bottom",
                    duration: 2000
                });
                return;
            }
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios
                .post(`/jeecg-boot/ncApp/moldsUsage/uploadPdf`, param)
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res);
                        this.pictureList.push({ picUrl: res.data.message });
                    } else {
                        this.uploader.splice(name.index, 1);
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        //删除图片
        beforeDel(file, name) {
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/ncApp/moldsUsage/deletePic?id=${""}&picUrl=${this.pictureList[name.index].picUrl
                            }`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                            } else {
                                Indicator.close();
                                this.uploader.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                    this.uploader.splice(name.index, 1);
                    this.pictureList.splice(name.index, 1);
                })
                .catch(() => {
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
        onSubmit() {
            let flag = true;
            // 数量只能是正整数
            if (flag) {
                flag = false;
                if (/^[1-9]\d*$/.test(this.info.count)) {
                    this.info.creator = localStorage.getItem("userCode");
                    this.info.createName = localStorage.getItem("userName");
                    this.info.pictureList = this.pictureList;
                    if (this.pictureList.length > 0) {
                        //如果已领用必须要 选中领用记录
                        if (this.active == 1) {
                            this.info.type = "5";
                            if (this.radio != "") {
                                let index = "";
                                this.receiveArr.forEach((v, i) => {
                                    if (v.id == this.radio) {
                                        index = i;
                                    }
                                });
                                this.info.parentId = this.receiveArr[index].id;

                                // 报损数量不能大于领用数量
                                if (
                                    this.info.count * 1 <=
                                    this.receiveArr[index].count * 1 -
                                    this.receiveArr[index].backNumber * 1
                                ) {
                                    this.$axios
                                        .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                                        .then(res => {
                                            if (res.data.code == 200) {
                                                Toast({
                                                    message: "操作成功",
                                                    position: "bottom",
                                                    duration: 2000
                                                });
                                                this.$router.replace({
                                                    name: "ModuleInfo"
                                                });
                                                flag = true;
                                            } else {
                                                Toast({
                                                    message: res.data.message,
                                                    position: "bottom",
                                                    duration: 2000
                                                });
                                            }
                                        });
                                } else {
                                    Toast({
                                        message: "报损数量不能大于领用数量",
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            } else {
                                Toast({
                                    message: "请选择领用记录",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        }
                        //未领用
                        else if (this.active == 2) {
                            this.info.type = "4";
                            if (this.info.count * 1 <= this.info.availableNumber * 1) {
                                this.$axios
                                    .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                                    .then(res => {
                                        if (res.data.code == 200) {
                                            Toast({
                                                message: "操作成功",
                                                position: "bottom",
                                                duration: 2000
                                            });
                                            this.$router.replace({
                                                name: "ModuleInfo"
                                            });
                                            flag = true;
                                        } else {
                                            Toast({
                                                message: res.data.message,
                                                position: "bottom",
                                                duration: 2000
                                            });
                                        }
                                    });
                            } else {
                                Toast({
                                    message: "报损数量不能大于可用数量",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        }
                    } else {
                        Toast({
                            message: "至少上传一张图片",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                } else {
                    Toast({
                        message: "请输入正整数",
                        position: "bottom",
                        duration: 2000
                    });
                }
            }
        }
    }
};
</script>

<style scoped>

</style>
