<template>
    <div>

        <van-form @submit="onSubmit">
            <van-field label="数量" v-model="info.count" placeholder="请输入"
                :rules="[{ required: true, message: '请填写数量' }]" />
            <van-field label="备注" v-model="info.remarks" placeholder="请输入" />


            <a-button type="dashed" style="width: 80px;text-align: left;" @click="addInput">
                <a-icon type="plus" /> 新增
            </a-button>
            <div style="height: 450px;overflow:auto ;">
                <div v-for="(item, index) in items" :key="item.keyId">
                    <van-divider>{{ index + 1 }}</van-divider>
                    <van-field readonly clickable name="picker" v-model="items[index].jobCenter" label="工作中心"
                        placeholder="请选择工作中心" @click="onShowPop(index)"
                        :rules="[{ required: true, message: '请选择工作中心' }]" />
                    <van-popup v-model="showPicker" position="bottom">
                        <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
                    </van-popup>

                    <van-field label="产品编码" v-model="item.productNo" placeholder="请输入"
                        :rules="[{ required: true, message: '请输入产品编码' }]"
                        @blur="e => onPNChange(e, index, 'productNo')" />
                    <van-field label="产品名称" v-model="item.productName" placeholder="请输入" readonly
                        :rules="[{ required: true, message: '请输入产品名称' }]" />
                    <van-field label="备注" v-model="item.remarks" placeholder="请输入" />
                    <a-button type="dashed" style="width: 80px;text-align: left;" @click="removeInput(index)">
                        <a-icon type="minus" /> 删除
                    </a-button>
                </div>
            </div>
            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast } from 'mint-ui';

export default {
    data() {
        return {
            info: {},
            items: [],
            jobCenter: [],
            columns: [],
            showPicker: false,
            currIndex: 0,
        }
    },
    created() {
        this.info = this.$route.params
        this.items = []
        if (!this.info.id) {
            this.$router.replace({
                name: "ModuleInfoDetail",
            });
        } else {
            this.info.moldsId = this.info.id
            this.info.remarks = ''
            this.info.type = '2'
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getJobCenter?book=${this.info.book}&workshop=${this.info.workshop}`)
                .then(res => {
                    if (res.data.code == 200) {
                        res.data.result.forEach(item => {
                            this.columns.push(item.jobCenter)
                        })
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
    },
    methods: {
        onPNChange(e, index, type) {
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getGoodsInfo?code=${e.target.value}`)
                .then(res => {
                    if (res.data.code == 200) {
                        if (res.data.result) {
                            this.$set(this.items[index], 'productName', res.data.result.name)
                        } else {
                            Toast({
                                message: '请重新输入产品编码',
                                position: "bottom",
                                duration: 2000
                            });
                            this.$set(this.items[index], 'productName', '')
                        }
                    }
                });

        },
        onShowPop(pIndex) {
            this.currIndex = pIndex
            this.showPicker = true
        },
        onConfirm(value) {
            this.items[this.currIndex].jobCenter = value
            // this.$set(this.items[i], 'jobCenter', value)
            this.showPicker = false;
        },
        addInput() {
            this.items.push({ jobCenter: '', productNo: '', productName: '', remarks: "" });
        },
        removeInput(index) {
            this.items.splice(index, 1)
        },
        onSubmit() {
            let flag = true
            if (flag) {
                flag = false
                if (this.info.count * 1 > this.info.availableNumber * 1) {
                    Toast({
                        message: '领用数量不得大于可用数量',
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    if (this.items.length == 0) {
                        Toast({
                            message: '请新增并填写适用的产品',
                            position: "bottom",
                            duration: 2000
                        });
                    } else {
                        this.info.type = 2
                        this.info.detailList = this.items
                        this.info.creator = localStorage.getItem('userCode')
                        this.info.createName = localStorage.getItem('userName')
                        // 数量只能是正整数
                        if (/^[1-9]\d*$/.test(this.info.count)) {
                            this.$axios
                                .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                                .then(res => {
                                    if (res.data.code == 200) {
                                        this.$router.replace({
                                            name: "ModuleInfo",
                                        });
                                        flag = true
                                    } else {
                                        Toast({
                                            message: res.data.message,
                                            position: "bottom",
                                            duration: 2000
                                        });
                                    }
                                });
                        } else {
                            Toast({
                                message: '请输入正整数',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    }
                }
            }
        }
    },
}
</script>

<style  scoped>
</style>