<template>
<div class="pageAdviceReply">
    <van-nav-bar fixed
        :title="title"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <!-- 投诉详情 -->
    <h3>投诉内容</h3>
    <van-cell-group>
        <van-cell class="vanCellClass" title="类型">
            <template #default>
                <van-tag type="danger" v-if="advice.type=='1'">投诉</van-tag>
                <van-tag type="primary" v-else-if="advice.type=='2'">建议</van-tag>
                <van-tag color="#999" v-else>未知</van-tag>
            </template>
        </van-cell>
        <van-cell class="vanCellClass" title="是否投诉宿管" :value="advice.isComplaint=='1'?'是':'否'" v-if="advice.type=='1'" ></van-cell>
        <van-cell class="vanCellClass" title="创建时间" :value="advice.createTime"></van-cell>
        <van-field label="内容" name="内容" readonly
            v-model="advice.content"
            placeholder="内容"
            type="textarea" rows="3" autosize 
        />
    </van-cell-group>
    <!-- 回复内容 -->
    <h3>回复内容</h3>
    <van-form @submit="replySubmit">
        <van-field label="回复" name="回复"
            v-model="replyForm.content"
            placeholder="回复"
            type="textarea" rows="3" autosize
            maxlength="300" show-word-limit
            :rules="[{ required: true, message: '回复内容必填' }]"
        />
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
        </div>
    </van-form>

</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            confirmLoading: false,
            mToast: null,
            title: "投诉与建议-回复",
            adviceId: "",
            advice: {}, //投诉与建议
            replyForm: {
                content: ''
            }
        }
    },
    methods:{
        replySubmit(){
            const that=this
            that.confirmLoading=true
            let params=JSON.parse(JSON.stringify(that.replyForm))
            params.csId=that.adviceId
            params.creator=localStorage.getItem('userCode')
            console.log("/dm/dmDailyCsReplyInfo/app/add", params)
            that.$axios.post("/dormApi/dm/dmDailyCsReplyInfo/app/add", params).then(rtn=>{
                if(rtn.status==200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.$router.push("/home")
                    }else{
                        Toast.fail(res.message)
                        console.error("ERROR3", res)
                    }
                }else{
                    Toast.fail("发生错误2")
                    console.error("ERROR2", rtn)
                }
            }).catch(err=>{
                Toast.fail("发生错误1")
                console.error("ERROR1", err)
            }).finally(()=>{
                that.confirmLoading=false
            })
        },
        getAdviceDetail(){
            const that=this
            const rParams={ id: that.adviceId }
            that.$axios.get("/dormApi/dm/dmDailyCsInfo/queryById", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        that.advice=res.result
                    }else{
                        Toast(res.message)
                        console.log("ERROR3", res)
                    }
                }else{
                    Toast("发生错误")
                    console.error("ERROR2", rtn)
                }
            }).catch(err=>{
                Toast("发生错误")
                console.error("ERROR1",err)
            }).finally(()=>{
                that.mToast.clear()
            })
        },
        onClickLeft(){
            this.$router.go(-1)
        }
    },
    created(){
        const that=this
        that.mToast = Toast.loading({
            duration: 0, // 持续展示 toast
            forbidClick: true,
            message: "加载中..."
        });
        that.adviceId=that.$route.query.id
        if(!that.adviceId || that.adviceId==""){
            that.mToast.clear()
            Toast.fail("缺少参数")
            return false;
        }
        that.getAdviceDetail()
    },
    filters: {
        // fType(pType){
        //     switch(pType){
        //         case "1": return "投诉";
        //         case "2": return "建议";
        //         default: return pType;
        //     }
        // },
    }
}
</script>
<style scoped>
.pageAdviceReply{
    padding-top: 50px;
    background: #F1F1F1;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>