<template>
    <div style="text-align:left;padding-bottom: 1%;">
        <van-sticky :offset-top="0">
            <van-nav-bar v-if="type == 0" title="例事" left-text="返回" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <van-nav-bar v-if="type == 1" title="例事" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>
        <van-popup v-model="show" position="bottom" :style="{ height: '35%' }">
            <van-field v-model="userName" clearable label="姓名：" placeholder="请输入姓名" />
            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>
        <div v-for="(item, index) in list" :key="index" @click="goDetail(item)"
            style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 7px #e1dbe1;">
            <div style="margin-bottom:0.3rem;">
                <van-row>
                    <van-col span="12">
                        <div style="font-size:18px;font-weight: 700;color: #000;">
                            {{ item.userName }}
                        </div>
                    </van-col>
                </van-row>
            </div>
            <van-row>
                <van-col span="24">
                    <div style="color:gary">编码：<span style="color:black;">{{ item.userCode }}</span></div>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24">
                    <div style="color:gary">部门：<span style="color:black;">{{ item.department }}</span></div>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24">
                    <div style="color:gary">岗位：<span style="color:black;">{{ item.ncWork }}</span></div>
                </van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
    data() {
        return {
            // 搜索弹出层是否显示
            show: false,
            userName: '',
            userCode: '',
            list: [],
            type: 0,
        };
    },
    created() {
        this.type = localStorage.getItem('exampleAuth')
        if (this.type == 0) {
            this.userName = localStorage.getItem('userName') ? localStorage.getItem('userName') : ''
            this.userCode = localStorage.getItem('userCode')
        } else {
            this.userName = ''
            this.userName = ''
        }
        this.search();
    },
    methods: {
        search() {
            this.$axios
                .get(
                    `/jeecg-boot/app/example/getStaffInfoList?userName=${this.userName}&userCode=${this.userCode}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.list = res.data.result.records;
                        this.show = false
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.replace({
                name: "EmpMenu"
            });
        },
        onClickRight() {
            this.show = true;
        },
        goDetail(item) {
            this.$router.push({
                name: "postRoutineDetailSee",
                query: item
            });
        },

    }
};
</script>

<style scoped></style>