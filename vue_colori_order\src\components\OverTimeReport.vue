<template>
  <div>
    <van-sticky :offset-top="0">
      <van-nav-bar title="加班工时报表" right-text="筛选" left-arrow @click-left="onClickLeft" @click-right="onClickRight" />
    </van-sticky>
    <van-popup v-model="show" position="right" :style="{ height: '100%', width: '90%' }">
      <van-field v-model="filter.year" clearable label="年份：" type="number" placeholder="请输入年份（例2022）" />
      <van-field v-model="filter.startMonth" clearable type="number" label="开始月份：" placeholder="请输入起始月份(默认01)" />
      <van-field v-model="filter.endMonth" clearable  type="number" label="结束月份：" placeholder="请输入结束月份(默认12)" />
      <van-field v-model="filter.llqUserCode" clearable label="员工编号：" placeholder="请输入员工编号" />
      <van-field v-model="filter.staffName" clearable label="员工姓名：" placeholder="请输入员工姓名" />
      <van-field v-model="filter.departmentName" clearable label="所属部门：" placeholder="请输入所属部门" />
      
      <van-button type="info" @click="search" style="width: 90%;height:5%">
        确定
      </van-button>
    </van-popup>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100"
        error-text="请求失败，点击重新加载">
        <div v-for="(item, index) in staffArr" :key="index" @click="detail(item, index)"
          style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
          <div style="display: flex;">
            <p style="width:60%;overflow-x: auto;white-space:nowrap;">
              <span style="font-weight: 900;font-size: 18px;">
                {{ item.staffName }}
              </span>
              | {{ item.sex }} |
              {{ item.education }}
            </p>
            <p style="flex:1;text-align: right;">{{ item.joinDt }}</p>
          </div>
          <div style="display: flex;">
            <p style="width:60%;overflow-x: auto;white-space:nowrap;">
              {{ item.departmentName }} &nbsp; {{ item.positionName }}
            </p>
            <p style="width:40%;text-align: right;">{{ item.llqUserCode }}</p>
          </div>
          <div style="display: flex;">
            <p style="width:60%;">{{ item.school }} &nbsp; {{ item.professional }}</p>
            <p style="width:40%;text-align: right;">{{ item.telPhone }}</p>
          </div>
          <div style="display: flex;">
            <p style="width:60%;overflow-x: auto;white-space:nowrap;">
              {{ item.comefrom }} &nbsp; {{ item.marriageorno }}
            </p>
            <!-- <p v-else style="width:60%;font-size: 8px;">
            {{ item.enCompany }}
          </p> -->
            <p style="width:40%;text-align: right;">
              {{ item.ncLevel }} | {{ item.grading }} | <span style="color:blue">{{item.avgs}}</span>
            </p>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  data() {
    return {
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      show: false,
      staffArr: [],
      sex: "",
      filter: {
        year: "2022",
        startMonth: "",
        endMonth: "",
        llqUserCode: "",
        staffName: "",
        departmentName: "",
        total:"",
        pageSize:30
      }
    };
  },

  methods: {
    getList() {
      let quest = "";
      for (let prop in this.filter) {
        if (this.filter[prop] != '') {
          quest += `&${prop}=${this.filter[prop]}`;
        }
      }
      let userCode = localStorage.getItem('userCode');
      this.$axios
        .get(
          `/jeecg-boot/app/staff/getOverTimeByManage?userCode=${userCode}&pageNo=${this.pageNo}` + quest
        )
        .then(res => {
          if (res.data.code == 200) {
            let len = res.data.result.records.length;
            if (len == 0) {
              this.staffArr = []; // 清空数组
              this.finished = true; // 停止加载
            }
            this.total = res.data.result.total;
            this.filter.total=res.data.result.total;
            this.staffArr.push(...res.data.result.records);

            this.staffArr.forEach(item => {
              if (item.sex == "M") {
                item.sex = "男";
              } else if (item.sex == "F") {
                item.sex = "女";
              }
              if (item.ncLevel == "99") {
                item.ncLevel = "挂靠";
              } else if (item.ncLevel.length == 1) {
                item.ncLevel = "G" + item.ncLevel;
              } else if (item.ncLevel == "99") {
                item.ncLevel = "待定";
              }
            });
            this.loading = false;
            if (this.staffArr.length < 30) {
              this.finished = true; // 结束加载状态
            }
            if (this.staffArr.length >= res.total) {
              this.finished = true; // 结束加载状态
            }
          } else {
            Toast({
              message: res.data.msg,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    onLoad() {
      let timer = setTimeout(() => {
        if (this.refreshing) {
          this.staffArr = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        this.getList(); // 调用上面方法,请求数据
        this.pageNo++; // 分页数加一
        this.finished && clearTimeout(timer); //清除计时器
      }, 100);
    },
    onRefresh() {
      this.finished = false; // 清空列表数据
      this.loading = true; // 将 loading 设置为 true，表示处于加载状态
      this.page = 1; // 分页数赋值为1
      this.staffArr = []; // 清空数组
      this.onLoad(); // 重新加载数据
    },
    sexChange(value) {
      if (value == "男") {
        this.filter.sex = "M";
      } else if (value == "女") {
        this.filter.sex = "F";
      } else if (value == "全部") {
        this.filter.sex = " ";
      }
    },
    detail(item, index) {
      this.$router.push({
        name: "OverTimeDetail",
        params: { item }
      });
    },
    onClickLeft() {
      this.$router.push({
        name: "EmpMenu"
      });
    },
    onClickRight() {
      this.show = true;
    },
    search() {
      let quest = `userCode=${localStorage.getItem('userCode')}`;
      for (let prop in this.filter) {
        if (this.filter[prop] != '') {
          quest += `&${prop}=${this.filter[prop]}`;
        }
      }
      this.$axios
        .get(`/jeecg-boot/app/staff/getOverTimeByManage?${quest}`)
        .then(res => {
          if ((res.data.code = 200)) {
            this.staffArr = res.data.result.records;
            
            if (this.staffArr.length < 30) {
              this.finished = true; // 结束加载状态
            }
          } else {
            Toast({
              message: res.data.msg,
              position: "bottom",
              duration: 2000
            });
          }
        });
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
