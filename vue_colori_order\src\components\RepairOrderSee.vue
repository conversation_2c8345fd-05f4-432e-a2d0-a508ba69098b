<template>
    <div style="text-align:left;">
        <van-sticky :offset-top="0">
            <van-nav-bar v-show="active == 0" title="维修工单" left-text="返回" right-text="筛选" left-arrow
                @click-left="onClickLeft" @click-right="onClickRight" />
            <van-nav-bar v-show="active == 1" title="维修工单" left-text="返回" left-arrow @click-left="onClickLeft" />
            <van-nav-bar v-show="active == 2" title="维修工单" left-text="返回" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-popup v-model="show" position="bottom" :style="{ height: '50%' }">
            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />
            <van-field v-model="id" label="维修单号" placeholder="请输入维修单号" />
            <van-field v-model="moId" label="MO单号" placeholder="请输入MO单号" />
            <van-field v-model="leader" label="接单人" placeholder="请输入接单人" />
            <van-field v-model="creator" label="组长" placeholder="请输入组长" />
            <van-field v-model="machine" label="设备名称" placeholder="请输设备名称" />
            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>
        <van-tabs v-model="active" @click="onClick" color="#1989fa">
            <van-tab title="已完成">
                <div v-for="(item, index) in dataArr0" @click="detail(item, index, 0)" :key="index"
                    style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;"> {{ item.id }} </p>
                        <p style="flex:1;text-align: right;">{{ item.machine }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.jobCenter }} &nbsp; {{ item.category }}
                        </p>
                        <p style="width:40%;text-align: right;">{{ item.lineType }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.book }}
                        </p>
                        <p style="width:40%;text-align: right;overflow-x: auto;white-space:nowrap;">
                            {{ item.workshop }}
                        </p>
                    </div>
                </div>
            </van-tab>
            <van-tab title="未接单">
                <div v-for="(item, index) in dataArr1" @click="detail(item, index, 1)" :key="index"
                    style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;"> {{ item.id }} </p>
                        <p style="flex:1;text-align: right;">{{ item.machine }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.jobCenter }} &nbsp; {{ item.category }}
                        </p>
                        <p style="width:40%;text-align: right;">{{ item.lineType }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.book }}
                        </p>
                        <p style="width:40%;text-align: right;">
                            {{ item.workshop }}
                        </p>
                    </div>
                </div>
            </van-tab>
            <van-tab title="已接单">
                <div v-for="(item, index) in dataArr2" @click="detail(item, index, 2)" :key="index"
                    style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;"> {{ item.id }} </p>
                        <p style="flex:1;text-align: right;">{{ item.machine }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.jobCenter }} &nbsp; {{ item.category }}
                        </p>
                        <p style="width:40%;text-align: right;">{{ item.lineType }}</p>
                    </div>
                    <div style="display: flex;">
                        <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                            {{ item.book }}
                        </p>
                        <p style="width:40%;text-align: right;">
                            {{ item.workshop }}
                        </p>
                    </div>
                </div>
            </van-tab>

        </van-tabs>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            active: 1,
            // 搜索弹出层是否显示
            show: false,
            userCode: localStorage.getItem('userCode'),
            // 已完成
            dataArr0: [],
            // 未接单
            dataArr1: [],
            // 已接单
            dataArr2: [],
            beginDay: this.getDate(-7),
            endDay: this.getDate(0),
            // 搜索条件 时间
            date: this.getDate(-7) + ' ~ ' + this.getDate(0),
            // 时间选择框是否显示
            show1: false,
            // 搜索条件 id
            id: '',
            // 搜索条件 moId
            moId: '',
            // 搜索条件 接单人
            leader: '',
            // 搜索条件 组长
            creator: '',
            // 搜索条件 设备名称
            machine: '',
        };
    },
    created() {
        this.$axios
            .get(
                `/jeecg-boot/app/mac/getAppRecordList?userCode=${this.userCode}&status=${this.active}`
            )
            .then(res => {
                if (res.data.code == 200) {
                    this.dataArr1 = res.data.result
                } else {
                    Toast({
                        message: res.data.msg,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
    },
    methods: {
        onClick(name, title) {
            if (name == 0) {
                // 已完成 请求
                this.$axios
                    .get(
                        `/jeecg-boot/app/mac/getAppRecordList?userCode=${this.userCode}&status=${name}&beginDay=${this.beginDay}&endDay=${this.endDay}
                       `
                    )
                    .then(res => {
                        if (res.data.code == 200) {
                            this.dataArr0 = res.data.result
                        } else {
                            Toast({
                                message: '失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            } else {
                // 未接单 已接单 请求
                this.$axios
                    .get(
                        `/jeecg-boot/app/mac/getAppRecordList?userCode=${this.userCode}&status=${name}`
                    )
                    .then(res => {
                        if (res.data.code == 200) {
                            if (name == 1) {
                                this.dataArr1 = res.data.result
                            } else if (name == 2) {
                                this.dataArr2 = res.data.result
                            }
                        } else {
                            Toast({
                                message: '失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            }
        },
        // 工单详情
        detail(item, index, status) {
            this.$router.push({
                name: "RepairDetail1See",
                params: { item, status }
            });
            localStorage.setItem('repair', JSON.stringify(item));
        },
        onClickLeft() {
            this.$router.go(-1)
        },
        onClickRight() {
            this.show = true;
        },
        search() {
            this.show = false;
            this.$axios
                .get(
                    `/jeecg-boot/app/mac/getAppRecordList?userCode=${this.userCode}&status=${0}&beginDay=${this.beginDay}&endDay=${this.endDay}&id=${this.id}&moId=${this.moId}&leader=${this.leader}&creator=${this.creator}&machine=${this.machine}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.dataArr0 = res.data.result
                    } else {
                        Toast({
                            message: '失败',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        getDate(day) {
            var date1 = new Date(),
                time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
        },

        onConfirm(date) {
            const [start, end] = date;
            this.show1 = false;
            this.beginDay = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
            this.endDay = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
            this.date = `${this.beginDay}~${this.endDay}`
        },
    }
}
</script>

<style scoped>
</style>