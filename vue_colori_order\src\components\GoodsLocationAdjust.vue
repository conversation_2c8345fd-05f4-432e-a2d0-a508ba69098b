<template>
  <div>
    <van-field label="账套" v-model="info.bookName" readonly />
    <van-field label="仓库" v-model="info.storeName" readonly />
    <van-field label="原货位" v-model="info.rackName" readonly />
    <van-field label="产品名称" v-model="info.name" readonly/>
    <van-field label="产品编号" v-model="info.code" readonly/>
    <van-field label="规格" v-model="info.spec" readonly/>
    <van-field label="结存主数量" v-model="info.mainQuantity+info.mainUnit" readonly/>
    <van-field label="结存辅数量" v-model="info.quantity+info.unit" readonly/>
    <van-field label="新货位" v-model="info.newRackName" />
    
    <!-- <div style="height:40rem;overflow:auto ;">
      <div v-for="(item, index) in detailList" :key="item.keyId">
        <van-divider>{{ index + 1 }}</van-divider>
        <van-field label="名称" v-model="item.materialName" readonly />
        <van-field label="编码" v-model="item.materialCode" readonly />
        <van-field label="批次号" v-model="item.customer" readonly />
        <van-field label="状态" v-model="item.status" readonly />
        <van-field label="型号" v-model="item.spec" readonly />
        <van-field
          label="库存主数量"
          v-model="item.quantity + item.unit"
          readonly
        />
        <van-field
          label="库存辅数量"
          v-model="item.mainQuantity + item.mainUnit"
          readonly
        />
        <van-field
          label="转入货位"
          v-model="item.rackName"
          placeholder="请输入"
          clearable
          @blur="
            e => {
              if (e.target.value != '') {
                checkRackName(item, e);
              }
            }
          "
        />
        <van-field
          label="转出数量"
          v-model="item.count"
          type="digit"
          placeholder="请输入"
          clearable
        />
      </div>
    </div> -->
    <div style="margin: 16px;">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      detailList: [],
      info: {}
    };
  },
  created() {
    this.info = this.$route.params.item;
  },
  methods: {
    submit() {
      this.$axios
        .get(`/jeecg-boot/app/warehouseUsage/getPickStockExchange?id=${this.info.id}&newRackName=${this.info.newRackName}&userCode=${localStorage.getItem("userCode")}`)
        .then(res => {
          if (res.data.code == 200) {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            this.$router.go(-1);
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    checkRackName(item, e) {
      this.$axios
        .get(
          `/jeecg-boot/app/warehouse/getRackList?bookName=${item.bookName}&storeName=${item.storeName}&rackName=${e.target.value}`
        )
        .then(res => {
          if (res.data.result.length == 1) {
            this.$set(item, "inRackId", res.data.result[0].rackId);
            console.log(item);
          } else {
            item.rackName = "";
            Toast({
              message: "货位号不正确",
              position: "bottom",
              duration: 2000
            });
          }
        });
    }
  }
};
</script>

<style scoped></style>
