<template>
  <div style="text-align:left;background-color:#fff;">
    <van-tabs v-model="active" @click="onClick" color="#1989fa">
      <van-tab title="拉料工校验">
        <div
          v-for="(item, index) in dataArr1"
          :key="index"
          @click="detail(item, 1)"
          style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;"
        >
          <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
            <van-row>
              <van-col span="24">
                <span style="font-size:18px;font-weight: 700;color: #000;">{{
                  item.id
                }}</span>
              </van-col>
            </van-row>
          </div>
          <van-row>
            <van-col span="24" style="color:gary">
              <van-row>
                <van-col span="6"> 所属MO单：</van-col>
                <van-col span="18">
                  <span
                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;"
                  >
                    {{ item.moId }}
                  </span>
                </van-col>
              </van-row>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24">
              <span style="color:gary;overflow-x: auto;white-space:nowrap;">
                工作中心：
                <span style="color:black">&emsp;{{ item.jobCenter }} </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24">
              <span style="color:gary;overflow-x: auto;white-space:nowrap;"
                >原料数量：
                <span style="color:black"
                  >&emsp;{{ item.materialNumber }}种
                </span>
              </span>
            </van-col>
          </van-row>
        </div>
      </van-tab>
      <van-tab title="操作工校验">
        <div
          v-for="(item, index) in dataArr2"
          :key="index"
          @click="detail(item, 2)"
          style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;"
        >
          <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
            <van-row>
              <van-col span="24">
                <span style="font-size:18px;font-weight: 700;color: #000;">{{
                  item.id
                }}</span>
              </van-col>
            </van-row>
          </div>
          <van-row>
            <van-col span="24" style="color:gary">
              <van-row>
                <van-col span="6"> 所属MO单：</van-col>
                <van-col span="18">
                  <span
                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;"
                  >
                    {{ item.moId }}
                  </span>
                </van-col>
              </van-row>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24">
              <span style="color:gary;overflow-x: auto;white-space:nowrap;">
                工作中心：
                <span style="color:black">&emsp;{{ item.jobCenter }} </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24">
              <span style="color:gary;overflow-x: auto;white-space:nowrap;"
                >原料数量：
                <span style="color:black"
                  >&emsp;{{ item.materialNumber }}种
                </span>
              </span>
            </van-col>
          </van-row>
        </div>
      </van-tab>
      <van-tab title="校验已完成">
        <div
          v-for="(item, index) in dataArr3"
          :key="index"
          @click="detail(item, 3)"
          style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;"
        >
          <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
            <van-row>
              <van-col span="24">
                <span style="font-size:18px;font-weight: 700;color: #000;">{{
                  item.id
                }}</span>
              </van-col>
            </van-row>
          </div>
          <van-row>
            <van-col span="24" style="color:gary">
              <van-row>
                <van-col span="6"> 所属MO单：</van-col>
                <van-col span="18">
                  <span
                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;"
                  >
                    {{ item.moId }}
                  </span>
                </van-col>
              </van-row>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24" style="color:gary">
              <van-row>
                <van-col span="6"> 工作中心：</van-col>
                <van-col span="18">
                  <span style="color:black;">
                    {{ item.jobCenter }}
                  </span>
                </van-col>
              </van-row>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24" style="color:gary">
              <van-row>
                <van-col span="6"> 原料数量：</van-col>
                <van-col span="18">
                  <span style="color:black;">
                    {{ item.materialNumber }}种
                  </span>
                </van-col>
              </van-row>
            </van-col>
          </van-row>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      active: 0,
      userCode: localStorage.getItem("userCode"),
      //   拉料工校验
      dataArr1: [],
      //   操作工校验
      dataArr2: [],
      //   已完成
      dataArr3: []
    };
  },
  created() {
    if (this.userCode == null || this.userCode == "") {
      Toast({
        message: "请先登录",
        position: "bottom",
        duration: 2000
      });
      this.$router.push({
        name: "LoginIndex"
      });
    } else {
      this.$axios
        .get(`/jeecg-boot/app/mix/getMixMainList?userCode=${this.userCode}`)
        .then(res => {
          if (res.data.code == 200) {
            console.log(res.data.result.records);
            res.data.result.records.forEach(item => {
              if (item.status == 5) {
                // 完成
                this.dataArr3.push(item);
              } else if (item.status == 4) {
                // 操作工校验
                this.dataArr2.push(item);
              } else if (item.status == 1) {
                // 拉料工校验
                this.dataArr1.push(item);
              }
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    }
  },
  methods: {
    onClick(name, title) {
      console.log(name, title);
    },
    detail(item, num) {
      if (num == 1) {
        this.$router.push({
          name: "FeedingCheckDetail1",
          params: item
        });
      } else if (num == 2) {
        this.$router.push({
          name: "FeedingCheckDetail2",
          params: item
        });
      } else if (num == 3) {
        this.$router.push({
          name: "FeedingCheckDetail3",
          params: item
        });
      }
    }
  }
};
</script>

<style scoped></style>
