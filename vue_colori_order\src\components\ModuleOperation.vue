<template>
    <div>
        <img src="../../static/images/module_operation.png" width="100%" />
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('4')">
                <img src="../../static/images/ModuleInfo.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>模具信息</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('5')">
                <img src="../../static/images/SpareCheck.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>模具盘点</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('7')">
                <img src="../../static/images/ModuleBorrow.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>借用记录</p>
            </div>
        </div>
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/ModuleStorage.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>入库记录</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('2')">
                <img src="../../static/images/ModuleReturn.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>归还记录</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('3')">
                <img src="../../static/images/ModuleReceive.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>领用记录</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('6')">
                <img src="../../static/images/ModuleDestory.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>报损记录</p>
            </div>
        </div>

    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
        }
    },
    created() {

    },
    methods: {
        goDetail(i) {
            if (i == 1) {
                this.$router.push({ name: "ModuleStorageList" })
            } else if (i == 2) {
                this.$router.push({ name: "ModuleReturnList" })
            } else if (i == 3) {
                this.$router.push({ name: "ModuleReceiveList" })
            } else if (i == 4) {
                this.$router.push({ name: "ModuleInfo" })
            } else if (i == 5) {
                this.$router.push({ name: "ModuleCheck" })
            } else if (i == 6) {
                this.$router.push({ name: "ModuleDestroyList" })
            } else if (i == 7) {
                this.$router.push({ name: "ModuleBorrowList" })
            }
        },
    },
}
</script>

<style scoped>
.menu_order_more_item {
    float: left;
    height: 100%;
    width: 25%;
}

.menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}
</style>