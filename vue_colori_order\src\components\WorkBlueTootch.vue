<template>
    <div>
      <div class="add_upload_imgBox float_left all_width">
        <div class="add_upload_imgDiv float_left" v-for="(item, index) in imgs" :key="item.id + index">
          <img v-image-preview :src="item.base64" />
          <p class="add_upload_close" @click="handleDeleteImage(item.id)" v-if="item.flag">
            <img :src="'../../static/images/delete.png'" />
          </p>
        </div>

        <div class="add_upload float_left add_width">
          <button class="add_upload_button">
            <img class="add_upload_icon" :src="'../../static/images/add_pic.png'" />
            <input id="upfile" type="file" accept="image/*" class="add_upload_file" @change="fileUpload" />
          </button>
        </div>
      </div>
    </div>
</template>

<script>
import { MessageBox } from 'mint-ui';
// import VConsole from 'vconsole';

// let vConsole = new VConsole();

export default {
  name: 'Add',
  data () { 
    return {
      imgs:[],
      imageUrls:[],
    }
  },
  created(){
    this.getImage();
  },
  methods: {
    getImage(){
      let self=this;
      self.$axios.get('/jeecg-boot/app/gcWorkshop/getImageById',{params:{id:'1761',type:'2'}}).then(res=>{
        if(res.data.code=='200'){
          self.imageUrls=res.data.result
          for(var i=0;i<self.imageUrls.length;i++){
            self.imgs.push({id: i, base64: self.imageUrls[i],flag:false})
          }
        }else{
          Toast({
            message: res.data.message,
            position: 'bottom',
            duration: 2000
          });
        }
      })
    },
    fileUpload(){
      let that = this;
      let file = document.getElementById('upfile');
      let fileName = file.value;
      let files = file.files;
      console.log(files[0])
      if(fileName == null || fileName==""){
        alert("请选择文件");
      }else{
        let fileType = fileName.substr(fileName.length-4,fileName.length);
        console.log("fileType:"+fileType)
        if(fileType == ".jpg" || fileType == ".png"){
          if (files[0]) {
            let formData = new window.FormData()
            formData.append('file', files[0])
            formData.append('id', 555)
            formData.append('description', "巡检喷码信息")
            formData.append('type', "1")
            let data = {
              mod: 5,
              opt: 2,
              formData
            };
            fetch('jeecg-boot/app/gcWorkshop/uploadPic', {
              method: 'POST',
              body: formData,
              headers: {
                // Auth: 'token'
                'Access-Control-Allow-Origin': '*',
                Authorization: 'Bearer ',
              },
            }).then((res) => {
              return res.json()
            }).then((res) => {
              console.log(res)
              if (res.sta == 0) {
                // 上传代码返回结果之后
                // console.log(res.data)
              } else {
                console.log(res.msg)
              }
            })

            let reader = new FileReader();
            reader.readAsDataURL(files[0]);
            reader.onload = function (e) {
              let timestamp = (new Date()).valueOf();
              that.imgs.push({id: timestamp, base64: this.result,flag:true})
              console.log(that.imgs)
            }
          } else {
            alert("请选择要上传的图片");
          }
        }else{
          alert("上传文件类型错误！");
        }
      }
    },

    handleDeleteImage(id) {
      let that = this;
      MessageBox.confirm('确定删除该图片吗?').then(action => {
        console.log(action)
        if(action == 'confirm') {
          deleteImage()
        }
      });
      function deleteImage() {
        for(let i = 0; i < that.imgs.length; i+=1) {
          if(that.imgs[i].id === id) {
            that.imgs.splice(i, 1);
            break;
          }
        }
      }
      
    },

  }
}
</script>

<style scoped>
.float_left {
  float: left;
}
.all_width{
  width: 100%;
}
.add_width{
  width: 29%;
}
.add_upload .add_upload_button {
  position: relative;
  width: 100%;
  height: 8rem;
  border: none;
  background: rgb(236,236,236);
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload .add_upload_icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.add_upload .add_upload_file {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  font-size: 0;
}

.add_upload_imgBox .add_upload_imgDiv {
  position: relative;
  width: 29%;
  height: 8rem;
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload_imgBox .add_upload_imgDiv img {
  width: 100%;
  height: 100%;
}
.add_upload_imgBox .add_upload_close {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 30%;
}
.add_upload_imgBox .add_upload_close img {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
</style>
