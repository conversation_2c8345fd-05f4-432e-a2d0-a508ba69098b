<template>
<div class="pageRepairAdd">
    <van-nav-bar fixed
        title="维修完成"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <van-form @submit="recordSubmit">
        <van-button plain block type="info" :loading="confirmLoading" native-type="button" @click="chooseEqu">选择器材</van-button>
        <template v-for="(item,index) in rForm.equipmentInventoryDOS">
            <van-field :label="'器材'+(index+1)" :name="'equipmentInventoryDOS'+index+'equName'"
                disabled
                :key="'zName'+index"
                v-model="item.equName"
                placeholder="器材" />

            <van-field label="出库时间" :name="'equipmentInventoryDOS'+index+'outTime'"
                readonly
                clickable
                
                :key="'zTime'+index"
                v-model="item.outTime"
                placeholder="出库时间" 
                @click="chooseTime(item,index)" />

            
            
            <van-field label="使用数量" :name="'equipmentInventoryDOS'+index+'outNumber'"
                v-model="item.outNumber"
                :key="'zNum'+index"
                placeholder="使用数量"
                type="digit"
                :rules="[{ required: true, message: '必填' }]"
            >
                <template #button>
                    <van-button size="small" plain type="danger" icon="minus" native-type="button" @click="delDreq(item,index)"></van-button>
                </template>
            </van-field>
        </template>

        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker
                type="datetime"
                @confirm="onConfirm"
                @cancel="showPicker = false"
            />
        </van-popup>
        
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
        </div>
    </van-form>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
import moment from 'moment'
export default {
    data(){
        return{
            rForm: {
                id: null,
                roomId: null,
                maintainStatus: null,
                equipmentInventoryDOS: []
            },
            confirmLoading: false,
            showPicker: false,
            curIndex: -1
        }
    },
    methods:{
        // asyncValidator(value, rule) {
        //     console.log("🚀 ~ asyncValidator ~ value, rule:", value, rule)

        //     return false;
        // },
        recordSubmit(){
            this.confirmLoading=true
            
            let params={
                ...this.rForm,
                userCodeRequest:localStorage.getItem('userCode'),
            }
            console.log("maintainOrFinish:",params)
            this.$axios.post('/dormApi/maintain/app/finish', params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.rForm={}
                        this.$router.go(-1)
                    }else{
                        Toast.fail(res.message)
                        this.confirmLoading=false
                        console.log("ERROR",res)
                    }
                }else{
                    Toast.fail("发生错误")
                    this.confirmLoading=false
                    console.log("error",rtn)
                }
            })
        },
        
        onSelectType(){
            this.popTypeShow=true
        },
        onClickLeft(){
            this.$router.go(-1)
        },
        chooseEqu() {
            let that = this;
            this.$router.replace({name:'dmChooseEqu',params:{rForm: that.rForm}});
        },
        delDreq(record, index) {
            this.rForm.equipmentInventoryDOS = this.rForm.equipmentInventoryDOS.filter((item) => item.key !== record.key);
        },
        chooseTime(record, index) {
            this.curIndex = index;
            this.showPicker = true;
        },
        onConfirm(time) {
            console.log("🚀 ~ onConfirm ~ time:", time)
            console.log("🚀 ~ onConfirm ~ time:", moment(time).format('YYYY-MM-DD HH:mm:ss'))
            this.rForm.equipmentInventoryDOS[this.curIndex].outTime = moment(time).format('YYYY-MM-DD HH:mm:ss')
            this.showPicker = false;
        },

    },
    created(){ 
        this.rForm.id= this.$route.query.id
        this.rForm.roomId= this.$route.query.rId
        this.rForm.maintainStatus= this.$route.query.maintainStatus
        console.log("🚀 ~ created ~ this.roomId:", this.rForm)

        let source = this.$route.params.chooseEquList;

        console.log("🚀 ~ created ~ source:", source)
        console.log("🚀 ~ created ~ source:", this.$route.params.rForm)
        if(this.$route.params && this.$route.params.rForm) {
            Object.assign(this.rForm, this.$route.params.rForm)

            if(this.rForm.equipmentInventoryDOS != null && this.rForm.equipmentInventoryDOS.length > 0) {
                for(let i = 0; i < source.length; i++) {
                    let flag = 0;
                    for(let j = 0; j < this.rForm.equipmentInventoryDOS.length; j++) {
                        if(this.rForm.equipmentInventoryDOS[j].equipmentCode === source[i].code) {
                            flag = 1;
                            break;
                        }
                    }
                    if(flag == 0) {
                        this.rForm.equipmentInventoryDOS.push({
                            id: null,
                            key: Date.now()+''+Math.floor(Math.random()*1001),
                            equipmentCode: source[i].code,
                            equName: source[i].name,
                            outTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                            outNumber: null,
                        })
                    }
                }
            } else {
                for(let i = 0; i < source.length; i++) {
                    this.rForm.equipmentInventoryDOS.push({
                        id: null,
                        key: Date.now()+''+Math.floor(Math.random()*1001),
                        equipmentCode: source[i].code,
                        equName: source[i].name,
                        outTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                        outNumber: null,
                    })
                    
                }
            }
            this.$forceUpdate()
            console.log("🚀 ~ created ~ this.rForm.equipmentInventoryDOS:", this.rForm.equipmentInventoryDOS)
        }
    },
    filters: {
        // fType(pType){
        //     switch(pType){
        //         case "1": return "投诉";
        //         case "2": return "建议";
        //         default: return pType;
        //     }
        // },
    }
}
</script>
<style scoped>
.pageRepairAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>