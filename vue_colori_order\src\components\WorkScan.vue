<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <van-button type="primary" :disabled="status" @click="handleScan" style="margin:10px;width:80%;border-radius:10px;">扫码入库确认</van-button>
    
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';

// let wx=window.wx

export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            item:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_hour:require('../../static/images/my_menu.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            reports:{},
            status:false,
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')
        this.item=self.$route.params.item
        // this.getWxInfo()
        // this.getCodeInfo("2112090001","1")
    },
    methods: {
        handleScan(){
            let self=this;
            self.status=true;//按钮置灰
            wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                success: function(res) {
                  console.log(res.resultStr);
                  self.getCodeInfo(res.resultStr,self.item.id);
                }
            });
        },
        getCodeInfo(resultStr,qualityId){
            let self=this;
            self.$axios.get('/jeecg-boot/app/appQuality/getCodeInfo',{params:{id:resultStr,qualityId:qualityId}})
                .then(res=>{
                if(res.data.code==200){
                    self.$router.push({name:"CheckCodeInfo",params:{item:JSON.stringify(res.data.result),id:self.item.id}})
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
</style>