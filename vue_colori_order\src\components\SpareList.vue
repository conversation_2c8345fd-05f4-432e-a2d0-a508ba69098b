<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="备件清单" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '20%' }">
            <van-field v-model="product" clearable label="备件：" placeholder="请输入备件" />
            <van-field v-model="spec" clearable label="规格：" placeholder="请输入规格" />

            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>


        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
                <van-cell v-for="item in list" :key="item.id">
                    <div @click="goDetail(item)"
                        style="color: rgb(112 100 100 / 85%);text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
                        <van-row>
                            <van-col span="24" style="display:flex;color: rgb(38 37 37 / 85%);">
                                <span style="font-size:large;font-weight:700;text-align:left">
                                    {{ item.name }}
                                </span>
                                <span style="flex:1;text-align: right;">
                                    {{ item.location }}
                                </span>
                            </van-col>
                        </van-row>
                        <van-row>
                            <van-col span="24">备件编号: {{ item.code }}
                            </van-col>

                        </van-row>
                        <van-row>
                            <van-col span="24">备件规格: {{ item.spec }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="24">现&nbsp;&nbsp;存&nbsp;&nbsp;量: {{ item.stock }}</van-col>
                        </van-row>
                    </div>

                </van-cell>
            </van-list>
        </van-pull-refresh>



        <!-- <div v-for="(item, index) in spareList" :key="index" @click="goDetail(item)"
            style="color: rgb(112 100 100 / 85%);text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="24" style="display:flex;color: rgb(38 37 37 / 85%);">
                    <span style="font-size:large;font-weight:700;text-align:left">
                        {{ item.name }}
                    </span>
                    <span style="flex:1;text-align: right;">
                        {{ item.location }}
                    </span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24">备件编号: {{ item.code }}
                </van-col>
                
            </van-row>
            <van-row>
                <van-col span="24">备件规格: {{ item.spec }}</van-col>
            </van-row>
            <van-row>
                <van-col span="24">现&nbsp;&nbsp;存&nbsp;&nbsp;量: {{ item.stock }}</van-col>
            </van-row>
        </div> -->

    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            spareList: [],
            show: false,
            product: "",
            spec: "",


            list: [],
            loading: false,
            finished: false,
            refreshing: false,

            pageNum: 0,
        }
    },
    created() {

    },
    methods: {
        onLoad() {
            this.loading = true;
            //分页
            this.pageNum++
            console.log(this.pageNum);
            //请求数据
            this.getList()
        },
        getList() {
            this.$axios.get(`/jeecg-boot/ncApp/parts/getPartsList?product=${this.product}&spec=${this.spec}&pageNo=${this.pageNum}&pageSize=${10}`).then(res => {
                this.show = false
                if (res.data.code == 200) {
                    this.list = this.list.concat(res.data.result.records)
                    console.log(res.data.result.records.length);
                    if (res.data.result.records.length < 10) {
                        this.finished = true
                    } else {
                        this.finished = false
                    }
                    this.loading = false;
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            })
        },
        onRefresh() {
            // 清空列表数据
            this.list = []
            this.finished = false;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            this.pageNum = 1
            this.getList();
        },

        goDetail(item) {
            // SpareListDetail
            localStorage.setItem('SpareListItem', JSON.stringify(item));
            this.$router.push({
                name: "SpareListDetail"
            });
        },
        search() {
            this.pageNum = 1
            this.list = []
            this.finished = false
            this.$axios
                .get(`/jeecg-boot/ncApp/parts/getPartsList?product=${this.product}&spec=${this.spec}&pageNo=${this.pageNum}&pageSize=${10}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.list = this.list.concat(res.data.result.records)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "SparePart"
            });
        },
        onClickRight() {
            this.show = true;
        }
    },
}
</script>

<style scoped></style>