<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="设备改进" right-text="筛选" @click-right="onClickRight" left-arrow
                @click-left="onClickLeft" />
            <van-button round type="info" @click="add" style="width: 100%;">
                新增
            </van-button>
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />

            <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
                @click="showbookNamePicker = true" />
            <van-popup v-model="showbookNamePicker" position="bottom">
                <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                    @cancel="showbookNamePicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="workshop" label="车间：" placeholder="点击选择车间"
                @click="showworkshopPicker = true" />
            <van-popup v-model="showworkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                    @cancel="showworkshopPicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="status" label="状态：" placeholder="点击选择状态"
                @click="showstatusPicker = true" />
            <van-popup v-model="showstatusPicker" position="bottom">
                <van-picker show-toolbar :columns="statusColumns" @confirm="statusConfirm"
                    @cancel="showstatusPicker = false" />
            </van-popup>

            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in dataArr1" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
                <van-row>
                    <van-col span="18">
                        <span style="font-size:18px;font-weight: 700;color: #000;">
                            {{ item.machineName }}
                        </span>
                    </van-col>
                    <van-col span="4">
                        <span v-if="item.status == '1'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            新工单
                        </span>
                        <span v-if="item.status == '2'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            审批通过
                        </span>
                        <span v-if="item.status == '3'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            驳回/撤销
                        </span>
                        <span v-if="item.status == '4'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            进行中
                        </span>
                        <span v-if="item.status == '5'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            安装完成
                        </span>
                    </van-col>
                </van-row>
            </div>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 账套：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.book }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 车间：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.workshop }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 工作中心：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.mitosome }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 设备编码：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.machineNo }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 审批状态：</van-col>
                        <van-col span="18">
                            <span v-if="item.spStatus == '1'"
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                已发起审批
                            </span>
                            <span v-if="item.spStatus == '2'"
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                审批通过
                            </span>
                            <span v-if="item.spStatus == '3'"
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                驳回/撤销
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 当前状况：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.currentSituation }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 预计情况：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.expectSituation }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row gutter="5">
                
                <van-col span="6"><van-button v-if="item.spStatus==null" type="info" style="width:100%" size="mini"
                        @click="detail(item)">编辑</van-button></van-col>
                <van-col span="6"><van-button v-if="item.spStatus==null" type="info" style="width:100%" size="mini"
                        @click="del(item)" >删除</van-button></van-col>
                <van-col span="6"><van-button v-if="item.spStatus==null" type="info" style="width:100%" size="mini"
                        @click="initiateApproval(item)">发起审批</van-button></van-col>
                <van-col span="6"><van-button v-if="item.spStatus==2" type="info" style="width:100%" size="mini"
                        @click="setup(item)">备件安装</van-button></van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
import { Toast,Dialog } from "vant";

import { Indicator } from "mint-ui";
export default {
    data() {
        return {
            date: this.getDate(-1) + " ~ " + this.getDate(0),
            startDate: this.getDate(-1),
            endDate: this.getDate(0),

            active: 0,
            userCode: localStorage.getItem("userCode"),
            //   拉料工校验
            dataArr1: [],
            //   操作工校验
            dataArr2: [],
            //   已完成
            dataArr3: [],

            show: false,
            show1: false,

            showbookNamePicker: false,
            bookName: "",
            bookNameColumns: [],

            showworkshopPicker: false,
            workshop: "",
            workshopColumns: [],

            showstatusPicker: false,
            status: "",
            statusColumns: ['新工单', '已发起审批', '驳回/撤销', '审批通过', '已完成',],
            moId: "",
            glue: ""
        };
    },
    created() {
        if (this.userCode == null || this.userCode == "") {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000
            });
            this.$router.push({
                name: "LoginIndex"
            });
        } else {
            this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
                if (res.data.code == 200) {
                    console.log(res.data.result);
                    res.data.result.forEach(item => {
                        this.bookNameColumns.push(item.name);
                    });
                } else {
                }
            });
            this.bookName = localStorage.getItem('feedingCheckBook')
            console.log("🚀 ~ created ~ localStorage.getItem('feedingCheckBook'):", localStorage.getItem('feedingCheckBook'))
            this.bookNameConfirm(this.bookName)
            this.search();
        }
    },
    methods: {

        initiateApproval(item) {
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .get("/jeecg-boot/app/improve/initiateApproval", {
                    params: { mainId: item.id, userCode: localStorage.getItem('userCode') }
                })
                .then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        del(item) {
            Dialog.confirm({
                message: '确定要删除吗?',
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete("/jeecg-boot/app/improve/deleteByMainId", {
                            params: { mainId: item.id, userCode: localStorage.getItem('userCode') }
                        })
                        .then(res => {
                            Indicator.close();
                            if (res.data.code == 200) {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.search()
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                })
                .catch(() => {
                    Toast({
                        message: `您点击了取消`,
                        position: 'bottom',
                        duration: 1000
                    });
                });

        },
        setup(item) {
            this.$router.push({
                name: "eqImprovementSetup",
                params: item
            });
        },
        bookNameConfirm(value) {
            this.bookName = value;
            this.workshop = '';
            this.jobCenter = '';
            localStorage.setItem('feedingCheckBook', this.bookName)
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value }
                })
                .then(res => {
                    if (res.data.code == 200) {
                        this.workshopColumns = []
                        res.data.result.forEach(item => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.workshop = value;
            this.showworkshopPicker = false;
            // this.jobCenter = '';
            // // 查找工作中心
            // this.$axios
            //     .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
            //         params: { book: this.bookName, workshop: value }
            //     })
            //     .then(res => {
            //         if (res.data.code == 200) {
            //             console.log(res.data.result);
            //             res.data.result.forEach(item => {
            //                 this.jobCenterColumns.push(item.jobCenter);
            //             });
            //         } else {
            //         }
            //     });
        },
        statusConfirm(value) {
            this.status = value;
            this.showstatusPicker = false;
        },
        onClickRight() {
            this.show = true;
        },
        search() {
            this.show = false;
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle"
            });
            let statusNo = ''
            if (this.status == '新工单') {
                statusNo = 1
            } else if (this.status == '已发起审批') {
                statusNo = 2
            } else if (this.status == '驳回/撤销') {
                statusNo = 3
            } else if (this.status == '审批通过') {
                statusNo = 4
            } else if (this.status == '已完成') {
                statusNo = 5
            }
            this.$axios
                .get(
                    `/jeecg-boot/app/improve/getMacImproveList?startDate=${this.startDate}&endDate=${this.endDate}&book=${this.bookName}&workshop=${this.workshop}&userCode=${this.userCode}&status=${statusNo}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result.records);
                        // res.data.result.records.forEach(item => {
                        //   if (item.status == 5) {
                        //     // 完成
                        //     this.dataArr3.push(item);
                        //   } else if (item.status == 4) {
                        //     // 操作工校验
                        //     this.dataArr2.push(item);
                        //   } else if (item.status == 3) {
                        //     // 拉料工校验
                        //     this.dataArr1.push(item);
                        //   }
                        // });
                        this.dataArr1 = res.data.result.records;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        onClick(name, title) {
            console.log(name, title);
        },
        detail(item) {
            this.$router.push({
                name: "eqImprovementDetail",
                params: item
            });
        },
        add(item) {
            this.$router.push({
                name: "eqImprovementAdd",
                params: item
            });
        },
        onConfirm(date) {
            const [start, end] = date;
            this.show1 = false;
            this.startDate =
                start.getFullYear() +
                "-" +
                (start.getMonth() + 1 < 10
                    ? "0" + (start.getMonth() + 1)
                    : start.getMonth() + 1) +
                "-" +
                (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
            this.endDate =
                end.getFullYear() +
                "-" +
                (end.getMonth() + 1 < 10
                    ? "0" + (end.getMonth() + 1)
                    : end.getMonth() + 1) +
                "-" +
                (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
            this.date = `${this.startDate}~${this.endDate}`;
        },
        getDate(day) {
            var date1 = new Date(),
                time1 =
                    date1.getFullYear() +
                    "-" +
                    (date1.getMonth() + 1) +
                    "-" +
                    date1.getDate(); //time1表示当前时间
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return (
                date2.getFullYear() +
                "-" +
                (date2.getMonth() + 1 < 10
                    ? "0" + (date2.getMonth() + 1)
                    : date2.getMonth() + 1) +
                "-" +
                (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
            );
        },
    }
};
</script>

<style scoped></style>