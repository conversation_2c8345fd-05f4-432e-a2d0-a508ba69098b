<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_schedual" width="100%"/>
        <van-cell :title="dateTitle" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false" position="right" />

        <div v-for="(item,index) in schedualList" :key="index">
            <div class="sch_item" @click="getDetail(item)">
                <div class="sch_mo" v-if="item.mitosome!=null && item.mitosome!='null'">
                    <scan :class="item.mitosome.indexOf('Y')!=-1?'ycl-style':''">
                        {{item.moId}}
                    </scan>
                </div>
                <div class="sch_mo" v-else>
                    {{item.moId}}
                </div>
                <div class="sch_line"></div>
                <div class="sch_item_text">
                    <div class="item_left">{{$t('productName')}}</div>
                    <div class="item_right">{{item.name}}</div>
                    <div style="clear:both;"></div>
                </div>
                <div class="sch_item_text">
                    <div class="item_left">{{$t('workshop')}}</div>
                    <div class="item_right"><span style="vertical-align: middle;">{{item.workshop}}</span></div>
                     <div style="clear:both;"></div>
                </div>
                <div class="sch_item_text">
                    <div class="item_left">{{$t('mitosome')}}</div>
                    <div class="item_right"><span style="vertical-align: middle;">{{item.mitosome}}</span></div>
                     <div style="clear:both;"></div>
                </div>
                <div class="sch_item_text">
                    <div class="item_left">{{$t('plannedStaff')}}</div>
                    <div class="item_right">{{item.tpnum}}</div>
                     <div style="clear:both;"></div>
                </div>
                <div class="sch_item_text">
                    <div class="item_left">{{$t('quota')}}</div>
                    <div class="item_right">{{item.tquota}}</div>
                     <div style="clear:both;"></div>
                </div>
                <div class="sch_line"></div>
                <div class="sch_item_text">
                    <div class="sch_bottom" :class="item.gcLeadPlanAppList.length>0?'leave-style':''" v-if="item.gcLeadPlanAppList.length>0">{{$t('scheduled')}}</div>
                    <div class="sch_bottom" v-else>{{$t('startScheduling')}}</div>
                </div>
                <div style="clear:both;"></div>
            </div>
        </div>

        <div style="background:#f3f4f6;height:10px;"></div>


        
    </div>
</template>
<script>
import { DatetimePicker } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            date:'',
            minDate:'',
            schedual:this.$t('startScheduling'),
            selectedValue: this.formatDate(new Date()),
            my_schedual:require('../../static/images/mySchedual.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            schedualList:[],
            dateTitle:this.$t('date'),
            fee:{}
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        sessionStorage.setItem('workList','')
        this.date=this.formatDate(new Date)
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 1 * 24 * 3600 * 1000);
        this.getPlanList()
    },
    methods: {
        getPlanList(){
            let self=this
            let userCode=localStorage.getItem('userCode')
            self.$axios.get('/jeecg-boot/app/gcLeadPlanApp/getPlanList',{params:{usercode:userCode,createTime:this.date}}).then(res=>{
                if(res.data.code==200){
                    self.schedualList=res.data.result.records
                    console.log(self.schedualList)
                }
            })
        },
        getDetail(item){
            console.log("item:"+item)
            sessionStorage.setItem('item',JSON.stringify(item))
            this.$router.push({path:'/schedualDetail'})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getPlanList()
        },
        
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 22rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.ycl-style{
    color: crimson;
}
</style>