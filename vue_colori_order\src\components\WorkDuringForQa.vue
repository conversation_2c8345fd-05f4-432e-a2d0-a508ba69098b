<template>
    <div>
        <van-cell-group style="text-align:center;">
            <div style="margin:10px;text-align:left">巡检项目</div>
            <div v-for="(item,index) in modelInfo" :key="index">
                <!-- <van-field :label="item.moldName" type="digit" required v-if="item.moldType=='数字'" v-model="item.results" @change="inputChange(item,index)"/>
                <van-field :label="item.moldName" required v-if="item.moldType=='文本'" v-model="item.results" @change="inputChange(item,index)"/>
                <van-field :label="item.moldName" rows="3" v-if="item.moldType=='多行文本'" autosize type="textarea" maxlength="100" v-model="item.results"  @change="inputChange(item,index)"/> -->
                <van-field-Pick style="white-space: pre-wrap;" v-model="item.result" :label="item.content" required :columns="['正常','异常','NA']"  @change="e => inputChange(e, index)"/>
                <van-field label="异常描述" rows="3" v-if="item.show" autosize type="textarea" maxlength="100" v-model="item.reason" @blur="e => inputChange2(e, index)"/>
                <div class="add_upload_imgBox float_left all_width" v-if="item.show">
                    <div class="add_upload_imgDiv float_left" v-for="(items, index) in item.pictureInfoList" :key="items.id + index">
                    <img :src="items.picUrl" @click="showImg(items.url)"/>
                    <p class="add_upload_close" @click="handleDeleteImage(item,items.id)" v-if="items.flag">
                        <img :src="'../../static/images/delete.png'" />
                    </p>
                    </div>

                    <div class="add_upload float_left add_width">
                    <button class="add_upload_button">
                        <img class="add_upload_icon" :src="'../../static/images/add_pic.png'" />
                        <input :id="'upfile'+index" type="file" accept="image/*" class="add_upload_file" @change="fileUpload(item,index)" />
                    </button>
                    </div>
                </div>
            </div>
        </van-cell-group>
        
        <!-- <div class="sudoku_row"  >
            <div class="sudoku_item " :class="opacity" v-for="(sudoku,index) in urls" :key="index" >
                <img :src="sudoku" :key="sudoku" width="90%" height="100vw" @click="previewImg(index)" >
            </div>
            <div class="j-upload-btn" @click="chooseImg()">
            <span class="j-upload-add">+</span>
            </div>
        </div> -->
        <van-button type="primary" @click="submit()" style="margin:10px;width:80%;border-radius:10px;">{{button_name}}</van-button>

        <pic-modal ref="modalForm" ></pic-modal>
    </div>
</template>

<script>
import Vue from 'vue'
import { DatetimePicker,Toast,MessageBox,Indicator   } from 'mint-ui';
import PicModal from './list/PicModal.vue'

export default ({
    components: { PicModal},
    data() {
        return{
            itemParams:{},
            modelId:'',
            mitosome:'',
            detailInfoList:{},
            show:true,
            modelList:[],
            modelType:'',
            button_name:"提交",
            itemId:'',
            childId:'',
            modelInfo:[],
            urls:[],
            type:"",
            postUrl:"",
            pictureInfoList:[],
            imageParams:{
                id:'',
                localIds:[],
                serverIds:[]
            },
            isUpdate:false
        }
        
    },
    created:function(){
        let self=this;
        self.type=self.$route.params.type


        console.log(self.type)
        if(self.type=='1'){
            self.modelId=self.$route.params.id
            self.postUrl="/jeecg-boot/app/appBos/addMainDetail";
            self.getModel()
        }else{
            self.modelId=""
            self.modelInfo=self.$route.params.item
            for(var i=0;i<self.modelInfo.length;i++){
                self.modelInfo[i].content=self.modelInfo[i].content.replace('[换行]','\r\n')
            }
            self.postUrl="/jeecg-boot/app/appBos/editMainDetailByUserCode";
        }

        
    },
    methods: {
        getModel(){
            let self=this
            self.$axios.get('/jeecg-boot/app/appBos/getDetailInfo',{params:{id:self.modelId}}).then(res=>{
                if(res.data.code==200){
                    self.modelInfo=res.data.result
                    for(var i=0;i<self.modelInfo.length;i++){
                        self.modelInfo[i].content=self.modelInfo[i].content.replace('[换行]','\r\n')
                        self.modelInfo[i].result="正常"
                        self.modelInfo[i].reason=""
                        self.modelInfo[i].show=false
                        self.modelInfo[i].pictureInfoList=[]
                    }
                }
            })
        },
        inputChange2(value,index){
            this.$forceUpdate()
        },
        inputChange(value,index){
            let self=this
            console.log(value)
            console.log(index)

            if(value=='异常'){
                self.$set(self.modelInfo[index],'show',true)
            }else{
                self.$set(self.modelInfo[index],'show',false)
            }

            console.log(self.modelInfo)
            console.log(self.modelInfo[index])

            this.$forceUpdate()
        },
        showImg(url){
            this.$refs.modalForm.show(url);
            this.$refs.modalForm.disableSubmit = true;
        },
        handleDeleteImage(item,id) {
            let that = this;
            MessageBox.confirm('确定删除该图片吗?').then(action => {
                console.log(action)
                if(action == 'confirm') {
                    deleteImage(item)
                }
            });
            function deleteImage(item) {
                console.log(item.pictureInfoList)
                for(let i = 0; i < item.pictureInfoList.length; i+=1) {
                    if(item.pictureInfoList[i].id === id) {
                        console.log(item.pictureInfoList[i])
                        item.pictureInfoList.splice(i, 1);
                        that.$forceUpdate()
                        break;
                    }
                }
            }
        },
        
        submit(){
            let self=this;

            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });

            let params={
                detailInfoList:self.modelInfo,
                tableId:self.modelId,
                createNo:localStorage.getItem('userCode')
            }
            self.$axios.post(self.postUrl,params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    localStorage.setItem('qaModel',"true");
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                    // self.uploadPic()
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        fileUpload(item,index){
            let that = this;
            let file = document.getElementById('upfile'+index);
            let fileName = file.value;
            let files = file.files;
            console.log(files[0])
            if(fileName == null || fileName==""){
                alert("请选择文件");
            }else{
                let fileType = fileName.substr(fileName.length-4,fileName.length);
                console.log("fileType:"+fileType)
                if(fileType == ".jpg" || fileType == ".png"){
                if (files[0]) {
                    let formData = new window.FormData()
                    formData.append('file', files[0])
                    formData.append('id', item.id)
                    formData.append('childId', "")
                    formData.append('description', "")
                    formData.append('type', "3")

                    that.$axios.post('jeecg-boot/app/appBos/uploadPic',formData).then(res=>{
                        if(res.data.code==200){
                            let timestamp = (new Date()).valueOf();
                            item.pictureInfoList.push({id: timestamp, picUrl: res.data.message,flag:true})
                            this.$forceUpdate()
                        }else{
                            Toast({
                                message: "上传失败，请稍候再试！",
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                    
                    console.log(item);
                } else {
                    alert("请选择要上传的图片");
                }
                }else{
                alert("上传文件类型错误！");
                }
            }
        },
        
    }
})
</script>


<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}
.addControl{
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
.sudoku_row{
    display: flex;
    align-items: center;
    width:90%;
    flex-wrap: wrap;
    margin-left: 5%;
    margin-top: 5vh;
    margin-bottom: 5vh;
}
.sudoku_item{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width:33%;
    padding-top: 10px;
    padding-bottom: 10px;
}
.opacity{
    opacity: 0.4;
    background: #e5e5e5;
}
.sudoku_item img{
    margin-bottom: 3px;
    display: block;
}
.j-pic-upload{
    margin-top:2vh;
    margin-bottom:1vh;
    padding: 10rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}
.j-upload-btn{
    border: 1px dashed #ddd;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    width:32%;
    height:30vw;
}
.j-upload-add{
    font-size: 80rpx;
    font-weight: 500;
    color:#C9C9C9;
}
/deep/ .van-field__label{
    width: 12em;
}

</style>

<style scoped>
.float_left {
  float: left;
}

.add_upload .add_upload_button {
  position: relative;
  width: 6rem;
  height: 6rem;
  border: none;
  background: rgb(236,236,236);
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload .add_upload_icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.add_upload .add_upload_file {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  font-size: 0;
}

.add_upload_imgBox .add_upload_imgDiv {
  position: relative;
  width: 6rem;
  height: 6rem;
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload_imgBox .add_upload_imgDiv img {
  width: 100%;
  height: 100%;
}
.add_upload_imgBox .add_upload_close {
  position: absolute;
  top: 0;
  left: 0;
  width: 28%;
  height: 28%;
}
.add_upload_imgBox .add_upload_close img {
  width: 100%;
  height: 100%;
}
</style>
