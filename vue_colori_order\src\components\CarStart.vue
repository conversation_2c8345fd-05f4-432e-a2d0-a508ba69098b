<template>
    <div style="background:#f3f4f6;">
        <img :src="car_top" width="100%"/>
        <div class="top_title">派车事由</div>
        <div class="top_hint">在这里您可以查看车辆调度情况</div>
        <div class="line"></div>
        <div style="text-align:left;margin-left:5%">
            <span class="car_title">车牌号：</span><span class="car_card">{{carInfo[0].carNum}}</span>
            <span class="car_status" v-if="carInfo[0].status=='1'">预定</span>
            <span class="car_status" v-if="carInfo[0].status=='2'">在途</span>
            <span class="car_status" v-if="carInfo[0].status=='3'">归队</span>
        </div>
        <div class="line"></div>
        <div class="first_item">
            <span>用车联系人：</span><span>{{carInfo[0].contractName}}</span>
        </div>
        <div class="other_item">
            <span>联系电话：</span><span>{{carInfo[0].contractMobi}}</span>
        </div>
        <div class="other_item">
            <span>乘坐人数：</span><span>{{carInfo[0].takeNumber}}</span>
        </div>
        <div class="other_item">
            <span>用车单位：</span><span>{{carInfo[0].userCompany}}</span>
        </div>
        <div class="other_item">
            <span>用车事由：</span><span>{{carInfo[0].reason}}</span>
        </div>
        <div class="other_item">
            <span>用车开始时间：</span><span>{{carInfo[0].startTime}}</span>
        </div>
        <div class="other_item">
            <span>用车结束时间：</span><span>{{carInfo[0].endTime}}</span>
        </div>
        <div class="other_item">
            <span>行程：</span><span>{{carInfo[0].trip}}</span>
        </div>
        <div class="other_item">
            <span>调度要求：</span><span>{{carInfo[0].demand}}</span>
        </div>
        <div class="other_item">
            <span>出发地点：</span><span>{{carInfo[0].startPlace}}</span>
        </div>
        <div class="other_item">
            <span>到达地点：</span><span>{{carInfo[0].endPlace}}</span>
        </div>
        <div class="other_item">
            <span>驾驶员：</span><span>{{carInfo[0].driverName}}</span>
        </div>

        <mt-field label="起始里程:" placeholder="" type="number" v-model.trim="startOdo" v-if="spStatus=='1'" style="margin-top:3rem;background:#fff;"></mt-field>
        <div class="line" style="margin-top:3rem;" v-if="carInfo[0].spStatus=='0'"></div>
        <div class="line" style="margin-top:1rem;" v-if="carInfo[0].spStatus!='0'"></div>
        <div style="padding:1rem;" @click="handleClick()">{{button_name}}</div>
        <div class="line"></div>

        <div class="work_button">
        <div class="work_item" @click="callPhone(carInfo[0].contractMobi)">打电话</div>
        <div class="work_item" @click="sendMsg()" :disabled="isSend">{{sendMsgTitle}}</div>
        </div>

        <mt-button type="default" class="back_button">返回</mt-button>
        <mt-button type="primary" class="back_button">关闭</mt-button>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox,Indicator } from 'mint-ui';
export default {
    data(){
        return{
            button_name:"抵达出发点",
            selected:'',
            startOdo:'',
            spStatus:'',
            isSend:false,
            sendMsgTitle:'发送抵达短信',
            car_top:require('../../static/images/car_top.png'),
            carInfo:[]
        }
    },
    components:{
        DatetimePicker,
        Toast
    },
    created(){
        this.spNo=this.$route.query.spNo;
        this.getCarInfo()
    },
    methods: {
        sendMsg(){
            const self=this
            MessageBox.confirm('是否确认需要发送短信?').then(action => {
                if(action=='confirm'){
                    self.$axios.get('/jeecg-boot/car/sendMsg',{params:{mobile:self.carInfo[0].contractMobi,carNum:self.carInfo[0].carNum,driverMobi:self.carInfo[0].driverMobi}}).then(res=>{
                        if(res.data.code===200){
                            this.sendMsgTitle='已发送';
                            this.isSend=true
                        }else{
                            Toast({
                                message: '发送短信失败',
                                position: 'bottom',
                                duration: 5000
                            });
                        }
                    })
                }
            });
            
        },
        sendMsgToUser(){
            let self=this;
            self.$axios.get('/jeecg-boot/car/sendMsg',{params:{mobile:self.carInfo[0].contractMobi,carNum:self.carInfo[0].carNum}}).then(res=>{
                        if(res.data.code===200){
                            this.sendMsgTitle='已发送';
                            this.isSend=true
                        }else{
                            Toast({
                                message: '发送短信失败',
                                position: 'bottom',
                                duration: 5000
                            });
                        }
                    })
        },
        callPhone (phoneNumber) {
            MessageBox.confirm('是否确认需要拨打电话?').then(action => {
                if(action=='confirm'){
                    window.location.href = 'tel://' + phoneNumber
                }
            });
        },
        getCarInfo(){
            const self=this
            self.$axios.get('/jeecg-boot/car/getCarInfoById',{params:{spNo:self.spNo}}).then(res=>{
                if(res.data.code===200){
                    console.log(res.data.result)
                    self.carInfo=res.data.result
                    if(self.carInfo[0].spStatus=='0'){
                        self.button_name="抵达出发点";
                        self.spStatus='0';
                    }else if(self.carInfo[0].spStatus=='1'){
                        self.button_name="开始计时";
                        self.spStatus='1';
                    }else if(self.carInfo[0].spStatus=='2'){
                        self.button_name="确认抵达";
                        self.spStatus='2';
                    }else if(self.carInfo[0].spStatus=='3' || self.carInfo[0].spStatus=='4'){
                        self.button_name="行程结束";
                        self.spStatus=self.carInfo[0].spStatus;
                    }else{
                        self.button_name="抵达出发点";
                    }

                }
            })
            console.log(self.carInfo)
        },
        handleClick(){
            let self=this
            // Indicator.open({
            //     text: '处理中，请稍后……',
            //     spinnerType: 'fading-circle'
            // });
            if(self.button_name=="抵达出发点"){
                self.$axios.post('/jeecg-boot/car/arriveCar',{id:self.spNo}).then(res=>{
                    if(res.data.code===200){
                        // Toast({
                        //     message: '已发送短信通知乘车人，请稍候！',
                        //     position: 'bottom',
                        //     duration: 5000
                        // });
                        self.sendMsgToUser();
                        self.button_name="开始计时";
                        self.spStatus='1';
                    }
                })
            }else if(self.button_name=="开始计时"){
                if(self.startOdo=='' || self.startOdo==null){
                    Toast({
                        message: '请先输入起始里程！',
                        position: 'bottom',
                        duration: 5000
                    });
                    return;
                }
                self.$axios.post('/jeecg-boot/car/startTrip',{spNo:self.spNo,odo:self.startOdo}).then(res=>{
                    console.log(res.data.code)
                    if(res.data.code===200){
                        Toast({
                            message: '行程计时开始',
                            position: 'bottom',
                            duration: 5000
                        });
                        self.button_name="确认抵达";
                        self.spStatus='2';
                    }
                })
            }else if(self.button_name=="确认抵达"){
                self.$axios.post('/jeecg-boot/car/endTrip',{spNo:self.spNo}).then(res=>{
                    if(res.data.code===200){
                        Toast({
                            message: '行程结束，请填写行程单！',
                            position: 'bottom',
                            duration: 5000
                        });
                        self.spStatus='3';
                        self.$router.push({path:"/carFinish",query:{spNo:self.spNo}})
                    }
                })
            }else if(self.button_name=="行程结束"){
                self.$router.push({path:"/carFinish",query:{spNo:self.spNo}})
            }
            
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
    margin-bottom: 3rem;
}
.car_title{
    font-size: 1.2rem;
    color: #455a64;
    font-weight: 500;
}
.car_card{
    font-size: 1.5rem;
    margin-left: 5%;
    color: #000;
    font-weight: 550;
}
.line{
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    background: #bfbfbf;
    height: 1px;
    width: 100%;
}
.first_item{
    margin-top: 2rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.other_item{
    margin-top: 0.8rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.mint-cell{
    background-color: transparent;
}
.mint-cell-wrapper{
    font-size: 1.2rem;
}
.back_button{
    width: 80%;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    height: 4rem;
    border-radius: 10px;
    border: 1px solid #bfbfbf;
}
.car_status{
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
    text-align: center;
    float: right;
    margin-right: 10%;
}
.work_button{
    margin-left: 5%;
    width: 90%;
    height: 3rem;
}
.work_item{
    width: 45%;
    margin: 2%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    border: #888 1px solid;
    border-radius: 10px;
}
</style>