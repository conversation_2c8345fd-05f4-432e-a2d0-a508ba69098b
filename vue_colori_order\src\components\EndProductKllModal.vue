<template>
  <a-modal
    :title="title"
    :width="1400"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    :footer="null"
    @cancel="handleCancel">

        <a-spin :spinning="confirmLoading">
          <!-- <a-button v-print="'#printabc'">{{$t('print')}}</a-button> -->
          <!-- <a-button @click="exportToExcel('tableOne','KLL生产管制表（除牙膏车间以外）')">{{$t('export')}}</a-button> -->
          <a-button v-if="pageFlag" @click="handleNextPage">下一页</a-button>
          <div id="printabc" style="color:black;font-size:11px;width:100%;background-color:white;">
            <table id="tableOne" border="1" style="width:100%;text-align:center;table-layout: fixed;">
              <th colspan="12" style="height:40px;vertical-align:middle;">
                <a-row>
                  <a-col :span="6"></a-col>
                  <a-col :span="12">
                    <span><h3><b>{{endProductInfo.tableName}}</b></h3></span>
                  </a-col>
                  <a-col :span="6">{{endProductInfo.tableNumber}}</a-col>
                </a-row>
              </th>
              <tr>
                <td colspan="12" style="height:30px;vertical-align:middle;">
                  <a-row :gutter="16">
                    <a-col :span="5">灌包日期：{{endProductInfo.workDay}}</a-col>
                    <a-col :span="5">生产车间：{{endProductInfo.workshop}}</a-col>
                    <a-col :span="4">看产线：{{endProductInfo.mitosome}}</a-col>
                    <a-col :span="5">生产线长：{{endProductInfo.leaderNo}}</a-col>
                    <a-col :span="5">灌装设备：{{endProductInfo.machine}}</a-col>
                  </a-row>
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;">
                <td>产品编码</td>
                <td colspan="2">{{endProductInfo.code}}</td>
                <td>产品名称</td>
                <td colspan="4">{{endProductInfo.name}}</td>
                <td>产品批号</td>
                <td colspan="3"><div >{{endProductInfo.batchNumber}}</div></td>
              </tr>
              <tr style="height:30px;vertical-align:middle;" v-if="endProductInfo.defV28!='' && endProductInfo.defV28!=null">
                <td colspan="2">
                  生产日期
                </td>
                <td colspan="4">
                  {{endProductInfo.defV28}}
                </td>
                <td colspan="2">
                  限用日期
                </td>
                <td colspan="4">
                  {{endProductInfo.defV29}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;">
                <td colspan="12">
                  开线前检查
                </td>
              </tr>
              <!-- <template v-if="endProductInfo.checkStatus==2"> -->
                <!-- <td colspan="4">
                  {{$t('colloidalDensity')}}
                </td>
                <td colspan="8">
                  {{endProductInfo.defV27}}
                </td> -->
              <!-- </template>
              <template v-else> -->
                <tr style="height:30px;vertical-align:middle;">
                <td colspan="2">
                  包装工艺是否现行版本，且与操作一致
                </td>
                <td colspan="4">
                  {{endProductInfo.defV1}}
                </td>
                <td colspan="2">
                  材料复核是否完成，且与工艺要求一致
                </td>
                <td colspan="4">
                  {{endProductInfo.defV2}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;">
                <td colspan="2">
                  批记录是否已在现场，且完成生产前清场确认
                </td>
                <td colspan="4">
                  {{endProductInfo.defV3}}
                </td>
                <td colspan="2">
                  外观确认样是否已完成
                </td>
                <td colspan="4">
                  {{endProductInfo.defV4}}
                </td>
              </tr>
              

              <tr style="height:30px;vertical-align:middle;" v-if="endProductInfo.key=='高露洁美白笔'||endProductInfo.key=='是' || endProductInfo.key=='高露洁瓶装'">
                <td colspan="6">
                  封样是否已在现场，且已复核确认
                </td>
                <td colspan="6">
                  {{endProductInfo.defV16}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;" v-else>
                <td colspan="2">
                  封样是否已在现场，且已复核确认
                </td>
                <td colspan="4">
                  {{endProductInfo.defV16}}
                </td>
                <td colspan="2">
                  胶体密度
                </td>
                <td colspan="4">
                  {{endProductInfo.defV27}}
                </td>
              </tr>
              <!-- </template> -->
          
              
              <tr style="height:30px;vertical-align:middle;">
                <td colspan="2">
                  半成品名称/批号
                </td>
                <td colspan="10">
                  {{endProductInfo.defV5}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;" v-if="endProductInfo.key=='高露洁美白笔'||endProductInfo.key=='是' || endProductInfo.key=='高露洁瓶装'">
                <td>
                  规格(g)
                </td>
                <td>
                  {{endProductInfo.defV7}}
                </td>
                <td colspan="2">
                  皮重(g)
                </td>
                <td colspan="2">
                  {{endProductInfo.defV8}}
                </td>
                <td>
                  标准净重(g)
                </td>
                <td>
                  {{endProductInfo.defV9}}
                </td>
                <td colspan="2">
                  净重范围(g)
                </td>
                <td colspan="2">
                  <a-row>
                    <a-col :span="10">{{endProductInfo.defV10}}</a-col>
                    <a-col :span="2">~</a-col>
                    <a-col :span="10">{{endProductInfo.defV11}}</a-col>
                  </a-row>
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;" v-if="endProductInfo.key=='高露洁美白笔'||endProductInfo.key=='是' || endProductInfo.key=='高露洁瓶装'">
                <td>
                  正标货号
                </td>
                <td>
                  {{endProductInfo.defV12}}
                </td>
                <td colspan="2">
                  背标货号
                </td>
                <td colspan="2">
                  {{endProductInfo.defV13}}
                </td>
                <td>
                  膜货号
                </td>
                <td>
                  {{endProductInfo.defV14}}
                </td>
                <td colspan="2">
                  {{$t('outerContainerNo')}}
                </td>
                <td colspan="2">
                  外箱货号
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;" v-if="endProductInfo.key=='高露洁美白笔'">
                <td>
                  笔身货号
                </td>
                <td>
                  {{endProductInfo.defV30}}
                </td>
                <td colspan="2">
                  说明书货号
                </td>
                <td colspan="2">
                  {{endProductInfo.defV31}}
                </td>
                <td>
                  彩盒货号
                </td>
                <td>
                  {{endProductInfo.defV32}}
                </td>
                <td colspan="2">
                  外箱货号
                </td>
                <td colspan="2">
                  {{endProductInfo.defV33}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;">
                <td colspan="2">
                  开线前异常状况描述
                </td>
                <td colspan="10">
                  {{endProductInfo.defV6}}
                </td>
              </tr>
              <tr style="height:30px;vertical-align:middle;">
                <td colspan="12">
                  过程检查
                </td>
              </tr>
              <tr>
                <td colspan="2" id="lineTd">
                  <span style="float:left;margin-top:20px;">检验项目</span>
                  <span style="float:right;margin-top:0px;">时间段</span>
                </td>
                <td v-for="(item,index) in pageDetailList" :key="index">
                  <span v-if="index == 0&& currentPage == 1">首检：</span>
                  <span v-if="item != null">{{filterTime(item[0].createTime)}}</span>
                </td>
                <td v-for="(item,index) in detailLength" :key="index">:</td>
              </tr>
              <tr v-for="(item,index) in detailList[0]" :key="index">
                <td colspan="2">{{item.moldName}}</td>
                <td v-for="(detailItem,detailIndex) in pageDetailList" :key="detailIndex" style="word-break:break-all;position:relative;">
                  <!-- <div style="background-color:white;z-index:2;position: absolute;opacity:0.1;width:100%;height:100%;left:0px;top:0px;" @click="handleClick(detailItem[index],detailIndex)" v-has="'cpscgzb:abnormalEdit'"></div> -->
                  <div v-if="detailItem">
                    <span v-if="detailItem[index]"><span v-html="detailItem[index].results"></span>
                      <span v-if="detailItem[index].moldId!=null && detailItem[index].moldId!='' && detailItem[index].sign=='是'">/{{detailItem[index].moldId}}</span>
                    </span>
                    
                    <!-- <span>{{detailItem[index].results}}</span> -->
                    <!-- <span v-if="detailItem[index].result != null && detailItem[index].results.indexOf('/')!=-1">{{detailItem[index].results}}1</span>
                    <span v-if="detailItem[index].result != null && detailItem[index].results.indexOf('/')==-1">
                      {{detailItem[index].results}}2
                      <span v-if="detailItem[index].moldId!=null && detailItem[index].moldId!='' && detailItem[index].moldType=='数字'">/{{detailItem[index].moldId}}</span>
                    </span> -->
                    
                  </div>
                  <!-- {{detailItem[index]}} -->
                  
                </td>
                <td v-for="(item,index) in detailLength" :key="index" style="min-width:50px;">
                  &nbsp;
                </td>
              </tr>
              <!-- <tr>
                <td colspan="12" style="text-align:left;border-style:none;">
                  <a-row>
                    <a-col :span="2">说明：</a-col>
                    <a-col :span="22" v-if="orderMainModel.explain" style="white-space:pre-line;">
                      <div v-text="orderMainModel.explain">

                      </div>
                    </a-col>
                    <!== <a-col :span="22">{{$t('endProductOneNote')}}</a-col>
                    <a-col :span="2"> </a-col>
                    <a-col :span="22">{{$t('endProductTwoNote')}}</a-col>
                    <a-col :span="2"> </a-col>
                    <a-col :span="22">{{$t('endProductThreeNote')}}</a-col>
                    <a-col :span="2"> </a-col>
                    <a-col :span="22">{{$t('endProductFourNote')}}</a-col> ==>
                  </a-row>
                </td>
              </tr> -->
              <!-- <tr>
                <td colspan="12">
                  <a-row>
                    <a-col :span="5">生产总量件{{endProductInfo.qty}}件</a-col>
                    <a-col :span="5">不合格量{{endProductInfo.unquality}}件</a-col>
                    <a-col :span="5">复核人：{{endProductInfo.checkName}}</a-col>
                    <a-col :span="5">检查人：{{endProductInfo.closeName}}</a-col>
                    <a-col :span="4">审核：{{endProductInfo.auditNo}}</a-col>
                  </a-row>
                </td>
              </tr> -->
            </table>
          </div>
        </a-spin>
  </a-modal>
</template>

<script>
  import moment from "moment"

  export default {
    name: "EndProductKllModal",
    components: {
    },
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        labelCol: {
          xs: {span: 24},
          sm: {span: 5},
        },
        craftFile:"",
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        mouldList:[],
        validatorRules: {},
        // 全表内容
        endProductInfo:{},
        // 表2 内容
        detailList:[],
        pageDetailList:[],
        detailLength:10,
        currentPage:1,
        pageFlag:false,
        url: {
          add: "/test/order/add",
          edit: "/orderPlan/updateQTA",
          orderCustomerList: "/test/order/listOrderCustomerByMainId",
          orderTicketList: "/test/order/listOrderTicketByMainId",
          exportXlsUrl: "/gc/gcQuality/exportXls",
        },
      }
    },
    methods: {
      add() {
        this.edit({});
      },
      edit(record) {
        this.visible = true
        this.form.resetFields();
        this.orderMainModel = Object.assign({}, record);
        //初始化明细表数据
        this.endProductInfo = record
        this.detailList = record.detailList
        let compare = function(obj1, obj2) {
          let val1 = Number(obj1.id);
          let val2 = Number(obj2.id);
          if(val1 < val2) {
            return -1;
          } else if(val1 > val2) {
            return 1;
          } else {
            return 0;
          }
        }
        for(let i = 0; i < this.detailList.length; i++) {
          console.log('排序：',this.detailList[i].sort(compare));
        }

        let end = 0;
        this.detailLength = 10 - this.detailList.length
        if(this.detailLength <= 0) {
          this.detailLength = 0
          this.pageFlag = true
          end = 10;
        } else {
          end = this.detailList.length
        }
        for(let i = 0; i < end; i++) {
          this.pageDetailList[i] = this.detailList[i]
        }
        
        if(this.endProductInfo.batchNumber && this.endProductInfo.batchNumber.length > 32) {
          this.endProductInfo.batchNumber = this.endProductInfo.batchNumber.substring(0,20)+'\n'+this.endProductInfo.batchNumber.substring(20)
        }

        this.pageDetailList.forEach(v=>{
          let changeArray=[]
          v.forEach((item,index)=>{
            if (item.moldType.indexOf('动态文本') > -1) {
              changeArray.push(Object.assign({}, item, {index}))
            }
          })
          changeArray.forEach((c, ci) => {
            let resArr=[]
            v[c.index].results.split(',').forEach((str, strI) => {
              if (v[c.upLimit - 1].results * 1 > 0 && v[c.downLimit - 1].results * 1 > 0) {
                if ((v[c.upLimit - 1].results * 1) > 0) {
                  if (str * 1 >= v[c.downLimit - 1].results * 1 && str * 1 <= v[c.upLimit - 1].results * 1) {
                    resArr.push(`<span style="color: #000;">${str}</span>${(strI + 1) % 2 == 0 ? '<br />' : ' '} `)
                  } else {
                    resArr.push(`<span style="color: red;">${str}</span>${(strI + 1) % 2 == 0 ? '<br />' : ' '} `)
                  }
                } else {
                  if (str * 1 >= v[c.downLimit - 1].results * 1) {
                    resArr.push(`<span style="color: #000;">${str}</span>${(strI + 1) % 2 == 0 ? '<br />' : ' '} `)
                  } else {
                    resArr.push(`<span style="color: red;">${str}</span>${(strI + 1) % 2 == 0 ? '<br />' : ' '} `)
                  }
                }
              } else {
                resArr.push(`<span style="color: #000;">${str}</span>${(strI + 1) % 2 == 0 ? '<br />' : ' '} `)
              }
            })
            v[c.index].results = resArr.join('')
          })
        })
      },
      callback(key) {
        console.log(key);
      },
      filterTime(time) {
        console.log('time:',time);
        if(time != null && time != '') {
          return moment(time).format("HH:mm");
        } else {
          return '';
        }
        
      },
      close() {
        this.$emit('close');
        this.visible = false;
        this.detailLength = 10;
        this.endProductInfo={};
        this.detailList=[];
        this.pageDetailList = [];
        this.currentPage = 1;
        this.pageFlag = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.close()
      },
      // 下一页
      handleNextPage() {
        if(this.detailList.length > (this.currentPage * 10)) {
          let start= this.currentPage * 10;
          this.currentPage = this.currentPage + 1;
          this.pageDetailList = [];
          let index = 0;
          
          let end = this.detailList.length;

          if((this.currentPage) * 10 < this.detailList.length) {
            
            end = (this.currentPage) * 10;
          }

          for(let i = start; i < end; i++) {
            this.pageDetailList[index] = this.detailList[i];
            
            index++;
          }
          this.detailLength = (this.currentPage * 10) - end

        } else {
          this.$message.warning("已经是最后一页了！")
        }
      },
      handleCancel() {
        this.close()
      },

      handleClick(item,index) {
        if(item && item.moldName.indexOf('异常状况') != -1) {
          this.$refs.editForm.edit(item,index);
          this.$refs.editForm.title = '编辑';
          this.$refs.editForm.disableSubmit = false;
        }
      },
      getSource(source,index) {
        for(let i = 0; i < this.pageDetailList[index].length; i++) {
          if(this.pageDetailList[index][i].id == source.id) {
            this.pageDetailList[index][i].results = source.results
          }
        }
      },
    },
    filters: {
      
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }

  *{padding:0;margin:0;}

  th,td{
    /* border:1px red solid; */
    text-align:center;
    /* font-size:12px; */
    line-height:20px;
  }
  .out{
    border-top:40px yellow solid;
    width:0px;
    height:0px;
    border-left:300px #ffffff solid;
    position:relative;
  }
  .outOne{
    font-style:normal;display:block;position:absolute;top:-20px;left:-300px;width:55px;
  }
  .outTwo{
    font-style:normal;display:block;position:absolute;top:-40px;left:-50px;width:55px;
  }
  #lineTd {
      background: #fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjEiLz48L3N2Zz4=) no-repeat 100% center;
  }
  .check-style-2 {
    display: inline-block;
    width: 16px;
    height: 8px;
    border-left: 3px solid green;
    border-bottom: 3px solid green;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }


</style>