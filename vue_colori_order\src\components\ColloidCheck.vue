<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">查询条件</div>
        <van-field label="胶体编号" v-model="colloidCode"/>
        <van-field label="生产批次" v-model="customer"/>
        <van-field label="胶体状态" v-model="colloidStatus" readonly @click="showColloidStatus=true"/>

        <van-button type="info" @click="search()"  style="margin:10px;width:40%;border-radius:10px;">查询</van-button>
        <!-- <van-button type="primary" @click="scanTank()"  style="margin:10px;width:40%;border-radius:10px;">扫码校验</van-button> -->

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px" v-if="pullList.length>0">
            查询结果：({{pullList.length}}) &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="color:red;" @click="allItemClick">{{selectAll}}</span>
        </div>

        <van-popup  v-model="showColloidStatus" position="bottom">
            <van-picker :columns="colloidStatusList" @cancel="onCancel" @confirm="onConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>


        <div style="display: flex;width: 100%;padding:3%;" v-for="(item,index) in pullList" :key="index">

            <div style="flex: 1;text-align:center;align-self: center;" @click="selectItem(index,item)">
                <van-checkbox v-model="item.selected" style="margin-left:30%"></van-checkbox>
            </div>

            <div style="flex: 6;text-align:left;" @click="sendDetail(item)">
                <div style="width:100%">
                    <div style="float:left;font-size:16px;font-weight:800;width:70%;">{{item.tankNo}}</div>
                    <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:orange" v-if="item.realProductStatus=='1'">待检</div>
                    <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:green" v-if="item.realProductStatus=='2'">已检</div>
                    <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:gray" v-if="item.realProductStatus=='0'">锁定</div>
                    <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:blue" v-if="item.realProductStatus=='9'">工艺已核</div>
                    <div style="clear:both"></div>
                </div>
                
                <div style="background:#f0f0f0;height:1px;width:100%"></div>

                <div style="width:100%;font-size:14px;color:#888888">胶体编号：{{item.realProductNo}}</div>
                <div style="width:100%;font-size:14px;color:#888888">胶体名称：{{item.realProductName}}</div>
                <div style="width:100%;font-size:14px;color:#888888">胶体批次：{{item.realCode}}</div>
                <div style="width:100%;font-size:14px;color:#888888">胶体净重：{{item.tankVolume-item.realVolume}}KG</div>


                <div style="clear:both"></div>
            </div>

            <div style="clear:both;"></div>

        </div>



        <div style="position:fixed;bottom:0;width:100%;height:5rem;display: flex;
    align-items: center;
    justify-content: center;font-weight:800;">
            <van-button type="primary" @click="check('2')" v-if="colloidStatus!='合格'" style="margin:10px;width:30%;border-radius:10px;">批量合格</van-button>
            <van-button type="danger" @click="check('3')"  v-if="colloidStatus!='不合格'" style="margin:10px;width:30%;border-radius:10px;">批量不合格</van-button>
            <van-button type="info" @click="check('0')" v-if="colloidStatus!='锁定'" style="margin:10px;width:30%;border-radius:10px;">批量锁定</van-button>
        </div>

        <!-- <div v-for="(item,index) in pullList" :key="index" style="text-align:left;padding:5px;margin:5px;background:#FFF;">
            <div style="width:100%">
                <div style="float:left;font-size:16px;font-weight:800;width:70%;color:green;">{{item.tankNo}}</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:blue" v-if="item.status=='1'">待灌包</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:orange" v-if="item.status=='2'">灌包中</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:gray" v-if="item.status=='0'">已完成</div>
                <div style="clear:both"></div>
            </div>
            
            <div style="background:#f0f0f0;height:1px;width:100%"></div>

            <div style="width:100%;font-size:14px;color:#888888">胶体编号：{{item.code}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体名称：{{item.name}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体批次：{{item.customer}}</div>
            <div style="width:100%;font-size:14px;color:#888888">胶体净重：{{item.volume}}KG</div>


            <div style="clear:both"></div>
        </div> -->

        
    </div>
</template>
<script>
import { Indicator,MessageBox   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            customer:"",
            colloidCode:"",
            colloidStatus:"待检",
            pullList:[],
            selectAll:"全选",
            showColloidStatus:false,
            colloidStatusList:["待检","合格","不合格","锁定","工艺已核"]
            
        }
    },
    created:function(){
    },
    methods:{
        onCancel(){
            this.showColloidStatus=false
        },
        onConfirm(value){
            this.colloidStatus = value
            this.showColloidStatus = false
        },
        sendDetail(item){
            let self=this
            let result=item.id+'&'+item.realProductNo+'&'+item.realCode
            self.$router.push({name:"ScanColloidInfo",params:{result:result,type:'1'}})
        },
        allItemClick(){
            let self=this
            if(self.selectAll=="全选"){
                for(var i=0;i<self.pullList.length;i++){
                    self.pullList[i].selected=true;
                }
                self.selectAll="取消全选"
            }else{
                for(var i=0;i<self.pullList.length;i++){
                    self.pullList[i].selected=false;
                }
                self.selectAll="全选"
            }
        },
        search(){
            let self=this
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/tank/check/getTankInfoByCustomer',{params:{code:self.colloidCode,customer:self.customer,colloidStatus:self.colloidStatus}}).then(res=>{
                if(res.data.code==200){
                    self.pullList=res.data.result;
                    self.selectAll="全选"
                    Indicator.close();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        // scanTank(){
        //     let self=this
        //     wx.scanQRCode({
        //         desc: 'scanQRCode desc',
        //         needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
        //         scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
        //         success: function(res) {
        //             // 回调
        //             var result = res.resultStr;//当needResult为1时返回处理结果
        //             // self.$router.push({name:"ScanResult",params:{result:result,type:'1'}})
        //             // self.$router.push({name:"ScanColloidInfo",params:{result:result,type:'1'}})

        //             let info=result.split("&");

        //             self.colloidCode=info[1];
        //             self.customer=info[2];

        //             self.search();

        //         },
        //         error: function(res) {
        //             if (res.errMsg.indexOf('function_not_exist') > 0) {
        //                 alert('版本过低请升级')
        //             }
        //         }
        //     });
        // },
        selectItem(index,item){
            let self=this
            if(item.selected){
                item.selected=false;
            }else{
                item.selected=true
            }
            self.$set(self.pullList,index,item)
        },
        check(num){
            let self=this;

            let ids="";
            for(var i=0;i<self.pullList.length;i++){
                if(self.pullList[i].selected){
                    ids=ids+self.pullList[i].id+","
                }
            }

            let params={
                tankId:ids,
                status:num,
                checker:localStorage.getItem('userCode')
            }

            var message="是否确认将选中胶体设为合格？";
            if(num=='3'){
                message="是否确认将选中胶体设为不合格？";
            }else if(num=='0'){
                message="是否确认将选中胶体锁定？";
            }

            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/tank/check/batchCheckColloidInfo',params).then(res=>{
                            if(res.data.code==200){
                                self.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}

</style>