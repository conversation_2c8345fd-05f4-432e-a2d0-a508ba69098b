<template>
  <div>
    <van-field v-model="info.name" label="产品" readonly />
    <van-field v-model="info.code" label="编码" readonly />
    <van-field v-model="info.count" label="总量" readonly />
    <van-field v-model="info.defectCount" label="缺数" readonly />
    <van-field v-model="info.defectRemark" label="备注" />
    <van-divider
      :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }"
    >
      已拣货
    </van-divider>
    <div style="height:29rem;overflow: auto;margin-top:3%;">
      <van-checkbox-group v-model="result" ref="checkboxGroup">
        <van-cell-group>
          <van-cell
            v-for="(item, index) in dataArr"
            :key="index"
            style="width:90%; text-align: left; margin:3% auto;padding:3%;"
          >
            <template #default>
              <van-row>
                <van-col span="12">托码:{{ item.stickerId }}</van-col>
                <van-col span="12">货位:{{ item.rackName }}</van-col>
              </van-row>
              <van-row>
                <van-col span="12">批次号:{{ item.customer }}</van-col>
                <van-col span="12">已拣货:{{ item.stock }}</van-col>
              </van-row>
              <van-row>
                <van-col span="24">
                  <van-field v-model="item.count" type="digit" label="数量：" />
                </van-col>
              </van-row>
            </template>
            <template #icon>
              <van-checkbox :name="item" ref="checkboxes" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </div>
    <van-row
      style="background-color:#fff;position: fixed;bottom: 0;right: 0;z-index: 99;width: 100%;height: 5%;"
      gutter="30"
    >
      <van-col span="4">
        <van-button
          :plain="plain"
          icon="success"
          type="info"
          round
          size="mini"
          @click="toggleAll"
        >
        </van-button>
      </van-col>
      <van-col span="3"></van-col>
      <van-col span="8"> </van-col>
      <van-col span="8">
        <van-button
          round
          style="height:30px"
          type="info"
          size="large"
          @click="submit"
          loading-type="spinner"
        >
          退库
        </van-button>
      </van-col>
    </van-row>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      plain: true,
      info: {},
      dataArr: [],
      // 已选择数组
      result: []
    };
  },
  created() {
    if (this.$route.params) {
      // this.dataArr = this.$route.params
      this.info = this.$route.params;
      this.dataArr = this.$route.params.locationList;
      console.log(this.dataArr);
      this.dataArr.forEach(item => {
        this.$set(item, "count", item.stock);
      });
    } else {
      this.$router.go(-1);
    }
  },
  methods: {
    submit() {
      if (this.info.checkerNo == localStorage.getItem("userCode")) {
        this.info.locationList = this.result;
        let flag = true;
        this.info.locationList.forEach(item => {
          if (item.count * 1 > item.stock * 1) {
            flag = false;
          }
        });
        if (flag) {
          if (this.info.locationList.length == 0) {
            Toast({
              message: "至少勾选一条记录",
              position: "bottom",
              duration: 2000
            });
          } else {
            this.$axios
              .post(`/jeecg-boot/app/warehouseUsage/getPickInput`, this.info)
              .then(res => {
                if (res.data.code == 200) {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                  this.$router.go(-1);
                } else {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                }
              });
          }
        } else {
          Toast({
            message: "退货数不能大于已拣货数",
            position: "bottom",
            duration: 2000
          });
        }
      } else {
        Toast({
          message: "您不是本单保管员,没有权限",
          position: "bottom",
          duration: 2000
        });
      }
    },
    checkAll() {
      this.$refs.checkboxGroup.toggleAll(true);
    },
    toggleAll() {
      if (this.result.length != this.dataArr.length) {
        this.plain = false;
        this.$refs.checkboxGroup.toggleAll(true);
      } else {
        this.plain = true;
        this.$refs.checkboxGroup.toggleAll();
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
