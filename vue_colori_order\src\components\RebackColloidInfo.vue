<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div>
            <van-field label="所属车间" :value="workshop"  readonly/>
            <van-field label="使用情况" v-model="status" @click="handleStatus" readonly/>
            <van-field label="结余重量(KG)" v-model="lastColloidWeight" v-if="status!='全部使用'" :readonly="weightFlag"/>
            <van-field label="是否留用" v-model="stayFlag"  @click="handleFlag" v-if="status!='全部使用'" readonly/>
            <van-field label="退回区域" v-model="rebackArea"  v-if="stayFlag=='否' && workshop=='制胶车间'" readonly/>
            <van-field label="工作中心" v-model="jobCenter" @click="showJobCenter=true" v-if="stayFlag=='是' && status!='全部使用'"  readonly/>
            <van-field label="是否存在次品胶体" v-model="rejectFlag" @click="handleReject" readonly/>
            <van-field label="次品胶体来源" v-model="rejectSource" @click="handleSource" v-if="rejectFlag=='是'"/>
            <van-field label="次品胶体重量(KG)" v-model="rejectColloidWeight" v-if="rejectFlag=='是'"/>
            <van-field label="次品桶" v-model="tankInfo.tankNo" @click="handleScan" v-if="rejectFlag=='是'"/>
            
            <div style="width:100%;margin-top:50px;">
                <van-button type="primary" @click="reback()" :loading="loading" loading-type="spinner" style="margin:5px;width:40%;border-radius:10px;">退桶</van-button>
            </div>


        </div>


        <van-popup  v-model="showSource" position="bottom">
            <van-picker :columns="sourceList"  @cancel="onSourceCancel" @confirm="onSourceConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showReject" position="bottom">
            <van-picker :columns="rejectList"  @cancel="onRejectCancel" @confirm="onRejectConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showStatus" position="bottom">
            <van-picker :columns="statusList"  @cancel="onStatusCancel" @confirm="onStatusConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showFlag" position="bottom">
            <van-picker :columns="flagList" @cancel="onFlagCancel" @confirm="onFlagConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showJobCenter" position="bottom">
            <van-picker :columns="jobCenterList" @cancel="onJobCenterCancel" @confirm="onJobCenterConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showArea" position="bottom">
            <van-picker :columns="areaList" @cancel="onAreaCancel" @confirm="onAreaConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

    
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx=window.wx
export default {
    data(){
        return{
            result:'',
            showStatus:false,
            showFlag:false,
            showJobCenter:false,
            showReject:false,
            weightFlag:false,
            showArea:false,
            loading:false,
            info:{},
            type:'',
            itemParams:{},
            statusList:["全部使用","未使用完成"],
            flagList:["是","否"],
            rejectList:["是","否"],
            areaList:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","不合格"],
            sourceList:["换产","调机","回吸料"],
            rejectFlag:'否',
            jobCenterList:[],
            tankInfo:{},
            showWorkshop:false,
            showJobCenter:false,
            showSource:false,
            lastColloidWeight:'',
            rejectColloidWeight:'',
            rejectTank:'',
            rejectSource:'',
            jobCenter:'',
            id:'',
            volume:'',
            rebackArea:'',
            colloidStatus:'',
            status:'全部使用',
            stayFlag:'是',
        }
    },
    components:{
        DatetimePicker
    },
    
    created:function(){
        this.id=this.$route.params.id
        this.result=this.$route.params.result
        this.workshop=this.$route.params.workshop
        this.volume=this.$route.params.volume
        this.colloidStatus=this.$route.params.status

        if(this.workshop!=null && this.workshop !=''){
            this.getJobCenter(this.workshop);
        }

        if(this.id==null || this.id==''){
            this.$route.params.back = "1";
            this.$router.back();
        }

        if(this.colloidStatus=='1'){
            this.status="未使用完成";
            this.lastColloidWeight=this.volume
            this.stayFlag='否'
            this.rejectFlag='否'
            this.weightFlag=true
        }

    },
    methods: {
        handleScan(){
            let self=this;
            MessageBox.confirm('',{
                message: '扫码还是录入？',
                title: '提示',
                confirmButtonText: '扫码',
                cancelButtonText: '录入'
            }).then(action => {
                if(action=="confirm"){
                    wx.scanQRCode({
                        desc: 'scanQRCode desc',
                        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                        success: function(res) {
                            // 回调
                            var result = res.resultStr;//当needResult为1时返回处理结果
                            self.getTankInfo(result)
                        },
                        error: function(res) {
                            if (res.errMsg.indexOf('function_not_exist') > 0) {
                                alert('版本过低请升级')
                            }
                        }
                    });
                }
            }).catch((res)=>{
                if(res=="cancel"){
                    MessageBox.prompt('请输入储罐编码').then(({ value, action }) => {
                        if(action=="confirm"){
                            self.getTankInfo(value)
                        }
                    });
                }
            });
        },
        getTankInfo(value){
            let self=this
            self.$axios.get('/jeecg-boot/app/tank/check/getTankInfoToReject',{params:{id:value}}).then(res=>{
                if(res.data.code==200){
                    self.tankInfo=res.data.result;
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        handleReject(){
            let self=this
            if(self.colloidStatus=='1'){
                Toast({
                    message: '该胶体未加料，无法切换状态！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }else{
                self.showReject=true
            }
        },
        handleFlag(){
            let self=this
            if(self.colloidStatus=='1'){
                Toast({
                    message: '该胶体未加料，无法切换状态！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }else{
                self.showFlag=true
            }
        },
        handleSource(){
            let self=this
            if(self.colloidStatus=='1'){
                Toast({
                    message: '该胶体未加料，无法切换状态！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }else{
                self.showSource=true
            }
        },
        handleStatus(){
            let self=this
            if(self.colloidStatus=='1'){
                Toast({
                    message: '该胶体未加料，无法切换状态！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }else{
                self.showStatus=true
            }
            
        },
        reback(){
            let self=this

            let params={
                id:self.id,
                acceptUser:localStorage.getItem('userCode'),
                status:self.status,
                lastColloidWeight:self.lastColloidWeight,
                stayFlag:self.stayFlag,
                area:self.rebackArea,
                jobCenter:self.jobCenter,
                rejectFlag:self.rejectFlag,
                rejectSource:self.rejectSource,
                rejectColloidWeight:self.rejectColloidWeight,
                tankInfo:self.tankInfo
            }

            if(self.status!='全部使用'){
                if(self.lastColloidWeight<=0){
                    Toast({
                        message: '结余不得小于等于零！',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }

                if(self.lastColloidWeight>self.volume){
                    Toast({
                        message: '结余不得大于胶体容量！',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }

                if(self.stayFlag=='是'){
                    if(self.jobCenter==null || self.jobCenter==''){
                        Toast({
                            message: '工作中心不得为空！',
                            position: 'bottom',
                            duration: 2000
                        });
                    return;
                    }
                }else{
                    if(self.workshop=='制胶车间'){
                        if(self.rebackArea==null || self.rebackArea==''){
                            Toast({
                                message: '退回区域不得为空！',
                                position: 'bottom',
                                duration: 2000
                            });
                            return;
                        }
                    }
                }
            }

            if(self.rejectFlag=='是'){
                let typeStatus = false;
                let title = '';
                if(self.rejectSource==null || self.rejectSource==''){
                    typeStatus=true;
                    title+='次品胶体来源,'
                }

                if(self.rejectColloidWeight==null || self.rejectColloidWeight==''){
                    typeStatus=true;
                    title+='次品胶体重量,'
                }

                if(self.tankInfo.id==null || self.tankInfo.id==''){
                    typeStatus=true;
                    title+='次品桶信息,'
                }

                if(typeStatus){
                    title+='不得为空!'
                    Toast({
                        message: title,
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            }




            self.loading=true
            self.$axios.post('/jeecg-boot/app/tank/check/rebackColloid',params).then(res=>{
                if(res.data.code==200){
                    self.loading=false
                    self.$route.params.back = "1";
                    self.$router.back();
                }else{
                    self.loading=false
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                        });
                    }
                })
                
        },
        onStatusCancel(){
            this.showStatus=false
        },
        onSourceCancel(){
            this.showSource=false
        },
        onRejectCancel(){
            this.showReject=false
        },
        onFlagCancel(){
            this.showFlag=false
        },
        onJobCenterCancel(){
            this.showJobCenter=false
        },
        onAreaCancel(){
            this.showArea=false
        },
        onRejectConfirm(value){
            this.rejectFlag=value
            this.onRejectCancel();
        },
        onStatusConfirm(value){
            this.status=value;
            this.onStatusCancel();
        },
        onAreaConfirm(value){
            this.rebackArea = value
            this.onAreaCancel();
        },
        onSourceConfirm(value){
            this.rejectSource=value;
            this.onSourceCancel();
        },
        onFlagConfirm(value){
            this.stayFlag=value;
            this.onFlagCancel();
        },
        onJobCenterConfirm(value){
            this.jobCenter=value;
            this.onJobCenterCancel();
        },
        getJobCenter(value){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getJobCenterByShop',{params:{workshop:value}}).then(res=>{
                if(res.data.code==200){
                    self.jobCenterList=res.data.result
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sign{
    width:100%;
    height:5rem;
}
.sign-type{
    height:100%;
    width:100%;
    color:white;
    display: flex;
    align-items: center;
    justify-content: center;
}
/deep/.van-field__control{
    color: black;
}
/deep/.van-field__label{
    color: #777777;
}
</style>