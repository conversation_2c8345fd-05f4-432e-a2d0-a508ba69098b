<template>
    <div class="order">

        <div style="text-align:left;padding:10px;background:white;" @click="changeScanStatus" >
            <div style="float:left;width:80%;">连续扫码</div>
            <div style="float:left;width:20%;text-align:center;" :class="scanStatus=='开'?'scanGreen':'scanRed'">{{scanStatus}}</div>
            <div style="clear:both;"></div>
        </div>


        <div style="width:100%;margin-top:20px;position: relative;">
            <van-field label="托码编号" v-model="stickerId" @keyup.enter.native="queryQrCode"  style="width:70%;float:left;"/>
            <van-button type="info" @click="scanStickerId" style="height:35px;margin:5px;width:25%;border-radius:10px;float:left;">扫码</van-button>
            <div style="clear:both;"></div>
        </div>

        <van-button type="primary" @click="scanQRCode()"  style="margin-left:10px;margin-top:20px;width:40%;border-radius:10px;">扫描箱码</van-button>
        <div style="font-weight:900;">应扫数量：{{palletNum}}个 &emsp;&emsp; 已扫数量：{{qrcode.length}}个</div>
        <!-- <van-button type="primary" @click="writeStickerId()"  style="margin-left:10px;margin-top:40px;width:40%;border-radius:10px;">录入箱码</van-button> -->

        <div v-for="(item,index) in qrcode" :key="index" style="margin-top:20px;background:white;">
            <div style="width:70%;float:left;word-break: break-all;text-align:left;padding:10px;">{{item.qrCode}}<br />{{item.qrId}}</div>
            <van-button type="danger" @click="deleteQrCode(item.id)" style="height:35px;margin:25px;width:15%;border-radius:10px;float:right;">删除</van-button>
            <div style="clear:both;"></div>
        </div>

        
    </div>
</template>
<script>
import { Indicator,MessageBox   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            stickerId:"",
            qrcode:[],
            scanStatus:"开",
            palletNum:0,
        }
    },
    created:function(){
        this.stickerId = localStorage.getItem('stickerId');
        this.scanStatus = localStorage.getItem('scanStatus');
        if(this.scanStatus==null || this.scanStatus==''){
            this.scanStatus="开"
        }
        this.queryQrCode();
        this.getSticker();
    },
    methods:{
        getSticker(){
            this.$axios.get('/jeecg-boot/app/gcQrCode/getSticker',{params:{id:this.stickerId,type:'2'}}).then(res => {
                this.palletNum=res.data.result.palletNum
            })
        },
        writeStickerId(){
            let self = this 
            MessageBox.prompt('请输入箱码编码').then(({ value, action }) => {
                if (action == "confirm") {
                    localStorage.setItem('stickerId',self.stickerId);
                    self.saveQrCode(self.stickerId,value);
                }
            });
            
        },
        changeScanStatus(){
            let self = this
            if(self.scanStatus=='开'){
                self.scanStatus='关'
            }else{
                self.scanStatus='开'
            }
            localStorage.setItem('scanStatus',self.scanStatus);
        },
        scanStickerId(){
            let self = this
            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    self.stickerId = res.resultStr;//当needResult为1时返回处理结果
                    localStorage.setItem('stickerId',self.stickerId);
                    self.queryQrCode();
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
        },
        scanQRCode(){
            if(this.qrcode.length==this.palletNum){
                Toast({
                    message: '箱码已扫够',
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let self = this

            if(self.stickerId=='' || self.stickerId==null){
                Toast({
                    message: "请先扫描托码！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    localStorage.setItem('stickerId',self.stickerId);
                    self.saveQrCode(self.stickerId,result);
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });

            // localStorage.setItem('stickerId',self.stickerId);
            // self.saveQrCode(self.stickerId,'88.297.1.DC1DA5E55FEE4726B1B533BA96A095DC000200');

        },
        saveQrCode(stickerId,qrcode){
            if(this.qrcode.length==this.palletNum){
                Toast({
                    message: '箱码已扫够',
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let self = this
            let params={
                stickerId:stickerId,
                qrId:qrcode,
                creator:localStorage.getItem('userCode')
            }
            console.log(params)
            self.$axios.post('/jeecg-boot/app/workshop/gcStickerQrcodeInfo/add',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.queryQrCode();

                    if(self.scanStatus=='开'){
                        self.scanQRCode();
                    }

                    // self.scanQRCode();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })
            
        },
        queryQrCode(){
            let self = this
            self.$axios.get('/jeecg-boot/app/workshop/gcStickerQrcodeInfo/list',{params:{stickerId:self.stickerId,status:'1'}}).then(res=>{
                if(res.data.code==200){
                    self.qrcode = res.data.result.records
                    self.getSticker();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })
        },
        deleteQrCode(id){
            let self = this
            MessageBox.confirm('',{
                message: '是否确认删除此箱码',
                title: '提示',
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(action => {
                if(action=="confirm"){
                    self.$axios.delete('/jeecg-boot/app/workshop/gcStickerQrcodeInfo/delete',{params:{id:id,userCode:localStorage.getItem('userCode')}}).then(res=>{
                        if(res.data.code==200){
                            self.queryQrCode();
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                }
            }).catch((res)=>{
                // console.log("res:"+res)
            });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.scanGreen{
    color: green;
}
.scanRed{
    color: red;
}
</style>