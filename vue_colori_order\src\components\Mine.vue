<template>
    <div>
        <div class="top_mine_title">我的</div>
        <div style="height:8rem">
            <div class="name_sign">
                <div class="name_title">{{userName}}</div>
                <div class="name_other">{{departmentName}}</div>
                <div class="name_other">{{positionName}}</div>
            </div>
            <div class="name_pic">
                <img :src="avatar" width="70rem" height="70rem" style="border-radius:50%;"/>
            </div>
        </div>
        <div class="mine_line" v-if="userCode=='HI1111120001'"></div>
        <div class="menu_mine_item" @click="push(1)" v-if="userCode=='HI1111120001'">
            <img :src="car" class="left_pic"/>
            <label class="menu_title">派车视图</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(2)" :hidden="isDriver">
            <img :src="car" class="left_pic"/>
            <label class="menu_title">{{$t('mileage')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line" :hidden="isDriver"></div>
        <div class="menu_mine_item" @click="push(31)">
            <img :src="scheduling" class="left_pic"/>
            <label class="menu_title">组长排班</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line" :hidden="isDriver"></div>
        <div class="menu_mine_item" @click="push(3)">
            <img :src="scheduling" class="left_pic"/>
            <label class="menu_title">{{$t('scheduling')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line" :hidden="isDriver"></div>
        <div class="menu_mine_item" @click="push(32)">
            <img :src="ordersReceivedPNG" class="left_pic"/>
            <label class="menu_title">车间接单</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(4)">
            <img :src="attence" class="left_pic"/>
            <label class="menu_title">{{$t('attendance')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(8)">
            <img :src="hour" class="left_pic"/>
            <label class="menu_title">{{$t('workHour')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <!-- <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(5)">
            <img :src="money" class="left_pic"/>
            <label class="menu_title">{{$t('salary')}}</label>
            <img :src="jt" class="right_pic"/>
        </div> -->
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(10)">
            <img :src="money" class="left_pic"/>
            <label class="menu_title">外箱识别码</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(6)">
            <img :src="time_data" class="left_pic"/>
            <label class="menu_title">{{$t('lineData')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(9)">
            <img :src="repair_order" class="left_pic"/>
            <label class="menu_title">{{$t('repairOrder')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="menu_mine_item" @click="push(7)">
            <img :src="exit" class="left_pic"/>
            <label class="menu_title">{{$t('out')}}</label>
            <img :src="jt" class="right_pic"/>
        </div>
        <div class="mine_line"></div>
        <div class="mine_line"></div>
        <div class="mine_line"></div>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
export default {
    props:["message"],
    data(){
        return{
            dept:"",
            position:"",
            name:"",
            userCode:"",
            userName:"",
            positionName:"",
            avatar:"",
            isDriver:false,
            top_image:'https://wework.qpic.cn/wwhead/nMl9ssowtibVGyrmvBiaibzDlBnG0QUNI6WYpHb8vq96HvVnQz6yhKxd6tJlk8X0ewwP0hrtajlwIY/0',
            car:require('../../static/images/car.png'),
            jt:require('../../static/images/jt.png'),
            scheduling:require('../../static/images/scheduling.png'),
            hour:require('../../static/images/hour.png'),
            attence:require('../../static/images/attence.png'),
            money:require('../../static/images/money.png'),
            time_data:require('../../static/images/time_data.png'),
            exit:require('../../static/images/exit.png'),
            repair_order:require('../../static/images/repair-order.png'),
            ordersReceivedPNG:require('../../static/images/ordersReceivedPNG.png'),
        }
    },
    created:function(){
        this.userCode=localStorage.getItem('userCode');
        this.userName=localStorage.getItem('userName');
        this.positionName=localStorage.getItem('positionName');
        this.departmentName=localStorage.getItem('departmentName');
        this.avatar=localStorage.getItem('avatar');
    },
    methods:{
        push(index){
            switch(index){
                case 1:
                    this.$router.push({path:'/car'})
                    break;
                case 2:
                    this.$router.push({path:'/carOdo'})
                    break;
                case 3:
                    this.$router.push({path:'/schedual'})
                    break;
                case 31:
                    this.$router.push({path:'/groupLeaderScheduling'})
                    break;
                case 32:
                    this.$router.push({path:'/ordersReceived'})
                    break;
                case 4:
                    this.$router.push({path:'/attence'})
                    // Toast({
                    //     message: '功能正在优化中，临时关闭！',
                    //     position: 'bottom',
                    //     duration: 2000
                    // });
                    break;
                case 5:
                    Toast({
                        message: '功能暂未开启，敬请期待！',
                        position: 'bottom',
                        duration: 2000
                    });
                    break;
                case 6:
                    this.$router.push({path:'/productData'})
                    break;
                case 7:
                    localStorage.clear();
                    this.$router.push({path:'/login'})
                    break;
                case 8:
                    // Toast({
                    //     message: '功能正在优化中，临时关闭',
                    //     position: 'bottom',
                    //     duration: 2000
                    // });
                    this.$router.push({path:'/myHour'})
                    break;
                case 9:
                    this.$router.push({path:'/RepairOrderSee'})
                    break;
                case 10:
                    this.$router.push({path:'/idIcInfo'})
                    break;
            }
        }
    }
}
</script>
<style>
.top_mine_title{
    font-size: 1.2rem;
    font-weight: 600;
    margin: 3rem;
}
.name_sign{
    float: left;
    width: 60%;
}
.name_pic{
    float: left;
    width: 30%;
    margin-left: 10%;
}
.name_title{
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    margin-left: 5%;
    margin-bottom: 5%;
}
.name_other{
    font-size: 16px;
    text-align: left;
    margin-top: 2%;
    margin-left: 5%;
    color: #898989;
}
.mine_line{
    background: #f5f5f5;
    height: 0.8rem;
}
.menu_mine_item{
    height:3.5rem;
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 5%;
    padding-right: 5%;
}
.left_pic{
    float: left;
    width: 10%;
}
.right_pic{
    position: absolute;
    right: 10px;
    width: 8%;
}
.menu_title{
    font-size: 1rem;
    float: left;
    margin-left: 2%;
}
</style>