<template>
    <div>
        <van-sticky :offset-top="0">
            <div style="background-color: #fff;">
                <van-nav-bar title="模具详情" left-arrow @click-left="onClickLeft" />
                <van-row gutter="20">
                    <van-col span="2"></van-col>
                    <van-col span="4">
                        <van-button type="info" size="mini" @click="Storage">入库</van-button>
                    </van-col>
                    <van-col span="4">
                        <van-button type="info" size="mini" @click="Receive">领用</van-button>
                    </van-col>
                    <van-col span="4">
                        <van-button type="info" size="mini" @click="Return">归还</van-button>
                    </van-col>
                    <van-col span="4">
                        <van-button type="info" size="mini" @click="Destroy">报损</van-button>
                    </van-col>
                    <van-col span="4">
                        <van-button type="info" size="mini" @click="Borrow">借用</van-button>
                    </van-col>
                    <van-col span="2"></van-col>
                </van-row>
            </div>
        </van-sticky>
        <van-field label="编号" :value="info.code" readonly />
        <van-field label="名称" :value="info.name" readonly />
        <van-field label="账套" :value="info.book" readonly />
        <van-field label="车间" :value="info.workshop" readonly />
        <van-field label="单位" :value="info.unit" readonly />
        <van-field label="规格" :value="info.spec" readonly />
        <van-field label="型号" :value="info.model" readonly />
        <van-field label="柜内编号" :value="info.cabinetNo" readonly />
        <van-field label="位置" :value="info.location" readonly />
        <van-field label="用途" :value="info.purpose" readonly />
        <van-field label="总量" :value="info.totalNumber" readonly />
        <van-field label="可用数量" :value="info.availableNumber" readonly />
        <van-field label="领用数量" :value="info.lendNumber" readonly />
        <van-field label="管理员" :value="info.manager" readonly />
        <van-field label="备注" :value="info.remarks" readonly />

        <div style="height:2rem;"></div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            info: {},
        }
    },
    created() {
        this.info = JSON.parse(localStorage.getItem("moduleListItem"))
        console.log(this.info)
    },
    methods: {
        Storage() {
            this.$router.replace({
                name: "ModuleStorage",
                params: this.info
            });
        },
        Receive() {
            this.$router.replace({
                name: "ModuleReceive",
                params: this.info
            });
        },
        Return() {
            this.$router.replace({
                name: "ModuleReturn",
                params: this.info
            });
        },
        Destroy() {
            this.$router.replace({
                name: "ModuleDestroy",
                params: this.info
            });
        },
        Borrow() {
            this.$router.replace({
                name: "ModuleBorrow",
                params: this.info
            });
        },
        onClickLeft() {
            this.$router.replace({
                name: "ModuleInfo"
            });
        }
    },
}
</script>

<style scoped>
</style>