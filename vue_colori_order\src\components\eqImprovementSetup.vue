<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="安装" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-form validate-first @submit="onSubmit">

            <van-field readonly v-model="formData.book" name="book" rows="1" label="账套" type="text" />
            <van-field readonly v-model="formData.workshop" name="workshop" rows="1" label="车间" type="text" />
            <van-field readonly v-model="formData.mitosome" name="mitosome" rows="1" label="工作中心" type="text" />

            <van-field readonly v-model="formData.machineId" name="machineId" rows="1" label="设备ID" type="text" />
            <van-field readonly v-model="formData.machineNo" name="machineNo" rows="1" label="设备编码" type="text" />
            <van-field readonly v-model="formData.machineName" name="machineName" rows="1" label="设备名称" type="text" />
            <van-field v-model="formData.currentSituation" name="currentSituation" rows="3" autosize label="当前状况"
                type="textarea" placeholder="请输入" readonly />
            <van-field v-model="formData.expectSituation" name="expectSituation" rows="3" autosize label="预计情况"
                type="textarea" placeholder="请输入" readonly />
            <van-field readonly clickable name="type" v-model="formData.type" label="改进类型：" />
            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="formData.uploader" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" :max-count="formData.uploader.length"
                        :disabled='true' :deletable="false" />
                </template>
            </van-field>
        </van-form>
        <div v-for="(v, index) in detailList" :key="index" style="margin-top:2rem;">
            <van-field readonly v-model="v.partsName" name="partsName" label="备件名称" placeholder="请输入" type="text" />
            <van-field readonly v-model="v.stockCount" name="stockCount" label="备件库存" placeholder="请输入" type="text" />
            <van-field readonly v-model="v.expectCount" name="expectCount" label="预期数量" placeholder="请输入" type="text" />
            <van-field v-model="v.actualCount" name="actualCount" label="使用数量" placeholder="请输入" type="number" />
            <van-button type="primary" style="width:100%;margin-top:2rem;" @click="onSubmit(v)"
                :disabled="v.status == '5'">安装</van-button>
        </div>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
export default {

    data() {
        return {
            info: {},
            formData: {
                uploader: [],
            },

            addObjData: {},

            showAddPopup: false,



            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenterColumns: [],
            showTypePicker: false,
            typeColumns: ['生产维修', '设备项目'],

            pictureList: [],

            detailList: [],
        };
    },
    created() {
        this.info = this.$route.params
        this.formData = Object.assign(this.formData, { ...this.info })

        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
            if (res.data.code == 200) {
                console.log(res.data.result);
                res.data.result.forEach(item => {
                    this.bookNameColumns.push(item.name);
                });
            } else {
            }
        });
        this.$axios.get(`/jeecg-boot/app/improve/getMacImproveDetailList?mainId=${this.info.id}`).then(res => {
            if (res.data.code == 200) {
                this.detailList = res.data.result
                console.log("🚀 ~ this.$axios.get ~  this.detailList:",  this.detailList)
            } else {
            }
        });
        this.$axios.get(`/jeecg-boot/app/improve/getMacImprovePictureList?mainId=${this.info.id}`).then(res => {
            if (res.data.code == 200) {
                this.formData.uploader = res.data.result.map(item => {
                    return {
                        url: item.picUrl,
                        id: item.id,
                    }
                })
                this.pictureList = res.data.result.map(item => {
                    return {
                        picUrl: item.picUrl,
                        id: item.id,
                    }
                })
            } else {
            }
        });
    },
    methods: {
        getList(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/app/device/list?id=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result.records[0].deviceName);
                        console.log(res.data.result.records[0]);
                        this.$set(self.formData, "machineName", res.data.result.records[0].deviceName)
                        this.$set(self.formData, "machineId", res.data.result.records[0].id)
                        this.$set(self.formData, "machineNo", res.data.result.records[0].deviceNo)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        onSubmit(item) {
            console.log(item.actualCount=='');
            if (item.actualCount==''||item.actualCount==null||item.actualCount==undefined) {
                Toast({
                    message: `使用数量不能为空`,
                    position: "bottom",
                    duration: 2000
                });
                return
            }
            let goalNum = item.stockCount * 1 < item.expectCount * 1 ? item.stockCount * 1 : item.expectCount * 1
            if (item.actualCount > goalNum) {
                Toast({
                    message: `使用数量不能大于预期数量或者库存`,
                    position: "bottom",
                    duration: 2000
                });
                return
            }

            let param = {
                ...item,
                userCode: localStorage.getItem('userCode'),
                userName: localStorage.getItem('userName')
            }
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .put(`/jeecg-boot/app/improve/checkImproveDetail`, param)
                .then(res => {
                    if (res.data.code == 200) {
                        Indicator.close();
                        this.$router.go(-1);
                    } else {
                        Indicator.close();
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios.post(`/jeecg-boot/app/improve/uploadPic`, param).then(res => {
                if (res.data.code == 200) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            console.log(file, name);
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then((res) => {
                    console.log(res);
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/app/mac/deletePic?id=${file.id}&picUrl=${file.url}`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                            } else {
                                Indicator.close();
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });

                })
                .catch(() => {
                    Indicator.close();
                    this.formData.uploader.splice(name.index, 1);
                    this.pictureList.splice(name.index, 1);
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
    }
};
</script>

<style scoped>
:deep(.ant-select-dropdown) {
    z-index: 2050;
}
</style>