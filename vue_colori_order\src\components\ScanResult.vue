<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div v-if="result!='' && result!=null">
            <div class="sign">
                <div class="sign-type" style="background:orange;" v-if="itemParams.qcStatus=='0'">未质检</div>
                <div class="sign-type" style="background:green;" v-if="itemParams.qcStatus=='1'">质检合格</div>
                <div class="sign-type" style="background:red;" v-if="itemParams.qcStatus=='2'">质检不合格</div>
                <div style="clear:both;"></div>
            </div>
            <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">扫描信息</div>
            <van-field label="托码号" :value="result" readonly/>
            <van-field label="MO单号" :value="itemParams.moId" readonly/>
            <van-field label="账套" :value="itemParams.book" readonly/>
            <van-field label="车间" :value="itemParams.workshop" readonly/>
            <van-field label="工作中心" :value="itemParams.mitosome" readonly/>
            <van-field label="组长" :value="itemParams.leaderNo" readonly/>
            <van-field label="班次" :value="itemParams.category" readonly/>
            <van-field label="产品编号" :value="itemParams.code" readonly/>
            <van-field label="产品名称" :value="itemParams.name" readonly/>
            <van-field label="生产日期" :value="itemParams.createTime" readonly/>
            <van-field label="客户批次号" :value="itemParams.customer" readonly/>
            <van-field label="质检人" :value="itemParams.qcCheckerName" v-if="itemParams.qcStatus=='1'" readonly/>
            <van-field label="质检时间" :value="itemParams.qcCheckTime" v-if="itemParams.qcStatus=='1'" readonly/>
            <van-field label="报工状态" value="未报工" v-if="itemParams.cxStatus=='0'" readonly/>
            <van-field label="报工状态" value="已报工" v-if="itemParams.cxStatus=='1'" readonly/>
            <van-field label="报工人" :value="itemParams.cxCheckerName" v-if="itemParams.cxStatus=='1'" readonly/>
            <van-field label="报工时间" :value="itemParams.cxCheckTime" v-if="itemParams.cxStatus=='1'" readonly/>
            <van-field :label="title1" :value="itemParams.palletNum" readonly/>
            <van-field label="换算率" v-model="itemParams.exchange" v-if="type=='2'" readonly/>
            <van-field :label="title2" :value="itemParams.cxOutput?itemParams.cxOutput:0" v-if="type!='2'"  readonly/>
            <van-field :label="title2" :value="itemParams.cxOutput?itemParams.cxOutput:0" v-if="type=='2' && itemParams.infos.length>0"  readonly/>
            <van-field :label="title2" v-model="itemParams.cxOutput" v-if="type=='2' && itemParams.infos.length <= 0" @input="changeOutput" @click="inputOutput"/>
            <van-field :label="title3" v-model="itemParams.cxMinOutput" v-if="type=='2' && itemParams.infos.length>0" readonly/>
            <van-field :label="title3" v-model="itemParams.cxMinOutput" v-if="type=='2' && itemParams.infos.length<=0" @input="changeMinOutput"/>
            <van-field label="入库状态" value="未入库" v-if="itemParams.ckStatus=='0'" readonly/>
            <van-field label="入库状态" value="已入库" v-if="itemParams.ckStatus=='1'" readonly/>
            <van-field label="仓库名称" v-model="itemParams.warehouse" v-if="type=='3'"/>

            <div style="width:100%;position: relative;">
                <van-field label="货位号" v-model="itemParams.hwh" v-if="type=='3'"  @keyup.enter.native="reHwh"  style="width:70%;float:left;"/>
                <van-button type="info" v-if="type=='3'" @click="changeHwh" style="height:35px;margin:5px;width:25%;border-radius:10px;float:left;">扫码</van-button>
                <div style="clear:both;"></div>
            </div>

            

            <div style="width:100%;margin-top:50px;" v-if="type=='1'">
                <van-button type="primary" @click="check('1')" v-if="itemParams.qcStatus!='1'"  style="margin:5px;width:40%;border-radius:10px;">{{$t('qualified')}}</van-button>
                <van-button type="danger" @click="check('2')" v-if="itemParams.qcStatus!='2'"  style="margin:5px;width:40%;border-radius:10px;">{{$t('unqualified')}}</van-button>
            </div>

            <div style="width:100%;margin-top:50px;" v-if="type=='2'">
                <van-button type="primary" @click="submit('1')" v-if="itemParams.cxStatus!='1'"  style="margin:5px;width:40%;border-radius:10px;">{{$t('ok')}}</van-button>
                <van-button type="info" @click="submit('2')" v-if="itemParams.cxStatus=='1'"  style="margin:5px;width:40%;border-radius:10px;">{{$t('change')}}</van-button>
            </div>

            <div style="width:100%;margin-top:50px;" v-if="type=='3' && itemParams.qcStatus=='1' && itemParams.cxStatus=='1'">
                <van-button type="primary" @click="grounding('1')" v-if="itemParams.ckStatus!='1'" :loading="loading" loading-type="spinner"   style="margin:5px;width:40%;border-radius:10px;">{{$t('ok')}}</van-button>
                <van-button type="info" @click="grounding('2')" v-if="itemParams.ckStatus=='1'" :loading="loading" loading-type="spinner"  style="margin:5px;width:40%;border-radius:10px;">{{$t('change')}}</van-button>
                <van-button type="danger" @click="cancelWareHouseIn" v-if="itemParams.ckStatus=='1'"  style="margin:5px;width:40%;border-radius:10px;">{{$t('cancelReceipt')}}</van-button>
            </div>

        </div>
        <div v-else>
            二维码已失效，请退出后重新扫描！
        </div>
    
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            result:'',
            type:'',
            itemParams:{},
            title1:'',
            title2:'',
            title3:'',
            loading:false,
            rackId:'',
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.result=this.$route.params.result
        this.type=this.$route.params.type
        this.getSticker();
    },
    methods: {
        changeHwh(){
            let self=this
            // self.$axios.get('/jeecg-boot/app/sticker/getWarehouseById',{params:{bookName:self.itemParams.book,storeName:self.itemParams.warehouse,rackName:'01010102',id:'01010102'}}).then(res=>{
            //             if(res.data.code==200){
            //                 self.itemParams.hwh=res.data.result.rackName
            //                 self.rackId=res.data.result.rackId
            //             }
            //         })
            wx.scanQRCode({
                    desc: 'scanQRCode desc',
                    needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                    scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                    success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$axios.get('/jeecg-boot/app/sticker/getWarehouseById',{params:{bookName:self.itemParams.book,storeName:self.itemParams.warehouse,rackName:result,id:result}}).then(res=>{
                        if(res.data.code==200){
                            self.itemParams.hwh=res.data.result.rackName
                            self.rackId=res.data.result.id
                        }
                    })
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
        },
        reHwh(e){
            let self=this;
            self.$axios.get('/jeecg-boot/app/sticker/getWarehouseById',{params:{id:e.target.value}}).then(res=>{
                if(res.data.code==200){
                    self.itemParams.hwh=res.data.message
                }
            })
        },
        inputOutput(e){
            let self=this;
            self.itemParams.cxOutput=0
        },
        changeOutput(value){
            let self=this;
            var vcxMinOutput=value*self.itemParams.exchange;
            self.itemParams.cxMinOutput=vcxMinOutput
        },
        changeMinOutput(value){
            let self=this;
            var vcxOutput=value/self.itemParams.exchange;
            if(vcxOutput==parseInt(vcxOutput)){
                vcxOutput = parseInt(vcxOutput);
            }else{
                vcxOutput = vcxOutput.toFixed(3)
            }
            self.itemParams.cxOutput=vcxOutput
        },
        cancelWareHouseIn(){
            let self=this;

            MessageBox.confirm('',{
                message: '请确认是否撤销入库？',
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        
                        self.$axios.get('/jeecg-boot/app/sticker/cancelWareHouseIn',{params:{id:self.result,userCode:localStorage.getItem('userCode')}}).then(res=>{
                            if(res.data.code==200){
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                        position: 'bottom',
                                        duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });


            
        },
        grounding(num){
            let self=this;
            let params={
                id:this.result,
                type:this.type,
                flag:num,
                exchange:self.itemParams.exchange,
                hwh:self.itemParams.hwh,
                warehouse:self.itemParams.warehouse,
                book:self.itemParams.book,
                rackId:self.rackId,
                userCode:localStorage.getItem('userCode')
            }

            if(self.itemParams.hwh==null || self.itemParams.hwh==''){
                Toast({
                    message: '货位号不得为空！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.itemParams.nature=='产成品'){
                Toast({
                    message: '产成品请至WMS进行入库上架！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });


            var message="是否确认提交产品上架的货位号为"+self.itemParams.hwh+"?";
            if(num=='2'){
                message="是否确认更改产品上架的货位号为"+self.itemParams.hwh+"?";
            }


            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.loading=true
                        self.$axios.post('/jeecg-boot/app/gcQrCode/addSticker',params).then(res=>{
                            if(res.data.code==200){
                                Indicator.close();
                                self.loading=false;
                                this.$router.go(-1);
                            }else{
                                Indicator.close();
                                self.loading=false;
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                    Indicator.close();   
                });

        },
        submit(num){
            let self=this;
            let params={
                id:this.result,
                type:this.type,
                flag:num,
                book:self.itemParams.book,
                plot:self.itemParams.plot,
                customer:self.itemParams.customer,
                exchange:self.itemParams.exchange,
                cxOutput:self.itemParams.cxMinOutput,
                rackId:self.rackId,
                userCode:localStorage.getItem('userCode')
            }

            if(self.itemParams.cxMinOutput<=0 || self.itemParams.cxMinOutput==""){
                Toast({
                    message: "请输入产量！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }


            var message="是否确认提报报工数量为"+self.itemParams.cxOutput+self.itemParams.unit+"?";
            if(num=='2'){
                message="是否更改提报报工数量为"+self.itemParams.cxOutput+self.itemParams.unit+"?";
            }



            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/gcQrCode/addSticker',params).then(res=>{
                            if(res.data.code==200){
                                this.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });

        },
        check(num){
            let self=this;

            let params={
                id:this.result,
                type:this.type,
                qcStatus:num,
                userCode:localStorage.getItem('userCode'),
                exchange:self.itemParams.exchange,
                cxOutput:(self.itemParams.cxMinOutput*1),
            }

            var message="是否确认此托码设为合格？";
            if(num=='2'){
                message="是否确认此托码设为不合格？";
            }

            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/gcQrCode/addSticker',params).then(res=>{
                            if(res.data.code==200){
                                this.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });
        },
        getSticker(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcQrCode/getSticker',{params:{id:self.result,type:self.type}}).then(res=>{
                if(res.data.code==200){
                    self.itemParams=res.data.result
                    self.title1="应报工数量("+self.itemParams.unit+")";
                    self.title2="实报工数量("+self.itemParams.unit+")";
                    self.title3="实报工小数量("+self.itemParams.mainUnit+")";

                    if(self.type=='2' || self.type=='1'){
                        if(self.itemParams.cxStatus=='0'){
                            self.itemParams.cxOutput=self.itemParams.palletNum
                            self.itemParams.cxMinOutput=self.itemParams.palletNum*self.itemParams.exchange
                        }else{
                            self.itemParams.cxMinOutput=self.itemParams.cxOutput*self.itemParams.exchange
                        }
                    }

                    if(self.itemParams.infos.length>0){
                        self.itemParams.cxOutput=self.itemParams.infos.length
                        self.itemParams.cxMinOutput=self.itemParams.infos.length*self.itemParams.exchange
                    }

                    if(self.type=='3' && self.itemParams.ckStatus=='0'){
                        if(self.itemParams.code.startsWith('5')){
                            if (self.itemParams.workshop=='灌包车间') {
                                self.itemParams.warehouse = "一车间灌包仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='爽身粉灌包车间') {
                                self.itemParams.warehouse = "爽身粉车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='蛇油灌包车间') {
                                self.itemParams.warehouse = "蛇油车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='彩妆灌包车间') {
                                self.itemParams.warehouse = "彩妆车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='花露水灌包车间') {
                                self.itemParams.warehouse = "花露水车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='消毒灌包车间') {
                                self.itemParams.warehouse = "消毒车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='香皂车间') {
                                self.itemParams.warehouse = "香皂车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='牙膏灌包车间') {
                                self.itemParams.warehouse = "牙膏车间仓";
                                self.itemParams.hwh="";
                            } else if (self.itemParams.workshop=='漱口水灌包车间') {
                                self.itemParams.warehouse = "漱口水车间仓";
                                self.itemParams.hwh="";
                            } else {
                                self.itemParams.warehouse = "车间仓";
                                self.itemParams.hwh="半成品货位";
                            }
                            
                        }else{
                            self.itemParams.warehouse="成品仓库";
                        }
                        
                    }

                }else{
                     Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sign{
    width:100%;
    height:5rem;
}
.sign-type{
    height:100%;
    width:100%;
    color:white;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>