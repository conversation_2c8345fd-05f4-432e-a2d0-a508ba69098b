<template>
  <div style="text-align:center;">
    <van-image
        width="100"
        height="100"
        style="margin-top:100px"
        src="../../static/images/1.png"
        />


    <h3>提交成功！</h3>

  </div>
</template>


<script>
export default {
  name: 'HelloWorld',
  data () {
    return {
      message: '提交成功',
      
    }
  },
  methods: {
    reback() {
      this.$router.go(-1);
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      }
      return val;
    },
    onGxConfirm(value){
      this.info.jjlx.gx=value;
      this.showGxPicker = false;
    },
    onJGxConfirm(value){
      this.info.jsr.gx=value;
      this.showJGxPicker = false;
    },
    onXxjlBConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl.bdate=yy+"年"+mm+"月";
      this.showBDatePicker = false;
    },
    onXxjlEConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl.edate=yy+"年"+mm+"月";
      this.showDDatePicker = false;
    },
    onGzDDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.edate=yy+"年"+mm+"月";
      this.showGzDDatePicker = false;
    },
    onGzBDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.bdate=yy+"年"+mm+"月";
      this.showGzBDatePicker = false;
    },
    onConfirm(value){
      this.info.xueli=value;
      this.showPicker=false;
    }
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.jc {
  margin: 0;
  color: rgba(69, 90, 100, 0.6);
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  padding-left: 4%;
  padding-top: 2%;
  padding-bottom: 2%;
  background: #f0f0f0;
}
h1, h2 {
  font-weight: normal;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
