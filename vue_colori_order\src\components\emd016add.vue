<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar :title="editType == 'edit'?'编辑':'新增'" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-form validate-first @submit="onSubmit" ref="form">

            <van-cell title="点击扫码" @click="handleSearch()" style="width: 90%; " />

            <van-field readonly v-model="formData.bookName" name="content" rows="1" label="账套：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.workshopName" name="content" rows="1" label="车间：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.workCenter" name="content" rows="1" label="工作中心：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipId" name="content" rows="1" label="设备ID" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipCode" name="content" rows="1" label="设备编码" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipName" name="content" rows="1" label="设备名称" type="text"
                :rules="validateRule" />
            <van-field v-model="formData.problem" name="problem" rows="3" autosize label="问题点" type="textarea"
                placeholder="请输入" :rules="validateRule" />
            <van-field readonly clickable name="type" v-model="formData.type" label="发起类型："
                placeholder="点击选择改进类型" @click="showTypePicker = true" :rules="validateRule" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="typeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>


            <van-field readonly clickable name="estimatedMaintenanceTime" v-model="formData.estimatedMaintenanceTime" label="预计完成时间："
                placeholder="点击选择预计完成时间" @click="showTimePicker = true" :rules="validateRule" />
            <van-popup v-model="showTimePicker" position="bottom">
                <van-picker show-toolbar :columns="timeColumns" @confirm="timeConfirm"
                    @cancel="showTimePicker = false" />
            </van-popup>


            <van-field readonly clickable name="picker" v-model="formData.dateChoose" label="日期：" placeholder="点击选择日期"
                @click="show1 = true" :rules="validateRule" />
            <van-calendar v-model="show1" type="single" @confirm="onDateChooseConfirm" :min-date="new Date(2022)"
                color="#1989fa" />
            <!-- <van-field v-model.trim="formData.groupLeader" label="组长" placeholder="请输入组长编号" :rules="[
                { validator: value => validator(value), message: '必填' }
            ]" />
            <van-field v-model="formData.groupLeaderName" label="组长姓名" readonly :rules="validateRule" /> -->
            <van-field @click="groupLeaderClick" readonly clickable v-model.trim="formData.groupLeader" label="组长"
                placeholder="请输入组长编号" :rules="validateRule" />
            <van-field v-model="formData.groupLeaderName" label="组长姓名" readonly :rules="validateRule" />

            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="formData.fileArr" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" :max-count="2" />
                </template>
            </van-field>
        </van-form>





        <van-button type="primary" style="width:100%;margin-top:2rem;" @click="onSubmit">提交</van-button>

        <SearchUserModal ref="modalForm" @ok="modalFormOk"></SearchUserModal>

    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
import moment from "moment";
import SearchUserModal from './list/SearchUserModal.vue'

export default {
    components: { SearchUserModal },
    data() {
        return {
            info: {},
            formData: {},

            validateRule: [
                { required: true, message: '不能为空', trigger: 'onBlur' },
            ],
            show1:false,
            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenterColumns: [],
            showTypePicker: false,
            typeColumns: ['生产维修', '设备项目'],
            showTimePicker: false,
            timeColumns: ['2小时之内', '超过2小时'],

            pictureList: [],

            detailList: [],
            editType: '',
        };
    },
    created() {
        this.info = this.$route.params

        this.formData = {
            ...this.$route.params
        }
        if (this.formData.id) {
            this.editType = 'edit'
            if(this.$route.params.fileUrl){
                this.$set(this.formData,'fileArr',[])
                this.formData.fileArr=[]
                this.$route.params.fileUrl.split(',').map(item=>{
                    this.formData.fileArr.push({
                        url:`http://service.colori.com/jeecg-boot/sys/common/static/${item}`
                    })
                    this.pictureList.push({ picUrl: `${item}` });
                })
            }
        } else {
            this.editType = 'add'
            this.$set(this.formData,'fileArr',[])
        }
        console.log( this.pictureList);
        console.log(this.formData);
        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
            if (res.data.code == 200) {
                console.log(res.data.result);
                res.data.result.forEach(item => {
                    this.bookNameColumns.push(item.name);
                });
            } else {
            }
        });
    },
    methods: {
        modalFormOk(val) {
            this.$set(this.formData, "groupLeader", val.split('-')[1]);
            this.$set(this.formData, "groupLeaderName", val.split('-')[0]);
        },
        groupLeaderClick() {
            console.log(1);
            this.$refs.modalForm.edit({...this.formData});
        },
        leaderChange(item, e) {
            if (item.groupLeader != '' || item.groupLeader != null || item.groupLeader != undefined) {
                item.groupLeader = ''
            }
        },
        // 通过编号搜索名字
        validator(value) {
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        this.$set(this.formData, "groupLeaderName", res.data.message);
                        return true;
                    } else {
                        this.$set(this.formData, "groupLeaderName", '');

                        Toast({
                            message: "请检查组长编码是否正确",
                            position: "bottom",
                            duration: 2000
                        });
                        return false;
                    }
                });
        },
        handleDel(i) {
            this.detailList.splice(i, 1);
        },
        addParts() {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果

                                self.getParts(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入备件编号").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getParts(self.code)
                            }
                        });
                    }
                });
        },
        getParts(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/ncApp/parts/getPartsListByCode?code=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        self.detailList.push({
                            arr: res.data.result
                        })
                    } else {
                        console.log(res.data);
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        handleSearch(v) {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果
                                self.getList(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入设备ID").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getList(self.code)
                            }
                        });
                    }
                });
        },
        onDateChooseConfirm(data) {
            this.show1 = false
            this.formData.dateChoose = moment(data).format('YYYY-MM-DD')
        },
        getList(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/app/device/list?id=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result.records[0]);
                        this.$set(self.formData, "equipName", res.data.result.records[0].deviceName)
                        this.$set(self.formData, "equipId", res.data.result.records[0].id)
                        this.$set(self.formData, "equipCode", res.data.result.records[0].deviceNo)
                        this.$set(self.formData, "bookName", res.data.result.records[0].bookName)
                        this.$set(self.formData, "workshopName", res.data.result.records[0].workshopName)
                        this.$set(self.formData, "workCenter", res.data.result.records[0].jobCenter)
                        console.log('@', self.formData);
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        typeConfirm(value) {
            this.formData.type = value;
            this.showTypePicker = false;
        },
        timeConfirm(value) {
            this.formData.estimatedMaintenanceTime = value;
            this.showTimePicker = false;
        },
        onSubmit() {
            const that =this
            this.$refs.form
                .validate()
                .then((res) => {
                    let param = {
                        ...this.formData,
                        detailList: this.detailList,
                        pictureList: this.pictureList,
                        fileUrl: this.pictureList.map(item=>{return item.picUrl}).join(','),
                        userCode: localStorage.getItem('userCode'),
                        userName: localStorage.getItem('userName')
                    }
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    if (this.editType == 'add') {

                        this.$axios
                            .post(`/jeecg-boot/app/inspection/maintenanceAdd`, param)
                            .then(res => {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                Indicator.close();
                                if (res.data.code == 200) {
                                    MessageBox.confirm("", {
                                        message: "是否发齐审批",
                                        title: "",
                                        confirmButtonText: "是",
                                        cancelButtonText: "否"
                                    })
                                        .then(action => {
                                            if (action == "confirm") {
                                                Indicator.open({
                                                    text: "处理中，请稍后……",
                                                    spinnerType: "fading-circle"
                                                });
                                                that.$axios
                                                    .post("/jeecg-boot/app/inspection/maintenanceApproval", { id: res.data.result, userCode: localStorage.getItem('userCode') })
                                                    .then(resa => {
                                                        Indicator.close();
                                                        if (resa.data.code == 200) {
                                                            that.$router.go(-1);
                                                            Toast({
                                                                message: resa.data.message,
                                                                position: "bottom",
                                                                duration: 2000
                                                            });
                                                        } else {
                                                            Toast({
                                                                message: resa.data.message,
                                                                position: "bottom",
                                                                duration: 2000
                                                            });
                                                        }
                                                    });
                                            }
                                        })
                                        .catch(res => {
                                            that.$router.go(-1);
                                        });
                                } else {
                                }
                            });
                    } else {
                        this.$axios
                            .put(`/jeecg-boot/app/inspection/maintenanceEdit`, param)
                            .then(res => {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                Indicator.close();
                                if (res.data.code == 200) {
                                    MessageBox.confirm("", {
                                        message: "是否发齐审批",
                                        title: "",
                                        confirmButtonText: "是",
                                        cancelButtonText: "否"
                                    })
                                        .then(action => {
                                            if (action == "confirm") {
                                                Indicator.open({
                                                    text: "处理中，请稍后……",
                                                    spinnerType: "fading-circle"
                                                });
                                                that.$axios
                                                    .post("/jeecg-boot/app/inspection/maintenanceApproval", { id: res.data.result, userCode: localStorage.getItem('userCode') })
                                                    .then(resa => {
                                                        Indicator.close();
                                                        if (resa.data.code == 200) {
                                                            that.$router.go(-1);
                                                            Toast({
                                                                message: resa.data.message,
                                                                position: "bottom",
                                                                duration: 2000
                                                            });
                                                        } else {
                                                            Toast({
                                                                message: resa.data.message,
                                                                position: "bottom",
                                                                duration: 2000
                                                            });
                                                        }
                                                    });
                                            }
                                        })
                                        .catch(res => {
                                            that.$router.go(-1);
                                        });
                                } else {
                                }
                            });
                    }

                })
                .catch((err) => {
                    Toast({
                        message: "未填写完成",
                        position: "bottom",
                        duration: 2000
                    });
                });

        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("biz", "016");
            this.$axios.post(`/jeecg-boot/sys/common/upload`, param).then(res => {
                if (res.data.success) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                  
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            console.log(file, name);
            this.formData.fileArr.splice(name.index, 1);
            this.pictureList.splice(name.index, 1);
            // Dialog.confirm({
            //     message: "确定删除吗?",
            //     theme: "round-button",
            //     confirmButtonColor: "#1989fa",
            //     cancelButtonColor: "#CCCCCC"
            // })
            //     .then(() => {
            //         Indicator.open({
            //             text: "处理中，请稍后……",
            //             spinnerType: "fading-circle"
            //         });
            //         this.$axios
            //             .delete(
            //                 `/jeecg-boot/app/mac/deletePic?id=${""}&picUrl=${this.pictureList[name.index].picUrl
            //                 }`
            //             )
            //             .then(res => {
            //                 if (res.data.code == 200) {
            //                     Indicator.close();
            //                     Toast({
            //                         message: "删除成功",
            //                         position: "bottom",
            //                         duration: 2000
            //                     });
            //                     this.formData.uploader.splice(name.index, 1);
            //                     this.pictureList.splice(name.index, 1);
            //                 } else {
            //                     Indicator.close();
            //                     this.formData.uploader.splice(name.index, 1);
            //                     Toast({
            //                         message: "删除失败",
            //                         position: "bottom",
            //                         duration: 2000
            //                     });
            //                 }
            //             });

            //     })
            //     .catch(() => {
            //         Toast({
            //             message: "取消",
            //             position: "bottom",
            //             duration: 1000
            //         });
            //     });
        },
    }
};
</script>

<style scoped></style>