<template>
  <div style="text-align:left;background-color:#fff;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="配料清单" left-arrow @click-left="onClickLeft" />
    </van-sticky>

    <van-field v-model="code" center clearable label="批次号" placeholder="请输入批次号">
      <template #button>
        <van-button size="small" type="info" @click="getMixCheckInfo">查询</van-button>
      </template>
    </van-field>

    <div v-for="(item, index) in dataArr1" :key="index" @click="select(item.id,item)"
      style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="24">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{item.id}}
            </span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 账套：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.book }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 车间：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.workshop }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 所属MO单：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.moId }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体编码：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体批次号</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueBatchCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体名称：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueName }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 工作中心：</van-col>
            <van-col span="18">
              <span style="color:black;">
                {{ item.jobCenter }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 重量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.expectWeight }}KG </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 原料数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.materialNumber }}种 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 标签数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.signNumber }}张 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 备注：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.remark }} </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { MessageBox,Indicator  } from 'mint-ui';
  export default {
    data() {
      return {
        lpId: "",
        dataArr1: [],
        code:'',
      };
    },
    created() {
      this.lpId = this.$route.params.lpId;
      this.getMixCheckInfo();
    },
    methods: {
      onClickLeft() {
        let self = this;
        self.$router.go(-1);
      },
      select(id,item) {
        let self = this;
        MessageBox.confirm('是否确认与此配料单关联？').then(action => {
          if (action == "confirm") {
            let param = {
              lpId: self.lpId,
              mid: id,
              bindingUser: localStorage.getItem('userCode')
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/mix/binding', param).then(res => {
              if (res.data.success) {
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                if(item.checkStatus==1 || item.checkStatus==99){
                  self.$router.go(-1);
                }else{
                  self.$router.push({
                    name: "checkBeforeProduction",
                    params: item
                  });
                }
              } else {
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            }).finally(() => {
              Indicator.close();
            });
          }
        })
      },
      getMixCheckInfo() {
        let self = this;
        self.$axios.get('/jeecg-boot/app/mix/getMixCheckInfo', { params: { lpId: self.lpId,glueBatchCode:self.code } }).then(res => {
          if (res.data.success) {
            this.dataArr1 = res.data.result
          } else {
            this.$router.go(-1)
            // Toast({
            //   message: res.data.message,
            //   position: 'bottom',
            //   duration: 2000
            // });
          }
        })
      }
    }

  };
</script>

<style scoped></style>