<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div style="text-align:left;font-weight:800;padding:10px;font-size:18px;">当前测评人员:{{userName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ remainingTime }}</div> 

        <div style="width:100%;height:1px;background:#5f5f5f"></div>

        <div style="text-align:left;font-weight:800;padding:10px;font-size:18px;" v-if="hintShow">
            <p>&nbsp;&nbsp;&nbsp;&nbsp;首先非常感谢您参与本次360评估，您是被评估人的关键合作伙伴，因此您的评估对其有重大意义，请您在评估中注意以下事项：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;1、在评估时秉承公平、公正、客观原则；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;2、评估时请根据被评估人日常相处印象给予最直观的选项；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;3、评估时请确认环境，避免在评估过程中受到干扰或他人影响；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;4、本次评估结果会记录在系统内。如果您的评估严重偏离事实，影响评估结果，此次评估行为将会对您今后晋升、调岗等均产生严重影响。</p>
            <p>请仔细阅读上述注意事项后再开始测评。</p>

            <van-button type="primary" @click="start()" :disabled="disabled" style="margin-left:30%;width:40%;margin-top:40px;margin-bottom:30px;float:left;">{{readingTitle}}</van-button>
            
        </div>

        <div v-for="(item,index) in evaluationCheck" :key="index">
            <div v-if="item.type!='3' && item.type!='4'">
                <div v-if="item.show&&item.describe!=''" style="text-align:left;font-weight:800;padding:10px;font-size:20px;">测评维度：{{item.describe}}</div>

                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:20px;">测评项目{{index+1}}：{{item.project}}</div>

                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:20px;color:#5529f6">{{item.content}}</div>

                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:20px;">请打分:</div>

                <div class="star-rating" v-if="item.show" style="text-align:right;">
                    <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= item.score }]"
                    @click="setRating(n,index)"
                    @mouseover="hoverRating(n,index)"
                    @mouseleave="hoverRating(0,index)">
                    </i>
                </div>

                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:16px;">评分说明:</div>

                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:16px;margin-left:10px;" v-html="item.illustrate"></div>
            </div>

            <div v-else-if="item.type=='3'">
                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:18px;">人员品性评估</div>
                <div v-if="item.show" style="text-align:left;font-size:16px;">
                    <mt-radio align="left" title="1、电子游戏" v-model="note1" :options="['不了解','无','偶尔','经常']"></mt-radio>
                    <mt-radio align="left" title="2、吸烟" v-model="note2" :options="['不了解','无','偶尔','频繁']"></mt-radio>
                    <mt-radio align="left" title="3、向同事借钱" v-model="note7" :options="['不了解','无','偶尔','经常']"></mt-radio>
                    <mt-radio align="left" title="4、赌博" v-model="note3" :options="['不了解','无','偶尔','经常']"></mt-radio>
                    <mt-radio align="left" title="5、打架斗殴" v-model="note4" :options="['不了解','无','有被处罚经历','有被拘案史']"></mt-radio>
                    <mt-radio align="left" title="6、喝酒" v-model="note5" :options="['不了解','无','偶尔','常喝']"></mt-radio>
                    <mt-radio align="left" title="7、言语粗暴" v-model="note6" :options="['不了解','无','侮辱人格','态度严厉']"></mt-radio>
                </div>
            </div>

            <div v-else-if="item.type=='4'">
                <div v-if="item.show" style="text-align:left;font-weight:800;padding:10px;font-size:18px;">奋斗意愿评估</div>
                <div v-if="item.show" style="text-align:left;font-size:16px;">
                    <mt-radio align="left" title="奋斗意愿" v-model="struggle" :options="['普通劳动者：朝九晚五，偶尔加班','一般奋斗者：为达成目标，坚持不懈的努力','卓越奋斗者：引领变革，塑造新模式、新业务']"></mt-radio>
                    <van-field v-model="project" v-if="struggle.indexOf('卓越奋斗者')!=-1" rows="1" autosize label="具体事例" type="textarea" maxlength="100" placeholder="请输入具体事例" show-word-limit />
                    <div v-if="mineShow">
                        <mt-radio align="left" title="调动意愿" v-model="transfer" :options="['满足现有岗位，无调动意愿','有调动意愿']" @change="handleTransfer"></mt-radio>
                        <div style="margin:10px;text-align:left;font-weight:900;" v-if="transfer=='有调动意愿'">如有机会，您最期望从事/晋升的部门、岗位</div>
                        <van-field-Pick label="部门" v-if="transfer=='有调动意愿'" v-model="deptName" required :columns="['克劳丽体系买手部', '电商生态事业部', '董事长办公室', '财务管理总部', '工艺技术部', 
                        'ODM事业部', '循环加工厂', '质量管理中心', '生产运营中心', '营运与人力资源部', '法务部', '仓储物流部', '行政管理部', '产品创意开发部', '产品开发中心', 
                        '克劳丽人力资源部','口腔事业部','车辆管理部','蛇油技术研究中心','信息管理部','战略经营部','拍摄运营部','直播间运营部','审计安保部','创意拍摄部',
                        '市场部','线上分销事业部','口腔分销业务部']" @change="handleDeptChange" />
                        <van-field-Pick label="岗位" v-if="transfer=='有调动意愿'" v-model="positionName" required :columns="positionColumns" />
                    </div>
                </div>
            </div>

            <van-button v-if="item.show && index>0" type="primary" @click="up(index)" style="margin-left:5%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">{{upBtn}}</van-button>
            <van-button v-if="item.show" type="primary" :loading="loading" @click="next(index)" style="margin-left:10%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">{{nextBtn}}</van-button>

        </div>

<!-- 
        <div v-if="estimateShow">
            

            <van-button type="primary" @click="up(99)" style="margin-left:5%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">{{upBtn}}</van-button>
            <van-button type="primary" :loading="loading" @click="submit()" style="margin-left:10%;width:40%;margin-top:30px;margin-bottom:30px;float:left;">提交</van-button>
        </div> -->


        
        
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

export default {
    data(){
        return{
            selectedRating: 0,
            hoverRatings: 0,
            questions: [
                {
                    text: '电子游戏',
                    answers: ['不了解', '无', '偶尔', '经常'],
                    note:""
                },
            ],
            id:'',
            disabled:true,
            loading:false,
            hintShow:true,
            curPosition:0,
            estimateShow:false,
            manageShow:false,
            mineShow:false,
            note1:'',
            note2:'',
            note3:'',
            note4:'',
            note5:'',
            note6:'',
            note7:'',
            struggle:'',
            transfer:'',
            deptName:'',
            project:'',
            positionName:'',
            positionColumns:{},
            evaluationCheck:[],
            checkInfo:[{
                id:"vdef1",
                project:"可靠度",
                describe:"",
                content:"做事情靠谱的程度",
                illustrate:"1、言行不一致，不汇报、不回应。<br />2、缺乏主动汇报，需多次提醒方能给与回应；<br />3、偶尔需跟踪/督促，能给予回应；<br />4、事事主动跟踪并及时汇报进度/结果；<br />5、能超预期提前完成工作，并能提出优化方案；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef2",
                project:"亲和力",
                describe:"",
                content:"与人沟通合作时表情、言语给人热情亲和感，愿意倾听，神情专注",
                illustrate:"1、表情厌恶，拒绝沟通。<br />2、表情不耐烦，语气粗暴，不愿意倾听，选择性互动；<br />3、表情严肃，语气生硬，选择性倾听与互动；<br />4、表情、语气平和，愿意倾听与互动；<br />5、面带微笑，语气友善，愿意倾听，积极互动；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef3",
                project:"自我程度",
                describe:"",
                content:"能公正平衡个人与他人的需求及利益关系",
                illustrate:"1、几乎总是优先考虑自己的需求，很少考虑他人的需求。可能会忽视或者不关心他人的感受，行为主要由自我驱动，只关注自己的利益。<br />2、倾向于优先考虑自己的需求，虽然有时也会考虑他人的需求。可能会在必要时帮助他人，但其行为主要是由自己的需求和利益驱动的；<br />3、能在自我和他人的需求之间找到平衡。既不会忽视自己的需求，也不会忽视他人的需求。其行为可能会根据具体情况而变化，有时更关注自我，有时更倾向于他人。<br />4、通常会考虑他人的需要，但也会关注自己的需求。可能会在他人和自己的需求之间作出权衡，愿意帮助他人，但也有意识地保护和关注自己的利益。<br />5、总是将他人的需要放在自己的利益之前，倾向于为别人做出牺牲并优先满足他人的请求。具有高度的同情心和公正感，尽管可能面临利己的选择，但其行为总是反映出对他人需求的关注和尊重；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef4",
                project:"担当",
                describe:"",
                content:"愿意扛事，能承担个人/部门工作，引领部门业务发展、变革",
                illustrate:"1、遇到问题，推诿，逃避，不愿意承担责任解决问题；<br />2、遇到问题，解决问题意愿不强，且拖延症较为严重，处理问题结果差强人意；<br />3、遇到问题，能及时处理，但有时结果会有偏差；<br />4、遇到问题，能主动承担并妥善解决；<br />5、主动承担重大发展任务或紧急、困难、危机项目；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef5",
                project:"工作投入程度",
                describe:"",
                content:"工作目标清晰，能全力以赴投入工作",
                illustrate:"1、工作倦怠，精神萎靡，负面情绪、牢骚多，无法有效完成工作；<br />2、精神不振，情绪压抑，不能有效利用工作时间，工作效率差；<br />3、精神饱满，态度端正，能有效、充分利用工作时间；<br />4、精力充沛，乐观积极，能将大量时间投入工作；<br />5、精力充沛，乐观积极，全身心投入工作；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef6",
                project:"成就动机",
                describe:"",
                content:"有明确职业规划和晋升、发展方向，成功意愿较为强烈",
                illustrate:"1、职业定位模糊，对现状认识不足，无改变需求。<br />2、满足现状，无晋升意愿，无改变需求；<br />3、职业定位明确，但晋升意愿不强，有学习和提升的想法，而行动力不足；<br />4、职业发展方向明确，有晋升意愿，能主动学习和提升能力，并付诸行动；<br />5、职业发展方向清晰，有强烈晋升意愿，愿意主动学习及提升自我能力，并能付诸工作当中，提升业绩，注重自我价值实现；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"vdef7",
                project:"稳定性",
                describe:"",
                content:"结合其家庭情况（父母、配偶、子女、亲友等健康、居住地、工作/学习等变动情况）、通勤路程、身心状况、对公司/部门负面情绪、能力与岗位匹配度、工作压力等综合评估其愿意长期稳定在公司工作、发展程度",
                illustrate:"1、随时有跳槽离职风险；<br />2、综合环境对其有一定影响，会因其中一项原因而离职；<br />3、在公司有一定学习和发展意愿，能维持2-3年左右稳定期；<br />4、在公司有较强学习和发展意愿，能维持5年左右稳定期；<br />5、愿意长期在公司发展，离职动机较弱；",
                score:0,
                type:'1',
                show:false,
            },{
                id:"manage1",
                describe:"制定目标",
                project:"制定、拆解、规划、落实",
                content:"管理人员对工作目标，关键业绩指标清晰，能有计划的拆解目标，制定规划，带领团队按计划或超预期完成。",
                illustrate:"1、有安排工作意识，但缺乏相应能力，更偏向于独自埋头苦干；<br />2、任务目标不清晰，对下属日常任务（工作）安排缺乏合理性，下属常抱怨；<br />3、任务目标不清晰，但日常任务（工作）安排明确，能满足部门正常运营；<br />4、短期（年度）目标清晰，并可以分解到日常（月、季），能条理清晰安排下属工作；<br />5、中长期（3-5年规划）目标清晰，并可以分解到日常（月、季、年），能制定实施规划且督促下属定期完成；",
                score:0,
                type:'2',
                show:false,
            },{
                id:"manage2",
                describe:"组织工作",
                project:"分工明确、匹配业务、拓展职能",
                content:"围绕目标有序分工，优化升级工作流程，根据业务需求及时调整组织架构完善、拓展部门职能。",
                illustrate:"1、缺乏经验，部门组织分工不明确，导致大量工作失误；<br />2、部门人员工作分工无法跟随业务发展而及时调整，虽能维持日常工作，但会有部分工作缺失；<br />3、部门人员工作分工能够跟随业务发展而略有调整，有明确的工作职责，基本能完成部门职能工作；<br />4、部门人员工作分工能够跟根据业务需要主动拓展或调整部门组织能力；<br />5、拓展及建立新的部门组织适应业务长期规划发展；",
                score:0,
                type:'2',
                show:false,
            },{
                id:"manage3",
                describe:"沟通交流",
                project:"复盘会议、单独沟通、会议纪要、任务追踪",
                content:"能定期开展部门复盘会议，必要时与下属采取单独沟通，留有记录便于追踪任务完成情况，建立和维持部门良好发展氛围。",
                illustrate:"1、不关注下属意见和需求，缺乏同理心，缺乏内部会议和交流，不能有效传达信息；<br />2、沟通方法单一，必要时会组织会议与单独交流，但忽略下属意见和需求；<br />3、定期开展部门会议，倾听下属需求和意见，必要时采取单独沟通，虽然有时效果会有偏差；<br />4、根据业务、团队发展需求，采用不同方式开展内部讨论与沟通，愿意倾听，尊重他人，必要时采取单独沟通，并留有记录，虽有时缺乏后续跟踪；<br />5、能够有效定期开展部门会议或单独沟通，留有记录并定期跟踪，因地制宜采用不同渠道和方式，确保信息的准确传达，建立与维护部门良好发展趋势；",
                score:0,
                type:'2',
                show:false,
            },{
                id:"manage4",
                describe:"绩效评估",
                project:"制定评估制度、提供基础数据、正向激励人员",
                content:"能积极配合绩效薪酬部门制定评估制度，并能及时准确提供考核基础数据，公平客观实施评估，提供部门工作效率与责任心。",
                illustrate:"1、无部门人员业绩能力的评估制度，部门人心涣散，责任不强；<br />2、虽无绩效评估制度，但部门工作效率尚可，满足基本运营；<br />3、能够按工作内容对部门人员实施较客观评估，虽缺乏一定基础数据与规范制度，但具有较好正向激励效果；<br />4、能实施部分人员业绩能力评估制度，虽不完善，但具有较好正向激励效果；<br />5、能够建立（优化）并实施部门人员业绩能力的评估制度，能有效激励部门人员效率最大化，增强人员主动担当意识；",
                score:0,
                type:'2',
                show:false,
            },{
                id:"manage5",
                describe:"人员培养",
                project:"培养他人、提升自我",
                content:"不断提升自我专业及管理能力，能识别关键岗位人才并有接班人培养计划与行动。",
                illustrate:"1、对部门岗位人才需求定位不清晰，无人才培养意识；<br />2、有培养人员意识，但缺乏培训体系与目标；<br />3、能够识别优秀人员，有一定培养意识，但欠缺培养经验或有所保留；<br />4、能够识别并推荐优秀人员（包括自己的接班人），具有较为系统的培养课程与学习规划；<br />5、能够识别并推荐优秀人员（包括自己的接班人），具有较成熟内部培养体系与轮岗计划，愿意将人才输送其他部门；",
                score:0,
                type:'2',
                show:false,
            },{
                id:"note",
                describe:"人员品性",
                project:"",
                content:"",
                illustrate:"",
                score:0,
                type:'3',
                show:false,
            },{
                id:"struggle",
                describe:"奋斗意愿",
                project:"",
                content:"",
                illustrate:"",
                score:0,
                type:'4',
                show:false,
            }],
            userCode:'',
            startTime:'',
            upBtn:'上一步',
            nextBtn:'下一步',
            userName:'',
            remainingTime:'10:00',
            remainingSeconds: 600, // 5分钟 * 60秒/分钟
            readingSeconds:10,
            readingTitle:'',
            timer: null,
            checkTimer: null
        }
    },
    components:{
        DatetimePicker
    },
    async created() {
        this.id = this.$route.query.id
        this.userName = this.$route.query.name

        this.getEvaluationStatus(this.id)
        this.reading();
    },
    methods: {
        reading(){
            this.checkTimer = setInterval(() => {
                if (this.readingSeconds > 0) {
                    this.readingSeconds -= 1;
                    this.readingTitle="阅读注意事项("+this.readingSeconds+")";
                } else {
                    this.disabled=false
                    this.readingTitle="开始测评";
                }
            }, 1000);
        },
        handleDeptChange(value){
            let self = this
            self.positionName = "";
            self.$axios.get('/jeecg-boot/app/user/getPositionNameByDeptName',{params:{deptName:value}}).then(res=>{
                if(res.data.code==200){
                    self.positionColumns = res.data.result
                }
            });
        },
        handleTransfer(value){
            let self = this
            self.transfer = value
            self.deptName="";
            self.positionName = "";
        },
        start(){
            let self = this
            if(self.userName==null || self.userName=='' || self.userName==undefined){
                MessageBox.prompt('请输入您的姓名').then(({ value, action }) => {
                    if(action=="confirm"){
                        if(value==null || value==''){
                            Toast({
                                message: "请输入您的姓名",
                                position: 'bottom',
                                duration: 2000
                            });
                            return
                        }
                        self.userName=value
                        for(var i=0;i<self.checkInfo.length;i++){
                            self.evaluationCheck.push(self.checkInfo[i])
                        }
                        console.log("userName:"+self.userName)
                        clearInterval(self.checkTimer);
                        self.checkTimer = null;
                        self.$set(self.evaluationCheck[0],"show",true)
                        self.hintShow = false
                        self.startCountdown()
                    }
                });
            }else{
                clearInterval(self.checkTimer);
                self.checkTimer = null;
                self.$set(self.evaluationCheck[0],"show",true)
                self.hintShow = false
                self.startCountdown()
            }
        },
        startCountdown() {
            this.timer = setInterval(() => {
                if (this.remainingSeconds > 0) {
                    this.remainingSeconds -= 1;
                    this.remainingTime = this.formatTime(this.remainingSeconds);
                } else {
                    this.clearCountdown();
                }
            }, 1000);
        },
        clearCountdown() {
            clearInterval(this.timer);
            this.timer = null;
            this.$axios.get('/jeecg-boot/app/user/cancelEvaluation',{params:{id:this.id}}).then(res=>{
                if(res.data.code==200){
                    this.$router.push({path:'/evalFail'});
                }
            });
        },
        formatTime(time) {
            const minutes = Math.floor(time / 60);
            const seconds = time % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        },
        getEvaluationStatus(id){
            let self=this;
            self.manageShow = false
            self.$axios.get('/jeecg-boot/app/user/getEvaluationStatus',{params:{id:id}}).then(res=>{
                if(res.data.code==200){

                    let personInfo = res.data.result
                    if(personInfo.status=='2' || personInfo.status=='4'){
                        clearInterval(self.checkTimer);
                        self.checkTimer = null;
                        self.$router.push({path:'/evalSuccess'});
                    }else if(personInfo.status=='3'){
                        clearInterval(self.checkTimer);
                        self.checkTimer = null;
                        self.$router.push({path:'/evalFail'});
                    }else{
                        if(personInfo.checkinfos=='manage'){
                            self.manageShow = true
                        }
                        if(personInfo.staffName==personInfo.optName){
                            self.mineShow = true
                        }
                        let checkinfos = personInfo.checkinfos.split(",");
                        for(var i=0;i<checkinfos.length;i++){
                            console.log(checkinfos[i])
                            if(checkinfos[i]=='vdef1'){
                                this.evaluationCheck.push(this.checkInfo[0])
                            }else if(checkinfos[i]=='vdef2'){
                                this.evaluationCheck.push(this.checkInfo[1])
                            }else if(checkinfos[i]=='vdef3'){
                                this.evaluationCheck.push(this.checkInfo[2])
                            }else if(checkinfos[i]=='vdef4'){
                                this.evaluationCheck.push(this.checkInfo[3])
                            }else if(checkinfos[i]=='vdef5'){
                                this.evaluationCheck.push(this.checkInfo[4])
                            }else if(checkinfos[i]=='vdef6'){
                                this.evaluationCheck.push(this.checkInfo[5])
                            }else if(checkinfos[i]=='vdef7'){
                                this.evaluationCheck.push(this.checkInfo[6])
                            }else if(checkinfos[i]=='note'){
                                this.evaluationCheck.push(this.checkInfo[12])
                            }else if(checkinfos[i]=='struggle'){
                                this.evaluationCheck.push(this.checkInfo[13])
                            }else if(checkinfos[i]=='project'){
                                this.evaluationCheck.push(this.checkInfo[14])
                            }else if(checkinfos[i]=='manage'){
                                this.evaluationCheck.push(this.checkInfo[7])
                                this.evaluationCheck.push(this.checkInfo[8])
                                this.evaluationCheck.push(this.checkInfo[9])
                                this.evaluationCheck.push(this.checkInfo[10])
                                this.evaluationCheck.push(this.checkInfo[11])
                            }
                        }
                        console.log(this.evaluationCheck)
                    }
                }
            });
        },
        setRating(rating,index) {
            console.log(rating)
            this.$set(this.evaluationCheck[index],"score",rating)
            console.log(this.checkInfo)
        },
        hoverRating(rating,index) {
            // this.hoverRatings = rating;
            // this.$set(this.checkInfo[index],"score",rating)
            // console.log("hoverRatings:"+this.hoverRatings)
        },
        submit(){
            let self = this
            let params = {
                id:this.id,
                vdef1:'0',
                vdef2:'0',
                vdef3:'0',
                vdef4:'0',
                vdef5:'0',
                vdef6:'0',
                vdef7:'0',
                vdef8:'',
                vdef9:'',
                note1:this.note1,
                note2:this.note2,
                note3:this.note3,
                note4:this.note4,
                note5:this.note5,
                note6:this.note6,
                note7:this.note7,
                manage1:'',
                manage2:'',
                manage3:'',
                manage4:'',
                manage5:'',
                struggle:this.struggle,
                project:this.project,
                transfer:this.transfer,
                deptName:this.deptName,
                positionName:this.positionName
            }
            
            for(var i=0;i<self.evaluationCheck.length;i++){
                if(self.evaluationCheck[i].id=='vdef1'){
                    params.vdef1 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef2'){
                    params.vdef2 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef3'){
                    params.vdef3 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef4'){
                    params.vdef4 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef5'){
                    params.vdef5 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef6'){
                    params.vdef6 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef7'){
                    params.vdef7 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef8'){
                    params.vdef8 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='vdef9'){
                    params.vdef9 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='manage1'){
                    params.manage1 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='manage2'){
                    params.manage2 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='manage3'){
                    params.manage3 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='manage4'){
                    params.manage4 = self.evaluationCheck[i].score
                }else if(self.evaluationCheck[i].id=='manage5'){
                    params.manage5 = self.evaluationCheck[i].score
                }
            }

            if(self.id == null || self.id == '' || self.id == undefined){
                //外部人员评估
                params.name=self.userName
                self.submit3(params)
            }else{
                if(params.vdef1=='5' || params.vdef3=='5' || params.vdef4=='5' || params.vdef5=='5' 
                || params.vdef6=='5' || params.manage1=='5' || params.manage2=='5' || params.manage3=='5' 
                || params.manage4=='5' || params.manage5=='5'){
                    MessageBox.confirm('', {
                        message: '您此次评估可能存在不客观、公正的评分项，建议您重新打分。如您对本次评估结果确认无需修改，请您提交。您此次评估结果会直接发送至徐董，对于偏离事实严重的评估，徐董会直接驳回。评估系统会记录您的每次评估，您对同事的评估态度会对您今后调薪、晋升、调岗等产生一定影响',
                        title: '提示',
                        confirmButtonText: '提交',
                        cancelButtonText: '重新评估'
                    })
                    .then(action => {
                        if (action == "confirm") {
                            self.submit2(params)
                        }
                    }).catch((res) => {
                        // console.log("res:"+res)
                    });
                }else{
                    self.submit2(params)
                }
            }
            
        },
        submit3(params){
            let self = this;
            self.loading=true;
            self.$axios.post('/jeecg-boot/app/user/submitExternalInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    self.$router.push({name:'EvalResult',params:params});
                    clearInterval(self.timer);
                    self.timer = null;
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    clearInterval(self.timer);
                    self.timer = null;
                }
            })
        },
        submit2(params){
            let self = this;
            self.loading=true;
            self.$axios.post('/jeecg-boot/app/user/checkEvaluationInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    self.$router.push({path:'/evalSuccess'});
                    clearInterval(self.timer);
                    self.timer = null;
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    clearInterval(self.timer);
                    self.timer = null;
                }
            })
        },
        up(index){
            let self=this;
            if(index==99){
                self.$set(this.evaluationCheck[self.evaluationCheck.length-1],"show",true)
                if(!self.manageShow){
                    self.estimateShow = false;
                }
            }else{
                self.$set(this.evaluationCheck[index],"show",false)
                self.$set(this.evaluationCheck[index-1],"show",true)
                self.nextBtn="下一步"
            }
            
        },
        next(index){
            let self=this;

            if(this.evaluationCheck[index].type == '4'){
                if(self.struggle==null || self.struggle == ''){
                    Toast({
                        message: "奋斗意愿尚未填写完全，请检查！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
                if(this.struggle.indexOf('卓越')!=-1){
                    if(this.project=='' || this.project==null){
                        Toast({
                            message: "具体事例必填",
                            position: 'bottom',
                            duration: 2000
                        });
                        return;
                    }
                }
                if(self.mineShow){
                    if(self.transfer==null || self.transfer == ''){
                        Toast({
                            message: "调动意愿尚未填写完全，请检查！",
                            position: 'bottom',
                            duration: 2000
                        });
                        return;
                    }
                    if(self.transfer=='有调动意愿'){
                        if(self.deptName==null || self.deptName == ''){
                            Toast({
                                message: "部门尚未填写完全，请检查！",
                                position: 'bottom',
                                duration: 2000
                            });
                            return;
                        }
                        if(self.positionName==null || self.positionName==''){
                            Toast({
                                message: "岗位尚未填写完全，请检查！",
                                position: 'bottom',
                                duration: 2000
                            });
                            return;
                        }
                    }
                }
            }

            if(index==self.evaluationCheck.length-1){
                this.submit();
            }else if(index==self.evaluationCheck.length-2){
                self.$set(this.evaluationCheck[index],"show",false)
                self.$set(this.evaluationCheck[index+1],"show",true)
                self.nextBtn = "提交"
            }else if(index+1==self.evaluationCheck.length-1){
                self.$set(this.evaluationCheck[index],"show",false)
                self.$set(this.evaluationCheck[index+1],"show",true)
            }else{
                if(this.evaluationCheck[index].type == '3'){
                    if(self.note1==null || self.note1 == '' || 
                    self.note2==null || self.note2 == '' || 
                    self.note3==null || self.note3 == '' || 
                    self.note4==null || self.note4 == '' ||
                    self.note5==null || self.note5 == '' || 
                    self.note6==null || self.note6 == '' || 
                    self.note7==null || self.note7 == ''){
                        Toast({
                            message: "人员品性评估尚未填写完全，请检查！",
                            position: 'bottom',
                            duration: 2000
                        });
                        return;
                    }
                    self.$set(this.evaluationCheck[index],"show",false)
                    self.$set(this.evaluationCheck[index+1],"show",true)
                }else if(this.evaluationCheck[index].score<=0 && this.evaluationCheck[index].type != '3'){
                    Toast({
                        message: "请评分！",
                        position: 'bottom',
                        duration: 2000
                    });
                }else{
                    self.$set(this.evaluationCheck[index],"show",false)
                    self.$set(this.evaluationCheck[index+1],"show",true)
                }
            }
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.star-rating {
  display: flex;
  margin-right:20px;
  justify-content: right;
}
.star {
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s;
}
.star::before {
  content: '☆';
}
.star.filled::before,
.star:hover::before {
  content: '★';
  color: gold;
}
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
/deep/ .van-field__label{
    width: 16em;
}
/deep/ .van-field__control{
    text-align: center;
    color: #00f;
}
/deep/ .mint-radiolist-title{
    font-size: 18px;
    color: #000;
}
</style>