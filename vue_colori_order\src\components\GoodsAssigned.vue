<template>
  <div style="text-align:left;padding-bottom:1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="指派" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
        @click-right="onClickRight" />
    </van-sticky>
    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
      <van-cell title="选择时间" :value="date" @click="show1 = true" />
      <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)" color="#1989fa" />
      <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
        @click="showbookNamePicker = true" />
      <van-popup v-model="showbookNamePicker" position="bottom">
        <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
          @cancel="showbookNamePicker = false" />
      </van-popup>

      <van-field v-model="checkoutNo" clearable label="出库单号：" placeholder="请输入出库单号" />
      <van-field v-model="checker1" clearable label="复核员：" placeholder="请输入复核员" />
      <van-field v-model="customer" clearable label="客户：" placeholder="请输入客户" />

      <van-button type="info" @click="search1" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>
    <div v-for="(item, index) in dataArr" :key="index"
      style="text-align: left;background-color:#fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="18">
            <span style="font-size:18px;font-weight: 700;color: #000;">{{
                item.checkoutNo
            }}</span>
          </van-col>
          <van-col span="6" v-if="item.creator == userCode || item.leaderNo == userCode">
            <span v-if="item.pickerStatus == 0" style="color: #FF9933;">
              待指派保管员
            </span>
            <span v-else style="color: #009966;">已指派保管员</span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="16">
          <span style="color:gary">单据类型：<span style="color:black">{{ item.type }}</span></span>
        </van-col>

      </van-row>
      <van-row>
        <van-col span="16">
          <span style="color:gary">调度员：&emsp;<span style="color:black">{{ item.leaderName }}</span></span>
        </van-col>
        <van-col span="8"><span style="color:gary">复核员：<span style="color:black">{{
            item.checkerName
        }}</span></span></van-col>
      </van-row>
      <!-- <van-row v-if="item.bgy != ''">
        <van-col span="24">
          <span style="color:gary">
            保管员&emsp;：<span
              style="color:black;overflow-x: auto;white-space:nowrap;"
              >{{ item.bgy }}</span
            ></span
          >
        </van-col>
      </van-row> -->
      <van-row>
        <van-col span="24">
          <span style="color:gary">客户名称：<span style="color:black;overflow-x: auto;white-space:nowrap;">{{ item.customer
          }}</span></span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary;overflow-x: auto;white-space:nowrap;">客户地址：<span style="color:black">{{ item.address
          }}</span></span>
        </van-col>
      </van-row>

      <van-row>
        <van-col span="24" style="text-align:right">
          <!-- 调度员 -->
          <van-button v-if="item.creator == userCode" size="mini" round hairline type="info"
            @click="assignDispatcher(item)">指派调度员
          </van-button>

          <van-popup v-model="show4" position="bottom" :style="{ height: '35%' }">
            <van-field readonly clickable name="picker" v-model="dispatcher" label="调度员：" placeholder="点击选择调度员"
              @click="showDispatcherPicker = true" />
            <van-popup v-model="showDispatcherPicker" position="bottom">
              <van-picker show-toolbar :columns="dispatcherColumns" @confirm="value => dispatcherConfirm(value)"
                @cancel="showDispatcherPicker = false" />
            </van-popup>

            <van-button type="info" @click="assign(3)" style="width: 100%;" round>
              确定
            </van-button>
          </van-popup>

          <!-- 保管员
            v-if="item.creator == userCode && item.bgy == ''"
          -->
          <van-button v-if="item.creator == userCode" size="mini" round hairline type="info"
            @click="assignkeeper(item)">指派保管员
          </van-button>

          <van-popup v-model="show2" position="bottom" :style="{ height: '35%' }">
            <div v-for="(item1, x) in arrCheck" :key="x">
              <van-field readonly clickable :value="item1.user" :label="item1.area" placeholder="点击指派"
                @click="showCheckPickerFn(item1, x)" />
              <van-popup v-model="showCheckPicker" position="bottom">
                <van-picker show-toolbar :columns="checkColumns" @confirm="onCheckConfirm"
                  @cancel="showCheckPicker = false" />
              </van-popup>
            </div>
            <van-button type="info" @click="assign(1)" style="width: 100%;" round>
              确定
            </van-button>
          </van-popup>
          <!-- 复核员 
            v-if="item.leaderNo == userCode && !item.checkerNo"
          -->
          <van-button v-if="item.leaderNo == userCode" size="mini" hairline round type="info"
            @click="assignReviewers(item)">指派复核员
          </van-button>

          <van-popup v-model="show3" position="bottom" :style="{ height: '30%' }">
            <van-field readonly clickable name="picker" v-model="checker" label="复核员：" placeholder="点击选择复核员"
              @click="showcheckerPicker = true" />
            <van-popup v-model="showcheckerPicker" position="bottom">
              <van-picker show-toolbar :columns="checkerColumns" @confirm="value => checkerConfirm(value)"
                @cancel="showcheckerPicker = false" />
            </van-popup>

            <van-button type="info" @click="assign(2)" style="width: 100%;" round>
              确定
            </van-button>
          </van-popup>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      date: this.getDate(-1) + " ~ " + this.getDate(0),
      beginDay: this.getDate(-1),
      endDay: this.getDate(0),
      userCode: localStorage.getItem("userCode"),
      // 搜索弹出层是否显示
      show: false,
      // 复核员选择框
      showcheckerPicker: false,
      // 时间选择显示隐藏
      show1: false,
      // 指派保管员
      show2: false,
      // 指派复核员
      show3: false,
      dataArr: [],
      checker: "",
      // 指派数组
      arrCheck: [],

      // 区域选择显示隐藏
      showCheckPicker: false,
      // 区域数组
      checkColumns: [],
      // 复核员数组
      checkerColumns: [],

      clickIndex: "",

      showbookNamePicker: false,
      bookName: "",
      bookNameColumns: [],

      checker1: "",
      checkoutNo: "",
      customer: "",

      show4: false,
      arrDispatcher: [],
      showDispatcherPicker: false,
      dispatcher: "",
      dispatcherColumns: [],
      info: {},
    };
  },
  created() {
    // localStorage.setItem("userCode",'HI2205060080')
    this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
      if (res.data.code == 200) {
        console.log(res.data.result);
        res.data.result.forEach(item => {
          this.bookNameColumns.push(item.name);
        });
      } else {
      }
    });
    this.search(this.beginDay, this.endDay);
  },
  methods: {
    bookNameConfirm(value) {
      this.bookName = value;
      this.showbookNamePicker = false;
    },
    search1() {
      this.search(this.beginDay, this.endDay);
      this.show = false;
    },

    assign(type) {
      if (type == 1) {
        this.show2 = false;
        // 统计指派 保管员
        if (this.info.creator == localStorage.getItem("userCode")) {
          // 指派保管员
          this.info.pickerList = this.arrCheck;
          this.info.pickerList.forEach(item => {
            delete item.data
          })
          this.info.userCode = localStorage.getItem("userCode");
          this.$axios
            .post("/jeecg-boot/app/warehousePick/getPickPicker", this.info)
            .then(res => {
              if (res.data.code == 200) {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
                this.search(this.beginDay, this.endDay);
              } else {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
              }
            });
        } else {
          Toast({
            message: "您不是该单子的统计员",
            position: "bottom",
            duration: 2000
          });
        }
      } else if (type == 2) {
        console.log('复核员2', this.info);
        // 指派复核员
        // 调度员指派复核员
        if (this.info.leaderNo == localStorage.getItem("userCode")) {
          if (
            this.checker == "" ||
            this.checker == null ||
            this.checker == undefined
          ) {
            Toast({
              message: "复核员姓名与编号不能为空",
              position: "center",
              duration: 2000
            });
          } else {
            this.info.userCode = localStorage.getItem("userCode");
            this.$axios
              .post("/jeecg-boot/app/warehousePick/getPickChecker", this.info)
              .then(res => {
                if (res.data.code == 200) {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                  this.show3 = false;
                  this.checkerNo = "";
                  this.checkerName = "";
                  this.search(1, "", "");
                } else {
                  this.show3 = false;
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                }
              });
          }
        } else {
          this.show3 = false;
          Toast({
            message: "您不是该单子的调度员",
            position: "bottom",
            duration: 2000
          });
        }
      } else if (type == 3) {
        console.log('外确定info', this.info);
        // 指派调度员
        if (
          this.dispatcher == "" ||
          this.dispatcher == null ||
          this.dispatcher == undefined
        ) {
          Toast({
            message: "调度员姓名与编号不能为空",
            position: "bottom",
            duration: 2000
          });
        } else {
          this.info.userCode = localStorage.getItem("userCode");
          this.$axios
            .post("/jeecg-boot/app/warehousePick/getPickLeader", this.info)
            .then(res => {
              if (res.data.code == 200) {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
                this.show4 = false;
                this.dispatcherNo = "";
                this.dispatcherName = "";
                this.search(1, "", "");
              } else {
                this.show4 = false;
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
              }
            });
        }
      }
    },
    search(beginDay, endDay) {
      this.$axios
        .get(
          `/jeecg-boot/app/warehousePick/getPickMainList?userCode=${this.userCode}&status=2&beginDay=${beginDay}&endDay=${endDay}&book=${this.bookName}&checkoutNo=${this.checkoutNo}&checker=${this.checker1}&customer=${this.customer}&type=2`
        )
        .then(res => {
          if (res.data.code == 200) {
            this.dataArr = res.data.result.records;
            // if (this.dataArr.length) {
            //   this.dataArr.forEach(item => {
            //     let str = "";
            //     let arr = [];
            //     item.detailList.forEach(v => {
            //       arr.push(v.checkerName);
            //     });
            //     arr = [...new Set(arr)];
            //     arr.forEach(v => {
            //       if (v) {
            //         str += v + ",";
            //       }
            //     });
            //     str = str.substring(0, str.lastIndexOf(","));
            //     item.bgy = str;
            //   });
            //   console.log(this.dataArr);
            // }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    onClickLeft() {
      this.$router.replace({
        name: "GoodsOutbound"
      });
    },
    onClickRight() {
      this.show = true;
    },
    getDate(day) {
      var date1 = new Date(),
        time1 =
          date1.getFullYear() +
          "-" +
          (date1.getMonth() + 1) +
          "-" +
          date1.getDate(); //time1表示当前时间
      var date2 = new Date(date1);
      date2.setDate(date1.getDate() + day);
      return (
        date2.getFullYear() +
        "-" +
        (date2.getMonth() + 1 < 10
          ? "0" + (date2.getMonth() + 1)
          : date2.getMonth() + 1) +
        "-" +
        (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
      );
    },
    onConfirm(date) {
      const [start, end] = date;
      this.show1 = false;
      this.beginDay =
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1 < 10
          ? "0" + (start.getMonth() + 1)
          : start.getMonth() + 1) +
        "-" +
        (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
      this.endDay =
        end.getFullYear() +
        "-" +
        (end.getMonth() + 1 < 10
          ? "0" + (end.getMonth() + 1)
          : end.getMonth() + 1) +
        "-" +
        (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
      this.date = `${this.beginDay}~${this.endDay}`;
    },
    searchPeople() {
      this.$axios
        .get(
          `/jeecg-boot/app/utils/getStaffNameByCode?userCode=${this.checkerNo}`
        )
        .then(res => {
          if (res.data.message != null) {
            this.checkerName = res.data.message;
          } else {
            this.checkerName = "";
            Toast({
              message: "编号错误,请重新输入",
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    assignkeeper(x) {
      this.info = x
      console.log(this.info);
      this.$axios
        .get(
          `/jeecg-boot/app/warehouseRack/getAreaConnect?id=${this.info.id}&userCode=${this.userCode}`
        )
        .then(res => {
          if (res.data.code == 200) {
            this.show2 = true;
            this.arrCheck = res.data.result;
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    assignDispatcher(x) {
      this.dispatcher = ''
      this.info = x
      console.log(this.info);
      this.$axios
        .get(`/jeecg-boot/app/warehousePeople/getCheckerInfo?type=1`)
        .then(res => {
          if (res.data.code == 200) {
            this.show4 = true;
            this.dispatcherColumns = [];
            if (res.data.result) {
              res.data.result.forEach(item => {
                this.dispatcherColumns.push(
                  `${item.userName}-${item.userCode}`
                );
              });
            }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    assignReviewers(x) {
      this.checker = ''
      this.info = x
      console.log(this.info);
      this.$axios
        .get(`/jeecg-boot/app/warehousePeople/getCheckerInfo?type=2`)
        .then(res => {
          if (res.data.code == 200) {
            this.show3 = true;
            this.checkerColumns = [];
            if (res.data.result) {
              res.data.result.forEach(item => {
                this.checkerColumns.push(`${item.userName}-${item.userCode}`);
              });
            }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    // 区域确认
    onCheckConfirm(value) {
      this.arrCheck[this.clickIndex].user = value;
      this.arrCheck[this.clickIndex].userName = value.split("-")[0];
      this.arrCheck[this.clickIndex].userCode = value.split("-")[1];
      this.showCheckPicker = false;

      console.log(this.arrCheck);
    },
    showCheckPickerFn(item, index) {
      this.checkColumns = []
      this.clickIndex = index;
      this.showCheckPicker = true;
      item.data.forEach(item => {
        this.checkColumns.push(item.userName + '-' + item.userCode)
      })
    },
    checkerConfirm(value) {
      this.checker = value;
      this.info.checkerName = value.split("-")[0];
      this.info.checkerNo = value.split("-")[1];
      this.showcheckerPicker = false;
    },
    dispatcherConfirm(value) {
      this.dispatcher = value;
      this.info.leaderName = value.split("-")[0];
      this.info.leaderNo = value.split("-")[1];
      this.showDispatcherPicker = false;
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
