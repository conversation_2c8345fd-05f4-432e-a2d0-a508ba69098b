<template>
  <div id="logo">
    <van-image :src="require('../assets/manage_logo.png')" contain style="width:25%;position:absolute;top:2%;left:70%">
    </van-image>

    <van-sticky>
      <van-steps :active="active"
        style="text-align:center;background-color:transparent;width:80%;position:absolute;left:10%;top:10%;text-color:#fff"
        active-color="#00ff00">
        <van-step style="margin-left: -23px;">基本信息</van-step>
        <!-- <van-step>学习经历</van-step> -->
        <van-step>工作经历</van-step>
        <van-step>家庭成员</van-step>
        <van-step>提交</van-step>
      </van-steps>
    </van-sticky>

    <div v-show="active == 0">
      <section>
        <van-cell-group style="margin-top:10rem;text-align:center;">
          <van-col span="8"></van-col>
          <van-col span="8">
            <div style="border:0px solid #d9d9d9; margin-right: 5px;">
              <!-- <van-uploader
                v-model="fileList"
                :before-read="beforeRead"
                accept="image/*"
                :max-size="5 * 1024 * 1024"
                @oversize="onOversize"
                :after-read="afterRead"
                :preview-image="notpr"
              >
                <van-image
                  width="100%"
                  height="100%"
                  fit="contain"
                  :src="baseinfo.photourl"
                />
              </van-uploader> -->
              <van-image width="100%" height="100%" fit="contain" :src="baseinfo.photourl" />
            </div>
          </van-col>
          <div class="form_item">
            <van-field v-model="baseinfo.paperNum" maxlength="18" disabled clearable label="身份证"
              placeholder="请输入身份证号码" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.staffName" clearable label="姓名" disabled placeholder="请输入姓名" />
          </div>
          <div class="form_item">
            <van-field-Pick v-model="baseinfo.sex" label="性别" placeholder="请选择性别" required :columns="['男', '女']" />
          </div>
          <div class="form_item">
            <!-- <DatePick v-model="baseinfo.birthday" label="出生日期" required placeholder="请选择出生日期" /> -->
            <van-field v-model="baseinfo.birthday" clearable label="出生日期" disabled placeholder="请输入出生日期" />
          </div>
          <!-- <div class="form_item">
            <van-field
              v-model="baseinfo.shengao"
              type="number"
              maxlength="3"
              required
              clearable
              label="身高"
              placeholder="请输入身高(cm)"
            />
          </div>
          <div class="form_item">
            <van-field
              v-model="baseinfo.tizhong"
              type="number"
              maxlength="5"
              required
              clearable
              label="体重"
              placeholder="请输入体重(Kg)"
            />
          </div> -->
          <div class="form_item">
            <van-field-Pick v-model="baseinfo.marriageorno" label="婚育情况" placeholder="请选择" required
              :columns="['未婚', '已婚', '已婚育']" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.ethnic" clearable label="民族" disabled placeholder="请输入民族" />
            <!-- <van-field-Pick disabled v-model="baseinfo.ethnic" label="民族" placeholder="请选择" required :columns="[
              '汉族',
              '蒙古族',
              '回族',
              '藏族',
              '维吾尔族',
              '苗族',
              '彝族',
              '壮族',
              '布依族',
              '朝鲜族',
              '满族',
              '侗族',
              '瑶族',
              '白族',
              '土家族',
              '哈尼族',
              '哈萨克族',
              '傣族',
              '黎族',
              '僳僳族',
              '佤族',
              '畲族',
              '高山族',
              '拉祜族',
              '水族',
              '东乡族',
              '纳西族',
              '景颇族',
              '柯尔克孜族',
              '土族',
              '达斡尔族',
              '仫佬族',
              '羌族',
              '布朗族',
              '撒拉族',
              '毛南族',
              '仡佬族',
              '锡伯族',
              '阿昌族',
              '普米族',
              '塔吉克族',
              '怒族',
              '乌孜别克族',
              '俄罗斯族',
              '鄂温克族',
              '德昂族',
              '保安族',
              '裕固族',
              '京族',
              '塔塔尔族',
              '独龙族',
              '鄂伦春族',
              '赫哲族',
              '门巴族',
              '珞巴族',
              '基诺族'
            ]" /> -->
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.comefrom" clearable label="籍贯" disabled placeholder="请输入籍贯" />
            <!-- <van-field-Pick disabled v-model="baseinfo.comefrom" label="籍贯" placeholder="请选择" required :columns="[
              '北京',
              '天津',
              '河北',
              '山西',
              '内蒙古',
              '辽宁',
              '吉林',
              '黑龙江',
              '上海',
              '江苏',
              '浙江',
              '安徽',
              '福建',
              '江西',
              '山东',
              '河南',
              '湖北',
              '湖南',
              '广东',
              '广西',
              '海南',
              '重庆',
              '四川',
              '贵州',
              '云南',
              '西藏',
              '陕西',
              '甘肃',
              '青海',
              '宁夏',
              '新疆'
            ]" /> -->
          </div>
          <div class="form_item">
            <van-field-Pick v-model="baseinfo.ncFace" label="政治面貌" placeholder="请选择" required
              :columns="['中共党员', '中共预备党员', '共青团员', '群众']" />
          </div>

          <!-- <div class="form_item">
            <van-field
              v-model="baseinfo.sfzzz"
              required
              clearable
              label="身份住址"
              placeholder="请输入身份证地址地址"
            />
          </div> -->
          <div class="form_item">
            <van-field v-model="baseinfo.education" disabled clearable label="学历" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.educationMsg" disabled clearable label="入学方式" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.telPhone" type="tel" disabled label="联系电话" placeholder="请输入电话号码" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.ncEmail" type="email" clearable label="电子邮箱" placeholder="请输入Email地址" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.introducePeople" clearable label="介绍人" placeholder="请输入介绍人" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.sosName" clearable label="紧急联系姓名" placeholder="请输入紧急联系人姓名" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.sosRelation" clearable label="紧急联系关系" placeholder="请输入紧急联系人关系" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.sosTel" type="tel" clearable label="紧急联系电话" placeholder="请输入紧急联系人电话" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.sosAddress" clearable label="紧急联系地址" placeholder="请输入紧急联系人地址" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.address" disabled clearable type="textarea" label="身份证住址" placeholder="请输入身份证住址" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.nowAddress" clearable type="textarea" label="现住址" placeholder="请输入现在居住地址" />
          </div>
          <!-- <div class="form_item">
            <van-field
              v-model="baseinfo.positionName"
              required
              clearable
              label="应聘岗位"
              placeholder="请输入应聘岗位"
            />
          </div> -->
          <!-- <div class="form_item">
            <van-field-Pick
              v-model="baseinfo.zhuangtai"
              label="目前状态"
              placeholder="请选择"
              required
              :columns="['在职', '待业']"
            />
          </div> -->
          <!-- <div class="form_item">
            <van-field-Pick
              v-model="baseinfo.dgsj"
              label="到岗时间"
              placeholder="请选择"
              required
              :columns="['一周内', '半个月', '一个月', '一个月以上']"
            />
          </div> -->
          <!-- <div class="form_item">
            <van-field
              v-model="baseinfo.xzyq"
              required
              clearable
              label="期望薪资"
              placeholder="请输入税前薪资要求"
            />
          </div> -->
        </van-cell-group>
        <!-- <van-image
          :src="require('../assets/manage_jj.png')"
          fit="contain"
          style="margin-top:1rem;"
        ></van-image>
        <van-cell-group>
           <div class="form_item">
            <van-field
              v-model="baseinfo.jjlxr.xm"
              required
              clearable
              label="姓名"
              placeholder="请输入紧急联系人姓名"
            />
          </div> 
           <div class="form_item">
            <van-field-Pick
              required
              v-model="baseinfo.jjlxr.gx"
              label="关系"
              placeholder="请选择与联系人的关系"
              :columns="[
                '父亲',
                '母亲',
                '丈夫',
                '妻子',
                '儿子',
                '女儿',
                '爷爷',
                '奶奶',
                '孙子',
                '孙女',
                '兄弟',
                '姐妹',
                '其他'
              ]"
            />
          </div> 
          <div class="form_item">
            <van-field
              v-model="baseinfo.jjlxr.sosTel"
              type="tel"
              required
              clearable
              label="联系电话"
              placeholder="请输入联系人手机号码"
            />
          </div>
          <div class="form_item">
            <van-field
              v-model="baseinfo.jjlxr.address"
              required
              clearable
              label="地址"
              placeholder="请输入地址"
            />
          </div>
        </van-cell-group> -->

        <!-- <van-image
          :src="require('../assets/manage_other.png')"
          fit="contain"
          style="margin-top:1rem;"
        ></van-image>
        <van-cell-group>
          <div class="form_item">
            <van-field
              v-model="baseinfo.aihaotc"
              clearable
              label="爱好特长"
              placeholder="请输入个人爱好及特长"
            />
          </div>
          <div class="form_item">
            <van-field
              v-model="baseinfo.yinyusp"
              clearable
              label="英语水平"
              placeholder="请输入英语水平或证书"
            />
          </div>
          <div class="form_item">
            <van-field
              v-model="baseinfo.waiyusp"
              clearable
              label="其他外语"
              placeholder="请输入其他外语或证书"
            />
          </div>
          <div class="form_item">
            <van-field
              v-model="baseinfo.zsjl"
              clearable
              label="证书/培训"
              placeholder="请输入所持证书或培训经历"
            />
          </div>
          <div class="form_item">
            <van-field-Pick
              v-model="baseinfo.wsgzjl"
              label="我司工作经历"
              placeholder="请选择"
              required
              :columns="['无', '工作过', '实习过', '临时工', '其他']"
            />
          </div>
          <div class="form_item">
            <van-switch-cell
              v-model="jsrchecked"
              @change="diplayjsr()"
              title="是否有我司内部介绍"
            />
          </div>
        </van-cell-group> -->
        <!-- <div v-show="this.jsrchecked">
          <van-cell-group>
            <div class="form_item">
              <van-field
                v-model="baseinfo.jsr.xm"
                clearable
                label="姓名"
                placeholder="请输入介绍人姓名"
              />
            </div>
            <div class="form_item">
              <van-field-Pick
                v-model="baseinfo.jsr.gx"
                label="关系"
                placeholder="请选择与介绍人的关系"
                :columns="['亲戚', '朋友', '旧同事', '其他']"
              />
            </div>
            <div class="form_item">
              <van-field
                v-model="baseinfo.jsr.bm"
                clearable
                label="部门"
                placeholder="请输入介绍人部门"
              />
            </div>
          </van-cell-group>
        </div> -->
      </section>
      <div class="bottom_div">
        <!-- <van-image :src="require('../assets/button_up.png')" disabled fit="contain" width="35%" @click="prevstep()"></van-image> -->
        <van-image :src="require('../assets/button_next.png')" fit="contain" width="35%" @click="nextstep()">
        </van-image>
      </div>
      <van-image :src="require('../assets/buttom.png')" fit="contain" style="margin-top:1rem;"></van-image>
    </div>

    <!-- <div v-show="active == 1" style="margin-top:18rem;">
      <section>
        <van-cell-group style="margin-top:1rem;text-align:center;" v-for="(item, index) in baseinfo.xxjl" :key="index">
          <div class="item_group">学习经历{{ index + 1 }}</div>
          <div class="form_item">
            <Yearmonthpick v-model="item.bdate" label="起始时间" required placeholder="请选择起始年月" />
          </div>
          <div class="form_item">
            <Yearmonthpick v-model="item.edate" label="结束时间" required placeholder="请选择截止年月" />
          </div>
          <div class="form_item">
            <van-field v-model="item.school" clearable label="学校名称" required placeholder="请输入学校名称" />
          </div>
          <div class="form_item">
            <van-field v-model="item.professional" clearable label="专业" required placeholder="请输入专业名称" />
          </div>
          <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
            <div class="item_group" style="border-radius: 10px;" @click="reduceEdu(item)">
              减少
            </div>
          </div>
        </van-cell-group>
        <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
          <div class="item_group" style="border-radius: 10px;" @click="addEdu()">
            新增
          </div>
        </div>
      </section>
      <div class="bottom_div">
        <van-image :src="require('../assets/button_up.png')" disabled fit="contain" width="35%" @click="prevstep()">
        </van-image>
        <van-image :src="require('../assets/button_next.png')" fit="contain" width="35%" @click="nextstep()">
        </van-image>
      </div>
      <van-image :src="require('../assets/buttom.png')" fit="contain" style="margin-top:1rem;"></van-image>
    </div> -->

    <div v-show="active == 1" style="margin-top:18rem;">
      <section>
        <van-cell-group style="margin-top:1rem;text-align:center;" v-for="(item, index) in baseinfo.gzjl" :key="index">
          <div class="item_group">工作经历{{ index + 1 }}</div>
          <div class="form_item">
            <yearmonthpick v-model="item.bdate" label="起始时间" placeholder="请选择起始年月" />
          </div>
          <div class="form_item">
            <yearmonthpick v-model="item.edate" label="结束时间" placeholder="请选择截止年月" />
          </div>
          <div class="form_item">
            <van-field v-model="item.comp" clearable label="工作单位" placeholder="请输入工作单位或地址" />
          </div>
          <div class="form_item">
            <van-field v-model="item.zhiwu" clearable label="职务" placeholder="请输入职务名称" />
          </div>
          <div class="form_item">
            <van-field v-model="item.zmr" clearable label="证明人" placeholder="请输入证明人" />
          </div>
          <div class="form_item">
            <van-field v-model="item.lxdh" clearable label="联系电话" placeholder="请输入联系电话" />
          </div>
          <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
            <div class="item_group" style="border-radius: 10px;" @click="reduceJob(item)">
              减少
            </div>
          </div>
        </van-cell-group>
        <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
          <div class="item_group" style="border-radius: 10px;" @click="addJob()">
            新增
          </div>
        </div>
      </section>
      <div class="bottom_div">
        <van-image :src="require('../assets/button_up.png')" disabled fit="contain" width="35%" @click="prevstep()">
        </van-image>
        <van-image :src="require('../assets/button_next.png')" fit="contain" width="35%" @click="nextstep()">
        </van-image>
      </div>
      <van-image :src="require('../assets/buttom.png')" fit="contain" style="margin-top:1rem;"></van-image>
    </div>

    <div v-show="active == 2" style="margin-top:18rem;">
      <section>
        <van-cell-group style="margin-top:1rem;text-align:center;" v-for="(item, index) in baseinfo.jtcy" :key="index">
          <div class="item_group">家庭成员{{ index + 1 }}</div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].xm" required clearable label="姓名" placeholder="请输入成员姓名" />
          </div>
          <div class="form_item">
            <van-field-Pick required v-model="baseinfo.jtcy[index].gx" label="关系" placeholder="请选择与成员的关系" :columns="[
              '父亲',
              '母亲',
              '丈夫',
              '妻子',
              '儿子',
              '女儿',
              '爷爷',
              '奶奶',
              '孙子',
              '孙女',
              '兄弟',
              '姐妹',
              '其他'
            ]" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].nl" type="number" required clearable label="年龄"
              placeholder="请输入成员年龄" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].gzdw" required clearable label="工作单位" placeholder="请输入成员工作单位" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].zw" required clearable label="职务" placeholder="请输入成员职务" />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].tel" type="tel" required clearable label="联系电话"
              placeholder="请输入成员手机号码" />
          </div>
          <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
            <div class="item_group" style="border-radius: 10px;" @click="reduceHome(item)">
              减少
            </div>
          </div>
        </van-cell-group>
        <div class="form_item" style="width:60%;background: transparent;border-radius: 10px;">
          <div class="item_group" style="border-radius: 10px;" @click="addHome()">
            新增
          </div>
        </div>
      </section>
      <div class="bottom_div">
        <van-image :src="require('../assets/button_up.png')" disabled fit="contain" width="35%" @click="prevstep()">
        </van-image>
        <van-image :src="require('../assets/button_next.png')" fit="contain" width="35%" @click="nextstep()">
        </van-image>
      </div>
      <van-image :src="require('../assets/buttom.png')" fit="contain" style="margin-top:1rem;"></van-image>
    </div>

    <div v-show="active == 3" style="margin-top:10rem;">
      <section>
        <van-row gutter="10">
          <van-col span="8"></van-col>
          <van-col span="8" style="left:100%">
            <div>
              <!-- <van-uploader
                v-model="fileList"
                :before-read="beforeRead"
                :after-read="afterRead"
                :preview-image="notpr"
                disabled
              >
                <van-image
                  width="100%"
                  height="100%"
                  fit="contain"
                  :src="baseinfo.photourl"
                />
              </van-uploader> -->
              <van-image width="100%" height="100%" fit="contain" :src="baseinfo.photourl" />
            </div>
          </van-col>
        </van-row>
        <div class="item_group">提交预览</div>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.staffName" clearable disabled/>
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.sex" clearable />
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.birthday" clearable disabled/>
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.marriageorno" clearable />
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.ethnic" clearable disabled/>
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.comefrom" clearable disabled/>
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.ncFace" clearable />
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.paperNum" clearable disabled/>
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.education" clearable disabled/>
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.educationMsg" clearable disabled/>
            </div>
          </van-col>
        </van-row>
        <div class="form_item">
          <van-field v-model="baseinfo.telPhone" clearable disabled/>
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.ncEmail" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.address" clearable disabled/>
        </div>

        <div class="form_item">
          <van-field v-model="baseinfo.introducePeople" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.sosName" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.sosRelation" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.sosTel" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.sosAddress" clearable />
        </div>
        <!-- <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.zhuangtai" clearable />
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.dgsj" clearable />
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.positionName" clearable />
            </div>
          </van-col>
          <van-col span="12">
            <div class="form_item">
              <van-field v-model="baseinfo.xzyq" clearable />
            </div>
          </van-col>
        </van-row> -->

        <!-- <div class="item_group" style="margin-top:1rem;">其他信息</div>
        <div class="form_item">
          <van-field v-model="baseinfo.aihaotc" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.yinyusp" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.waiyusp" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.zsjl" clearable />
        </div>
        <div class="form_item">
          <van-field v-model="baseinfo.wsgzjl" clearable />
        </div>
        <div class="form_item" v-if="jsrchecked">
          <van-field v-model="baseinfo.jsr.xm" clearable />
        </div>
        <div class="form_item" v-if="this.jsrchecked">
          <van-field v-model="baseinfo.jsr.xm" clearable />
        </div> -->

        <!-- <div v-for="(item, index) in baseinfo.xxjl" :key="index">
          <div class="item_group" style="margin-top:1rem;">
            学习经历{{ index + 1 }}
          </div>
          <van-row gutter="10">
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.xxjl[index].bdate" clearable />
              </div>
            </van-col>
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.xxjl[index].edate" clearable />
              </div>
            </van-col>
          </van-row>
          <div class="form_item">
            <van-field v-model="baseinfo.xxjl[index].school" clearable />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.xxjl[index].professional" clearable />
          </div>
        </div> -->

        <div v-for="(item, index) in baseinfo.gzjl" :key="index">
          <div class="item_group" style="margin-top:1rem;">
            工作经历{{ index + 1 }}
          </div>
          <van-row gutter="10">
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.gzjl[index].bdate" clearable />
              </div>
            </van-col>
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.gzjl[index].edate" clearable />
              </div>
            </van-col>
          </van-row>
          <div class="form_item">
            <van-field v-model="baseinfo.gzjl[index].comp" clearable />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.gzjl[index].zhiwu" clearable />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.gzjl[index].zmr" clearable />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.gzjl[index].lxdh" clearable />
          </div>
        </div>

        <div v-for="(item, index) in baseinfo.jtcy" :key="index">
          <div class="item_group" style="margin-top:1rem;">
            家庭成员{{ index + 1 }}
          </div>
          <van-row gutter="10">
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.jtcy[index].xm" clearable />
              </div>
            </van-col>
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.jtcy[index].gx" clearable />
              </div>
            </van-col>
          </van-row>
          <van-row gutter="10">
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.jtcy[index].nl" clearable />
              </div>
            </van-col>
            <van-col span="12">
              <div class="form_item">
                <van-field v-model="baseinfo.jtcy[index].zw" clearable />
              </div>
            </van-col>
          </van-row>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].gzdw" clearable />
          </div>
          <div class="form_item">
            <van-field v-model="baseinfo.jtcy[index].tel" clearable />
          </div>
        </div>
      </section>
      <div class="bottom_div">
        <van-image :src="require('../assets/button_up.png')" disabled fit="contain" width="35%" @click="prevstep()">
        </van-image>
        <van-image :src="require('../assets/submit.png')" fit="contain" width="35%" @click="submit()"></van-image>
      </div>
      <van-image :src="require('../assets/buttom.png')" fit="contain" style="margin-top:1rem;"></van-image>
    </div>
  </div>
</template>
<script>
import {
  Button,
  Step,
  Steps,
  Field,
  Cell,
  CellGroup,
  Sticky,
  SwitchCell,
  Notify,
  Row,
  Col,
  Toast,
  Dialog,
  Image,
  Uploader
} from "vant";

import DatePick from "./datePick.vue";
import Yearmonthpick from "./yearmonthpick.vue";
export default {
  name: "manage",
  data() {
    return {
      userCode: "",
      showCalendar: false,
      active: 0,
      jsrchecked: false,
      fileList: [],
      notpr: false,
      files: {
        name: "",
        type: ""
      },
      baseinfo: {
        sosRelation: "",
        sosName: "",
        llqUserCode: "",
        birthday: "",
        photourl:
          "http://cloud.longrich.com/zhaopin/static/images/blankpic.jpg",
        staffName: "",
        flag: "2",
        sex: "",
        paperNum: "",
        sfzzz: "",
        mingzhu: "",
        marriageorno: "",
        ethnic: "",
        comefrom: "",
        ncFace: "",
        education: "",
        educationMsg: "",
        paperNum: "",
        telPhone: "",
        ncEmail: "",
        introducePeople: "",
        xueli: "",
        mobi: "",
        address: "",
        shengao: "",
        tizhong: "",
        rxfs: "",
        email: "",
        zhuangtai: "",
        positionName: "",
        dgsj: "",
        aihaotc: "",
        yinyusp: "",
        zsjl: "",
        wsgzjl: "",
        zzmm: "",
        xzyq: "",
        jjlxr: {
          xm: "",
          sosTel: "",
          sosAddress: "",
          gx: "",
          mobi: "",
          dw: ""
        },
        jsr: {
          xm: "",
          gx: "",
          mobi: "",
          bm: ""
        },
        xxjl: [
          {
            bdate: "",
            edate: "",
            school: "",
            professional: "",
            id: "",
            hrId: ""
          }
        ],
        gzjl: [
          {
            bdate: "",
            edate: "",
            comp: "",
            zhiwu: "",
            zmr: "",
            lxdh: "",
            id: "",
            hrId: ""
          }
        ],
        jtcy: [
          {
            xm: "",
            gx: "",
            nl: "",
            gzdw: "",
            zw: "",
            tel: "",
            id: "",
            hrId: ""
          }
        ]
      }
    };
  },
  created() {
    let userCode = localStorage.getItem("userCode");
    if (userCode == null || userCode == "") {
      this.$router.push({ name: "Login" });
    } else {
      this.onSearch(userCode);
    }
  },
  //   watch: {
  //     baseinfo(newVal, oldVal) {

  //     }
  //   },
  methods: {
    onSubmit(values) {
      console.log("submit", values);
    },
    onSearch(value) {
      console.log("搜索:", value);

      let that = this;
      this.$axios
        .get(`/jeecg-boot/zhaopin/getNcStaffInfoByCode?llqUserCode=${value}`)
        .then(res => {
          if ((res.data.code = 200)) {
            that.baseinfo = Object.assign({}, that.baseinfo, res.data.result);
            if (res.data.result.marriageorno == 1) {
              that.baseinfo.marriageorno = "未婚";
            } else if (res.data.result.marriageorno == 2) {
              that.baseinfo.marriageorno = "已婚";
            } else if (res.data.result.marriageorno == 3) {
              that.baseinfo.marriageorno = "已婚育";
            } else if (res.data.result.marriageorno == null) {
              that.baseinfo.marriageorno = "";
            }
            if (res.data.result.sex == "M") {
              that.baseinfo.sex = "男";
            } else if (res.data.result.sex == "F") {
              that.baseinfo.sex = "女";
            } else if (res.data.result.sex == null) {
              that.baseinfo.sex = "";
            }
            that.baseinfo.photourl = `http://service.colori.com//jeecg-boot/sys/common/static/temp/${res.data.result.llqUserCode}.JPG`;
            // that.baseinfo.staffName = res.data.result.staffName;
            // if (res.data.result.sex == "M") {
            //   that.baseinfo.sex = "男";
            // } else if (res.data.result.sex == "F") {
            //   that.baseinfo.sex = "女";
            // } else if (res.data.result.sex == null) {
            //   that.baseinfo.sex = "";
            // }
            // that.baseinfo.birthday = res.data.result.birthday;
            // if (res.data.result.marriageorno == 1) {
            //   that.baseinfo.marriageorno = "未婚";
            // } else if (res.data.result.marriageorno == 2) {
            //   that.baseinfo.marriageorno = "已婚";
            // } else if (res.data.result.marriageorno == 3) {
            //   that.baseinfo.marriageorno = "已婚育";
            // } else if (res.data.result.marriageorno == null) {
            //   that.baseinfo.marriageorno = "";
            // }
            // that.baseinfo.ethnic = res.data.result.ethnic;
            // that.baseinfo.ncStaffCode = res.data.result.ncStaffCode;
            // that.baseinfo.comefrom = res.data.result.comefrom;
            // that.baseinfo.ncFace = res.data.result.ncFace;
            // that.baseinfo.paperNum = res.data.result.paperNum;
            // that.baseinfo.education = res.data.result.education;
            // that.baseinfo.educationMsg = res.data.result.educationMsg;
            // that.baseinfo.telPhone = res.data.result.telPhone;
            // that.baseinfo.ncEmail = res.data.result.ncEmail;
            // that.baseinfo.ncWork = res.data.result.ncWork;
            // that.baseinfo.introducePeople = res.data.result.introducePeople;
            // that.baseinfo.sosTel = res.data.result.sosTel;
            // that.baseinfo.address = res.data.result.address;
            // that.baseinfo.xxjl = res.data.result.xxjl;
            // that.baseinfo.gzjl = res.data.result.gzjl;
            // that.baseinfo.jtcy = res.data.result.jtcy;
          } else {
            Notify({ type: "danger", message: res.data.msg.msg });
          }
        });
    },
    onCancel() {
      console.log("取消");
    },
    submit() {
      var self = this;
      if (self.baseinfo.sex == "男") {
        self.baseinfo.sex = "M";
      } else if (self.baseinfo.sex == "女") {
        self.baseinfo.sex = "F";
      } else if (self.baseinfo.sex == "") {
        self.baseinfo.sex = null;
      }

      if (self.baseinfo.marriageorno == "未婚") {
        self.baseinfo.marriageorno = 1;
      } else if (self.baseinfo.marriageorno == "已婚") {
        self.baseinfo.marriageorno = 2;
      } else if (self.baseinfo.marriageorno == "已婚育") {
        self.baseinfo.marriageorno = 3;
      } else if (self.baseinfo.marriageorno == "") {
        self.baseinfo.marriageorno = null;
      }

      const toast = Toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: "数据上传中，请稍候……"
      });

      self.$axios
        .post("/jeecg-boot/zhaopin/updateStaff", self.baseinfo)
        .then(res => {
          console.log("结果", res);
          if ((res.data.code = 200)) {
            // this.$router.push({ path: "/success" });
            toast.clear();
            self.$router.go(-1);
            Notify({ type: "success", message: res.data.message });
            console.log("成功", res);
          } else {
            toast.clear();
            Dialog.alert({
              title: "上传失败",
              message: response.data.errmsg
            }).then(() => {
              // on close
            });
          }
        });
    },
    onOversize(file) {
      console.log("file", file);
      Toast("超过5M");
    },
    getUserCode() {
      var self = this;
      this.$axios.get("/jeecg-boot/zhaopin/getUserCode").then(res => {
        if ((res.data.code = 200)) {
          self.baseinfo.llqUserCode = res.data.message;
          console.log(self.baseinfo.llqUserCode);
        } else {
          Notify({ type: "danger", message: res.data.msg.msg });
        }
      });
    },
    fileUpload() {
      let that = this;
      let file = document.getElementById("upfile");
      let fileName = file.value;
      let files = file.files;
      console.log(files[0]);
      if (fileName == null || fileName == "") {
        alert("请选择文件");
      } else {
        let fileType = fileName.substr(fileName.length - 4, fileName.length);
        console.log("fileType:" + fileType);
        if (fileType == ".jpg" || fileType == ".png") {
          if (files[0]) {
            let formData = new window.FormData();
            formData.append("file", files[0]);
            formData.append("userCode", that.baseinfo.llqUserCode); // 文件对象
            that.$axios
              .post("/jeecg-boot/zhaopin/saveImg", formData)
              .then(res => {
                if (res.data.code == 200) {
                  that.baseinfo.photourl = res.data.message;
                } else {
                  Toast({
                    message: "上传失败，请稍候再试！",
                    position: "bottom",
                    duration: 2000
                  });
                }
              });
          } else {
            alert("请选择要上传的图片");
          }
        } else {
          alert("上传文件类型错误！");
        }
      }
    },
    calSize(base64url) {
      let str = base64url
        .replace("data:image/png;base64,", "")
        .replace("data:image/jpeg;base64,", "");
      const equalIndex = str.indexOf("=");
      if (str.indexOf("=") > 0) {
        str = str.substring(0, equalIndex);
      }
      // console.log("str:"+str)
      const strLength = str.length;
      const fileLength = strLength - (strLength / 8) * 2;
      // 返回单位为MB的大小
      return (fileLength / (1024 * 1024)).toFixed(2);
    },
    afterRead: function (file) {
      var self = this;
      var param = new FormData(); // FormData 对象
      // console.log(self.calSize(file.content))
      param.append("file", file.content); // 文件对象
      param.append("userCode", self.baseinfo.llqUserCode); // 文件对象
      Toast.loading({ mask: true, message: "正在保存照片", duration: 0 });
      self.$axios
        .post("/jeecg-boot/zhaopin/saveImg", param, {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        })
        .then(function (response) {
          Toast.clear(true);
          if (response.data.code == 200) {
            self.baseinfo.photourl = response.data.message;
          } else {
            Dialog.alert({
              title: "出错啦",
              message: response.data.message
            }).then(() => {
              // on close
            });
          }
        });
    },
    isValidityBrithBy18IdCard: function (idCard18) {
      var year = idCard18.substring(6, 10);
      var month = idCard18.substring(10, 12);
      var day = idCard18.substring(12, 14);
      var temp_date = new Date(year, parseFloat(month) - 1, parseFloat(day));
      // 这里用getFullYear()获取年份，避免千年虫问题
      if (
        temp_date.getFullYear() != parseFloat(year) ||
        temp_date.getMonth() != parseFloat(month) - 1 ||
        temp_date.getDate() != parseFloat(day)
      ) {
        return false;
      } else {
        return true;
      }
    },
    //18位最后一位校验
    isTrueValidateCodeBy18IdCard: function (a_idCard) {
      var Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1]; // 加权因子
      var ValideCode = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2]; // 身份证验证位值.10代表X
      var sum = 0; // 声明加权求和变量
      if (a_idCard[17].toLowerCase() == "x") {
        a_idCard[17] = 10; // 将最后位为x的验证码替换为10方便后续操作
      }
      for (var i = 0; i < 17; i++) {
        sum += Wi[i] * a_idCard[i]; // 加权求和
      }
      var valCodePosition = sum % 11; // 得到验证码所位置
      if (a_idCard[17] == ValideCode[valCodePosition]) {
        return true;
      } else {
        return false;
      }
    },
    beforeRead: function (file) {
      if (
        file.type !== "image/jpeg" &&
        file.type !== "image/jpg" &&
        file.type !== "image/png"
      ) {
        Toast("只允许上传jpg/png格式的图片！");
        return false;
      }
      return true;
    },
    reduceEdu(item) {
      console.log(item);
      this.baseinfo.xxjl.forEach((v, i) => {
        if (item.id != "") {
          if (v.id == item.id) {
            this.$axios
              .get(`/jeecg-boot/zhaopin/delGJX?id=${item.id}&type=3`)
              .then(res => {
                if ((res.data.code = 200)) {
                  this.baseinfo.xxjl.splice(i, 1);
                } else {
                  Notify({ type: "danger", message: res.data.msg });
                }
              });
          }
        } else {
          if (
            v.professional == item.professional &&
            v.school == item.school &&
            v.bdate == item.bdate &&
            v.edate == item.edate
          ) {
            this.baseinfo.xxjl.splice(i, 1);
          }
        }
      });
    },
    addEdu() {
      this.baseinfo.xxjl.push({
        bdate: "",
        edate: "",
        school: "",
        professional: "",
        hrId: this.baseinfo.hrId,
        id: ""
      });
    },
    reduceJob(item) {
      this.baseinfo.gzjl.forEach((v, i) => {
        if (item.id != "") {
          if (v.id == item.id) {
            this.$axios
              .get(`/jeecg-boot/zhaopin/delGJX?id=${item.id}&type=1`)
              .then(res => {
                if ((res.data.code = 200)) {
                  this.baseinfo.gzjl.splice(i, 1);
                } else {
                  Notify({ type: "danger", message: res.data.msg });
                }
              });
          }
        } else {
          if (
            v.comp == item.comp &&
            v.lxdh == item.lxdh &&
            v.zhiwu == item.zhiwu &&
            v.zmr == item.zmr &&
            v.bdate == item.bdate &&
            v.edate == item.edate
          ) {
            this.baseinfo.gzjl.splice(i, 1);
          }
        }
      });
    },
    addJob() {
      this.baseinfo.gzjl.push({
        bdate: "",
        edate: "",
        comp: "",
        zhiwu: "",
        zmr: "",
        lxdh: "",
        hrId: this.baseinfo.hrId
      });
    },
    reduceHome(item) {
      this.baseinfo.jtcy.forEach((v, i) => {
        if (item.id != "") {
          if (v.id == item.id) {
            this.$axios
              .get(`/jeecg-boot/zhaopin/delGJX?id=${item.id}&type=2`)
              .then(res => {
                if ((res.data.code = 200)) {
                  this.baseinfo.jtcy.splice(i, 1);
                } else {
                  Notify({ type: "danger", message: res.data.msg });
                }
              });
          }
        } else {
          if (
            v.gx == item.gx &&
            v.gzdw == item.gzdw &&
            v.nl == item.nl &&
            v.tel == item.tel &&
            v.xm == item.xm &&
            v.zw == item.zw
          ) {
            this.baseinfo.jtcy.splice(i, 1);
          }
        }
      });
    },
    addHome() {
      this.baseinfo.jtcy.push({
        xm: "",
        gx: "",
        nl: "",
        gzdw: "",
        zw: "",
        tel: "",
        hrId: this.baseinfo.hrId
      });
    },
    prevstep() {
      this.active--;
    },
    nextstep() {
      //   输入校验;
      switch (this.active) {
        case 0: {
          //基本信息校验
          if (
            this.baseinfo.photourl ==
            "http://cloud.longrich.com/zhaopin/static/images/blankpic.jpg"
          ) {
            Notify({ type: "warning", message: "请上传照片！" });
            break;
          }
          if (this.baseinfo.staffName == "") {
            Notify({ type: "warning", message: "姓名不能为空！" });
            break;
          }
          //   let aname=this.baseinfo.username;
          if (!/^[一-龥]{2,6}$/.test(this.baseinfo.staffName)) {
            Notify({ type: "warning", message: "您输入的姓名有误!" });
            break;
          }
          if (this.baseinfo.sex == "") {
            Notify({ type: "warning", message: "性别必须要选！" });
            break;
          }
          if (this.baseinfo.birthday == "") {
            Notify({ type: "warning", message: "出生日期必须要选！" });
            break;
          }
          // if (this.baseinfo.shengao == "") {
          //   Notify({ type: "warning", message: "身高必须要填！" });
          //   break;
          // }
          // if (this.baseinfo.tizhong == "") {
          //   Notify({ type: "warning", message: "体重必须要填！" });
          //   break;
          // }
          if (this.baseinfo.marriageorno == "") {
            Notify({ type: "warning", message: "是否已婚必须要选！" });
            break;
          }
          // if (this.baseinfo.ethnic == "") {
          //   Notify({ type: "warning", message: "民族必须要选！" });
          //   break;
          // }
          // if (this.baseinfo.comefrom == "") {
          //   Notify({ type: "warning", message: "籍贯必须要选！" });
          //   break;
          // }
          if (this.baseinfo.ncFace == "") {
            Notify({ type: "warning", message: "政治面貌必须要填" });
            break;
          }
          //   if (this.baseinfo.paperNum == "") {
          //     Notify({ type: "warning", message: "身份证号必须填！" });
          //     break;
          //   }
          // if (this.baseinfo.sfzzz == "") {
          //   Notify({ type: "warning", message: "身份证地址必须填！" });
          //   break;
          // }
          // if (this.baseinfo.xzyq == "") {
          //   Notify({ type: "warning", message: "薪资要求必须填！" });
          //   break;
          // }
          //   if (this.baseinfo.paperNum.length != 18) {
          //     Notify({ type: "warning", message: "身份证号必须18位！" });
          //     break;
          //   }
          //   if (this.baseinfo.paperNum.length == 18) {
          //     var a_idCard = this.baseinfo.paperNum.split(""); // 得到身份证数组
          //     if (
          //       this.isValidityBrithBy18IdCard(this.baseinfo.paperNum) &&
          //       this.isTrueValidateCodeBy18IdCard(a_idCard)
          //     ) {
          //       //进行18位身份证的基本验证和第18位的验证
          //     } else {
          //       Notify({ type: "warning", message: "您输入的身份证有误" });
          //       break;
          //     }
          //   }
          // if (this.baseinfo.education == "") {
          //   Notify({ type: "warning", message: "学历必须要选！" });
          //   break;
          // }
          // if (this.baseinfo.educationMsg == "") {
          //   Notify({ type: "warning", message: "入学方式必须要选！" });
          //   break;
          // }
          // if (this.baseinfo.telPhone == "") {
          //   Notify({ type: "warning", message: "电话号码必须要填！" });
          //   break;
          // }
          //   if (this.baseinfo.xianzz == "") {
          //     Notify({ type: "warning", message: "现住址必须要填！" });
          //     break;
          //   }
          // if (this.baseinfo.zhuangtai == "") {
          //   Notify({ type: "warning", message: "目前工作状态必须要选！" });
          //   break;
          // }
          // if (this.baseinfo.dgsj == "") {
          //   Notify({ type: "warning", message: "到岗时间必须要选！" });
          //   break;
          // }
          //紧急联系人
          //   if (this.baseinfo.sosName == "") {
          //     Notify({ type: "warning", message: "紧急联系人姓名不能为空！" });
          //     break;
          //   }
          //   if (!/^[一-龥]{2,6}$/.test(this.baseinfo.introducePeople)) {
          //     Notify({ type: "warning", message: "紧急联系人姓名有误!" });
          //     break;
          //   }
          //   if (this.baseinfo.sosRelation == "") {
          //     Notify({ type: "warning", message: "现紧急联系人关系必须要填！" });
          //     break;
          //   }
          //   if (this.baseinfo.sosTel == "") {
          //     Notify({ type: "warning", message: "现紧急联系人手机必须要填！" });
          //     break;
          //   }
          //   if (this.baseinfo.address == "") {
          //     Notify({ type: "warning", message: "现紧急联系人地址必须要填！" });
          //     break;
          //   }
          //介绍人 （选填）
          // if (this.baseinfo.jsr.xm.length > 2) {
          //   if (!/^[一-龥]{2,6}$/.test(this.baseinfo.jsr.xm)) {
          //     Notify({ type: "warning", message: "紧急联系人姓名有误!" });
          //     break;
          //   }
          //   if (this.baseinfo.jsr.gx == "") {
          //     Notify({
          //       type: "warning",
          //       message: "既然填了介绍人，那么必须选关系！"
          //     });
          //     break;
          //   }
          // }
          this.active++;
          break;
        }
        case 1: {
          //学习经历
          for (let i = 0; i < this.baseinfo.xxjl.length; i++) {
            if (this.baseinfo.xxjl[i].bdate == "") {
              Notify({
                type: "warning",
                message: "学习经历1开始年月不能为空！"
              });
              break;
            }
            if (this.baseinfo.xxjl[i].edate == "") {
              Notify({
                type: "warning",
                message: "学习经历1结束年月不能为空！"
              });
              break;
            }
            if (this.baseinfo.xxjl[i].school == "") {
              Notify({
                type: "warning",
                message: "学习经历1学校名称不能为空！"
              });
              break;
            }
            if (this.baseinfo.xxjl[i].professional == "") {
              Notify({ type: "warning", message: "学习经历1专业不能为空！" });
              break;
            }
          }
          this.active++;
          break;
        }
        case 2: {
          //工作经历
          this.active++;
          break;
        }
        case 3: {
          //家庭成员
          for (let i = 0; i < this.baseinfo.jtcy.length; i++) {
            if (
              this.baseinfo.jtcy[i].xm == "" ||
              this.baseinfo.jtcy[i].gx == "" ||
              this.baseinfo.jtcy[i].nl == "" ||
              this.baseinfo.jtcy[i].gzdw == "" ||
              this.baseinfo.jtcy[i].zw == "" ||
              this.baseinfo.jtcy[i].tel == ""
            ) {
              Notify({ type: "warning", message: "家庭成员必须要填完一项！" });
              break;
            }
          }
          this.active++;
          break;
        }
      }
    }
  },
  components: { DatePick, Yearmonthpick }
};
</script>

<style scoped>
#logo {
  background-image: url("../assets/manage_b.png");
  background-size: 100%;
  background-repeat: no-repeat;
  text-align: center;
  overflow: hidden;
}

.search {
  position: absolute;
  width: 100%;
  top: 0%;
  background-color: rgba(255, 255, 255, 0);
}

.van-step--horizontal .van-step__circle-container {
  background-color: rgba(209, 108, 108, 0);
}

.van-step {
  color: white;
}

/deep/ .van-step__circle-container {
  background-color: transparent;
}

.bottom_div {
  text-align: center;
  margin-top: 2rem;
  margin-bottom: 20px;
}

.pri_btn {
  margin-right: 20px;
  width: 132px;
}

.next_btn {
  width: 132px;
}

.van-uploader {
  left: 100%;
}

.van-cell-group {
  background: transparent;
}

.van-cell {
  background: transparent;
  font-size: 0.8rem;
}

.form_item {
  min-height: 2.4rem;
  background: #edecea;
  width: 80%;
  display: inline-block;
  border-radius: 10px;
  margin-top: 1rem;
}

.item_group {
  background-image: url("../assets/group.png");
  background-size: 100%;
  padding: 0.6rem 0;
  color: white;
  font-size: 1rem;
  font-weight: 600;
}
</style>
<style scoped>
.float_left {
  float: left;
}

.all_width {
  width: 100%;
}

.add_width {
  width: 100%;
}

.add_upload .add_upload_button {
  position: relative;
  width: 100%;
  height: 8rem;
  border: none;
  background: rgb(236, 236, 236);
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.add_upload .add_upload_icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.add_upload .add_upload_file {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  font-size: 0;
}

.add_upload_imgBox .add_upload_imgDiv {
  position: relative;
  width: 29%;
  height: 8rem;
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.add_upload_imgBox .add_upload_imgDiv img {
  width: 100%;
  height: 100%;
}

.add_upload_imgBox .add_upload_close {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 30%;
}

.add_upload_imgBox .add_upload_close img {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
</style>
