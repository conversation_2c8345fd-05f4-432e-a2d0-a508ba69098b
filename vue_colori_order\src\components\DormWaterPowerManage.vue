<template>
<div class="pageRepairAdd">
    <van-nav-bar fixed
        title="水电用量管理"
        left-text="返回"
        left-arrow
        @click-left="onPageBack">
        <template #right>
            <van-popover
                v-model="popoverShow"
                trigger="click"
                placement="bottom-end"
                :actions="popoverActions"
                @select="onPopoverSelect">
                <template #reference>
                    <van-button icon="more-o"></van-button>
                </template>
            </van-popover>
        </template>
    </van-nav-bar>

    <!-- 列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getWaterPowerList">
            <div v-for="(wp, index) in wpList" :key="index" style="margin-top: 15px;">
                <van-cell-group inset>
                    <van-cell class="vanCellClass" title="年月" :value="wp.yearMonth" />
                    <!-- <van-cell class="vanCellClass" title="区域" :value="wp.areaId_dictText" />
                    <van-cell class="vanCellClass" title="楼栋" :value="wp.buildingId_dictText" />
                    <van-cell class="vanCellClass" title="房间" :value="wp.roomId_dictText" /> -->
                    <van-cell class="vanCellClass" title="区域/楼栋/房间" :value="`${wp.areaId_dictText}/${wp.buildingId_dictText}/${wp.roomId_dictText}`" />
                    <van-cell class="vanCellClass" title="水表刻度" :value="wp.waterScale" />
                    <van-cell class="vanCellClass" title="水用量" :value="wp.waterConsumption" />
                    <van-cell class="vanCellClass" title="电表刻度" :value="wp.powerScale" />
                    <van-cell class="vanCellClass" title="电用量" :value="wp.powerConsumption" />
                    <van-cell class="vanCellClass" title="类型" :value="wp.type | formatType" />
                    <van-cell class="vanCellClass" title="操作">
                        <template #default>
                            <van-button size="small" type="info" @click="onEditRow(wp)">编辑</van-button>
                            <van-button size="small" type="default" style="margin-left: 8px;" @click="onDeleteRow(wp)">删除</van-button>
                        </template>
                    </van-cell>
                    
                </van-cell-group>
            </div>
        </van-list>
    </van-pull-refresh>

<!-- 筛选条件 -->
<van-popup v-model="popFilterShow" position="top" :style="{ height: '32%' }" :close-on-click-overlay="false">
    <h2>筛选条件</h2>
    <van-cell-group>
        <van-field readonly v-model="queryParams.yearMonth" label="年月" placeholder="请选择年月" @focus="showDatePicker=true;" />
        <van-field readonly  v-model="selectedArea.label" label="区域" placeholder="请选择区域" @focus="showAreaPicker=true;" />
        <van-field readonly  v-model="selectedBuilding.label" label="楼栋" placeholder="请选择楼栋" @focus="showBuildingPicker=true;" />
        <van-field readonly  v-model="selectedRoom.label" label="房间" placeholder="请选择房间" @focus="showRoomPicker=true;" />
    </van-cell-group>
    <div style="margin: 0 auto; padding-top: 10px;">
        <van-button round type="default" size="small" style="width: 30%;" @click="searchReset">重置</van-button>
        <van-button round type="primary" size="small" style="width: 30%;margin-left: 2rem;" @click="searchQuery">查询</van-button>
    </div>
</van-popup>
<!-- 年月Picker -->
<van-popup v-model="showDatePicker" position="bottom" :style="{ height: '45%' }" :close-on-click-overlay="false">
    <van-picker show-toolbar title="年月"
        :columns="dateOptions"
        @confirm="onDateConfirm"
        @cancel="onDateCancel"
        />
</van-popup>
<!-- 区域、楼栋、房间Picker -->
<van-popup v-model="showAreaPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择区域"
        :columns="areaOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'区域')"
        @cancel="(value,index)=>onPickerCancel(value,index,'区域')" />
</van-popup>
<van-popup v-model="showBuildingPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择楼栋"
        :columns="buildingOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'楼栋')"
        @cancel="(value,index)=>onPickerCancel(value,index,'楼栋')" />
</van-popup>
<van-popup v-model="showRoomPicker" position="bottom" :style="{ height: '45%' }">
    <van-picker show-toolbar title="选择房间"
        :columns="roomOptions.map(item=>item.label)"
        @confirm="(value,index)=>onPickerConfirm(value,index,'房间')"
        @cancel="(value,index)=>onPickerCancel(value,index,'房间')" />
</van-popup> 
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            confirmLoading: false,
            popoverShow: false,
            popoverActions: [//下拉菜单
                { text: '筛选', icon: 'filter-o' },
                { text: '新增', icon: 'add-o' },
            ],
            popFilterShow: false,
            queryParams:{ 
                yearMonth: ''
            },
            iPage: {// 页数
                current: 1,
                size: 5,
                total: 1,
            },
            refreshing: false, // 是否下拉刷新
            wpList: [],   // 宿舍卫生列表
            loading: false, // 是否加载
            finished: false, // 是否完成  停止触底加载
            showDatePicker: false, 
            dateOptions: [
                { values: Array(300).fill(0).map((item,index) => `${index+2020}年`), defaultValue: `${new Date().getFullYear()}年` },
                { values: Array(12).fill(0).map((item,index) => `${index+1}月`), defaultValue: `${new Date().getMonth()+1}月` },
            ],
            showAreaPicker: false,
            areaOptions: [],
            selectedArea: {},
            showBuildingPicker: false,
            buildingOptions: [],
            selectedBuilding: {},
            showRoomPicker: false,
            roomOptions: [],
            selectedRoom: {},
        }
    },
    methods:{
        onEditRow(pRow){
            console.log(pRow)
            this.$router.push({ name: "waterPowerAdd", params: { wpModal:pRow } })
        },
        onDeleteRow(pRow){
            const that=this
            Dialog.confirm({
                title: '提示',
                message: '确认删除该条记录？',
            }).then(()=>{
                const requestURL="/dormApi/dm/dmHydropowerUsage/app/delete"
                const rParams={id: pRow.id}
                that.$axios.get(requestURL,{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 1000
                            });
                            this.onRefresh()
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            this.onRefresh()
                        }
                    }else{
                        Toast({
                            message: '发生错误',
                            position: 'bottom',
                            duration: 2000
                        });
                        this.onRefresh()
                    }
                })
            })
        },
        searchQuery(){
            this.wpList=[]
            this.iPage.current=1
            this.popFilterShow=false
            this.onRefresh()
        },
        searchReset(){
            this.wpList=[]
            this.iPage.current=1
            this.queryParams={}
            this.popFilterShow=false
            this.onRefresh()
        },
        onDateConfirm(pValue,pIndex){
            const yyyy=`${pIndex[0]+2020}`
            const MM=pIndex[1]+1
            this.queryParams.yearMonth=`${yyyy}-${MM>9?MM:'0'+MM}`
            this.showDatePicker=false
        },
        onDateCancel(){
            this.showDatePicker=false
        },
        onPickerConfirm(pValue,pIndex,pType){// 区域/楼栋/房间 选择确认
            if(pType=="区域"){
                // 清空楼栋&房间
                this.selectedBuilding=this.selectedRoom={}
                this.buildingOptions=this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedArea=this.areaOptions[pIndex]
                this.getBuildingList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                // 清空房间
                this.selectedRoom={}
                this.roomOptions=[]
                // 清空设置区域，重新请求楼栋Options
                this.selectedBuilding=this.buildingOptions[pIndex]
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom=this.roomOptions[pIndex]
                this.showRoomPicker=false
            }
        },
        onPickerCancel(pValue,pIndex,pType){// 区域/楼栋/房间 选择取消
            if(pType=="区域"){
                this.selectedArea=this.selectedBuilding=this.selectedRoom={}
                this.getBuildingList()
                this.getRoomList()
                this.showAreaPicker=false
            }else if(pType=="楼栋"){
                this.selectedBuilding=this.selectedRoom={}
                this.getRoomList()
                this.showBuildingPicker=false
            }else if(pType=="房间"){
                this.selectedRoom={}
                this.showRoomPicker=false
            }
        },
        onRefresh(){
            // 处于刷新状态
            this.refreshing=true;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            // 加载数据
            this.getWaterPowerList();
        },
        getWaterPowerList(){
            if (this.refreshing) {
                this.wsList = [];
                this.iPage.current = 1;
                this.refreshing = false;
                this.finished=false
            }
            let rParams=JSON.parse(JSON.stringify(this.queryParams))
            Object.assign(rParams,{
                areaId: this.selectedArea.value,
                buildingId: this.selectedBuilding.value,
                roomId: this.selectedRoom.value,
                pageNo: this.iPage.current,
                pageSize: this.iPage.size,
                userCodeRequest:localStorage.getItem('userCode'),
            })
            console.log("/dormApi/dm/dmHydropowerUsage/app/list",rParams)
            this.$axios.get("/dormApi/dm/dmHydropowerUsage/app/list", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.wpList.push(...res.result.records);
                        this.iPage={
                            current: res.result.current,
                            size: res.result.size,
                            total: res.result.pages,
                        }
                        // 关闭loading状态
                        this.loading = false;
                        // 判断是否到底了
                        if(this.iPage.current*1>=this.iPage.total*1){
                            this.finished = true;
                        }else{
                            this.iPage.current++;
                        }
                    }else{
                        Toast({
                            message: res.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: '发生错误',
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onPopoverSelect(pAction, pIndex){//下拉菜单选择
            if(pIndex===0){// 筛选
                this.popFilterShow=true
            }else if(pIndex===1){// 新增
                this.$router.push("/waterPowerAdd")
            }
        },
        onPageBack(){// 返回
            this.$router.go(-1)
        },
        getAreaList(){// 获取宿舍区域
            let params = {userCodeRequest:localStorage.getItem('userCode')};
            this.$axios.get("/dormApi/dormitory/app/getAreaListAll",params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.areaOptions=res.result.map(item=>{
                            return {
                                label: item.areaName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getBuildingList(){// 获取宿舍楼栋
            const params={ areaId: this.selectedArea.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getBuildingListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.buildingOptions=res.result.map(item=>{
                            return {
                                label: item.buildingName,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getRoomList(){// 获取宿舍楼栋
            const params={ buildingId: this.selectedBuilding.value,userCodeRequest:localStorage.getItem('userCode') }
            this.$axios.get("/dormApi/dormitory/app/getRoomInfoListAll",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.roomOptions=res.result.map(item=>{
                            return {
                                label: item.roomNumber,
                                value: item.id,
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
    },
    created(){ 
        this.getAreaList()
        this.getBuildingList()
        this.getRoomList()
    },
    filters: { 
        formatType: function(pValue){
            switch(pValue){
                case "1": return "正常";
                case "2": return "表重置";
                case "3": return "表更换";
                default: return pValue;
            }
        }
    }
}
</script>
<style scoped>
.pageRepairAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>