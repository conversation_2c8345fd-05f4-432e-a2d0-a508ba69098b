<template>
  <div>

  </div>
</template>

<script>
import Order from '@/components/Order'
import Mine from '@/components/Mine'
import { Notify,Toast } from 'vant';
const CORPID="wx3c2e87bb2e0f524d"
  export default {
    components: {
      Order,
        Mine,
    },
    data() {
      return {
        selected: 'order',
        userCode:'',
      };
    },
    created:function(){
      let self=this;
      // localStorage.clear();
      if(localStorage.getItem("userCode") !=null && localStorage.getItem("userCode") !=''){
        if(this.$route.query.route=='equiMenu'){
          self.$router.push({path:'/equiMenu',query:{id:this.$route.query.id,type:this.$route.query.type}})
        }else{
          self.$router.push({path:'/home'})
        }
        // self.$router.push({path:'/home'})
      }else{
        self.login();
      }
    },
    methods:{
      login(){
        var local = window.location.href
        this.code = this.getUrlCode().code
        if (this.code == null || this.code === '') { // 如果没有code，则去请求
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${CORPID}&redirect_uri=${encodeURIComponent(local)}&response_type=code&scope=snsapi_base&state=123&connect_redirect=1#wechat_redirect`
          // window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(local)}&response_type=code&scope=snsapi_base&state=123#wechat_redirect`
        } else {
          // 你自己的业务逻辑
          console.log("code:"+this.code);
          //获取用户基本信息
          this.getUserBaseInfo(this.code);
        }
      },
      getUserBaseInfo(code){
        let self=this;
        let url="/jeecg-boot/car/getUserId"
        const toast = Toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: "登录中..."
        });
        self.$axios.get(url,{params:{code:code}}).then(res=>{
          if(res.data.code===200){
            console.log(res.data)
            self.userCode=res.data.message
            self.getUserInfo(self.userCode)
          }else{
            toast.clear()//清除加载效果
            self.$router.push({path:'/loginIndex'})
          }
        })
      },
      getUserInfo(userCode){
          const self=this;
        const toast = Toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: "正在获取用户数据..."
        });
        const url="/jeecg-boot/car/getUserInfo"
          self.$axios.get(url,{params:{userId:userCode}}).then(res=>{
           toast.clear()//清除加载效果
          if(res.data.code===200){
            localStorage.setItem('userCode',userCode);
            localStorage.setItem('userName',res.data.result.name);
            localStorage.setItem('positionName',res.data.result.positionName);
            localStorage.setItem('departmentName',res.data.result.departmentName);
            localStorage.setItem('avatar',res.data.result.avatar);
            this.$router.push({path:'/home'})
          }else{
            self.$router.push({path:'/loginIndex'})
          }
        })
      },
      getUrlCode() { // 截取url中的code方法
        var url = location.search
        this.winUrl = url
        var theRequest = new Object()
        if (url.indexOf("?") != -1) {
          var str = url.substr(1)
          var strs = str.split("&")
          for(var i = 0; i < strs.length; i ++) {
            theRequest[strs[i].split("=")[0]]=(strs[i].split("=")[1])
          }
        }
        return theRequest
      }
    }
  };
</script>
<style>
.page-tabbar-container{
  height: 100%;
}
</style>