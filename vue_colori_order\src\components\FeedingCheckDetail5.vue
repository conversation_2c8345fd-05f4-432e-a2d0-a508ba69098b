<template>
  <div style="text-align:left;background-color:#F1F1F1;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="标签" left-arrow @click-left="onClickLeft" />
    </van-sticky>

    <van-dialog v-model="dialogshow" title="请输入原因" show-cancel-button showConfirmButton @confirm="confirm"
      @cancel="cancel">
      <van-field v-model="applyReason" label="申请原因" />
    </van-dialog>



    <div style="width:100%;height:100%;overflow: auto;">
      <div v-for="(item, index) in dataArr" :key="index"
        style="text-align: left;background-color:#fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
        <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
          <van-row>
            <van-col span="20">
              <span style="font-size:18px;font-weight: 700;color: #000;">
                {{ item.materialCode }}
              </span>
            </van-col>
            <van-col span="4">
              <van-button size="mini" round hairline type="info" @click="applyPrint(item)">申请打印</van-button>
            </van-col>
          </van-row>
        </div>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> ID：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.id }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> 原料名称：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.materialName }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6">区域：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.area }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 批次号：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.nccBatchCode }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 供应商：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.supplier }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 重量：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.actualWeight }}KG
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
  import { DatetimePicker, Indicator, MessageBox } from "mint-ui";
  import { Toast } from "vant";
  export default {
    data() {
      return {
        // 配料单ID
        id: "",
        userCode: localStorage.getItem("userCode"),
        dataArr: [],
        // 共有
        allNum: 0,
        // 剩余
        residueNum: 0,
        applyReason: '',//申请原因
        dialogshow: false,//申请原因弹窗
      };
    },
    created() {
      this.id = this.$route.params.id;
      if (this.$route.params) {
        Indicator.open({
          text: '正在加载中，请稍后……',
          spinnerType: 'fading-circle'
        });
        this.$axios
          .get(`/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.$route.params.id}`)
          .then(res => {
            if (res.data.code == 200) {
              this.residueNum = 0;
              console.log(res.data.result);
              this.dataArr = res.data.result;
              this.allNum = this.dataArr.length;
              this.dataArr.forEach(item => {
                if (item.status == 3) {
                  this.residueNum++;
                }
              });
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          }).finally(() => {
            Indicator.close();
          });
      } else {
        this.$route.go(-1);
      }
    },

    methods: {
      confirm() {
        if (this.applyReason.trim().length == 0) {
          Toast.fail({
            message: '请输入原因',
            position: "bottom",
            duration: 2000
          });
          return
        }
        let param = {
          weightId: this.applyObj.id,
          applyReason: this.applyReason,
          createNo: localStorage.getItem("userCode"),
          createName: localStorage.getItem("userName")
        }
        this.$axios
          .post(`/jeecg-boot/app/mixApply/add`, param)
          .then(res => {
            if (res.data.code == 200) {
              Toast.success({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            } else {

            }
          });
      },
      applyPrint(item) {
        if (item.actualWeight == 0) {
          Toast.fail({
            message: '未称重不能申请',
            position: "bottom",
            duration: 2000
          });
          return
        }
        this.applyObj = item
        this.dialogshow = true
      },
      cancel() {
        this.dialogshow = false
      },
      onClickLeft() {
        this.$router.go(-1);
      },

    }
  };
</script>

<style scoped>

</style>