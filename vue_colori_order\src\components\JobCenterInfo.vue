<template>
    <div style="background:#f3f4f6;min-height:100%">

        <img :src="sc" width="100%"/>
        
        <div class="content" v-for="(item,index) in info" :key="index">
            <div class="left" :class="item.info.length==0?'item-color':null">
                <div>{{item.jobCenter}}</div>
                <div>{{item.jobCenterComments}}</div>
            </div>
            <div class="right">派单数:{{item.info.length}}</div>
            <div class="line"></div>
            <div class="infos" v-for="(temp,key) in item.info" :key="key">
                <div class="info-left">
                    {{temp.moId}}
                    <div class="clear"></div>
                </div>
                <div class="info-right">
                    <span v-if="temp.status=='1'">未开线</span>
                    <span v-if="temp.status=='2'">开线中</span>
                    <span v-if="temp.status=='3'">开线中</span>
                    <span v-if="temp.status=='0'">已完工</span>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-main-left" >
                        对应OG订单：
                    </div>
                    <div class="info-main-right" v-if="temp.vordercode">
                        {{temp.vordercode}}
                    </div>
                    <div class="info-main-right" v-else>
                        无
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-main-left">
                        产品编码：
                    </div>
                    <div class="info-main-right">
                        {{temp.code}}
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-main-left">
                        产品名称：
                    </div>
                    <div class="info-main-right">
                        {{temp.name}}
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-item-center">
                        <div class="info-item-left">
                            班次：
                        </div>
                        <div class="info-item-right">
                            {{temp.category}}
                        </div>
                    </div>
                    <div class="info-item-center">
                        <div class="info-item-left">
                            组长：
                        </div>
                        <div class="info-item-right">
                            {{temp.leaderNo}}
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-item-center">
                        <div class="info-item-left">
                            派单人：
                        </div>
                        <div class="info-item-right">
                            {{temp.creator}}
                        </div>
                    </div>
                    <div class="info-item-center">
                        <div class="info-item-left">
                            NCC状态：
                        </div>
                        <div class="info-item-right">
                            {{temp.fitemstatus}}
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-item-center">
                        <div class="info-item-left">
                            订单计划量：
                        </div>
                        <div class="info-item-right">
                            {{temp.plot}}
                        </div>
                    </div>
                    <div class="info-item-center">
                        <div class="info-item-left">
                            派发计划量：
                        </div>
                        <div class="info-item-right">
                            {{temp.prePlot}}
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="info-item">
                    <div class="info-item-center">
                        <div class="info-item-left">
                            当前报工量：
                        </div>
                        <div class="info-item-right">
                            {{temp.quantity}}
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="clear"></div>
            </div>
           <div class="clear"></div>
        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            category:'白班',
            popupVisible:false,
            questionTypeVal:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            sc:require('../../static/images/sc.png'),
            fee:{},
            popupSlots:[
                {
                    values:[
                        {
                            id:0,
                            name:"白班"
                        },
                        {
                            id:1,
                            name:"晚班"
                        }
                    ]
                }
            ],
            info:"",
            type:"",
            index:"",
            workshop:"",
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.workshop = this.$route.params.workshop
        this.date = this.$route.params.date
        this.category = this.$route.params.category

        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        // this.date=this.formatDate(new Date)
        
        this.getMoInfoByWorkShop()
    },
    methods: {
        getMoInfoByWorkShop(){
            let self=this;
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMoInfoByWorkShop',{params:{workshop:this.workshop,date:this.date,category:this.category}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.info=res.data.result
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getWorkInfo(type,index){
            let self=this
            self.$router.push({name:"OrderInfoDetail",params:{status:status}})
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.sendShopToUser()
        },
        popupOk(){
            this.category = this.questionTypeVal.name;
            this.popupVisible = false;
            this.sendShopToUser()
        },
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year  + month  + date
        }
    }
}
</script>
<style scoped>
.content{
    width: 96%;
    padding: 2%;
}
.clear{clear:both;} /* 清除空白盒子的所有浮动 */
.left{
    float: left;
    width: 60%;
    height: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    text-align: left;
}
.right{
    float: left;
    width: 38%;
    height: 25px;
    display: flex;
    font-size: 0.9rem;
    align-items: center;
    justify-content: flex-end;
}
.line{
    background-color: #666;
    height: 1px;
    width: 100%;
    display: inline-block;
}
.infos{
    width: 95%;
    padding-top: 3%;
    padding-left: 5%;
}
.info-left{
    width: 60%;
    float: left;
    text-align: left;
    font-size: 0.8rem;
    padding-bottom: 5px;
    font-weight: 600;
}
.info-right{
    width: 30%;
    float: left;
    text-align: right;
    padding-right: 10%;
    padding-bottom: 5px;
    font-size: 0.8rem;
}
.info-item{
    width: 100%;
    text-align: left;
    font-size: 0.8rem;
    padding-top: 2%;
}
.info-main-left{
    float: left;
    width: 30%;
    color: #888;
}
.info-main-right{
    float: left;
    width: 70%;
}
.info-item-left{
    float: left;
    width: 45%;
    color: #888;
}
.info-item-right{
    float: left;
    width: 55%;
}
.info-item-center{
    float: left;
    width: 50%;
}
.item-color{
    color: red;
}
</style>