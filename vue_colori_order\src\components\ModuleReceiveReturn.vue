<template>
    <div>
        <!-- 归还 -->
        <van-form @submit="onSubmit">
            <van-field label="数量" v-model="formData.count" placeholder="请输入"
                :rules="[{ required: true, message: '请填写数量' }]" />
            <van-field readonly clickable name="picker" :value="formData.moldStatus" label="模具状态" placeholder="请选择模具状态"
                @click="showPicker = true" :rules="[{ required: true, message: '请选择模具状态' }]" />
            <van-popup v-model="showPicker" position="bottom">
                <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
            </van-popup>

            <van-field label="备注" v-model="formData.remarks" placeholder="请输入" />

            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast } from 'mint-ui';
export default {
    data() {
        return {
            count: '',
            info: {},
            columns: ['完好', '损坏'],
            showPicker: false,

            // 领用的
            receiveArr: [],
            formData: {
                count: '',
                moldStatus: '',
                remarks: '',
                type: 3,
                moldsId: '',
                parentId: '',
                creator: localStorage.getItem('userCode'),
                createName: localStorage.getItem('userName')
            },
        }
    },
    created() {
        this.info = JSON.parse(localStorage.getItem("moduleReceiveItem"))
        this.formData.parentId = this.info.id
        this.formData.moldsId = this.info.moldsId
        console.log('info', this.info);
        // if (!this.info.id) {
        //     this.$router.replace({
        //         name: "ModuleInfoDetail",
        //     });
        // }
    },
    methods: {
        onConfirm(value) {
            this.formData.moldStatus = value;
            this.showPicker = false;
        },
        onSubmit() {
            let flag = true
            if (flag) {
                flag = false
                if (this.formData.count * 1 > this.info.count * 1 - this.info.backNumber * 1) {
                    Toast({
                        message: '归还数量不能大于领用数量',
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    // 数量只能是正整数
                    if (/^[1-9]\d*$/.test(this.formData.count)) {
                        this.$axios
                            .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.formData)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.$router.replace({
                                        name: "ModuleReceiveList",
                                    });
                                    flag = true
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        Toast({
                            message: '请输入正整数',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                }
            }
        },
    },
}
</script>

<style  scoped>
</style>