<template>
  <div>
    <div>
      <img :src="home" class="factory"/>
      <img :src="logo" class="logo"/>
      <van-field label="用户名" v-model="userCode"  class="field1"/>
      <van-field label="密码" v-model="password"  class="field2"/>
      <van-button type="primary" class="loginBtn" @click="submit()">登陆</van-button>
    </div>
  </div>
</template>

<script>
import { DatetimePicker,Toast,MessageBox,Indicator   } from 'mint-ui';
  export default {
    components: {
        Toast
    },
    data() {
      return {
        home:require('../../static/images/home.png'),
        logo:require('../../static/images/colori_logo.png'),
        userCode:'',
        password:'Kll@1234',
        staffInfo:{}
      };
    },
    created:function(){

    },
    methods:{
      submit(){
        let self=this
        Indicator.open({
          text: '处理中，请稍后……',
          spinnerType: 'fading-circle'
        });
        let params={
          username:self.userCode,
          password:self.password
        }
        self.$axios.post('/jeecg-boot/app/login/login',params).then(res=>{
          if(res.data.code==200){
            Indicator.close();
            self.staffInfo=res.data.result;
            localStorage.setItem('userCode',res.data.result.code);
            localStorage.setItem('userName',res.data.result.name);
            localStorage.setItem('positionName',res.data.result.workshop);
            localStorage.setItem('departmentName',res.data.result.department);
            localStorage.setItem('avatar',"https://service.colori.com/jeecg-boot/sys/common/static/apk/norfound.jpg");
            this.$router.push({path:'/home'})
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 1000
            });
          }
        });
      }
      
    }
  };
</script>
<style scoped>
.page-tabbar-container{
  height: 100%;
}
.factory{
  background-size: 100% 100%;
  height: 100%;
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 1;
}
.logo{
  z-index: 99;
  position: absolute;
  top: 150px;
  width: 41%;
  left: 30%;
}
.field1{
  position:absolute;
  z-index: 99;
  top: 35%;
}
.field2{
  position:absolute;
  z-index: 99;
  top: 40%;
}
.loginBtn{
  position:absolute;
  z-index: 999;
  top: 50%;
  left: 30%;
  width: 40%;
}
</style>