<template>
    <div style="background:#f3f4f6;min-height:120px;text-align:left;">

        <div style="padding:5px">
        
            <div style="font-size:18px;font-weight:800;margin-left:5%;">材料情况</div>

            <div v-for="(item,index) in orderInfo.cl" :key="index" style="margin-left:10%;margin-bottom:10px;">
                <div>{{item.malCode}}</div>
                <div style="width:100%;height:1px;background:#808080"></div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">物料名称:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.malName}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">所属订单:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.orderId}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">应需数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.neednum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">实需数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.actnum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">采购数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.buynum==null?0:item.buynum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">当前状态:</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='1'">待处理</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='2'">足料</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='3'">足料按需</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='4'">采购询价</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='5'">询价中</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='6'">询价完成</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='7'">计划下单</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='8'">起订量校验</div>
                </div>
                <div style="width:100%;" v-if="item.status=='3' || item.status=='7'">
                    <div style="float:left;width:20%;text-align:left;">最晚到货日期:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.arrivalTime}}</div>
                </div>
                <div style="clear:both;"></div>
            </div>


            <div style="font-size:18px;font-weight:800;margin-left:5%;">原料情况</div>

            <div v-for="(item,index) in orderInfo.yl" :key="index" style="margin-left:10%;margin-bottom:10px;">
                <div>{{item.malCode}}</div>
                <div style="width:100%;height:1px;background:#808080"></div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">物料名称:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.malName}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">所属订单:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.orderId}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">应需数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.neednum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">实需数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.actnum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">采购数量:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.buynum==null?0:item.buynum}}{{item.measdoc}}</div>
                </div>
                <div style="width:100%;">
                    <div style="float:left;width:20%;text-align:left;">当前状态:</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='1'">待处理</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='2'">足料</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='3'">足料按需</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='4'">采购询价</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='5'">询价中</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='6'">询价完成</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='7'">计划下单</div>
                    <div style="float:left;width:80%;text-align:left;" v-if="item.status=='8'">起订量校验</div>
                </div>
                <div style="width:100%;" v-if="item.status=='3' || item.status=='7'">
                    <div style="float:left;width:20%;text-align:left;">最晚到货日期:</div>
                    <div style="float:left;width:80%;text-align:left;">{{item.arrivalTime}}</div>
                </div>
                <div style="clear:both;"></div>
            </div>


            <div style="clear:both;"></div>


        </div>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            status:"",
            orderId:"",
            orderInfo:[],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.orderId=this.$route.query.orderId
        console.log(this.orderId)
        this.getOrderMaterial();
    },
    methods: {
        getOrderMaterial(){
            let self=this
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMaterialByOrderId',{params:{orderId:this.orderId}}).then(res=>{
                if(res.data.code==200){
                    self.orderInfo=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.content{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #fff;
    border-radius: 5px;
}
.order{
    font-weight: 600;
    font-size: 1.2rem;
    color: #000;
}
.order-time{
    font-size: 0.6rem;
    color: #888;
    align-items: flex-end;
    display: flex;
}
.order-name{
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    padding-left: 2%;
}
.product{
    width: 100%;
    padding-top: 2%;
}
.product-title{
    padding-left: 1%;
    width: 20%;
    font-size: 0.6rem;
    color: #888;
    float: left;
}
.product-name{
    width: 78%;
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    float: left;
    word-wrap: break-word;
}
.clock{
    width: 100%;
    padding-top: 2%;
}
.clock-title{
    padding-left: 1%;
    width: 47%;
    text-align: left;
    padding-left: 3%;
    font-size: 0.6rem;
    color: #888;
    float: left;
}
.clock-name{
    width: 50%;
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    float: left;
    word-wrap: break-word;
}
</style>