<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_odo" width="100%"/>
        <div class="top_title">司机里程费用查询</div>
        <div class="top_hint">在这里您可以查看司机里程费用情况</div>
        <mt-button type="primary" style="margin-top:10px;width:75%;" size="small" v-on:click="selectData">{{this.currentDate}}</mt-button>
        <van-popup position="bottom" v-model="popup_seldate" >
        <van-datetime-picker @confirm="onConfirm" @cancel="popup_seldate=!popup_seldate" 
            v-model="currentDate"
            type="year-month"
            :min-date="minDate"
            :max-date="maxDate"
            :formatter="formatter"
        />  
        </van-popup>
        
        
        <div style="margin-top:2rem;margin-bottom:10rem;">
            <div class="car_item" v-for="(item,index) in feeList" :key="index" @click="handleCarClick(index)">
                <div class="item-label">出行开始时间：</div><div class="item-value">{{item.actStartTime}}</div>
                <div class="item-label">出行结束时间：</div><div class="item-value">{{item.actEndTime}}</div>
                <div class="item-label">出行里程数：</div><div class="item-value">{{item.odo}}km</div>
                <div class="item-label">出行餐补费用：</div><div class="item-value">¥{{item.foodSubsidy}}</div>
                <div class="item-label">出行加班费用：</div><div class="item-value">¥{{item.overtimeFee}}</div>
            </div>
        </div>

        <div style="height:5rem"></div>

        <div class="odo_bottom">
            <div class="fee">里程数总计：</div><div class="fee-value">{{fee.allOdo}}km</div>
            <div class="fee">餐费总计：</div><div class="fee-value">¥{{fee.allFood}}</div>
            <div class="fee">加班费总计：</div><div class="fee-value">¥{{fee.allOvertimeFee}}</div>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker } from 'vant';
import { formatDate } from '@/common/commonUtil.js';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            selectedValue: this.formatter(new Date()),
            my_odo:require('../../static/images/myOdo.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            feeList:[],
            fee:{},
            popup_seldate:false,
            date_show:false,
            minDate: new Date(2021, 0, 1),
            maxDate: new Date(),
            currentDate: formatDate(new Date(),'yyyyMM'),
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.getFeeInfo();
    },
    methods: {
        getFeeInfo(){
            let self=this;
            this.$axios.get('/jeecg-boot/car/getOdoFeeById',{
                params:{
                    userCode:localStorage.getItem('userCode'),
                    // userCode:'HI0901060701',
                    createTime:self.currentDate
                }
            }).then(res=>{
                if(res.data.code=200){
                    self.feeList=res.data.result.carOdoFees
                    self.fee=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'top',
                        duration: 5000
                    });
                }
                console.log(self.carList)
            })
        },
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            this.popup_seldate=true
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            console.log(this.currentDate)
            this.date_show=false;
        },
        onConfirm:function (value) {
            this.currentDate = formatDate(value,'yyyyMM');
            this.getFeeInfo()
            this.popup_seldate = !this.popup_seldate;
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatter(type, val) {
            if (type === 'year') {
                return `${val}年`;
            } else if (type === 'month') {
                return `${val}月`
            }
            return val;
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.item-label{
    padding-top: 2%;
    float: left;
    width: 40%;
}
.item-value{
    padding-top: 2%;
    float: left;
    width: 59%;
}
.fee{
    float: left;
    width: 40%;
    font-size: 1rem;
}
.fee-value{
    float: left;
    width: 59%;
    font-size: 1rem;
    font-weight: 800;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
}
.right_pic{
    position: absolute;
    right: 5%;
}
.car_title_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 1rem;
    color: #455a64;
    font-weight: 500;
}
.car_title_name{
    margin-left: 8%;
    font-size: 1.1rem;
    color: #000;
    font-weight: 600;
}
.car_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 0.88rem;
    color: #455a64;
}
.car_status{
    position:absolute;
    left: 20%;
    top: 8%;
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
}
.car_item{
    margin-top: 1rem;
    width: 100%;
    height: 9rem;
    background: #fff;
    position: relative;
    padding-left: 5%;
}
.odo_bottom{
    width: 100%;
    background: #fff;
    position: fixed;
    clear: both;
    bottom: 0;
    padding: 10px;
}
</style>