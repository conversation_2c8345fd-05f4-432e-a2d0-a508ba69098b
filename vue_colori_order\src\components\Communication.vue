<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div style="text-align:center;font-size:20px;margin:10px;font-weight:800;">交流指导记录表</div>
        <van-form ref='form'>
            <van-field label="交流日期" :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" required v-model="communionInfo.communDate" @click="c_show=true" style="width:70%;float:left;" :rules="[{required:true,message:'请输入交流日期'}]"/>
            <van-field label="交流地点" :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" required v-model="communionInfo.area" :rules="[{required:true,message:'请输入交流地点'}]"/>
            <div style="text-align:left;font-size:16px;margin:10px;">本周安排实习内容及掌握情况</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.practice" required rows="6" autosize type="textarea" :rules="[{required:true,message:'请输入实习内容及掌握情况'}]"
             maxlength="500" placeholder="请输入本周安排实习内容及掌握情况" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;color:#333333">直属领导<span style="text-decoration:underline;color: #00f;padding:5px">{{communionInfo.userLeader_dictText}}</span>评语：（了解困难问题，开展指导帮促）</div>

            <div style="text-align:left;font-size:16px;margin:10px;">态度（是否主动）</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.attitude" required rows="3" autosize type="textarea" :rules="[{required:true,message:'请输入态度情况'}]" maxlength="100" placeholder="请输入态度情况" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">沟通（是否谦虚，交流能力）</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.communicate" required rows="3" autosize type="textarea" :rules="[{required:true,message:'请输入沟通情况'}]" maxlength="100" placeholder="请输入沟通情况" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">做事（是否积极、可靠）</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.work" required rows="3" autosize type="textarea" :rules="[{required:true,message:'请输入做事情况'}]" maxlength="100" placeholder="请输入做事情况" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">奋斗（工作目标、努力程度）</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.fight" required rows="3" autosize type="textarea" :rules="[{required:true,message:'请输入奋斗情况'}]" maxlength="100" placeholder="请输入奋斗情况" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">学习（学习能力、培养潜力）</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.study" required rows="3" autosize type="textarea" :rules="[{required:true,message:'请输入学习情况'}]" maxlength="100" placeholder="请输入学习情况" show-word-limit />

            <div style="text-align:left;font-size:16px;margin:10px;color:#333333;font-weight:800;">综合评估：</div>
            <div style="text-align:left;font-size:16px;margin:10px;">优势：</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.advantage" required rows="6" autosize type="textarea" :rules="[{required:true,message:'请输入优势'}]" maxlength="500" placeholder="请输入优势" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">不足：</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.insufficient" required rows="6" autosize type="textarea" :rules="[{required:true,message:'请输入不足'}]" maxlength="500" placeholder="请输入不足" show-word-limit />
            
            <div style="text-align:left;font-size:16px;margin:10px;">建议：</div>
            <van-field :disabled="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.suggestion" style="margin-bottom:30px;" required rows="6" autosize type="textarea" :rules="[{required:true,message:'请输入建议'}]" maxlength="500" placeholder="请输入建议" show-word-limit />
            
            <div v-show="checkUser!=null && checkUser!='' && checkUser!=undefined" style="text-align:left;font-size:16px;margin:10px;">审核原因：</div>
            <van-field v-show="checkUser!=null && checkUser!='' && checkUser!=undefined" v-model="communionInfo.reason" style="margin-bottom:30px;" rows="3" autosize type="textarea" maxlength="100" placeholder="请输入审核原因" show-word-limit />
            
            <van-button type="primary" v-show="(checkUser==null || checkUser=='' || checkUser==undefined) && communionInfo.status=='1'" :loading="loading" @click="submit" style="width:100%;margin-top:30px;margin-bottom:50px;">提交</van-button>
            <van-button type="primary"  v-show="checkUser!=null && checkUser!='' && checkUser!=undefined && communionInfo.status=='2'" 
            :loading="loading" @click="check" style="width:100%;margin-top:30px;margin-bottom:10px;">审批</van-button>
            <van-button type="danger"  v-show="checkUser!=null && checkUser!='' && checkUser!=undefined && communionInfo.status=='2'" 
            :loading="loading" @click="reback" style="width:100%;margin-bottom:50px;">驳回</van-button>

            <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />
        </van-form>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

export default {
    data(){
        return{
            communionInfo:{
                id:"",
                communDate:"",
                area:"",
                advantage:"",
                attitude:"",
                communicate:"",
                work:"",
                insufficient:"",
                fight:"",
                study:"",
                practice:"",
                status:"1",
                reason:"",
                userLeader_dictText:"",
                suggestion:"",
                checkUserCode:"",
            },
            c_show:false,
            loading:false,
            minDate:"",
            maxDate:"",
            checkUser:"",
        }
    },
    components:{
        DatetimePicker
    },
    created: function () {
        let self = this;
        self.communionInfo.id = this.$route.query.id
        self.checkUser = this.$route.query.userCode
        let nowDate = new Date();
        self.minDate = new Date(nowDate.getTime() - 700 * 24 * 3600 * 1000);
        self.maxDate=nowDate
        console.log(self.checkUser)
        self.getCommunionInfo();
    },
    methods: {
        reback(){
            let self = this;
            if(self.communionInfo.reason==null || self.communionInfo.reason==''){
                Toast({
                    message: "请输入审核原因",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            MessageBox.confirm('是否驳回此交流记录？').then(action => {
                if(action=="confirm"){
                    self.communionInfo.checkUserCode = self.checkUser
                    self.$axios.put('/jeecg-boot/app/ncStaffCommunionInfo/reback',self.communionInfo).then(res=>{
                        if(res.data.code==200){
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            self.getCommunionInfo();
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    });
                }
            })
        },
        check(){
            let self = this;
            MessageBox.confirm('是否通过此交流记录？').then(action => {
                if(action=="confirm"){
                    self.communionInfo.checkUserCode = self.checkUser
                    self.$axios.put('/jeecg-boot/app/ncStaffCommunionInfo/check',self.communionInfo).then(res=>{
                        if(res.data.code==200){
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            self.getCommunionInfo();
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    });
                }
            })
        },
        getCommunionInfo(){
            let self = this;
            self.$axios.get('/jeecg-boot/app/ncStaffCommunionInfo/list',{params:{id:self.communionInfo.id}}).then(res=>{
                if(res.data.code==200){
                    self.communionInfo = res.data.result.records[0]
                    console.log("checkUserCode:"+self.communionInfo.checkUserCode)
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            });
        },
        submit(){
            let self = this
            self.$refs.form.validate().then(() => {
                self.edit()
            }).catch(() => {
                Toast({
                    message: '请将数据填写完整！',
                    position: 'bottom',
                    duration: 2000
                });
            })
        },
        edit(){
            let self = this;
            self.communionInfo.status = '2'
            self.$axios.put('/jeecg-boot/app/ncStaffCommunionInfo/edit',self.communionInfo).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getCommunionInfo();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            });
        },
        onConfirm(date){
            this.c_show = false;
            this.communionInfo.communDate = this.formatDate(date);
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.star-rating {
  display: flex;
  margin-right:20px;
  justify-content: right;
  text-align: right;
  width: 45%;
}
.star {
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s;
}
.star::before {
  content: '☆';
}
.star.filled::before,
.star:hover::before {
  content: '★';
  color: gold;
}
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
/deep/ .van-field__label{
    width: 7em;
}
/deep/ .van-cell__title{
    text-align: left;
}
/deep/ .van-field__control{
    color: #00f;
}
/deep/ .van-cell{
    font-size: 1rem;
}
/deep/ .mint-radiolist-title{
    font-size: 16px;
    color: #333;
}
</style>