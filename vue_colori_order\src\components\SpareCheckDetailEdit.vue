<template>
    <div>
        <van-field readonly label="编号" :value="item.code" />
        <van-field readonly label="名称" :value="item.name" />
        <van-field readonly label="货位" :value="item.location" />
        <van-field readonly label="厂家" :value="item.factory" />
        <van-field readonly label="型号" :value="item.model" />
        <van-field readonly label="规格" :value="item.spec" />
        <van-field readonly label="现存量" :value="item.stock" />
        <van-field readonly label="差异" :value="different" />
        <van-field readonly label="盘点人" :value="item.creator" />
        <van-field readonly label="盘点时间" :value="item.createTime" />

        <van-divider>修改</van-divider>
        <van-field label="盘点量" type="digit" v-model="item.checkStock" />
        <van-field label="备注" v-model="item.remarks" />

        <van-button type="info" @click="submit" style="width: 90%;">
            提交
        </van-button>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            item: {},
            different: ''
        }
    },
    created() {
        this.item = JSON.parse(localStorage.getItem("SpareCheckDetailEditItem"))
        this.different = this.item.stock - this.item.checkStock
    },
    methods: {
        submit() {
            let department = localStorage.getItem('department')
            console.log(department)
            if (department == '财务' || department == '督察' ) {
                let NcPartsCheckDetailInfo = this.item
                this.$axios
                    .post(`/jeecg-boot/ncApp/partsCheck/editDetailInfo`, NcPartsCheckDetailInfo)
                    .then(res => {
                        if (res.data.code == 200) {
                            localStorage.setItem('SpareCheckDetailEditItem', JSON.stringify(this.item))
                            this.$router.push({ name: "SpareCheckDetail" })
                        } else {
                            Toast({
                                message: res.data.msg,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            } else {
                Toast({
                    message: "没有权限",
                    position: 'bottom',
                    duration: 2000
                });
            }

        }
    },
}
</script>

<style scoped>
</style>