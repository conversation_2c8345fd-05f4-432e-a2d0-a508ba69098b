<template>
  <div style="text-align:left;background-color:#F1F1F1;">
    <van-sticky :offset-top="0">
      <van-nav-bar v-if="matCheckFlag=='1'" title="投料校验" right-text="手动录入" left-arrow @click-left="onClickLeft" @click-right="onClickRight" />
      <van-nav-bar v-else title="投料校验" left-arrow @click-left="onClickLeft"  />
      <van-button v-if="matCheckFlag=='1'" type="info" @click="check" style="width:100%;">
        扫码校验(共{{ allNum }}个,剩余{{ residueNum }}个)
      </van-button>
      <van-button v-else type="warning" @click="unlock" style="width:100%;">
        解锁
      </van-button>
    </van-sticky>
    <div style="width:100%;height:100%;overflow: auto;">
      <div v-for="(item, index) in dataArr" :key="index"
        style="text-align: left;background-color:#fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
        <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
          <van-row>
            <van-col span="20">
              <span style="font-size:18px;font-weight: 700;color: #000;">
                {{ item.materialCode }}
              </span>
            </van-col>
            <van-col span="4">
              <span v-if="item.feedCount != 0 " style="color:#66CC00;">
                已校验
              </span>
              <span v-else style="color:#FF0033">
                未校验
              </span>
            </van-col>
          </van-row>
        </div>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> ID：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.id }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6"> 原料名称：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.materialName }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" style="color:gary">
            <van-row>
              <van-col span="6">区域：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.area }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 批次号：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.nccBatchCode }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 供应商：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.supplier }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 重量：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.actualWeight }}KG
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24">
            <van-row>
              <van-col span="6"> 校验时间：</van-col>
              <van-col span="18">
                <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                  {{ item.feedTime }}
                </span>
              </van-col>
            </van-row>
          </van-col>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
  import { DatetimePicker, Indicator, MessageBox } from "mint-ui";
  import { Toast } from "vant";
  export default {
    data() {
      return {
        // 配料单ID
        id: "",
        userCode: localStorage.getItem("userCode"),
        dataArr: [],
        // 共有
        allNum: 0,
        // 剩余
        residueNum: 0,
        matCheckFlag:0,
      };
    },
    created() {
      this.matCheckFlag= localStorage.getItem("matCheckFlag")
      this.id = this.$route.params.id;
      if (this.$route.params) {
        Indicator.open({
          text: '正在加载中，请稍后……',
          spinnerType: 'fading-circle'
        });
        this.$axios
          .get(`/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.$route.params.id}`)
          .then(res => {
            if (res.data.code == 200) {
              this.residueNum = 0;
              console.log(res.data.result);
              this.dataArr = res.data.result;
              this.allNum = this.dataArr.length;
              this.dataArr.forEach(item => {
                if (item.feedCount == 0) {
                  this.residueNum++;
                }
              });
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          }).finally(() => {
            Indicator.close();
          });
      } else {
        this.$route.go(-1);
      }
    },
    methods: {
      unlock(){
        Toast.success('已解锁');
        localStorage.setItem("matCheckFlag",'1')
        this.matCheckFlag =  localStorage.getItem("matCheckFlag")
      },
      onClickLeft() {
        this.$router.go(-1);
      },
      // 扫码
      check() {
        let self = this;
        wx.scanQRCode({
          desc: "scanQRCode desc",
          needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
          success: function (res) {
            // 回调
            var result = res.resultStr; //当needResult为1时返回处理结果
            // 处理结果 result
            self.checkRequest(result);
          },
          error: function (res) {
            if (res.errMsg.indexOf("function_not_exist") > 0) {
              alert("版本过低请升级");
            }
          }
        });
      },
      onClickRight() {
        MessageBox.prompt("请录入ID").then(({ value, action }) => {
          if (action == "confirm") {
            if (!value) {
              Toast.fail("不能为空");
              return;
            } else {
              this.checkRequest(value);
            }
          }
        });
      },
      // 校验请求
      checkRequest(code) {
        let obj = {};
        let flag= false
        this.dataArr.forEach(item => {
          if (code == item.id) {
            obj = item;
            flag=true
          } 
        });
        if (obj.status == 5) {
          Toast.fail('已校验')

          localStorage.setItem("matCheckFlag",'0')
          this.matCheckFlag =  localStorage.getItem("matCheckFlag")
          return
        }
        if (!flag) {
        Toast.fail("未找到该原料");
        this.$axios
          .post(`/jeecg-boot/app/gcMixError/add`, {
            mainId: this.id,
            errorInfo: code,
            userCode: localStorage.getItem("userCode"),
            userName: localStorage.getItem("userName"),
          })
          .then((res) => {
            if (res.data.code == 200) {
              localStorage.setItem("matCheckFlag", "0");
              this.matCheckFlag = localStorage.getItem("matCheckFlag");
              this.$router.go(-1);
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000,
              });
            }
          });
        return;
      }
        obj.userCode = localStorage.getItem('userCode')
        obj.userName = localStorage.getItem('userName')
        Indicator.open({
          text: '正在加载中，请稍后……',
          spinnerType: 'fading-circle'
        });
        this.$axios.put(`/jeecg-boot/app/gcMix/feedMixWeight`, obj).then(res => {
          if (res.data.code == 200) {
            // 校验成功刷新列表
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            Indicator.open({
              text: '正在加载中，请稍后……',
              spinnerType: 'fading-circle'
            });
            this.$axios
              .get(`/jeecg-boot/app/gcMix/getMixDetailInfo?ids=${this.id}`)
              .then(res => {
                if (res.data.code == 200) {
                  this.residueNum = 0;
                  this.dataArr = res.data.result;
                  this.allNum = this.dataArr.length;
                  this.dataArr.forEach(item => {
                    if (item.feedCount == 0) {
                      this.residueNum++;
                    }
                  });
                  if (this.residueNum == this.allNum * 1 - this.residueNum * 1) {
                    Toast.success(res.data.message + ',全部校验完成');
                  } else {
                    let aaa = this.allNum * 1 - (this.allNum * 1 - this.residueNum * 1)
                    Toast.success(res.data.message + ',还剩' + aaa + '未校验');
                  }
                } else {
                  localStorage.setItem("matCheckFlag",'0')
                  this.matCheckFlag =  localStorage.getItem("matCheckFlag")
                  Toast.fail(res.data.message)
                }
              }).finally(() => {
                Indicator.close();
              });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        }).finally(() => {
          Indicator.close();
        });
      }
    }
  };
</script>

<style scoped>

</style>