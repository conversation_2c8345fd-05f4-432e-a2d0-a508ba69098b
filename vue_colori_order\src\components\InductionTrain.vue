<template>
    <div>
        <van-nav-bar :title="type == '1' ? '新员工入职培训' : '新员工入职培训(一线)'" left-arrow @click-left="onClickLeft"/>
        <div v-show="showForm">
            <van-form validate-first @submit="onSubmit">
                <div class="title">培训内容</div>
                <van-field name="checkboxGroup">
                    <template #input>
                        <van-checkbox-group v-model="checkboxGroup">
                            <van-checkbox v-for="(item,index) in chooseList" :key="item.id" :name="item.id" shape="square" style="margin-bottom: 10px;">
                                {{index+1}}. {{item.content}}
                            </van-checkbox>
                        </van-checkbox-group>
                    </template>
                </van-field>

                <div style="margin: 16px;">
                    <van-button round block type="info" native-type="submit">提交</van-button>
                </div>
            </van-form>
        </div>
        <div v-show="!showForm">
            <van-grid :border="false" class="confirmClass" icon-size="50" column-num="3">
                <van-grid-item />
                <van-grid-item icon="success" text="培训内容已确认" />
                <van-grid-item />
            </van-grid>
            
        </div>
        
    </div>
</template>

<script>
import { Toast, Dialog, Icon, Grid, GridItem} from "vant";
import { Indicator, MessageBox } from 'mint-ui';
export default {
    data() {
        return {
            info: {},
            showForm: false,
            id: '',
            llqUserCode: '',
            type: '',
            chooseList: [],
            checkboxGroup: []
        }
    },
    async created() {
        this.id = this.$route.query.id
        this.llqUserCode = this.$route.query.llqUserCode
        this.type = this.$route.query.type
        await this.getTemList()
        
    },
    methods: {
        getTemList() {
            this.$axios.get(`/jeecg-boot/app/trainConfirm/judge?mainId=${this.id}&type=${this.type}&llqUserCode=${this.llqUserCode}`)
            .then(res => {
                console.log('created-> res',res)
                if (res.data.code == 200) {
                    this.chooseList = res.data.result
                    this.showForm = true
                    for(let i = 0; i < this.chooseList.length; i++) {
                        this.checkboxGroup.push(this.chooseList[i].id)
                    }
                    
                } else {
                    this.showForm = false
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        onSubmit(values) {
            if (this.checkboxGroup.length == 0) {
                Toast({
                    message: '相关信息不完整',
                    position: "bottom",
                    duration: 2000
                });
            } else {
                // values.pictureList = this.pictureList
                // values.lineType = this.lineType
                // values.operator = this.operator
                // values.machineId = this.info.deviceNo
                // values.createNo = localStorage.getItem('userCode')
                // Object.assign(values, this.info)
                const that = this
                let param = {
                    mainId: this.id,
                    llqUserCode: this.llqUserCode,
                    content: this.checkboxGroup.join(','),
                    creator: localStorage.getItem('userCode')
                }

                Indicator.open({
                    text: '正在加载中，请稍后……',
                    spinnerType: 'fading-circle'
                });
                this.$axios.post(`/jeecg-boot/app/trainConfirm/add`, param).then(res => {
                    if (res.data.code == 200) {

                        Indicator.close();
                        that.getTemList()
                        // self.$router.go(-1);
                    } else {
                        Indicator.close();
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            }
        },
        onClickLeft() {
            this.$router.replace({
                name: "Home",
            });
        },
        
    },
}
</script>

<style scoped>
.title {
    font-size: 18px;
    font-weight: bold;
}
.confirmClass {
    height: 80vh;
}
</style>