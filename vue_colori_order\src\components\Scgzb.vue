<template>
    <div>
        <div style="height:6rem;">
            <div class="top_order_title">生产管制表</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div>
        </div>
        <div class="sign">
            <img :src="plotTop" width="90%"/>
            <div class="plotName">{{peopleInfo.name}}</div>
            <div class="plotCode">{{peopleInfo.code}}</div>
            <div class="plotFactory">{{peopleInfo.department}}</div>
            <div class="plotWorkshop">{{peopleInfo.workshop}}</div>
            <!-- <div class="plotMitosome">线体</div> -->
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div>

        <div class="sc_date">
            <div class="rq_date">日期</div>
            <div class="date_work" @click="c_show = true">{{date}}</div>
            <div class="right_jt"></div>
        </div>

        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false" position="right" />

        <div>
            <div class="orderType" v-if="runList.length>0">生产记录</div>
            <div v-for="(item,index) in runList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" :class="item.checkStatus=='2'?'ycl-style':''">{{item.name}}</div>
                        <div class="itemTitle">{{item.moId}}_{{item.mitosome}}_{{item.leaderNo}}</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
            <div class="orderType" v-if="closeList.length>0">关闭记录</div>
            <div v-for="(item,index) in closeList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle">{{item.moId}}_{{item.mitosome}}_{{item.leaderNo}}</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
import { DatetimePicker,Toast,Indicator } from 'mint-ui';
export default {
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            nowDate:'',
            minDate:'',
            date:'',
            peopleInfo:{},
            runList:[],
            closeList:[],
        }
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 1 * 24 * 3600 * 1000);
        this.date=this.formatDate(new Date)
        this.getMainList();
    },
    methods:{
        getMainList(){
            let self=this;
            self.userCode=localStorage.getItem('userCode');
            self.$axios.get('/jeecg-boot/app/appQuality/getMainList',{params:{userCode:self.userCode,workDay:self.date}}).then(res=>{
                if(res.data.code==200){
                    self.peopleInfo=res.data.result.peopleInfo
                    self.runList=res.data.result.runList
                    self.closeList=res.data.result.closeList
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
</style>