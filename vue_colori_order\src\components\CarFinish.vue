<template>
    <div style="background:#f3f4f6;">
        <img :src="car_top" width="100%"/>
        <div class="top_title">用车归队处理</div>
        <div class="top_hint">在这里您可以查看车辆调度情况</div>
        <div class="line"></div>
        <div style="text-align:left;margin-left:5%">
            <span class="car_title">车牌号：</span><span class="car_card">{{carInfo.carNum}}</span>
            <span class="car_status" v-if="carInfo.status=='1'">预定</span>
            <span class="car_status" v-if="carInfo.status=='2'">在途</span>
            <span class="car_status" v-if="carInfo.status=='3'">归队</span>
        </div>
        <div class="line"></div>
        <div class="first_item">
            <span>申请人：</span><span>{{carInfo.userName}}</span>
        </div>
        <div class="other_item">
            <span>使用人：</span><span>{{carInfo.contractName}}</span>
        </div>
        <div class="other_item">
            <span>申请出车时间：</span><span>{{carInfo.startTime}}</span>
        </div>
        <div class="other_item">
            <span>申请归车时间：</span><span>{{carInfo.endTime}}</span>
        </div>
        <div class="other_item">
            <span>实际出车时间：</span><span>{{carInfo.actStartTime}}</span>
        </div>
        <div class="other_item">
            <span>申请出车时间：</span><span>{{carInfo.actEndTime}}</span>
        </div>
        <div class="other_item">
            <span>起始里程：</span><span>{{carInfo.startOdo}}</span>
        </div>


        <mt-field label="里程度数:" placeholder="" type="number" v-model.trim="carInfo.endOdo" style="margin-top:3rem;"></mt-field>
        <mt-field label="餐   补   费:" placeholder="" type="number" v-model.trim="carInfo.foodSubsidy" ></mt-field>
        <mt-field label="加   班   费:" placeholder="" type="number" v-model.trim="carInfo.overtimeFee" ></mt-field>
        <mt-field label="停   车   费:" placeholder="" type="number" v-model.trim="carInfo.parkingFee" ></mt-field>
        <mt-field label="过   路   费:" placeholder="" type="number" v-model.trim="carInfo.tollFee" ></mt-field>

        <mt-button type="default" class="back_button" style="margin-top:3rem;" @click="back()">返回</mt-button>
        <mt-button type="primary" class="back_button" @click="submit()" v-if="carInfo.spStatus!='4'">提交</mt-button>
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            selected:'',
            car_top:require('../../static/images/car_top.png'),
            carInfo:{}
        }
    },
    components:{
        DatetimePicker
    },
    created(){
        this.spNo=this.$route.query.spNo;
        this.getCarInfo()
    },
    methods: {
        getCarInfo(){
            const self=this
            self.$axios.get('/jeecg-boot/car/getCarInfoById',{params:{spNo:self.spNo}}).then(res=>{
                if(res.data.code===200){
                    console.log(res.data.result)
                    const result=res.data.result
                    self.carInfo=result[0]
                }
            })
        },
        back(){
            let self=this
            self.$router.go(-1);
            Toast({
                message: '请点击左上角退出',
                position: 'bottom',
                duration: 5000
            });
        },
        submit(){
            const self=this
            self.$axios.post('/jeecg-boot/car/submitCarInfo',self.carInfo).then(res=>{
                if(res.data.code===200){
                    console.log(res.data.result)
                    Toast({
                        message: '行程提交成功！',
                        position: 'bottom',
                        duration: 5000
                    });
                    self.$router.go(0);
                }
            })
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
    margin-bottom: 3rem;
}
.car_title{
    font-size: 1.2rem;
    color: #455a64;
    font-weight: 500;
}
.car_card{
    font-size: 1.5rem;
    margin-left: 5%;
    color: #000;
    font-weight: 550;
}
.line{
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    background: #bfbfbf;
    height: 1px;
    width: 100%;
}
.first_item{
    margin-top: 2rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.other_item{
    margin-top: 0.8rem;
    text-align: left;
    margin-left: 5%;
    color: #455a64;
}
.mint-cell-text{
    color: #2285d0;
    font-weight: 600;
}
.mint-cell-wrapper{
    font-size: 1.2rem;
}
.back_button{
    width: 80%;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    height: 4rem;
    border-radius: 10px;
    border: 1px solid #bfbfbf;
}
.write_item{
    padding: 0.5rem;
}
.write_item_num{
    float: left;
    color: #2285d0;
    font-size: 1.2rem;
    font-weight: 600;
}
.mint-field-core{
    text-align: center;
}
.car_status{
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
    text-align: center;
    float: right;
    margin-right: 10%;
}
</style>