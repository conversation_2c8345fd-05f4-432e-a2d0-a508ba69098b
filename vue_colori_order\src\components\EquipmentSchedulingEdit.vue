<template>
    <div>
        <van-form validate-first @submit="onSubmit">
            <!-- 日期 -->
            <van-field readonly clickable name="workDay" :value="workDay" label="日期" placeholder="点击选择日期"
                @click="showCalendar = true" :rules="[{ required: true, message: '请选择日期' }]" />
            <van-calendar v-model="showCalendar" @confirm="workDayConfirm" color="#1989fa" />
            <!-- 账套 -->
            <van-field readonly clickable name="book" :value="book" label="账套" placeholder="点击选择账套"
                @click="showBookPicker = true" :rules="[{ required: true, message: '请选择账套' }]" />
            <van-popup v-model="showBookPicker" position="bottom">
                <van-picker show-toolbar :columns="bookColumns" @confirm="onBookConfirm"
                    @cancel="showBookPicker = false" />
            </van-popup>

            <!-- 车间 -->
            <van-field readonly clickable name="workshop" :value="workshop" label="车间" placeholder="点击选择车间"
                @click="showWorkshopPicker = true" :rules="[{ required: true, message: '请选择车间' }]" />
            <van-popup v-model="showWorkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="onWorkshopConfirm"
                    @cancel="showWorkshopPicker = false" />
            </van-popup>

            <!-- 班次 -->
            <van-field readonly clickable name="category" :value="category" label="班次" placeholder="点击选择班次"
                @click="showCategoryPicker = true" :rules="[{ required: true, message: '请选择班次' }]" />
            <van-popup v-model="showCategoryPicker" position="bottom">
                <van-picker show-toolbar :columns="categoryColumns" @confirm="onCategoryConfirm"
                    @cancel="showCategoryPicker = false" />
            </van-popup>
            <!-- 类别 -->
            <van-field readonly clickable name="type" :value="type" label="类别" placeholder="点击选择类别"
                @click="showTypePicker = true" :rules="[{ required: true, message: '请选择类别' }]" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="onTypeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>


            <!-- 操作人名字 编码 -->
            <van-field name="leader" v-model="leader" label="机修工编码" placeholder="请输入机修工员编号"
                :rules="[{ validator, message: '必填' }]" />
            <van-field name="leaderNo" v-model="leaderNo" label="机修工员姓名" readonly
                :rules="[{ required: true, message: '必填' }]" />


            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            // 日期选择框显隐
            showCalendar: false,
            // 账套选择框显隐
            showBookPicker: false,
            // 车间选择框显隐
            showWorkshopPicker: false,
            // 班次选择框显隐
            showCategoryPicker: false,
            // 类别选择框显隐
            showTypePicker: false,
            // 账套数据
            bookColumns: [],
            // 车间数据
            workshopColumns: [],
            // 机修工名字
            leaderNo: '',
            // 机修工编码
            leader: '',
            // 日期
            workDay: '',
            // 账套
            book: '',
            // 车间
            workshop: '',
            // 班次数据
            categoryColumns: ['白班', '晚班'],
            // 班次
            category: '',
            // 类别
            type: '',
            // 类别数据
            typeColumns: ['设备', '通用'],
            // 路由传递参数
            info: {},
        }
    },
    created() {
        this.info = this.$route.params.item
        console.log(this.info);
        this.workDay = this.info.workDay
        this.book = this.info.book
        this.category = this.info.category
        this.leaderNo = this.info.leaderNo
        this.leader = this.info.leader
        this.type = this.info.type
        this.workshop = this.info.workshop
        this.checkboxGroup = this.info.type
        // 获取账套
        this.$axios
            .get(`/jeecg-boot/app/device/getClassDepartMent?userCode=${localStorage.getItem('userCode')}`)
            .then(res => {
                if (res.data.code == 200) {
                    this.bookColumns = res.data.result
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        // 获取车间
        this.$axios
            .get(`/jeecg-boot/app/device/getClassWorkshop?userCode=${localStorage.getItem('userCode')}&dept=${this.info.book}`)
            .then(res => {
                if (res.data.code == 200) {
                    this.workshopColumns = res.data.result
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });

    },
    methods: {

        // 通过编号搜索名字
        validator(value) {
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        this.leaderNo = res.data.message
                        return true
                    } else {
                        this.leaderNo = ''
                        Toast({
                            message: '操作人员编号不正确',
                            position: "bottom",
                            duration: 2000
                        });
                        return false
                    }
                });
        },
        // 日期选择确认
        workDayConfirm(date) {
            this.workDay = `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1}-${date.getDate() < 10 ? "0" + date.getDate() : date.getDate()}`;
            this.showCalendar = false;
        },
        // 账套确认
        onBookConfirm(value) {
            this.book = value;
            this.showBookPicker = false;

            this.workshopColumns = []
            this.workshop = ''
            // 获取车间
            this.$axios
                .get(`/jeecg-boot/app/device/getClassWorkshop?userCode=${localStorage.getItem('userCode')}&dept=${value}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.workshopColumns = res.data.result
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 车间确认
        onWorkshopConfirm(value) {
            this.workshop = value;
            this.showWorkshopPicker = false;
        },
        //班次确认
        onCategoryConfirm(value) {
            this.category = value;
            this.showCategoryPicker = false;
        },
        //类别确认
        onTypeConfirm(value) {
            this.type = value;
            this.showTypePicker = false;
        },
        // 提交
        onSubmit(values) {
            values.creator = localStorage.getItem('userCode')
            values.id = this.info.id
            console.log('请求参数', values);
            this.$axios
                .put(`/jeecg-boot/app/device/edit`, values)
                .then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.$router.replace({
                            name: 'EquipmentScheduling'
                        })
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
    },
}
</script>

<style  scoped>
</style>