<template>
  <div style="text-align:center;">
    <div style="text-align:left;font-weight:800;">评估结果汇总</div>
    <div class="radarChart" ref="radarChart" id="radarChart" style="width: 100vw;height: 20rem;"></div>
    <div style="text-align:left;font-weight:800;">管理者五项测评</div>
    <div class="manageChart" ref="manageChart" id="manageChart" style="width: 100vw;height: 20rem;"></div>


  </div>
</template>

<script>

// 引入需要的图表类型，柱状图和雷达图
require('echarts/lib/chart/radar')
// 引入提示框和title组件，图例
require('echarts/lib/component/tooltip')
require('echarts/lib/component/title')
require('echarts/lib/component/legend')
const echarts = require('echarts/lib/echarts')

export default {
  name: 'HelloWorld',
  data () {
    return {
      message: '提交成功',
      radarChart: null,
      manageChart:null,
      vdef1:"",
      vdef2:"0",
      vdef3:"0",
      vdef4:"0",
      vdef5:"0",
      vdef6:"0",
      vdef7:"0",
      manage1:"0",
      manage2:"0",
      manage3:"0",
      manage4:"0",
      manage5:"0",
    }
  },
  created(){

    this.vdef1 = this.$route.params.vdef1
    this.vdef2 = this.$route.params.vdef2
    this.vdef3 = this.$route.params.vdef3
    this.vdef4 = this.$route.params.vdef4
    this.vdef5 = this.$route.params.vdef5
    this.vdef6 = this.$route.params.vdef6
    this.vdef7 = this.$route.params.vdef7

    this.manage1 = this.$route.params.manage1
    this.manage2 = this.$route.params.manage2
    this.manage3 = this.$route.params.manage3
    this.manage4 = this.$route.params.manage4
    this.manage5 = this.$route.params.manage5

    this.$nextTick(() => {
      this.radarChart = echarts.init(document.getElementById('radarChart'))
      this.radarChart.setOption(
      {
        title: {
          text: ''
        },
        radar: {
          indicator: [
            { name: '可靠性', max: 5 },
            { name: '亲和力', max: 5 },
            { name: '自我程度', max: 5 },
            { name: '担当', max: 5 },
            { name: '工作投入程度', max: 5 },
            { name: '成就动机', max: 5 },
            { name: '稳定', max: 5 },
          ]
        },
        series: [
          {
            name: '人员评测',
            type: 'radar',
            data: [
              {
                value: [this.vdef1 * 1 || 0, this.vdef2 * 1 || 0, this.vdef3 * 1 || 0, this.vdef4 * 1 || 0, this.vdef5 * 1 || 0, this.vdef6 * 1 || 0, this.vdef7 * 1 || 0],
                name: '人员评测',
                label: {
                  show: true,
                  formatter: function (params) {
                    return params.value;
                  }
                }
              },
            ]
          }]
      })
      this.manageChart = echarts.init(document.getElementById('manageChart'))
      this.manageChart.setOption(
      {
        title: {
          text: ''
        },
        radar: {
          indicator: [
            { name: '制定目标', max: 5 },
            { name: '组织工作', max: 5 },
            { name: '沟通交流', max: 5 },
            { name: '绩效评估', max: 5 },
            { name: '人员培养', max: 5 },
          ]
        },
        series: [
          {
            name: '管理者五项测评',
            type: 'radar',
            data: [
            {
              value: [this.manage1 * 1||0, this.manage2 * 1||0, this.manage3 * 1||0, this.manage4 * 1||0, this.manage5 * 1||0],
              name: '管理者五项测评',
              label: {
                show: true,
                formatter: function (params) {
                  return params.value;
                }
              }
            },
            ]
          }
        ]
      })
    })
    
  },
  methods: {
    reback() {
      this.$router.go(-1);
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      }
      return val;
    },
    onGxConfirm(value){
      this.info.jjlx.gx=value;
      this.showGxPicker = false;
    },
    onJGxConfirm(value){
      this.info.jsr.gx=value;
      this.showJGxPicker = false;
    },
    onXxjlBConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl.bdate=yy+"年"+mm+"月";
      this.showBDatePicker = false;
    },
    onXxjlEConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.xxjl.edate=yy+"年"+mm+"月";
      this.showDDatePicker = false;
    },
    onGzDDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.edate=yy+"年"+mm+"月";
      this.showGzDDatePicker = false;
    },
    onGzBDateConfirm(value){
      let yy = new Date(value).getFullYear();
      let mm = new Date(value).getMonth()+1;
      this.info.gzjl.bdate=yy+"年"+mm+"月";
      this.showGzBDatePicker = false;
    },
    onConfirm(value){
      this.info.xueli=value;
      this.showPicker=false;
    }
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.jc {
  margin: 0;
  color: rgba(69, 90, 100, 0.6);
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  padding-left: 4%;
  padding-top: 2%;
  padding-bottom: 2%;
  background: #f0f0f0;
}
h1, h2 {
  font-weight: normal;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
