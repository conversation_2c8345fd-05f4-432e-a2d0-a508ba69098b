<template>
    <div style="text-align: left; background-color: #f1f1f1">
        <van-sticky :offset-top="0">
            <van-nav-bar title="包材退料"  left-arrow 
                @click-left="onClickLeft" />
        </van-sticky>
        <div style="width: 100%; height: 100%; overflow: auto">
            <div v-for="(item, index) in dataArr" :key="index" style="
            text-align: left;
            background-color: #fff;
            padding: 3%;
            border-radius: 10px;
            width: 95%;
            margin: 0.3rem auto;
            margin-bottom: 3%;
          ">
                <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                    <van-row>
                        <van-col span="20">
                            <span style="font-size: 18px; font-weight: 700; color: #000">
                                {{ item.productName }}
                            </span>
                        </van-col>
                        <van-col span="4">
                            <span v-if="item.status == 2" style="color: #66cc00">
                                已校验
                            </span>
                            <span v-else style="color: #ff0033"> 未校验 </span>
                        </van-col>
                    </van-row>
                </div>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> ID：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.id }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <!-- <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 账套：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.bookName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 仓库：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.storeName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row> -->
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 编码：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.productNo }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6">货位：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.rackName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="8"> 客户批次号：</van-col>
                            <van-col span="16">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.customerBatchCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="8"> NCC批次号：</van-col>
                            <van-col span="16">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.nccBatchCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 供应商：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.supplier }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 过期时间：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.expiryDay }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <!-- <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 生产日期：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.workDay }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 库存状态：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    <span style="color:gray" v-if='item.stockStatus == 1'>待检</span>
                                    <span style="color:green" v-if='item.stockStatus == 2'>合格</span>
                                    <span style="color:red" v-if='item.stockStatus == 3'>不合格</span>
                                    <span style="color:gray" v-if='item.stockStatus == 4'>跟踪质检</span>
                                    <span style="color:gray" v-if='item.stockStatus == 5'>返工</span>
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 备注：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.remark }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row> -->
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 数量：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.mainQuantity }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row v-if="item.status==1">
                    <van-col span="20"></van-col>
                    <van-col span="4"><van-button type="danger" @click="checkRequest(item)"  size="small">撤回</van-button></van-col>
                </van-row>
            </div>
        </div>
    </div>
</template>

<script>
import { DatetimePicker, Indicator, MessageBox } from "mint-ui";
import { Toast } from "vant";
export default {
    data() {
        return {
            // 配料单ID
            id: "",
            userCode: localStorage.getItem("userCode"),
            dataArr: [],
            // 共有
            allNum: 0,
            // 剩余
            residueNum: 0,
            matCheckFlag: 0,
        };
    },
    created() {
        this.matCheckFlag = localStorage.getItem("matCheckFlag");
        this.id = this.$route.params.id;
        if (this.$route.params) {
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle",
            });
            this.$axios
                .get(
                    `/jeecg-boot/app/sync/getQiMoResetList?tpId=${this.$route.params.id}`
                )
                .then((res) => {
                    if (res.data.code == 200) {
                        this.dataArr = res.data.result;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000,
                        });
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        } else {
            this.$route.go(-1);
        }
    },

    methods: {
        onClickLeft() {
            this.$router.go(-1);
        },
        // 校验请求
        checkRequest(item) {
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle",
            });
            this.$axios
                .get(`/jeecg-boot/app/sync/editUsageTemp?id=${item.id}&userCode=${localStorage.getItem("userCode")}&userName=${localStorage.getItem("userName")}&status=0`)
                .then((res) => {
                    if (res.data.code == 200) {
                        // 校验成功刷新列表
                        Indicator.open({
                            text: "正在加载中，请稍后……",
                            spinnerType: "fading-circle",
                        });
                        this.$axios
                            .get(`/jeecg-boot/app/sync/getQiMoResetList?tpId=${this.$route.params.id}`)
                            .then((res) => {
                                if (res.data.code == 200) {
                                    console.log(res.data.result);
                                    this.dataArr = res.data.result;
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000,
                                    });
                                }
                            })
                            .finally(() => {
                                Indicator.close();
                            });
                    } else {
                        Toast.fail(res.data.message);
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        },
    },
};
</script>

<style scoped></style>