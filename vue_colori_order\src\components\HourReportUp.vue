<template>
    <div>
        <div style="margin:5%;color:red;font-weight:600;text-align:left;">托码报工请扫码修改产量，其他报工点击推送MES007进行修改</div>
        <!-- <table width="90%" style="margin:5%">
            <tr style="height:40px;font-size:18px">
                <th>提报类型</th>
                <th>提报时间</th>
                <th>提报数量({{item.mainUnit}})</th>
            </tr>
            <tr v-for="(item,index) in record" :key="index" style="height:60px" @click="edit(item)">
                <td>
                    {{item.type}}
                </td>
                <td>
                    {{item.createTime}}
                </td>
                <td>
                    {{item.output}}
                </td>
            </tr>

        </table> -->

        <van-button type="info" @click="scanUpdate" style="width:70%;">扫码修改</van-button>


        <div style="margin:5%;" v-for="(items, index) in record" :key="index" @click="edit(items)">
            <div style="width:100%;position: relative;">
                <div style="float:left;width:50%;font-size:18px;font-weight:600;text-align:left;display: inline-block;bottom: 0;">
                    {{ items.mainId }}
                </div>
                <div style="float:left;width:50%;color:#808080;font-size:14px;text-align:right;display: inline-block;bottom: 0;">
                    {{ items.createTime }}
                </div>
                <div style="clear:both;"></div>
            </div>
            <div style="height:1px;width:100%;background:#5f5f5f;"></div>
            <div style="width:100%;margin-top:2%">
            <div style="float:left;width:75%;color:#808080;font-size:16px;text-align:left;">
                {{ items.type }} | {{ items.customer }}
            </div>
                
                <div style="float:left;width:25%;color:#808080;font-size:16px;text-align:right;">
                    {{ items.output }} {{item.mainUnit}}
                </div>
            </div>
            <div style="clear:both;"></div>
        </div>
        
        <audit-modal ref="modalForm" @ok="modalFormOk"></audit-modal>
        <post-reocrd-modal-up ref="modalRForm" @ok="modalFormOk"></post-reocrd-modal-up>
            
    </div>
</template>

<script>
import { DatetimePicker,Toast,MessageBox,Indicator} from 'mint-ui';
import AuditModal from './list/AuditModal.vue';
import PostReocrdModal from './list/PostReocrdModal.vue';
import PostReocrdModalUp from './list/PostReocrdModalUp.vue';
let wx=window.wx
export default {
    components:{
        AuditModal,
        PostReocrdModal,
        PostReocrdModalUp
    },
    data(){
        return{
            item:[],
            record:[],
            xl:''
        }
    },
    created:function(){
        let self=this;
        self.item=self.$route.params.item
        self.getRecord();
    },
    methods:{
        getRecord(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getOutputInfo',{params:{lpId:self.item.lpId}}).then(res=>{
                if(res.data.code==200){
                    self.record=res.data.result
                }
            })
        },
        scanUpdate(){
            let self=this
            var userCode=localStorage.getItem('userCode');

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$router.push({name:"ScanResult",params:{result:result,type:'2'}})
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
        },
        getCurrentPresent(output){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getCurrentPresent',{params:{lpId:self.item.id,output:output}}).then(res=>{
                if(res.data.code==200){
                    self.xl=res.data.message
                }
            })
        },
        add2(){
            let self=this
            this.$refs.modalForm.edit(self.item.id,self.item.mainUnit);
            this.$refs.modalForm.title="质检取样";
            this.$refs.modalForm.disableSubmit = true;
        },
        modalFormOk(){
            let self=this;
            self.getRecord();
            self.getCurrentPresent("0");
        },
        edit(item){
            let self=this
            console.log(item);
            if(self.item.workType=='4'){
                this.$refs.modalRForm.update(item,self.item);
                this.$refs.modalRForm.title="胶体产量修改";
                this.$refs.modalRForm.disableSubmit = true;
                // MessageBox.prompt('请输入胶体产量('+self.item.unit+')').then(({ value, action }) => {
                //     if(action=="confirm"){
                //         let num=parseFloat(value)
                //         MessageBox.confirm('请确认是否修改提报产量为'+num+self.item.unit+'?').then(action => {
                //             if(action=="confirm"){
                //                 self.editLeadOutput(num,item);
                //             }
                //         });
                //     }
                // });
            }else if(self.item.workType=='1'){

                if(item.mainId.indexOf('T')!=-1){
                    Toast({
                        message: '请扫码修改产量',
                        position: 'bottom',
                        duration: 2000
                    });
                }else{
                    this.$refs.modalRForm.update(item,self.item);
                    this.$refs.modalRForm.title="灌包产量修改";
                    this.$refs.modalRForm.disableSubmit = true;
                }

                

                // MessageBox.prompt('请输入灌包产量('+self.item.unit+')').then(({ value, action }) => {
                //     if(action=="confirm"){
                //         let num=parseInt(value)
                //         MessageBox.confirm('请确认是否修改提报产量为'+num+self.item.unit+'?').then(action => {
                //             if(action=="confirm"){
                //                 self.editLeadOutput(num,item);
                //             }
                //         });
                //     }
                // });
            }
        },
        add(){
            let self=this
            if(self.item.workType=='4'){
                // MessageBox.prompt('请输入胶体产量('+self.item.unit+')').then(({ value, action }) => {
                //     if(action=="confirm"){
                //         try{
                //             let num=parseFloat(value)
                //             MessageBox.confirm('请确认是否提报产量为'+num+self.item.unit+'?').then(action => {
                //                 if(action=="confirm"){
                //                     self.addLeadOutput(num);
                //                 }
                //             });
                //         }catch(e){
                //             Toast({
                //                 message: e,
                //                 position: 'bottom',
                //                 duration: 2000
                //             });
                //         }
                        
                //     }
                // });
                this.$refs.modalRForm.edit(self.item.id,self.item);
                this.$refs.modalRForm.title="胶体产量录入";
                this.$refs.modalRForm.disableSubmit = true;

            }else if(self.item.workType=='1'){

                this.$refs.modalRForm.edit(self.item.id,self.item);
                this.$refs.modalRForm.title="灌包产量录入";
                this.$refs.modalRForm.disableSubmit = true;

                // MessageBox.prompt('请输入灌包产量('+self.item.unit+')').then(({ value, action }) => {
                //     if(action=="confirm"){
                //         try{
                //             let num=parseInt(value)
                //             MessageBox.confirm('请确认是否提报产量为'+num+self.item.unit+'?').then(action => {
                //                 if(action=="confirm"){
                //                     self.addLeadOutput(num);
                //                 }
                //             });
                //         }catch(e){
                //             Toast({
                //                 message: e,
                //                 position: 'bottom',
                //                 duration: 2000
                //             });
                //         }
                        
                //     }
                // });
            }
        },
        checkTime(item,workType){
            let self=this
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcWorkshop/checkTime',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    //1可以新增，0可以修改
                    let t=res.data.message
                    if(t=='1' && workType=='add'){
                        self.add();
                    }else if(t=='1' && workType=='add2'){
                        self.add2();
                    }else if(t=='0' && workType=='edit'){
                        self.edit(item);
                    }else{
                        if(workType=='add'){
                            Toast({
                                message: '未过5分钟，无法新增',
                                position: 'bottom',
                                duration: 2000
                            });
                        }else if(workType=='add2'){
                            Toast({
                                message: '未过5分钟，无法新增',
                                position: 'bottom',
                                duration: 2000
                            });
                        }else{
                            Toast({
                                message: '5分钟已过，无法修改',
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    }
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        addLeadOutput(num){
            let self=this
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let params={
                lpId:self.item.id,
                output:num,
                creator:localStorage.getItem('userCode'),
                type:'1'
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/addLeadOutput',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: '提报成功！',
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getRecord();
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        editLeadOutput(num,item){
            let self=this
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            item.output=num
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changeOutput',item).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: '提报成功！',
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getRecord();
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        }
    }
}
</script>
<style scoped>


</style>