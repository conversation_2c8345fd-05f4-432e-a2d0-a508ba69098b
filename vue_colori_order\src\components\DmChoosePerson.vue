<template>
    <div class="order">
        <van-nav-bar
            title="宿舍-选择人员"
            left-text="返回"
            right-text="筛选"
            left-arrow
            @click-left="onClickLeft"
            @click-right="onClickRight"
        />


        <van-popup v-model="show" position="top" :style="{ height: '35%' }">
            <van-nav-bar
                title="查询条件"
                
            />
            <van-field
                v-model="queryParam.userCode"
                clearable
                label="员工编号"
                placeholder="请输入员工编号"
            />
            <van-field
                v-model="queryParam.userName"
                clearable
                label="员工姓名"
                placeholder="请输入员工姓名"
            />
            <van-field
                v-model="queryParam.paperNum"
                clearable
                label="身份证号"
                placeholder="请输入身份证号"
            />
            
            <van-row  gutter="8" style="margin-top:4px;">
                <van-col span="12">
                    <van-button @click="resentQuery" style="width: 80%;">
                        重置
                    </van-button>
                </van-col>
                <van-col span="12">
                    <van-button type="info" @click="searchQuery" style="width: 80%;">
                        查询
                    </van-button>
                </van-col>
            </van-row>
            
        </van-popup>
        <!-- <van-row gutter="20">
            
        </van-row> -->
        <van-divider content-position="left" margin="12px 0 20px">选中人员</van-divider>
        <!-- <van-grid :column-num="2">
            <van-grid-item v-for="(item,index) in showPerson" :key="index">
                <van-button size="small" plain type="primary" style="width:100%;border:1px solid red;">{{item.code}}{{item.name}}</van-button>
                
            </van-grid-item>
        </van-grid> -->

        <van-row>
            <van-col span="8" v-for="(item,index) in showPerson" :key="index">
                <van-button plain type="primary" @click="cancelPerson(item,index)">
                    {{item.userCode}} {{item.userName}}
                    <!-- <van-icon name="cross" /> -->
                </van-button>
            </van-col>
        </van-row>
        <van-button type="primary" @click="submit()"  style="margin:5px;width:40%;border-radius:10px;">提交</van-button>

        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            >
            
            

            <van-cell v-for="item,index in listData"
                :key="index" 
                :title="item.userCode+' '+item.userName"  clickable @click="radio = 1">

                <van-checkbox slot="right-icon" v-model="item.checked" @change="changeCheck(item,index)"></van-checkbox>
            </van-cell>
            <!-- <van-cell
                v-for="item,index in listData"
                :key="index"
            >
                {{item.userCode}} {{item.userName}}
            </van-cell> -->
        </van-list>

        

        

        
        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            queryParam:{
                userCode:null,
                userName:null,
                liveStatus:'all',
                pageNo:1,
                pageSize:10,
                total:0,
            },


            showAreaPicker:false,
            showBuildPicker:false,
            showRoomPicker:false,
            areaColumns: [],
            areaResColumns: [],
            buildColumns: [],
            buildResColumns: [],
            roomColumns: [],
            roomResColumns: [],
            area:'',
            build:'',
            room:'',
            showTypePicker:false,
            typeColumns:['立刻','定时'],

            listData: [],
            show:false,
            loading: false,
            finished: false,

            showPerson:[],
            checked:false
        }
    },
    created:function(){
        let that = this;
        
        
        that.$axios.get('/dormApi/stay/getUserInfoList',{params:that.queryParam}).then(res=>{
            console.log('res:',res);
            if(res.data.success){
                that.queryParam.total = res.data.result.total;
                for(let i = 0; i < res.data.result.records.length; i++) {
                    
                    that.listData.push(res.data.result.records[i])
                    
                }
                console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.listData)
            }
        })
    },
    methods:{
        onClickLeft() {
            this.$router.go(-1);
        },
        onClickRight() {
            this.show = true;
        },
        onLoad() {
            // 异步更新数据
            let that = this;
            setTimeout(() => {
                that.queryParam.pageNo = that.queryParam.pageNo + 1;
                that.$axios.get('/dormApi/stay/getUserInfoList',{params:that.queryParam}).then(res=>{
                    console.log('res:',res);
                    if(res.data.code==200){
                        that.queryParam.total = res.data.result.total;
                        for(let i = 0; i < res.data.result.length; i++) {
                            
                            that.listData.push(res.data.result[i])
                        }
                    }
                })
                
                // 加载状态结束
                this.loading = false;

                // 数据全部加载完成
                if (this.listData.length >= that.queryParam.total) {
                    this.finished = true;
                }
            }, 500);
        },
        resentQuery() {
            let that = this;
            
            that.queryParam.userCode = null;
            that.queryParam.userName = null;
            that.searchQuery();
            
        },
        searchQuery() {
            let that = this;

            that.queryParam.pageNo = 1;
            that.listData = [];
            that.$axios.get('/dormApi/stay/getUserInfoList',{params:that.queryParam}).then(res=>{
                console.log('res:',res);
                if(res.data.success){
                    that.queryParam.total = res.data.result.total;
                    for(let i = 0; i < res.data.result.records.length; i++) {
                        
                        that.listData.push(res.data.result.records[i])
                        
                    }
                    that.show = false;
                    console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.listData)
                }
            })
        },
        changeCheck(item,index) {
            console.log("🚀 ~ file: DmChoosePerson.vue:193 ~ changeCheck ~ item,index:", item,index)
            if(item.checked == true) {

                let site = -1;
                for(let i = 0; i < this.showPerson.length; i++) {
                    if(this.showPerson[i].paperNum == item.paperNum) {
                        site = i;
                        break;
                    }
                }
                if(site == -1) {
                    this.showPerson.push(item);
                }
            } else {
                let site = -1;
                for(let i = 0; i < this.showPerson.length; i++) {
                    if(this.showPerson[i].paperNum == item.paperNum) {
                        site = i;
                        break;
                    }
                }
                if(site != -1) {
                    this.showPerson.splice(site,1)
                }
            }
        },
        cancelPerson(item,index) {
            
            for(let i = 0; i < this.listData.length; i++) {
                if(this.listData[i].paperNum == item.paperNum) {
                    this.listData[i].checked = false;
                    break;
                }
            }
            this.showPerson.splice(index,1);
        },
        submit(){
            let self=this

            if(self.showPerson.length == 0) {
                Toast({
                    message: "至少选择一人！",
                    position: 'bottom',
                    duration: 1500
                });
                return;
            }

            // let params={
            //     userCode:localStorage.getItem('userCode'),
            //     oldPwd:self.info.oldPwd,
            //     newPwd:self.info.newPwd
            // }


            self.$router.push({name:"DmNotify",params:{choosePerson:self.showPerson}})

            // self.$axios.post('/jeecg-boot/app/user/updatePwd',params).then(res=>{
            //     if(res.data.code==200){
            //         Toast({
            //             message: res.data.message,
            //             position: 'bottom',
            //             duration: 5000
            //         });
            //         self.$router.go(-1);
            //     }else{
            //         Toast({
            //             message: res.data.message,
            //             position: 'bottom',
            //             duration: 5000
            //         });
            //     }
            // })


        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>

</style>