<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">外箱识别码</div>
        <van-field label="数字代码"  v-model="icId"/>
        <van-field label="产品编码"  v-model="code"/>
        
        <van-button type="info" @click="searchIcId()" :loading="loading" loading-type="spinner" style="margin:10px;width:40%;border-radius:10px;">查询</van-button>

        <div style="width: 100%;display:flex;margin-top: 20px;justify-content: center;background: #fff;padding: 10px;">
        
            <div v-for="(item,index) in items" :key="index">
                <div class="boxOut" style="margin-left: 45px;" v-if="item==2">
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                    <div class="boxInner"></div>
                </div>
                <div style="width: 4.5px;border-left: 4.5px solid #000;height: 90px;float: left;margin-left: 45px;" v-if="item==0">
                </div>
                <div style="width: 24px;border-left: 24px solid #000;height: 90px;float: left;margin-left: 45px;" v-if="item==1">
                </div>
            </div>

        </div>

    </div>
</template>
<script>
import { Indicator,MessageBox   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            icId:"",
            code:"",
            items:[]
        }
    },
    created:function(){
        
    },
    methods:{
        searchIcId(){
            let self = this
            self.$axios.get('/jeecg-boot/app/user/showIdentificationCode', { params: { id: self.icId,code:self.code } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.items=res.data.result;
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.boxOut{
    width: 24px;
    height: 90px;
    background: #000;
    margin: 0 auto;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 9px 0;
}
.boxInner{
    width: 25px;
    height: 6px;
    background: #FFF;
    margin-top: 6px;
    transform: skewY(-30deg);
}
</style>