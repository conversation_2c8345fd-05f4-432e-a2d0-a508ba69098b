<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div style="padding:10px;text-align:left;background:white">
            <div style="font-weight:800;margin-bottom:10px;font-size:16px;">{{spRecord[0].spNo}}</div>
            <van-col span="10">
                <span>申&nbsp;&nbsp;请&nbsp;&nbsp;人：</span><span>{{spRecord[0].applyUser}}</span>
            </van-col>
            <van-col span="14">
                <span>申&nbsp;&nbsp;请&nbsp;&nbsp;&nbsp;时&nbsp;&nbsp;间：{{spRecord[0].createTime}}</span>
            </van-col>
            <van-col span="10">
                <span>申请状态：</span>
                <span v-if="spRecord[0].spStatus=='1'">审批中</span>
                <span v-if="spRecord[0].spStatus=='2'">已同意</span>
                <span v-if="spRecord[0].spStatus=='3'">已驳回</span>
                <span v-if="spRecord[0].spStatus=='4'">已撤销</span>
                <span v-if="spRecord[0].spStatus=='6'">通过后撤销</span>
                <span v-if="spRecord[0].spStatus=='7'">已删除</span>
                <span v-if="spRecord[0].spStatus=='10'">已支付</span>
            </van-col>
            <van-col span="14">
                <span>最后处理时间：{{spRecord[0].lastTime}}</span>
            </van-col>
            <van-col span="10">
                <span>流转时长：</span>
                <span>{{spRecord[0].roamDuration}}H</span>
            </van-col>
            <van-col span="14">
                <span>节点滞留时长：{{spRecord[0].stayDuration}}H</span>
            </van-col>
            <div style="clear:both"></div>
        </div>
        <div v-for="(item,index) in spRecordDetail" :key="index" style="padding:8px;text-align:left;background:white;margin-top:6px;">
            <van-row>
                <van-col span="14">
                    <span style="font-weight:800;font-size:18px;float:left;width:80%">{{item.nodeName}}</span>
                </van-col>
                <van-col span="10" style="text-align:right;">
                    <span style="font-weight:800;font-size:18px;color:orange;" v-if="item.spStatus=='1'">审批中</span>
                    <span style="font-weight:800;font-size:18px;color:green;" v-else-if="item.spStatus=='2'">已同意</span>
                    <span style="font-weight:800;font-size:18px;color:red;" v-else-if="item.spStatus=='3'">已驳回</span>
                    <span style="font-weight:800;font-size:18px;color:blue;" v-else-if="item.spStatus=='4'">已转审</span>
                    <span style="font-weight:800;font-size:18px;color:IndianRed;" v-else-if="item.spStatus=='11'">已退回</span>
                    <span style="font-weight:800;font-size:18px;color:DarkViolet;" v-else-if="item.spStatus=='12'">已加签</span>
                    <span style="font-weight:800;font-size:18px;color:DarkOliveGreen;" v-else-if="item.spStatus=='13'">已同意并加签</span>
                </van-col>
            </van-row>
            <div style="width:100%;height:1px;background:#333"></div>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">审批人：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.approver}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">审批时间</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.spTime}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">审批意见：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;" v-if="item.speech!=''">{{item.speech}}</span>
                    <span style="font-weight:800;font-size:16px;" v-else>无</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">审批时长：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;" v-if="item.nodeTime!='-'">{{item.nodeTime}}H</span>
                    <span style="font-weight:800;font-size:16px;" v-else>无</span>
                </van-col>
            </van-row>
        </div>
    </div>
</template>
<script>
import { Toast,MessageBox  } from 'mint-ui';
export default {
    data(){
        return{
            spNo:"",
            spRecord:[],
            spRecordDetail:[]
        }
    },
    created:function(){
        this.spNo = this.$route.query.spNo
        this.getSpRecordBySpNo()
    },
    methods: {
        getSpRecordBySpNo(){
            let self = this;
            self.$axios.get('/jeecg-boot/process/ncProcessModel/getSpRecordBySpNo',{params:{spNo:self.spNo}}).then(res=>{
                if(res.data.code==200){
                    self.spRecord = res.data.result
                    self.getSpRecordDetail(self.spRecord[0].createTime);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getSpRecordDetail(createTime){
            let self = this;
            self.$axios.get('/jeecg-boot/process/ncProcessModel/getNodeProcessInfo',{params:{spNo:self.spNo,createTime:createTime}}).then(res=>{
                if(res.data.code==200){
                    self.spRecordDetail = res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 22rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.ycl-style{
    color: crimson;
}
</style>