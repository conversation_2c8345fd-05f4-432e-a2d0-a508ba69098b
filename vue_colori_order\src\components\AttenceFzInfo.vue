<template>
  <div style="text-align:left;padding-bottom:0.1rem;">
    <van-sticky :offset-top="0">
      <van-nav-bar
        :title="title"
        right-text="筛选"
        left-arrow
        @click-left="onClickLeft"
        @click-right="onClickRight"
      />
    </van-sticky>
    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
      <van-field
        v-model="filter.llqUserCode"
        clearable
        label="员工编号："
        placeholder="请输入员工编号"
      />
      <van-field
        v-model="filter.staffName"
        clearable
        label="员工姓名："
        placeholder="请输入员工姓名"
      />
      <!-- <van-field
        readonly
        clickable
        name="picker"
        v-model="statusText"
        label="状态："
        placeholder="点击选择状态"
        @click="statusPicker = true"
      />
      <van-popup v-model="statusPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="statusColumns"
          @confirm="statusConfirm"
          @cancel="statusPicker = false"
        />
      </van-popup> -->

      <van-button type="info" @click="search" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>


    <van-tabs v-model="active" sticky animated swipeable>
      <van-tab :title="schedued">

        <van-row style="padding:9px;">
          <van-col span="8" style="padding:5px;text-align:center;" :class="clickNum==0?'clicked_style':'unclicked_style'" @click="handleClick(0)">全部已打卡({{parseInt(this.pushInfo.allNum)-parseInt(this.pushInfo.errorNum)}})</van-col>
          <van-col span="8" style="padding:5px;text-align:center;" :class="clickNum==1?'clicked_style':'unclicked_style'" @click="handleClick(1)">迟到({{pushInfo.lateNum}})</van-col>
          <van-col span="8" style="padding:5px;text-align:center;" :class="clickNum==2?'clicked_style':'unclicked_style'" @click="handleClick(2)">出差({{pushInfo.triveNum}})</van-col>
          <van-col span="8" style="padding:5px;text-align:center;" :class="clickNum==3?'clicked_style':'unclicked_style'" @click="handleClick(3)">请假({{pushInfo.leveNum}})</van-col>
          <van-col span="8" style="padding:5px;text-align:center;" :class="clickNum==4?'clicked_style':'unclicked_style'" @click="handleClick(4)">正常({{pushInfo.normalNum}})</van-col>
        </van-row>


        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            offset="100"
            error-text="请求失败，点击重新加载"
          >
            <div
              v-for="(item, index) in dataArr"
              :key="index"
              style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
            >
              <van-row>
                <van-col span="14">
                  <span style="color:gary;"
                    >员工编号：
                    <span style="color:black">{{ item.llqUserCode }}</span></span
                  >
                </van-col>
                <van-col span="10">
                    <span style="color:white;display:flex;justify-content:end;">
                      <span style="background:orange;padding:3px 15px;border-radius:8px;" v-if="item.lateFlag=='1'">迟到</span>
                      <span style="background:steelblue;padding:3px 15px;border-radius:8px;" v-else-if="item.triveFlag=='1'">出差</span>
                      <span style="background:blue;padding:3px 15px;border-radius:8px;" v-else-if="item.leveFlag=='1'">请假</span>
                      <span style="background:red;padding:3px 15px;border-radius:8px;" v-else-if="item.errorFlag=='1'">漏卡</span>
                      <span style="background:green;padding:3px 15px;border-radius:8px;" v-else>正常</span>
                    </span>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24">
                  <span style="color:gary;"
                    >员工姓名：
                    <span style="color:black">{{ item.staffName }}</span></span
                  >
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24">
                  <span style="color:gary;"
                    >上班打卡时间：
                    <span style="color:black">{{ item.checkTime==null?'无':item.checkTime }}</span></span
                  >
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24">
                  <span style="color:gary;"
                    >下班打卡时间：
                    <span style="color:black">{{ item.maxCheckTime==null?'无':item.maxCheckTime }}</span></span
                  >
                </van-col>
              </van-row>
            </div>
          </van-list>
        </van-pull-refresh>    
      </van-tab>
      <van-tab :title="errorNum">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            offset="100"
            error-text="请求失败，点击重新加载"
          >
            <div
              v-for="(item, index) in dataArr"
              :key="index"
              style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
            >
              <van-row>
                <van-col span="14">
                  <span style="color:gary;"
                    >员工编号：
                    <span style="color:black">{{ item.llqUserCode }}</span></span
                  >
                </van-col>
                <van-col span="10">
                    <span style="color:white;display:flex;justify-content:end;">
                      <span style="background:orange;padding:3px 15px;border-radius:8px;" v-if="item.lateFlag=='1'">迟到</span>
                      <span style="background:steelblue;padding:3px 15px;border-radius:8px;" v-else-if="item.triveFlag=='1'">出差</span>
                      <span style="background:blue;padding:3px 15px;border-radius:8px;" v-else-if="item.leveFlag=='1'">请假</span>
                      <span style="background:red;padding:3px 15px;border-radius:8px;" v-else-if="item.errorFlag=='1'">漏卡</span>
                      <span style="background:green;padding:3px 15px;border-radius:8px;" v-else>正常</span>
                    </span>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="12">
                  <span style="color:gary;"
                    >员工姓名：
                    <span style="color:black">{{ item.staffName }}</span></span
                  >
                </van-col>
                <van-col span="12">
                  <span style="color:gary;"
                    >归属部门：
                    <span style="color:black">{{ item.departmentName }}</span></span
                  >
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24">
                  <span style="color:gary;"
                    >打卡时间：
                    <span style="color:black">{{ item.checkInTime==null?'无':item.checkInTime }}</span></span
                  >
                </van-col>
              </van-row>
            </div>
          </van-list>
        </van-pull-refresh>    
      </van-tab>
    </van-tabs>


    
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import pdf from "vue-pdf";
export default {
  components: {
    pdf
  },
  data() {
    return {
      filter: {
        llqUserCode: "", //产品编号
        staffName: "", //产品名称
        type: "", //产品名称
        workDay: "", //产品名称
        lateFlag: "", //产品名称
        triveFlag:"",
        leveFlag:"",
        errorFlag:"",
        departmentName: "" //状态
      },
      active:0,
      statusText: "", //状态
      show: false,
      statusPicker: false, //状态选择弹窗
      statusColumns: ["生效", "新增待审批", "编辑待审批", "审批中"],
      pushInfo:{},
      dataArr: [],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      clickNum:0,
      total: 0,
      schedued:"",
      errorNum:"",
      title:"",
      //PDF
      pdfShow: false,
      pageNum: 1,
      pageTotalNum: 1,
      rotate: 0,
      rotateAdd: 90,
      scale: 100, //放大缩小
      fileName: ""
    };
  },
  watch:{
    // van-tabs 组件 切换tabs时，vant 是用van-tabs__track 下的transform属性实现的这就导致，下级组件display:fixed被降级为absolute
    active:{
      immediate:true,
      handler:function(val){
        console.log('第几个',val)
        if(val == 0){
          this.handleClick(0)
        }else{
          this.handleClick(5)
        }
      }
    },
  },
  async created() {
    this.filter.type = this.$route.query.type
    this.filter.workDay = this.$route.query.workDay
    this.filter.errorFlag = "0"
    if(this.filter.type==1){
      this.title = "克劳丽四级及以上人员出勤情况";
    }else{
      this.title = "克劳丽管理人员出勤情况";
    }
  },
  methods: {
    zoomIn() {
      this.scale += 50;
      const itemEl = this.$refs["pdfRef"].$el;
      itemEl.style.width = parseInt(this.scale) + "%";
    },
    zoomOut() {
      this.scale -= 50;
      const itemEl = this.$refs["pdfRef"].$el;
      itemEl.style.width = parseInt(this.scale) + "%";
    },
    pdfPopupShow(fileName) {
      this.fileName = fileName;
      this.pdfShow = true;
    },
    pdfPopupClose() {
      console.log("popup关闭");
      this.pageNum = 1;
      this.pageTotalNum = 1;
      this.rotate = 0;
      this.rotateAdd = 90;
      this.pdfShow = false;
      this.scale = 100;
    },
    clear(){
      this.filter.lateFlag = ""
      this.filter.triveFlag = ""
      this.filter.leveFlag = ""
      this.filter.errorFlag = ""
      this.finished = false;
      this.pageNo = 1
    },
    handleClick(num){
      this.clear();
      this.clickNum = num;
      if(this.filter.type==null || this.filter.type==''){
        return;
      }
      if(num==0){
        this.filter.errorFlag = '0'
        this.loading = true;
        this.search();
      }else if(num==1){
        this.filter.lateFlag = '1'
        this.loading = true;
        this.search();
      }else if(num==2){
        this.filter.triveFlag = '1'
        this.loading = true;
        this.search();
      }else if(num==3){
        this.filter.leveFlag = '1'
        this.loading = true;
        this.search();
      }else if(num==4){
        this.filter.lateFlag = '0'
        this.filter.triveFlag = '0'
        this.filter.leveFlag = '0'
        this.filter.errorFlag = '0'
        this.loading = true;
        this.search();
      }else if(num==5){
        this.filter.errorFlag = '1'
        this.loading = true;
        this.search();
      }
    },
    getList() {
      let quest = "";
      for (let prop in this.filter) {
        if (this.filter[prop] != "") {
          quest += `&${prop}=*${this.filter[prop]}*`;
        }
      }
      this.$axios
        .get(
          `/jeecg-boot/data/staffAttenInfo/fzList?pageNo=${this.pageNo}&pageSize=10` +
            quest
        )
        .then(res => {
          if (res.data.code == 200) {
            let len = res.data.result.pageList.records.length;
            if (len == 0) {
              this.dataArr = []; // 清空数组
              this.finished = true; // 停止加载
            }
            this.total = res.data.result.pageList.total;
            this.dataArr.push(...res.data.result.pageList.records);

            this.loading = false;
            if (this.dataArr.length < 10) {
              this.finished = true; // 结束加载状态
            }
            if (this.dataArr.length >= this.total) {
              this.finished = true; // 结束加载状态
            }
            console.log("length:"+this.dataArr.length+",total:"+this.total)
            this.pushInfo = res.data.result.pushInfo
            let normal = parseInt(this.pushInfo.allNum)-parseInt(this.pushInfo.errorNum);
            this.schedued = "已打卡("+normal+")"
            this.errorNum = "应到但未打卡("+this.pushInfo.errorNum+")"
          } else {
            Toast({
              message: res.data.msg,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    onLoad() {
      console.log("onLoad");
      let timer = setTimeout(() => {
        if (this.refreshing) {
          this.dataArr = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        this.getList(); // 调用上面方法,请求数据
        this.pageNo++; // 分页数加一
        this.finished && clearTimeout(timer); //清除计时器
      }, 100);
    },
    onRefresh() {
      console.log("onRefresh")
      this.finished = false; // 清空列表数据
      this.loading = true; // 将 loading 设置为 true，表示处于加载状态
      this.page = 1; // 分页数赋值为1
      this.dataArr = []; // 清空数组
      this.onLoad(); // 重新加载数据
    },
    statusConfirm(value) {
      this.statusText = value;
      switch (value) {
        case "生效":
          this.filter.status = "1";
          break;
        case "新增待审批":
          this.filter.status = "2 ";
          break;
        case "编辑待审批":
          this.filter.status = "3";
          break;
        case "审批中":
          this.filter.status = "9";
          break;
        default:
          this.filter.status = "";
      }
      this.statusPicker = false;
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onClickRight() {
      this.show = true;
    },
    prePage() {
      let p = this.pageNum;
      p = p > 1 ? p - 1 : this.pageTotalNum;
      this.pageNum = p;
    },
    nextPage() {
      let p = this.pageNum;
      p = p < this.pageTotalNum ? p + 1 : 1;
      this.pageNum = p;
    },
    handleRotate(type) {
      switch (type) {
        case 1:
          this.rotate -= this.rotateAdd;
          break;
        case 2:
          this.rotate += this.rotateAdd;
          break;
      }
    },
    pdfPopupClose() {
      console.log("popup关闭");
      this.pageNum = 1;
      this.pageTotalNum = 1;
      this.rotate = 0;
      this.rotateAdd = 90;
    },
    search() {
      let quest = "";
      for (let prop in this.filter) {
        if (this.filter[prop] != "") {
          quest += `&${prop}=*${this.filter[prop]}*`;
        }
      }
      this.$axios.get(`/jeecg-boot/data/staffAttenInfo/fzList?${quest}`).then(res => {
        if ((res.data.code = 200)) {
          this.loading = false;
          this.dataArr = res.data.result.pageList.records;
          this.total = res.data.result.pageList.total;
          this.pushInfo = res.data.result.pushInfo
          console.log("length:"+this.dataArr.length)
          if (this.total<=10) {
            this.finished = true; // 结束加载状态
          }else{
            this.pageNo++
          }
          console.log("finished:"+this.finished)
        } else {
          this.loading = false;
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
      this.show = false;
    }
  }
};
</script>

<style scoped>
.clicked_style{
  background-color: rgb(135, 206, 250,0.5);
  border-radius: 5px;
  color: royalblue;
}
.unclicked_style{
  background-color: #F0F0F0;
  border-radius: 5px;
  color: #333333;
}
</style>
