<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_title">生产管制表</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%" />
            </div>
        </div>
        <div class="sign">
            <img :src="plotTop" width="90%" />
            <div class="plotName">{{ peopleInfo.name }}</div>
            <div class="plotCode">{{ peopleInfo.code }}</div>
            <div class="plotId">{{ itemParams.workshop }}_{{ itemParams.workDay }}</div>
            <div class="plotFactory">{{ itemParams.name }}</div>
            <div class="plotWorkshop"><span
                    :class="item.predoes == '是' ? 'ycl-style' : ''">{{ itemParams.moId }}</span>-{{ itemParams.leaderNo }}-{{ itemParams.mitosome }}
            </div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div>

        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('1')">
                <img src="../../static/images/check.png"
                    style="display:block;margin:0 auto;height:60%;padding-top:10%" />
                <p>开线前检查</p>
            </div>

            <div class="menu_order_item" @click="workMode('2')">
                <img src="../../static/images/photo.png"
                    style="display:block;margin:0 auto;height:60%;padding-top:10%" />
                <p>生产随心拍</p>
            </div>

            <div class="menu_order_item" @click="workMode('3')">
                <img src="../../static/images/scan.png"
                    style="display:block;margin:0 auto;height:60%;padding-top:10%" />
                <p>箱贴信息</p>
            </div>

            <div class="menu_order_item" @click="workMode('4')">
                <img src="../../static/images/close.png"
                    style="display:block;margin:0 auto;height:60%;padding-top:10%" />
                <p>关单</p>
            </div>
        </div>

        <div class="allOpera" @click="seeAllInfo">
            <div class="person_num">
                当前记录数：{{ workDetaiList.length }}条
            </div>

        </div>

        <div v-if="workDetaiList.length > 0">
            <div class="person_item" v-for="(item, index) in workDetaiList" :key="index"
                @click="pushToDetail(item, index)">
                <div style="text-align:left;width:100%;margin-bottom:5px;">
                    <span style="float:left;width:70%">
                        过程巡检记录_{{ index + 1 }}
                    </span>
                    <span style="float:left;width:30%;text-align:right" @click.stop="del(item)">
                        <svg-icon style="color:#f00;font-size:18px;" icon-class="shanchu" />
                    </span>
                    <!-- <van-button type="default" size="mini" round style="margin-left:13rem;" @click.stop="del(item)">
                        
                    </van-button> -->
                    <div style="clear:both;"></div>
                </div>
                <div style="text-align:left;">
                    填写时间：{{ item[0].createTime }}
                </div>
            </div>
        </div>

        <div class="addUser" @click="addRecord">
            <img :src="add" width="10%" />
        </div>

        <reason-modal ref="modalForms" @ok="modalFormOk"></reason-modal>
        <out-modal ref="modalForm" @ok="modalFormOk"></out-modal>
        <close-modal ref="modalFormc" @ok="modalCloseFormOk"></close-modal>
        <EndProductKllModal ref="modalKllForm" @ok="modalFormOk"></EndProductKllModal>
    </div>
</template>
<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from 'mint-ui';
import ReasonModal from './list/ReasonModal.vue';
import OutModal from './list/OutModal.vue';
import CloseModal from './list/CloseModal.vue';
import EndProductKllModal from './EndProductKllModal.vue';
import { Dialog } from 'vant';
let wx = window.wx

export default {
    components: { ReasonModal, OutModal, CloseModal, EndProductKllModal },

    data() {
        return {
            plotTop: require('../../static/images/plat_top.png'),
            message: require('../../static/images/message.png'),
            card: require('../../static/images/card.png'),
            startMite: require('../../static/images/startMite.png'),
            endMite: require('../../static/images/endMite.png'),
            lxendMite: require('../../static/images/lx_endMite.png'),
            finishMo: require('../../static/images/finishMo.png'),
            menuPic: require('../../static/images/menu_pic.png'),
            personLogo: require('../../static/images/user_logo.png'),
            add: require('../../static/images/add.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal: '',
            lastNum: 0,
            show: false,
            value: false,
            menuTrainVis: true,
            menuSelectVis: true,
            reason: '',
            personList: [],
            start: false,
            end: true,
            item: [],
            workDay: '',
            questionType: '',
            questionTypeVal: '',
            peopleInfo: {},
            workDetaiList: [],
            newOrderList: [],
            workOrderList: [],
            errorOrderList: [],
            finishOrderList: [],
            popupVisible: false,
            lastStatus: '0',
            popupSlots: [
                {
                    values: [
                        '白班(上午)', '白班(下午)', '白班(加班)', '晚班(上半夜)', '晚班(下半夜)'
                    ]
                }
            ],
            train: '培训',
            select: '挑拣',
            itemParams: {},
            preCategory: '',
            gld: '请选择',
            worker: ''
        }
    },
    created: function () {
        // this.setWxInfo();
    },
    mounted() {
        let self = this;
        self.itemParams = JSON.parse(localStorage.getItem("controlItem"))
        self.peopleInfo = JSON.parse(localStorage.getItem("peopleInfo"))
        this.getworkInfo(self.itemParams.id)
        this.getMainInfo(self.itemParams.id)
    },
    methods: {
        seeAllInfo() {
            if (this.itemParams.tableId != null && this.itemParams.tableId != '') {
                this.$axios.get('/jeecg-boot/app/appQuality/checkDetail', { params: { id: this.itemParams.id } }).then((res) => {
                    if (res.data.code == 200) {
                        this.itemParams.detailList = res.data.result
                        if (this.itemParams.tableType == '2') {
                            this.$refs.modalKll1Form.edit(this.itemParams);
                            this.$refs.modalKll1Form.title = this.itemParams.tableName;
                            this.$refs.modalKll1Form.disableSubmit = false;
                        } else {
                            this.$refs.modalKllForm.edit(this.itemParams);
                            this.$refs.modalKllForm.title = this.itemParams.tableName;
                            this.$refs.modalKllForm.disableSubmit = false;
                        }
                    }
                })

            } else {
                this.$message.warning("暂无巡检记录，无法查看！")
            }
        },
        del(item) {

            Dialog.confirm({
                title: '提示',
                message: '确认要删除吗?',
            })
                .then(() => {
                    this.$axios.get('/jeecg-boot/app/appQuality/cancelRecords', { params: { id: this.itemParams.id, checkNumber: item[0].checkNumber,userCode:localStorage.getItem('userCode') } }).then(res => {
                if (res.data.code == 200) {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    this.getworkInfo(this.itemParams.id)
                    this.getMainInfo(this.itemParams.id)
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
                })
                .catch(() => {
                    // Toast({
                    //     message: '您点击了取消',
                    //     position: 'bottom',
                    //     duration: 2000
                    // });
                });
        },
        setWxInfo() {
            let self = this
            let url = window.location.href.split("#")[0];
            self.$axios.get('/jeecg-boot/app/wx/getWxInfo', { params: { url: url } })
                .then(res => {
                    wx.config({
                        beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                        debug: false,
                        appId: res.data.result.appId,
                        timestamp: res.data.result.timestamp,
                        nonceStr: res.data.result.noncestr,
                        signature: res.data.result.signature,
                        jsApiList: [
                            'getLocalImgData',
                            'chooseImage',
                            'previewImage',
                            'uploadImage',
                            'downloadImage',
                            'downloadImage',
                            'openBluetoothAdapter',
                            'closeBluetoothAdapter',
                            'getBluetoothAdapterState',
                            'startBluetoothDevicesDiscovery',
                            'onBluetoothDeviceFound',
                            'createBLEConnection',
                            'closeBLEConnection',
                            'getBLEDeviceServices',
                            'getBLEDeviceCharacteristics',
                            'writeBLECharacteristicValue',
                            'onBLECharacteristicValueChange',
                            'scanQRCode'
                        ]
                    });
                })
        },
        getworkInfo(id) {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this;
            self.$axios.get('/jeecg-boot/app/appQuality/getDetailList', { params: { id: id } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.workDetaiList = res.data.result.filter(arr => arr.length > 0);
                    console.log("0:" + self.workDetaiList[0][0].createTime)
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getMainInfo(id) {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this;
            self.$axios.get('/jeecg-boot/app/appQuality/getMainInfo', { params: { id: id } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.itemParams = res.data.result
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        modalCloseFormOk() {

        },
        getPrepareInfo() {
            let self = this;
            // let workDay='2021-07-31'
            let workDay = this.formatDate(new Date())
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getPrepareInfo', { params: { workDay: workDay, lpId: self.item.id } }).then(res => {
                if (res.data.code == 200) {
                    self.gld = res.data.result.laId
                    if (self.gld == null || self.gld == '') {
                        self.gld = "请选择"
                    }
                }
            })
        },
        pushToDetail(item, index) {
            let self = this;
            console.log("item:" + item)
            console.log("item:" + self.itemParams.id)
            self.$router.push({ name: "WorkDuring", params: { item: JSON.stringify(item), type: item.type, itemId: self.itemParams.id, childId: index ,tableType:self.itemParams.tableType} })
        },
        getInfo(item) {
            let self = this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo', { params: item }).then(res => {
                console.log(res)
                if (res.data.code == 200) {
                    self.peopleInfo = res.data.result.peopleInfo
                    self.newOrderList = res.data.result.newOrderList
                    self.workOrderList = res.data.result.workOrderList
                    self.errorOrderList = res.data.result.errorOrderList
                    self.finishOrderList = res.data.result.finishOrderList
                    if (self.newOrderList.length > 0) {
                        self.item = self.newOrderList[0]
                    } else if (self.workOrderList.length > 0) {
                        self.item = self.workOrderList[0]
                    } else if (self.errorOrderList.length > 0) {
                        self.item = self.errorOrderList[0]
                    } else if (self.finishOrderList.length > 0) {
                        self.item = self.finishOrderList[0]
                    }

                    self.getLastNum(self.item.moId, self.item.plot);

                    if (self.item.preCategory == '1') {
                        self.preCategory = '白班'
                    } else if (self.item.preCategory == '4') {
                        self.preCategory = '晚班'
                    } else if (self.item.preCategory == '6') {
                        self.preCategory = '培训'
                    } else {
                        self.preCategory = '无班次'
                    }
                    self.personList = self.item.gcWorkPlanList
                    console.log(self.item.status)
                    self.lastStatus = self.item.lastStatus
                    if (self.item.lastStatus == '1' || self.item.lastStatus == '3' || self.item.lastStatus == '9') {
                        self.end = false;
                        self.start = true;
                        self.menuTrainVis = false;
                        self.menuSelectVis = false;
                    } else if (self.item.lastStatus == '2') {
                        self.end = true;
                        self.start = false;
                        self.menuTrainVis = true;
                        self.menuSelectVis = true;
                    } else if (self.item.lastStatus == '0') {
                        self.end = true;
                        self.start = false;
                        self.menuTrainVis = true;
                        self.menuSelectVis = true;
                    } else if (self.item.lastStatus == '14') {
                        self.end = true;
                        self.start = true;
                        self.menuTrainVis = false;
                    } else if (self.item.lastStatus == '16') {
                        self.end = true;
                        self.start = true;
                        self.menuSelectVis = false;
                    } else {
                        self.end = true;
                        self.start = true;
                    }

                    for (var i = 0; i < self.personList.length; i++) {
                        if (self.personList[i].type == '14') {
                            self.train = '取消培训';
                        }
                        if (self.personList[i].type == '16') {
                            self.select = '取消挑拣';
                        }
                    }
                    self.getPrepareInfo()
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getLastNum(moId, plot) {
            let self = this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getRestCount', { params: { moId: moId, plot: plot } }).then(res => {
                if (res.data.code == 200) {
                    self.lastNum = res.data.message;
                    if (self.lastNum == null) {
                        self.lastNum = 0;
                    }
                }
            });
        },
        startToNext() {

        },
        modalFormOk() {
            this.getInfo(this.itemParams)
            if (self.personList.length == 0) {
                MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                    if (action == "confirm") {
                        let params = {
                            lpId: self.item.id,
                            gcWorkOperationList: self.personList,
                            type: '2',
                            preCategory: self.item.preCategory,
                            workType: self.item.workType,
                            createNo: localStorage.getItem('userCode'),
                            creator: localStorage.getItem('userName'),
                            repCreator: self.worker
                        }
                        this.$refs.modalFormc.edit(params);
                        this.$refs.modalFormc.title = "关线录入";
                        this.$refs.modalFormc.disableSubmit = true;
                    }
                })
            }
        },
        handleMenuClick(e) {
            let self = this
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let itemList = []
            for (var i = 0; i < self.personList.length; i++) {
                if (self.personList[i].type == '5') {
                    Toast({
                        message: '所选之人存在人员临时离开，请先归产',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
                if (self.personList[i].selected) {
                    itemList.push(self.personList[i])
                }
            }
            console.log("itemList:" + itemList);
            if (itemList.length <= 0) {
                Toast({
                    message: "至少选择一名员工操作！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }

            if (e.key == 1) {
                MessageBox.prompt('调入蓄水池原因').then(({ value, action }) => {
                    if (action == "confirm") {
                        self.sendToWorkDeploy('9', itemList)
                    }
                });

            } else if (e.key == 2) {
                for (var i = 0; i < itemList.length; i++) {
                    itemList[i].lpId = self.item.id

                }
                self.$refs.modalForm.edit(itemList);
                self.$refs.modalForm.title = "调离";
                self.$refs.modalForm.disableSubmit = true;
            } else if (e.key == 3) {
                for (var i = 0; i < itemList.length; i++) {
                    itemList[i].lpId = self.item.id
                }
                self.$refs.modalForms.edit(itemList);
                self.$refs.modalForms.title = "离开原因";
                self.$refs.modalForms.disableSubmit = true;
            } else if (e.key == 4) {
                if (self.train == "取消培训") {
                    self.sendToWorkDeploy('24', itemList)
                } else {
                    self.sendToWorkDeploy('14', itemList)
                }
            } else if (e.key == 5) {
                if (self.select == "取消挑拣") {
                    self.sendToWorkDeploy('26', itemList)
                } else {
                    self.sendToWorkDeploy('16', itemList)
                }

            }

        },
        sendToWorkDeploy(num, itemList) {
            let self = this;
            let params = {
                lpId: self.item.id,
                gcWorkOperationList: itemList,
                type: num,
                createNo: localStorage.getItem('userCode'),
                creator: localStorage.getItem('userName'),
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    if (num == '9') {
                        for (var a = 0; a < itemList.length; a++) {
                            self.personList.splice(this.personList.indexOf(itemList[a]), 1);
                        }
                        if (self.personList.length == 0) {
                            MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                                if (action == "confirm") {
                                    let params = {
                                        lpId: self.item.id,
                                        gcWorkOperationList: self.personList,
                                        type: '2',
                                        preCategory: self.item.preCategory,
                                        workType: self.item.workType,
                                        createNo: localStorage.getItem('userCode'),
                                        creator: localStorage.getItem('userName'),
                                        repCreator: self.worker
                                    }
                                    this.$refs.modalFormc.edit(params);
                                    this.$refs.modalFormc.title = "关线录入";
                                    this.$refs.modalFormc.disableSubmit = true;
                                }
                            })
                        }
                    } else if (num == '14') {
                        self.train = "取消培训"
                        this.getInfo(this.itemParams)
                    } else if (num == '24') {
                        self.train = "培训"
                        this.getInfo(this.itemParams)
                    } else if (num == '16') {
                        self.select = "取消挑拣"
                        this.getInfo(this.itemParams)
                    } else if (num == '26') {
                        self.select = "挑拣"
                        this.getInfo(this.itemParams)
                    } else if (num == '1') {
                        self.getInfo(self.itemParams);
                    }
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            });
        },
        chargeBtn(action, done) {
            let self = this
            console.log(action)
            if (action === 'cancel') {//取消按钮
                done();
            } else if (action === 'confirm') {//确定按钮
                //向后端传值并关闭dialog弹出框
                this.show = false;
                done();
            }

        },
        leave(item) {
            let self = this
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let list = []
            item.lpId = this.item.id
            list.push(item);
            this.$refs.modalForms.edit(list);
            this.$refs.modalForms.title = "离开原因";
            this.$refs.modalForms.disableSubmit = true;
        },
        goback(item) {
            let self = this;
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let itemList = []
            itemList.push(item)
            self.sendToWorkDeploy('1', itemList)
        },
        outWorkshop(item) {
            let self = this
            let list = []
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if (item.type == '5') {
                Toast({
                    message: '所选之人存在人员临时离开，请先归产',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            item.lpId = this.item.id
            list.push(item);
            this.$refs.modalForm.edit(list);
            this.$refs.modalForm.title = "调离";
            this.$refs.modalForm.disableSubmit = true;
        },
        pushPool(item) {
            let self = this;
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if (item.type == '5') {
                Toast({
                    message: '所选之人存在人员临时离开，请先归产',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }


            MessageBox.prompt('调入蓄水池原因').then(({ value, action }) => {
                if (action == "confirm") {
                    let person = [];
                    item.reason = value;
                    person.push(item);
                    let params = {
                        lpId: self.item.id,
                        gcWorkOperationList: person,
                        type: '9',
                        createNo: localStorage.getItem('userCode'),
                        creator: localStorage.getItem('userName')
                    }
                    self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy', params).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            self.personList.splice(this.personList.indexOf(item), 1);
                            if (self.personList.length == 0) {
                                MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                                    if (action == "confirm") {
                                        let params = {
                                            lpId: self.item.id,
                                            gcWorkOperationList: self.personList,
                                            type: '2',
                                            preCategory: self.item.preCategory,
                                            workType: self.item.workType,
                                            createNo: localStorage.getItem('userCode'),
                                            creator: localStorage.getItem('userName'),
                                            repCreator: self.worker
                                        }
                                        this.$refs.modalFormc.edit(params);
                                        this.$refs.modalFormc.title = "关线录入";
                                        this.$refs.modalFormc.disableSubmit = true;
                                    }
                                })
                            }
                        } else {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    });
                }
            });
        },
        addLabel(item) {
            let self = this;
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            console.log(item)
            MessageBox.prompt('请对其输入技能标签').then(({ value, action }) => {
                if (action == "confirm") {
                    self.$axios.get('/jeecg-boot/app/gcWorkshop/getLabelInfo', { params: { id: item.pid, label: value, createNo: localStorage.getItem('userCode'), creator: localStorage.getItem('userName') } }).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            item.label = value;
                        } else {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                }
            });
        },
        addRecord() {
            let self = this
            if (self.itemParams.status == '0') {
                Toast({
                    message: "当前已关单！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            var type = '1';
            if (self.workDetaiList.length > 0) {
                type = '2';
                console.log(self.itemParams.id)
                console.log(self.workDetaiList.length)
                self.$router.push({ name: "WorkDuring", params: { id: self.itemParams.tableId, type: type, itemId: self.itemParams.id,tableType:self.itemParams.tableType, childId: self.workDetaiList.length } })
            } else {
                localStorage.setItem('cMoId', self.itemParams.id)
                localStorage.setItem('isfirst', true)
                self.$router.push({ name: "SelectModel", params: { item: self.itemParams, type: type, id: self.itemParams.id } })
            }
        },
        pushPl() {
            let self = this;
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            this.$router.push({ name: "AddPl", params: { workDay: this.item.createTime, lpId: this.item.id, materialList: this.item.gcMaterialList } })
        },
        pushOther() {
            let self = this
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if (self.item.lastStatus == '0' || self.item.lastStatus == '2') {
                Toast({
                    message: "请先开线",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }

            console.log("item:" + self.item);
            self.item.userName = self.peopleInfo.name
            self.item.userCode = self.peopleInfo.code
            this.$router.push({ name: "OrderOther", params: self.item })
        },
        openQuestionType() {
            this.popupVisible = true;
        },
        workMode(num) {
            let self = this;
            if (num == 1) {
                let params = {
                    gcQualityMain: {}
                }
                params.gcQualityMain = self.itemParams;
                localStorage.setItem('beginItem', JSON.stringify(params));
                self.$router.push({ name: "WorkBegin", params: { items: self.itemParams, type: 2, lpId: self.itemParams.lpId } })
            } else if (num == 2) {
                if (self.itemParams.status == '0') {
                    Toast({
                        message: "当前已关单！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
                // if(self.peopleInfo.code=='HI2002250004'){
                //     this.$router.push({name:"WorkPhoto",params:self.item})
                // }else{
                //     Toast({
                //         message: "功能暂未开放！",
                //         position: 'bottom',
                //         duration: 2000
                //     });
                // }
                this.$router.push({ name: "WorkPhoto", params: self.item })
            } else if (num == 3) {
                // if(self.itemParams.status=='0'){
                //     Toast({
                //         message: "当前已关单！",
                //         position: 'bottom',
                //         duration: 2000
                //     });
                //     return
                // }
                console.log(this.itemParams.lpId)
                localStorage.setItem('stickerLpId', this.itemParams.lpId);
                this.$router.push({ name: "StickerList" })
            } else if (num == 4) {
                if (self.itemParams.status == '0') {
                    Toast({
                        message: "当前已关单！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
                // if(self.workDetaiList.length<=0){
                //     Toast({
                //         message: "请填写巡检记录！",
                //         position: 'bottom',
                //         duration: 2000
                //     });
                //     return
                // }

                // self.$axios.get('/jeecg-boot/app/appQuality/getCloseStatus',{params:{id:self.itemParams.id}}).then(res=>{
                //     if(res.data.message=='1'){
                //         self.getOutput();
                //     }else{
                //         Toast({
                //             message: "mo单未完结，无法关单！",
                //             position: 'bottom',
                //             duration: 2000
                //         });
                //         return
                //     }
                // })

                self.getOutput();

            }
        },
        getOutput() {
            let self = this;
            self.$axios.get('/jeecg-boot/app/appQuality/getOutputNew', { params: { id: self.itemParams.id, batchNumber: self.itemParams.batchNumber } }).then(res => {
                if (res.data.code == 200) {
                    let msg = "MO单生产总量为" + res.data.result.total + ",不合格数为:" + res.data.result.unquality + ",是否确认关单？";
                    MessageBox.confirm(msg).then(action => {
                        if (action == "confirm") {
                            self.closeOrder(res.data.result.total, res.data.result.unquality);
                        }
                    });
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
            })
        },
        closeOrder(total, unquality) {
            let self = this
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/appQuality/getClose', { params: { id: self.itemParams.id, total: total, unquality: unquality, userCode: localStorage.getItem('userCode') } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.$router.go(-1);
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendMode(num, params) {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this;
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeploy', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    if (num == '1') {
                        self.end = false;
                        self.start = true;
                    } else if (num == '2') {
                        self.end = true;
                        self.start = false;
                    }
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getInfo(self.itemParams)
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        popupOk() {
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        deleteUser(item) {
            let self = this;
            let person = []
            item.lpId = self.item.id
            person.push(item)
            let params = {
                gcWorkPlanAppList: person,
                type: '2',
                createNo: localStorage.getItem('userCode'),
                creator: localStorage.getItem('userName'),
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changePlanInfo', params).then(res => {
                if (res.data.code == 200) {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getInfo(self.itemParams)
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values) {
            this.questionTypeVal = values[0];
        },
        selectData() {
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        selectGld() {
            // Toast({
            //     message: '暂未开放，敬请期待！',
            //     position: 'bottom',
            //     duration: 2000
            // });
            let self = this
            if (self.item.lastStatus == '4') {
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if (self.item.lastStatus == '0' || self.item.lastStatus == '2') {
                Toast({
                    message: "请先开线",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            this.$router.push({ name: "OrderPre", params: { id: this.item.id } })
        },
        cancelGl() {
            //取消关联
            let self = this
            MessageBox.confirm('是否确认取消此单关联？').then(action => {
                if (action == "confirm") {
                    self.cancelSelected()
                }
            })
        },
        cancelSelected() {
            let self = this;
            let num = '9';
            let params = {
                lpId: self.item.id,
                laId: '',
                type: num,
                createNo: localStorage.getItem('userCode'),
                creator: localStorage.getItem('userName')
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeploy', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close()
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.gld = "请选择"
                } else {
                    Indicator.close()
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate(secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>

<style scoped>
.order {
    background-color: #ebecf7;
}

.top_title {
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}

.top_msg {
    float: right;
}

.items_d {
    padding: 5%;
    height: 6rem;
}

.item_bg {
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}

.item_add {
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}

.itemTitle {
    padding: 5%;
}

.sign {
    text-align: center;
}

.plotName {
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}

.plotCode {
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}

.plotCard {
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}

.plotId {
    position: absolute;
    top: 26%;
    left: 10%;
    color: #fff;
}

.plotFactory {
    position: absolute;
    top: 30%;
    left: 10%;
    width: 85%;
    text-align: left;
    color: #fff;
}

.plotWorkshop {
    position: absolute;
    top: 36%;
    left: 10%;
    color: #fff;
}

.plotMitosome {
    position: absolute;
    top: 38%;
    left: 35%;
    color: #fff;
}

.plotTime {
    background: url('../../static/images/search_time.png');
    width: 90%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.orderType {
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}

.menu_order_item {
    float: left;
    height: 100%;
    width: 25%;
}

.menu_order {
    background: white;
    border-radius: 10px;
    margin: 5%;
    width: 90%;
    height: 4.5rem;
}

.more {
    background-image: url('../../static/images/more.png');
    background-size: 100%, 100%;
    width: 33%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5%;
    color: #fff;
    background-repeat: no-repeat;
}

.other {
    background-image: url('../../static/images/other.png');
    background-size: 100%, 100%;
    width: 90%;
    margin: 5%;
    height: 12rem;
    background-repeat: no-repeat;
}

.other p {
    font-weight: 600;
    text-align: left;
    padding: 10%;
}

.person_item {
    background-image: url('../../static/images/person_item.png');
    background-repeat: no-repeat;
    background-size: 100%, 100%;
    margin: 5%;
    padding: 5%;
    height: 6rem;
}

.person_name {
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}

.person_menu {
    float: right;
    width: 26%;
    position: absolute;
    right: 0.5rem;
}

.person_line {
    background-color: #d8d2f7;
    height: 1px;
    width: 90%;
    margin-left: 5%;
}

.person-top {
    padding: 5%;
    height: 2rem;
}

.person-left {
    float: left;
    width: 65%;
    height: 100%;
    text-align: left;
}

.leave-style {
    color: crimson;
}

.leave-style2 {
    color: teal;
}

.leave-style3 {
    color: tomato;
}

.person-right {
    float: left;
    display: flex;
    align-items: center;
    width: 33%;
    height: 100%;
}

.circle {
    width: 10px;
    height: 10px;
    background-color: #4a1bf6;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.person_menu_item {
    padding: 5%;
}

.addUser {
    background-image: url('../../static/images/addUser.png');
    background-size: 100% 100%;
    width: 90%;
    margin: 5%;
}

.picker-toolbar-title {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    background-color: #eee;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
}

.usi-btn-cancel,
.usi-btn-sure {
    color: #26a2ff;
    font-size: 16px;
}

.popup-div {
    width: 100%;
}

.pl {
    background-image: url('../../static/images/item_bg.png');
    background-size: 100%, 100%;
    width: 90%;
    margin: 5%;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background-repeat: no-repeat;
}

.allOpera {
    width: 100%;
    height: 3rem;
}

.person_num {
    color: #4a1bf6;
    font-weight: 800;
    width: 40%;
    height: 100%;
    float: right;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5%;
    background-repeat: no-repeat;
}

.gl_bg {
    width: 90%;
    height: 3rem;
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 5%;
    margin-top: 5%;
    display: flex;
    align-items: center;
}

.gld_bg {
    background: url('../../static/images/gl_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42%;
    color: #fff;
    float: left;
}

.gld_title {
    width: 42%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gld_cancel {
    width: 15%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: blue;
}

.ycl-style {
    color: crimson;
}
</style>