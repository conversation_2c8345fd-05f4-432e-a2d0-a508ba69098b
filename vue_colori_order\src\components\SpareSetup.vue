<template>
    <div>
        <van-sticky :offset-top="0">
            <div style="backgroundColor:#fff">
                <van-nav-bar title="备件安装" right-text="筛选" left-arrow @click-left="onClickLeft"
                    @click-right="onClickRight" />

                <!-- <van-cell title="点击扫码" v-model="code" @click="handleScan"
                    style="margin:5%;width:90%;border-radius:10px;" /> -->
                <van-cell title="点击扫码" v-model="code" @click="handleSearch"
                    style="margin:5%;width:90%;border-radius:10px;" />
            </div>

        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '20%' }">
            <van-field v-model="product" clearable label="备件：" placeholder="请输入备件" />

            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>
        <!-- <div v-for="(item, index) in spareList" :key="index"
            style="text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="24">名称:{{ item.name }}</van-col>
            </van-row>
            <van-row>
                <van-col span="12">编码:{{ item.code }}</van-col>
                <van-col span="12">数量:{{ item.count }}</van-col>
            </van-row>
            <van-row>
                <van-col v-if="item.type == '0'" span="12">操作类型:期初</van-col>
                <van-col v-else-if="item.type == '1'" span="12">操作类型:入库</van-col>
                <van-col v-else-if="item.type == '2'" span="12">操作类型:领用/出库</van-col>
                <van-col v-else-if="item.type == '3'" span="12">操作类型:退库</van-col>
                <van-col v-else-if="item.type == '4'" span="12">操作类型:盘盈</van-col>
                <van-col v-else-if="item.type == '5'" span="12">操作类型:盘亏</van-col>
                <van-col v-else-if="item.type == '9'" span="12">操作类型:安装</van-col>
                <van-col v-else span="12">操作类型:</van-col>
                <van-col span="8">批次号:{{ item.customerId }}</van-col>
            </van-row>
            <van-row>
                <van-col span="12">操作人:{{ item.creator_dictText }}</van-col>
            </van-row>
            <van-row>
                <van-col span="24">操作时间:{{ item.createTime }}</van-col>
            </van-row>
        </div> -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
                <van-cell v-for="item in spareList" :key="item.id">
                    <div
                        style="color: rgb(112 100 100 / 85%);text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
                        <van-row>
                            <van-col span="24">名称:{{ item.name }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="12">编码:{{ item.code }}</van-col>
                            <van-col span="12">数量:{{ item.count }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col v-if="item.type == '0'" span="12">操作类型:期初</van-col>
                            <van-col v-else-if="item.type == '1'" span="12">操作类型:入库</van-col>
                            <van-col v-else-if="item.type == '2'" span="12">操作类型:领用/出库</van-col>
                            <van-col v-else-if="item.type == '3'" span="12">操作类型:退库</van-col>
                            <van-col v-else-if="item.type == '4'" span="12">操作类型:盘盈</van-col>
                            <van-col v-else-if="item.type == '5'" span="12">操作类型:盘亏</van-col>
                            <van-col v-else-if="item.type == '9'" span="12">操作类型:安装</van-col>
                            <van-col v-else span="12">操作类型:</van-col>
                            <van-col span="8">批次号:{{ item.customerId }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="12">账套:{{ item.bookName }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="12">车间:{{ item.workshopName }}</van-col>
                            <van-col span="12">用途:{{ item.purpose }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="12">单价:{{ item.singlePrice }}</van-col>
                            <van-col span="12">总价:{{ item.totalPrice }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="24">审批单号:{{ item.spNo }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="12">操作人:{{ item.creator_dictText }}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="24">操作时间:{{ item.createTime }}</van-col>
                        </van-row>
                    </div>

                </van-cell>
            </van-list>
        </van-pull-refresh>

    </div>
</template>

<script>
import { DatetimePicker, Indicator, Toast, MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx = window.wx
export default {
    data() {
        return {
            spareList: [],
            show: false,
            product: "",

            code: '',
            loading: false,
            finished: false,
            refreshing: false,

            pageNum: 0,
        }
    },
    created() {
        // this.$axios
        //     .get(`/jeecg-boot/ncApp/partsUsage/getPartsUsageList?userCode=${localStorage.getItem('userCode')}&type=9`)
        //     .then(res => {
        //         if (res.data.code == 200) {
        //             this.spareList = res.data.result.records
        //         } else {
        //             Toast({
        //                 message: res.data.msg,
        //                 position: "bottom",
        //                 duration: 2000
        //             });
        //         }
        //     });
    },
    methods: {
        onLoad() {
            this.loading = true;
            //分页
            this.pageNum++
            console.log(this.pageNum);
            //请求数据
            this.getList()
        },
        getList() {
            this.$axios.get(`/jeecg-boot/ncApp/partsUsage/getPartsUsageList?userCode=${localStorage.getItem('userCode')}&type=9&pageNo=${this.pageNum}&pageSize=${10}`).then(res => {
                this.show = false
                if (res.data.code == 200) {
                    this.spareList = this.spareList.concat(res.data.result.records)
                    console.log(res.data.result.records.length);
                    if (res.data.result.records.length < 10) {
                        this.finished = true
                    } else {
                        this.finished = false
                    }
                    this.loading = false;
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            })
        },
        onRefresh() {
            // 清空列表数据
            this.spareList = []
            this.finished = false;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            this.pageNum = 1
            this.getList();
        },
        handleScan() {
            let self = this;
            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function (res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$router.push({ name: "SpareSetupScan", params: { code: result } })
                },
                error: function (res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
        },
        handleSearch() {
            let self = this
            MessageBox.confirm('', {
                message: '请选择扫码或者录入',
                title: '提示',
                confirmButtonText: '扫码',
                cancelButtonText: '录入'
            }).then(action => {
                if (action == "confirm") {
                    wx.scanQRCode({
                        desc: 'scanQRCode desc',
                        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                        success: function (res) {
                            // 回调
                            var result = res.resultStr;//当needResult为1时返回处理结果
                            self.code = result
                            self.$router.push({ name: "SpareSetupScan", params: { code: self.code } })
                        },
                        error: function (res) {
                            if (res.errMsg.indexOf('function_not_exist') > 0) {
                                alert('版本过低请升级')
                            }
                        }
                    });
                }
            }).catch((res) => {
                if (res == "cancel") {
                    MessageBox.prompt('请录入产品编号').then(({ value, action }) => {
                        if (action == "confirm") {
                            self.code = value
                            self.$router.push({ name: "SpareSetupScan", params: { code: self.code } })
                        }
                    });
                }
            });
        },
        search() {
            this.pageNum = 1
            this.spareList = []
            this.finished = false
            this.$axios
                .get(`/jeecg-boot/ncApp/partsUsage/getPartsUsageList?product=${this.product}&type=9&userCode=${localStorage.getItem('userCode')}&pageNo=${this.pageNum}&pageSize=${10}`)
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.spareList = res.data.result.records
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "SparePart"
            });
        },
        onClickRight() {
            this.show = true;
        }
    },
}
</script>

<style scoped>
</style>