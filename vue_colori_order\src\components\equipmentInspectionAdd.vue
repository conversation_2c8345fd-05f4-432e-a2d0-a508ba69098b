<template>
    <div class="page">
        <!-- 顶部导航栏 -->
        <van-nav-bar :title="isEdit ? '编辑' : '新增'" left-text="返回" left-arrow @click-left="onClickLeft" />

        <van-form ref="form">
            <!-- 日期时间 -->
            <div class="form-card">
                <div class="form-item-title">巡检时间</div>
                <div class="date-time-container">
                    <van-field readonly clickable :value="inspectInfo.date + ' ' + inspectInfo.timeOnly" label=""
                        :class="{ 'disabled': isEdit }" @click="isEdit ? '' : showDatetimePicker">
                        <template #left-icon>
                            <van-icon name="calendar-o" size="20" color="#909399" />
                        </template>
                    </van-field>
                </div>
            </div>

            <!-- 巡检人员 -->
            <div class="form-card">
                <div class="form-item-title">巡检人员</div>
                <van-field v-model="currentUserName" readonly label="" placeholder="当前登录用户" />
            </div>
            <div class="form-card">
                <div class="form-item-title">巡检区域</div>
                <van-field v-model="area" readonly label="" placeholder="" />
            </div>

            <div class="form-card" v-for="(item, index) in inspectItems" :key="index">
                <div class="form-item-title">{{ item.equiName }}</div>
                <div class="status-select">
                    <div class="form-item-title">{{ item.deviceId }} _ {{ item.deviceName }}</div>
                </div>
                <!-- 巡检情况选择 -->
                <div class="status-select">
                    <div class="status-label">巡检状态:</div>
                    <van-radio-group v-model="item.result" direction="horizontal">
                        <van-radio name="正常">正常</van-radio>
                        <van-radio name="异常">异常</van-radio>
                    </van-radio-group>
                </div>

                <!-- 添加巡查实况部分 -->


                <div style="text-align: left;">巡查实况</div>
                <div class="btnUpload" @click="onUploadImg(index)" v-if="item.images && item.images.length < 2">
                    点击上传照片(最多2张)</div>
                <div class="padding-xs"></div>
                <van-grid :column-num="3" :border="false">
                    <van-grid-item v-for="(img, imgIndex) in item.images" :key="imgIndex"
                        @click="previewImage(index, imgIndex)">
                        <div class="image-container">
                            <van-image :src="img.url" class="grid-image" fit="cover" />
                            <div class="image-delete" v-if="!isEdit" @click.stop="deleteImage(index, imgIndex)">
                                <van-icon name="cross" color="#FFFFFF" size="20"></van-icon>
                            </div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-btn">
                <van-button type="primary" block @click="submit">提交</van-button>
            </div>
        </van-form>

        <!-- 日期时间选择器 -->
        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker v-model="pickerValue" type="datetime" title="选择日期时间" @confirm="confirmDateTime"
                @cancel="showPicker = false" :formatter="formatter" />
        </van-popup>

        <!-- 图片预览 -->
        <van-image-preview v-model="showImagePreview" :images="previewImages" :start-position="imageIndex"
            @close="closeImagePreview" />
    </div>
</template>

<script>
import { Toast } from 'vant';
export default {
    name: 'EquipmentInspectionAdd',
    data() {
        return {
            configService: {
                staticDomainURL: 'https://example.com' // 替换为实际的静态资源域名
            },
            id: '',
            isEdit: false,
            inspectInfo: {
                date: '',
                timeOnly: '',
                inspector: '',
                inspectorName: '',
            },
            // 当前登录用户信息
            currentUserId: localStorage.getItem('userCode'),
            currentUserName: localStorage.getItem('userName'),
            // 巡检项列表 - 硬编码固定项目
            inspectItems: [

            ],
            showPicker: false,
            pickerValue: new Date(),
            showImagePreview: false,
            previewImages: [],
            imageIndex: 0,
            area: '',
            currentInspectItemIndex: 0,
        };
    },
    methods: {
        onClickLeft() {
            this.$router.back();
        },

        // 获取详情数据
        async getDetail() {
            console.log(this.area)
            // 使用 this.$toast 之前先检查是否存在
            Toast.loading({
                mask: true,
                message: '加载中...'
            });
            this.$axios.get('/jeecg-boot/app/equiInspect/getInspectEquiList', {
                params: {
                    workshop: this.area
                }
            }).then(res => {
                this.inspectItems = res.data.result[0].equi.map(item => {
                    return {
                        ...item,
                        area: this.area,
                        result: '正常', // 默认状态为正常
                        images: [] // 初始化图片数组
                    }
                });
                console.log("🚀 ~ getDetail ~ this.inspectItems:", this.inspectItems)
                Toast.clear();
            }).catch(err => {
                Toast.fail('网络错误，请稍后重试');
            });
        },

        // 上传图片
        onUploadImg(index) {
            console.log("🚀 ~ onUploadImg ~ index:", index)
            this.currentInspectItemIndex = index;
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (event) => {
                const file = event.target.files[0];
                console.log("🚀 ~ onUploadImg ~ file:", file)
                if (file) {
                    const param = new FormData();
                    param.append("file", file);
                    param.append("description", '设备巡检');
                    param.append("type", '');
                    // this.$axios.post(`/jeecg-boot/app/improve/uploadPic`, param).then(res => {
                    this.$axios.post(`/jeecg-boot/app/equiInspect/uploadPic`, param).then(res => {
                        if (res.data.code == 200) {
                            console.log(res);
                            this.inspectItems[index].images.push({
                                url: res.data.message,
                            });
                        } else {
                            this.inspectItems[index].images.splice(index, 1);
                            Toast({
                                message: "上传失败,请选择图片上传",
                                position: "bottom",
                                duration: 2000
                            });
                        }
                        console.log("🚀 ~ this.$axios.post ~   this.inspectItems:", this.inspectItems)

                    });
                }
            };
            input.click();
        },

   

        // 预览图片
        previewImage(itemIndex, imageIndex) {
            this.previewImages = this.inspectItems[itemIndex].images.map(item => item.url);
            this.imageIndex = imageIndex;
            this.showImagePreview = true;
        },

        closeImagePreview() {
            this.showImagePreview = false;
        },

        // 提交
        submit() {
            Toast.loading({
                message: '提交中...',
                forbidClick: true
            });
            let flag = true;
            let arr = this.inspectItems.map(item => {
                if (item.images.length > 0) {
                    return {
                        ...item,
                        picUrl1: item.images.map(item => item.url).join(','),
                        creator: this.currentUserId,
                        createName: this.currentUserName,
                    }
                } else {
                    flag = false;
                }
            })
            if (!flag) {
                Toast.fail('请上传照片');
                return;
            }
            this.$axios.post('/jeecg-boot/app/equiInspect/add', arr).then(res => {
                console.log(res);
                Toast.clear();
                this.$router.go(-1);
            }).catch(err => {
                console.log(err);
                Toast.fail('提交失败');
            });

        },

        // 显示日期时间选择器
        showDatetimePicker() {
            // 如果已有时间，则设置为当前选中时间
            if (this.inspectInfo.date && this.inspectInfo.timeOnly) {
                const currentDateTime = new Date(`${this.inspectInfo.date} ${this.inspectInfo.timeOnly}`);
                if (!isNaN(currentDateTime.getTime())) {
                    this.pickerValue = currentDateTime;
                }
            }
            this.showPicker = true;
        },

        // 确认选择日期时间
        confirmDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            this.inspectInfo.date = `${year}-${month}-${day}`;
            this.inspectInfo.timeOnly = `${hours}:${minutes}`;
            this.showPicker = false;
        },

        formatter(type, value) {
            if (type === 'year') {
                return `${value}年`;
            }
            if (type === 'month') {
                return `${value}月`;
            }
            if (type === 'day') {
                return `${value}日`;
            }
            if (type === 'hour') {
                return `${value}时`;
            }
            if (type === 'minute') {
                return `${value}分`;
            }
            return value;
        }
    },
    created() {
        this.inspectInfo.inspector = this.currentUserId;
        this.inspectInfo.inspectorName = this.currentUserName;
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        this.inspectInfo.date = `${year}-${month}-${day}`;
        this.inspectInfo.timeOnly = `${hours}:${minutes}`;
        // 从路由参数中获取id
        if (this.$route.query.area) {
            console.log("🚀 ~ created ~ this.$route.query.area:", this.$route.query.area)
            this.area = this.$route.query.area;
            this.getDetail();
        }
    }
};
</script>

<style scoped>
.page {
    background-color: #f7f8fa;
    min-height: 100vh;
    padding-bottom: 50px;
}

.form-card {
    background-color: #ffffff;
    margin: 12px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.form-item-title {
    font-size: 14px;
    font-weight: bold;
    color: #323233;
    margin-bottom: 12px;
}

.date-time-container {
    display: flex;
    justify-content: space-between;
}

.disabled {
    color: #c8c9cc;
    background-color: #f2f3f5;
}

.status-select {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.status-label {
    margin-right: 12px;
    font-size: 14px;
    color: #646566;
}

.upload-section {
    margin-top: 16px;
}

.upload-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.upload-hint {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
}

.btnUpload {
    width: 100%;
    height: 80px;
    text-align: center;
    line-height: 80px;
    border: 1px dashed #dcdee0;
    color: #646566;
    margin-bottom: 12px;
    border-radius: 4px;
}

.btnUpload:active {
    background-color: #f2f3f5;
}

.image-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 4px auto;
}

.grid-image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

.image-delete {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9;
}

.padding-xs {
    padding: 5px;
}

.delete-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    padding: 8px 0;
    color: #ee0a24;
    font-size: 14px;
}

.delete-item span {
    margin-left: 4px;
}

.add-item-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 16px 12px;
    padding: 12px 0;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    color: #1989fa;
    font-size: 14px;
}

.add-item-btn span {
    margin-left: 4px;
}

.submit-btn {
    margin: 30px 16px;
}

.error-message {
    color: #ee0a24;
    font-size: 12px;
    margin-top: 5px;
}
</style>
