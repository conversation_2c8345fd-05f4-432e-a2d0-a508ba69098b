<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_order_title">模板选择</div>
        </div>
        <!-- <van-field label="MO单号" placeholder="请输入MO单号" v-model="moId" style="background:white"/>
        <van-field label="日期" :value="date" style="background:white" readonly @click="c_show = true"/>
        <van-button type="primary" @click="search()" style="margin-top:10px;margin-bottom:10px;border-radius:10px;width:50%">查询</van-button>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" /> -->


        <div v-for="(item,index) in leadAppInfo" :key="index" class="report_item">
            <div class="report_item_text">
                <div class="item_left">模板编号</div>
                <div class="item_right">{{item.id}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">模板名称</div>
                <div class="item_right">{{item.tableName}}</div>
            </div>
            <div class="report_line"></div>
            <div class="bottom_button" @click="selected(item)">选择</div>
        </div>

        <update-modal ref="modalForms" @ok="modalFormOk"></update-modal>
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
import UpdateModal from './list/UpdateModal.vue';
export default {
  components: { UpdateModal },
    data(){
        return{
            moId:'',
            itemId:'',
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal:'',
            minDate:'',
            maxDate:'',
            date:'',
            category:'',
            userName:'',
            userCode:'',
            popupVisible:false,
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            questionType:'',
            c_show:false,
            questionTypeVal:'',
            clickNum:0,
            isfirst:'',
            popupVisible:false,
            leadAppInfo:[],
            popupSlots:[
                {
                    values:[
                        '全部','白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 30 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')
        this.itemId=localStorage.getItem('cMoId')

        console.log("id:"+this.itemId)

        this.isfirst=localStorage.getItem('isfirst');
        console.log(this.isfirst)
        if(this.isfirst=='false'){
            this.$router.go(-1);
        }

        this.search(this.$route.params.item.key)
    },
    methods:{
        search(key){
            let self=this;
            self.$axios.get('/jeecg-boot/app/appQuality/getTables',null).then(res=>{
                if(res.data.code==200){
                    if(key=='D880瓶线'||key=='D880袋线'){
                        res.data.result.forEach(item => {
                            if(item.tableType=='2'){
                                self.leadAppInfo.push(item)
                            }
                        });
                    }else{
                        res.data.result.forEach(item => {
                            if(item.tableType!='2'){
                                self.leadAppInfo.push(item)
                            }
                        });
                    }
                    // self.leadAppInfo=res.data.result
                }
            })
        },
        selected(item){
            let self=this
            self.$axios.get('/jeecg-boot/app/appQuality/getConnect',{params:{tableId:item.id,tableName:item.tableName,mainId:self.itemId,userCode:this.userCode}}).then(res=>{
                if(res.data.code==200){
                    self.$router.push({name:"WorkDuring",params:{id:item.id,type:'1',tableType:item.tableType,itemId:self.itemId,childId:'0'}})
                }
            })
        },
        hiddenToUpdateOrder(){
            this.clickNum=this.clickNum+1;
            if(this.clickNum<5){
                var lastClick=5-this.clickNum
                var msg="再点击"+lastClick+"下即可打开管理员权限！"
                Toast({
                    message: msg,
                    position: 'bottom',
                    duration: 1000
                });
            }else{
                this.clickNum=0;
                if(this.userCode=='HI1606270001' || this.userCode=='HI2002250004'){
                    this.$router.push({name:"OrderUpdate"})
                }else{
                    Toast({
                        message: "对不起，您暂无权限！",
                        position: 'bottom',
                        duration: 1000
                    });
                } 
            }

            
        },
        modalFormOk(){
            this.getLeadAppInfo()
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
        },
        updateCount(item){
            let self=this;
            self.$refs.modalForms.edit(item);
            self.$refs.modalForms.title="产量更改";
            self.$refs.modalForms.disableSubmit = true;
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType(){
            this.popupVisible = true;
        },
        dateConfirm(value){
            this.newSelectedValue=this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getLeadAppInfo()
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getLeadAppInfo()
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    padding: 5%;
    font-size: 1rem;
    width: 50%;
    background: #fff;
    margin: 0 auto;
    margin-top: 5%;
    border-radius: 10px;
}
.report_mo{
    font-size: 1.2rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.report_line{
    background: #cfcfcf;
    height: 1px;
}
.report_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 24%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 75%;
}
.report_item{
    width: 90%;
    border-radius: 10px;
    margin: 0 auto;
    margin-bottom: 20px;
    background: #fff;
}
.bottom_button{
    padding: 5%;
    color: #ff0000;
    font-size: 1rem;
    font-weight: 600;
}
.bottom_button2{
    padding: 5%;
    color: chartreuse;
    font-size: 1rem;
    font-weight: 600;
}
.ycl-style{
    color: crimson;
}
</style>