<template>
    <div>
        <van-cell-group style="text-align:center;">
            <div style="margin:10px;text-align:left;font-weight:900;">MO单号数据</div>
            <van-field required label="MO单号" placeholder="请选择MO单号" :value="checkInfo.gcQualityMain.moId" readonly
                :disabled="disabled" />
            <van-field label="生产车间" :value="checkInfo.gcQualityMain.workshop" readonly />
            <van-field label="流水线" :value="checkInfo.gcQualityMain.mitosome" readonly />
            <van-field label="线体组长" :value="checkInfo.gcQualityMain.leaderNo" readonly />
            <van-field label="灌装设备" :value="checkInfo.gcQualityMain.machine" readonly />
            <van-field label="产品编号" :value="checkInfo.gcQualityMain.code" readonly />
            <van-field label="产品名称" type="textarea" autosize :value="checkInfo.gcQualityMain.name" readonly />
            <van-field label="产品批号" required v-model="checkInfo.gcQualityMain.batchNumber" v-if="!batchSelected"/>
            <van-field-Pick v-model="checkInfo.gcQualityMain.batchNumber" v-if="batchSelected" label="产品批号" required :columns="checkInfo.batchNumbers" />

            <div style="width:100%;position: relative;">
                <van-field label="生产日期" required v-model="checkInfo.gcQualityMain.defV28" @click="c_show=true" style="width:70%;float:left;" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1" />
                <van-button type="info" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1" @click="setDefault(1)" style="height:35px;margin:5px;width:25%;border-radius:10px;float:left;">N/A</van-button>
                <div style="clear:both;"></div>
            </div>

            <!-- <van-field label="产品有效期(天)" :value="validDay" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1" readonly /> -->

            <van-field label="产品有效期(月)" :value="validMonth" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1"
                readonly />

            <div style="width:100%;position: relative;">
                <van-field label="限用日期" required v-model="checkInfo.gcQualityMain.defV29" @click="c_show_e=true" style="width:70%;float:left;" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1"/>
                <!-- <van-button type="info" v-show="checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1" @click="setDefault(2)" style="height:35px;margin:5px;width:25%;border-radius:10px;float:left;">N/A</van-button> -->
                <div style="clear:both;"></div>
            </div>

            
            
            <van-field-Pick v-model="checkInfo.gcQualityMain.key" :disabled="disabled" label="类型" required :columns="['常规','高露洁瓶装','高露洁美白笔','D880瓶线','D880袋线']" @change="showProduct"/>
            <van-field label="规格(g)" v-model="checkInfo.gcQualityMain.defV7" v-show="glshow==1" />
            <van-field label="皮重(g)" v-model="checkInfo.gcQualityMain.defV8" v-show="glshow==1" />
            <van-field label="标准净重(g)" v-model="checkInfo.gcQualityMain.defV9" v-show="glshow==1" />
            <van-field label="净重最小(g)" v-model="checkInfo.gcQualityMain.defV10" v-show="glshow==1" />
            <van-field label="净重最大(g)" v-model="checkInfo.gcQualityMain.defV11" v-show="glshow==1" />
            <van-field label="正标货号" v-model="checkInfo.gcQualityMain.defV12" v-show="glshow==1" />
            <van-field label="背标货号" v-model="checkInfo.gcQualityMain.defV13" v-show="glshow==1" />
            <van-field label="膜货号" v-model="checkInfo.gcQualityMain.defV14" v-show="glshow==1" />
            <van-field label="外箱货号" v-model="checkInfo.gcQualityMain.defV15" v-show="glshow==1" />
            <div v-show="glshow==3">
                <van-field label="外箱" v-model="checkInfo.gcQualityMain.defV20" />
                <van-field label="瓶子" v-model="checkInfo.gcQualityMain.defV21" />
                <van-field label="正贴" v-model="checkInfo.gcQualityMain.defV22" />
                <van-field label="背贴" v-model="checkInfo.gcQualityMain.defV23" />
                <van-field label="广告贴" v-model="checkInfo.gcQualityMain.defV24" />
                <van-field label="盖子" v-model="checkInfo.gcQualityMain.defV25" />
            </div>
            <div v-show="glshow==4">
                <van-field label="外箱" v-model="checkInfo.gcQualityMain.defV20" />
                <van-field label="立袋" v-model="checkInfo.gcQualityMain.defV26" />
            </div>
            <div v-show="glshow==5">
                <van-field label="笔身货号" v-model="checkInfo.gcQualityMain.defV30" />
                <van-field label="说明书货号" v-model="checkInfo.gcQualityMain.defV31" />
                <van-field label="彩盒货号" v-model="checkInfo.gcQualityMain.defV32" />
                <van-field label="外箱货号" v-model="checkInfo.gcQualityMain.defV33" />
            </div>

            <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />
            <van-calendar v-model="c_show_e" :min-date="eMinDate" :max-date="eMaxDate" @confirm="onConfirmE" :show-confirm="false" position="right" />
            <div v-if="isGBY">
            <div style="margin:10px;text-align:left;font-weight:900;">预处理多选</div>
                <div @click="showHint">
                    <van-field placeholder="点击选择" label="预处理" :value="checkInfo.gcQualityMain.defV5" readonly />
                </div>
                <mt-popup v-model="showOverlay" popup-transition="popup-fade">
                    <mt-checklist title="" v-model="bcpArr"
                        :options="['预处理贴标', '预处理脱包', '预处理包膜', '预处理包盒', '预处理喷码', '预处理折面膜', '预处理灭菌消毒', '预处理调试机器', '其他']">
                    </mt-checklist>
                </mt-popup>
            </div>
            <div v-else>
                <div style="margin:10px;text-align:left;font-weight:900;">半成品或胶体数据</div>
                <van-field-Pick :disabled="disabled" label="胶体/半成品" required :columns="['胶体', '半成品']"
                    @change="jtbcpChange" />
                <div v-show="!this.jt">
                    <van-cell title="选择半成品" value="点击选择" @click="chooseclick"
                        style="width:100%;margin-top:5%;margin-bottom:5%" />
                    <div>
                        <van-field label="半成品名称/批号" type="textarea" rows="3" autosize
                            v-model="checkInfo.gcQualityMain.defV5" />
                    </div>
                    <mt-popup v-model="showOverlay" popup-transition="popup-fade">
                        <div v-if="arrbcp.length == 0" style="width:100%;padding:50% 5% 50% 5%">没有数据</div>
                        <mt-checklist v-else title="" v-model="bcpArr" :options="arrbcp">
                        </mt-checklist>
                    </mt-popup>
                </div>
                <div v-show="!this.bcp">
                    <van-cell title="可选择" :value="colloid" @click="getColloidList"
                        style="width:100%;margin-top:5%;margin-bottom:5%" />
                    <!-- <div v-for="(item,index) in goodsInfo" :key="index" style="margin-left:10px;margin-right:10px;margin-top:15px;margin-bottom:15px;">
                <div style="text-align:left;color:#008272;font-weight:800;">半成品名称{{index+1}}</div>
                <van-field label="物料编码" :value="item.materialcode" readonly />
                <van-field label="物料名称" :value="item.materialname" readonly />
                <van-field label="批次号" :value="item.vbatchcode" readonly />
            </div> -->
                    <van-cell title="已选择" value="查看已选择" @click="getColloidListChoose"
                        style="width:100%;margin-top:5%;margin-bottom:5%" />
                    <van-field label="半成品名称/批号" type="textarea" rows="3" autosize
                        v-model="checkInfo.gcQualityMain.defV5" />
                </div>
            </div>
        

            
            <div style="margin:10px;text-align:left;font-weight:900;">开线前检查</div>
            <van-field-Pick v-model="checkInfo.gcQualityMain.defV1" :disabled="disabled" label="包装工艺是否现行版本，且与操作一致"
                required :columns="['是', '否']" />
            <van-field-Pick v-model="checkInfo.gcQualityMain.defV2" :disabled="disabled" label="材料复核是否完成，且与工艺要求一致"
                required :columns="['是', '否']" />
            <van-field-Pick v-model="checkInfo.gcQualityMain.defV3" :disabled="disabled" label="批记录是否已在现场，且完成生产前清场确认"
                required :columns="['是', '否']" />
            <van-field-Pick v-model="checkInfo.gcQualityMain.defV4" :disabled="disabled" label="外观确认样是否已完成" required
                :columns="['是', '否']" />
            <van-field-Pick v-model="checkInfo.gcQualityMain.defV16" :disabled="disabled" label="封样是否已在现场，且已复核确认"
                required :columns="['是', '否']" />

            <div v-if="glshow==3||glshow==4">
                <van-field  v-model="checkInfo.gcQualityMain.defV17" :disabled="disabled" rows="1" autosize label="工艺文件及版本号" type="textarea" maxlength="150" placeholder="请输入" show-word-limit/>
                <van-field-Pick v-model="checkInfo.gcQualityMain.defV18" :disabled="disabled" label="文件是否为现行版本,且与操作一致" required :columns="['是','否']" />
                <van-field-Pick v-model="checkInfo.gcQualityMain.defV19" :disabled="disabled" label="工艺文件和BOM是否核对,且与现场工艺要求一致" required :columns="['是','否']" />
            </div>

            <van-field v-if="glshow == 2" v-model="checkInfo.gcQualityMain.defV27" :disabled="disabled" rows="1" autosize
                label="胶体密度" type="textarea" maxlength="100" placeholder="请输入胶体密度" show-word-limit />
            <van-field v-model="checkInfo.gcQualityMain.defV6" :disabled="disabled" rows="3" autosize label="异常描述"
                type="textarea" maxlength="100" placeholder="请输入异常描述（无异常请忽略）" show-word-limit />


            <van-row>
                <van-col span="12">
                    <div style="margin:10px;text-align:left;font-weight:900;">是否需要复核管制表</div>
                </van-col>
                <van-col span="12" >
                    <div style="margin:10px;"></div>
                    <van-radio-group direction="horizontal" v-model="checkInfo.gcQualityMain.checkStatus" :disabled="disabled">
                        <van-radio name="2" icon-size="24px">无需复核</van-radio>
                        <van-radio name="1" icon-size="24px">需要复核</van-radio>
                    </van-radio-group>
                </van-col>
            </van-row>


        </van-cell-group>
        <van-button type="info" v-show="ipshow" @click="reback()" style="margin:5px;width:40%;border-radius:10px;">退回
        </van-button>
        <van-button type="primary" v-show="show" @click="submit()" style="margin:5px;width:40%;border-radius:10px;">
            {{ submitText }}</van-button>
        <colloid-modal1 ref="modalForm" @ok="modalFormOk"></colloid-modal1>
    </div>
</template>

<script>
import { DatetimePicker,Toast,Indicator,MessageBox } from 'mint-ui';
import eventBus from '../common/eventBus';
import ColloidModal1 from './list/ColloidModal1.vue'
import moment from "moment";

export default ({
    components: { ColloidModal1 },
    data() {
        return {
            checkInfo: {
                gcQualityMain: {
                    moId: '',
                    defV1: '是',
                    defV2: '是',
                    defV3: '是',
                    defV4: '是',
                    defV16: '是',
                    defV18: '是',
                    defV19: '是',
                    defV28: '',
                    defV29: '',
                    key: '常规',
                    type: '0'
                }
            },
            goodsInfo: [],
            submitText: '提交',
            disabled: false,
            beginItem: {},
            show: true,
            items: {},
            type: '',
            moId: '',
            workDay: '',
            lpId: '',
            colloid: '点击获取',
            leadAppInfo: "",
            glshow:'2',
            ipshow: false,
            tankArr: [],
            bcpArr: [],
            arrbcp: [],
            jtbcp: '',
            maxDate:'',
            minDate:'',
            validDay:'',
            eMinDate:'',
            eMaxDate:'',
            validMonth:'',
            showOverlay: false,
            jt: true,
            bcp: false,
            isGBY: false,
            subClick:false,
            batchSelected:false,
            c_show:false,
            c_show_e:false,
        }

    },
    watch: {
        showOverlay() {
            this.checkInfo.gcQualityMain.defV5 = ''
            this.bcpArr.forEach(item => {
                this.checkInfo.gcQualityMain.defV5 += item + '，'
            })
            this.checkInfo.gcQualityMain.defV5 = this.checkInfo.gcQualityMain.defV5.substring(0, this.checkInfo.gcQualityMain.defV5.length - 1);
        }
    },
    created: function () {
        let self = this;
        self.items = self.$route.params.items
        self.type = self.$route.params.type
        self.lpId = self.$route.params.lpId
        self.checkFlag = self.$route.params.checkFlag
        self.checkInfo.gcQualityMain.defV28=this.formatDate(new Date)

        let nowDate = new Date();
        self.minDate = new Date(nowDate.getTime() - 700 * 24 * 3600 * 1000);
        self.eMaxDate = new Date(nowDate.getTime() + 2000 * 24 * 3600 * 1000);
        self.maxDate=nowDate
        self.eMinDate=nowDate
        
        if (self.type == '1') {
            this.search(localStorage.getItem('userCode'));
        } else {
            this.search(self.items.leader);
        }

        self.ipshow = false
        // if(self.checkFlag=='1' && self.type=='1'){
        //     self.waitforipqc();
        // }

        if (self.type == '2') {
            self.disabled = true
            self.submitText = "复核"
            self.ipshow = true
        }

        if (self.items.checkStatus == '2') {
            self.ipshow = false
            self.show = false
        }

    },
    methods: {
        onConfirm(date){
            this.c_show = false;
            this.checkInfo.gcQualityMain.defV28 = this.formatDate(date);
            this.checkInfo.gcQualityMain.defV29 = this.dayjs(this.checkInfo.gcQualityMain.defV28).add(this.validMonth,'month').add(-1,'day').format("YYYY-MM-DD")
        },
        onConfirmE(date){
            this.c_show_e = false;
            this.checkInfo.gcQualityMain.defV29 = this.formatDate(date);
        },
        setDefault(num){
            let self = this;
            if(num==1){
                self.checkInfo.gcQualityMain.defV28="N/A"
            }else{
                self.checkInfo.gcQualityMain.defV29="N/A"
            }
        },
        showHint(){
            let self=this
            MessageBox.confirm('',{
                            message: '选择还是录入？',
                            title: '提示',
                            confirmButtonText: '选择',
                            cancelButtonText: '录入'
                        })
                        .then(action => {
                            if(action=="confirm"){
                                self.showOverlay=true
                            }
                        }).catch((res)=>{
                            // console.log("res:"+res)
                            if(res=="cancel"){
                                MessageBox.prompt('请输入半成品名称/编码').then(({ value, action }) => {
                                    if(action=="confirm"){
                                        this.checkInfo.gcQualityMain.defV5=value
                                    }
                                });
                            }
                        });
        },
           jtbcpChange(value) {
            if(value=='胶体'){
                this.checkInfo.gcQualityMain.type='0'
                this.jt=true
                this.bcp=false
            }else if(value=='半成品'){
                this.bcp=true
                this.jt=false
                this.checkInfo.gcQualityMain.type='1'
            }
        },
        chooseclick() {
            this.$axios
                .get("/jeecg-boot/app/gcWorkshop/getTankList", {
                    params: {
                        productNo: this.checkInfo.gcQualityMain.code,
                        workshop: this.checkInfo.gcQualityMain.workshop,
                        lpId: this.checkInfo.gcQualityMain.lpId,
                        type: this.checkInfo.gcQualityMain.type
                    }
                })
                .then(res => {
                    if ((res.data.code = 200)) {
                        res.data.result.forEach(item => {
                            this.arrbcp = []
                            this.arrbcp.push(item.realProductName)
                        });
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.showOverlay = true
        },
        search(user) {
            let self = this;
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            console.log("user:" + user)
            self.$axios.get('/jeecg-boot/app/appQuality/getMenuList', { params: { lpId: self.lpId } }).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.leadAppInfo = res.data.result
                    self.checkInfo = self.leadAppInfo[0]

                    if(self.checkInfo.batchSelectFlag){
                        self.batchSelected=true;
                    }else{
                        self.batchSelected=false;
                    }

                    //search() 返回-1 代表不存在,存在返回下标
                    if (res.data.result[0].gcQualityMain.mitosome.search('GBY') != -1) {
                        self.isGBY = true
                        self.batchSelected=false;
                    }
                    // self.batchSelected=true;

                    self.validDay = self.checkInfo.validDay;

                    

                    if(self.checkInfo.batchNumbers!=null){

                        if(self.checkInfo.batchNumbers.length==1){
                            self.checkInfo.gcQualityMain.batchNumber=self.checkInfo.batchNumbers[0];
                        }

                    }

                    if (self.checkInfo.gcQualityMain.defV1 == null || self.checkInfo.gcQualityMain.defV1 == '') {
                        self.checkInfo.gcQualityMain.defV1 = '是'
                    }

                    if (self.checkInfo.gcQualityMain.defV2 == null || self.checkInfo.gcQualityMain.defV2 == '') {
                        self.checkInfo.gcQualityMain.defV2 = '是'
                    }

                    if (self.checkInfo.gcQualityMain.defV3 == null || self.checkInfo.gcQualityMain.defV3 == '') {
                        self.checkInfo.gcQualityMain.defV3 = '是'
                    }

                    if (self.checkInfo.gcQualityMain.defV4 == null || self.checkInfo.gcQualityMain.defV4 == '') {
                        self.checkInfo.gcQualityMain.defV4 = '是'
                    }

                    if (self.checkInfo.gcQualityMain.defV16 == null || self.checkInfo.gcQualityMain.defV16 == '') {
                        self.checkInfo.gcQualityMain.defV16 = '是'
                    }
                    if(self.checkInfo.gcQualityMain.defV18==null || self.checkInfo.gcQualityMain.defV18==''){
                        self.checkInfo.gcQualityMain.defV18='是'
                    }
                    if(self.checkInfo.gcQualityMain.defV19==null || self.checkInfo.gcQualityMain.defV19==''){
                        self.checkInfo.gcQualityMain.defV19='是'
                    }
                    if(self.checkInfo.gcQualityMain.defV28==null || self.checkInfo.gcQualityMain.defV28==''){
                        self.checkInfo.gcQualityMain.defV28='N/A'
                    }
                    // if(self.checkInfo.gcQualityMain.defV29==null || self.checkInfo.gcQualityMain.defV29==''){
                    //     self.checkInfo.gcQualityMain.defV29='N/A'
                    // }
                    if(self.checkInfo.gcQualityMain.key==null || self.checkInfo.gcQualityMain.key==''){
                        self.checkInfo.gcQualityMain.key='常规'
                    }
                    self.checkInfo.gcQualityMain.checkStatus='2'
                    self.showProduct(self.checkInfo.gcQualityMain.key)
                    self.getProductValidTime(self.checkInfo.gcQualityMain.code)
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            })
        },
        getProductValidTime(code){
            let self = this
            self.$axios.get('/jeecg-boot/app/staff/getProductValidTime', { params: { productNo: code } }).then(res => {
                if (res.data.code == 200) {
                    self.validMonth = res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        modalFormOk() {
            this.tankArr = []
            this.$axios
                .get(`/jeecg-boot/app/gcWorkshop/getTankTemp?lpId=${this.lpId}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.tankArr = res.data.result;
                        let def5 = "";
                        let str1 = "";
                        let str2 = "";
                        let productName = []
                        let customerId = []
                        if (this.tankArr.length != 0) {
                            this.tankArr.forEach(v => {
                                if (!productName.includes(v.productName)) {
                                    productName.push(v.productName)
                                }
                                if (!customerId.includes(v.customerId)) {
                                    customerId.push(v.customerId)
                                }
                            });
                            productName.forEach(v => {
                                str1 += v + '、'
                            })
                            customerId.forEach(v => {
                                str2 += v + '、'
                            })
                            str1 = str1.substring(0, str1.length - 1);
                            str2 = str2.substring(0, str2.length - 1);
                            def5 = str1 + ',' + str2
                            this.checkInfo.gcQualityMain.defV5 = def5
                        }else{
                            this.checkInfo.gcQualityMain.defV5 = ''
                        }
                        if (this.checkInfo.gcQualityMain.defV5 == ',') {
                            this.checkInfo.gcQualityMain.defV5 = ''
                        }
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        getColloidList() {
            if (this.checkInfo.gcQualityMain.type) {
                let self = this;
                if (!self.update) {
                    self.checkInfo.gcQualityMain.lpId = self.lpId
                    self.$refs.modalForm.edit(self.checkInfo.gcQualityMain, this.items.jtbcp);
                    self.$refs.modalForm.title = "半成品/胶体选择";
                    self.$refs.modalForm.disableSubmit = true;
                }
            } else {
                Toast({
                    message: "请选择胶体或半成品",
                    position: 'bottom',
                    duration: 2000
                });
            }
            // self.checkInfo.gcQualityMain.moId
            // self.$axios.get('/jeecg-boot/app/appQuality/getGoodsInfo',{params:{moId:'MO2112140038'}}).then(res=>{
            //     if(res.data.code==200){
            //     }else{
            //         Toast({
            //             message: res.data.message,
            //             position: 'bottom',
            //             duration: 2000
            //         });
            //     }
            // })
        },
        getColloidListChoose() {
            if (this.checkInfo.gcQualityMain.type) {
                let self = this;
                if (!self.update) {
                    self.checkInfo.gcQualityMain.lpId = self.lpId
                    self.$refs.modalForm.edit1(self.checkInfo.gcQualityMain);
                    self.$refs.modalForm.title = "查看已选择";
                    self.$refs.modalForm.disableSubmit = true;
                }
            } else {
                Toast({
                    message: "请选择胶体或半成品",
                    position: 'bottom',
                    duration: 2000
                });
            }
        },
        showProduct(value) {
            let self = this;
            if(value=="高露洁瓶装"){
                self.glshow=1;
            }else if(value=="高露洁美白笔"){
                self.glshow=5;
            }else if(value=="常规"){
                self.glshow=2;
            }else if(value=="D880瓶线"){
                self.glshow=3;
            }else if(value=="D880袋线"){
                self.glshow=4;
            }
        },
        reback() {
            let self = this;
            self.checkForIpqc("0");
        },
        submit() {
            let self = this;

            if(self.subClick){
                Toast({
                    message: "请勿重复点击！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            self.subClick=true

            if (self.checkInfo.gcQualityMain.moId == null || self.checkInfo.gcQualityMain.moId == '') {
                Toast({
                    message: "请选择MO单号",
                    position: 'bottom',
                    duration: 2000
                });
                self.subClick=false
                return;
            }

            if (self.glshow == 3 || self.glshow == 4) {
                if (self.checkInfo.gcQualityMain.defV17 == null || self.checkInfo.gcQualityMain.defV17 == '') {
                    Toast({
                        message: "工艺文件及版本号不能为空",
                        position: 'bottom',
                        duration: 2000
                    });
                    self.subClick=false
                    return;
                }
            }
            if (self.glshow == 2) {
                if (self.checkInfo.gcQualityMain.defV27 == null || self.checkInfo.gcQualityMain.defV27 == '') {
                    Toast({
                        message: "胶体密度不能为空",
                        position: 'bottom',
                        duration: 1500
                    });
                    self.subClick=false
                    return;
                }
            }

            if (self.checkInfo.gcQualityMain.batchNumber == null || self.checkInfo.gcQualityMain.batchNumber == '') {
                Toast({
                    message: "请输入产品批号",
                    position: 'bottom',
                    duration: 2000
                });
                self.subClick=false
                return;
            }

            if(self.checkInfo.gcQualityMain.mitosome.indexOf('Y')==-1){
                if (self.checkInfo.gcQualityMain.defV28 == null || self.checkInfo.gcQualityMain.defV28 == '') {
                    Toast({
                        message: "请输入生产日期",
                        position: 'bottom',
                        duration: 2000
                    });
                    self.subClick=false
                    return;
                }

                if (self.checkInfo.gcQualityMain.defV29 == null || self.checkInfo.gcQualityMain.defV29 == '') {
                    Toast({
                        message: "请输入截止日期",
                        position: 'bottom',
                        duration: 2000
                    });
                    self.subClick=false
                    return;
                }
            }

            self.checkInfo.gcQualityMain.creator = localStorage.getItem('userCode')

           

            if (self.type == "2") {
                self.checkForIpqc("2");
            } else {
                self.submitToIpqc();
            }


        },
        checkForIpqc(checktype) {
            let self = this;
            let params = {
                checkNo: localStorage.getItem('userCode'),
                checkStatus: checktype,
                id: self.items.id
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/appQuality/changeStatus', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.subClick=false;
                    self.$router.go(-1);
                } else {
                    Indicator.close();
                    self.subClick=false;
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        submitToIpqc() {
            let self = this;
            self.checkInfo.gcQualityMain.lpId = self.lpId
            if (self.checkInfo.gcQualityMain.checkStatus == 2) {
                self.checkInfo.gcQualityMain.checkNo = localStorage.getItem('userCode')
                self.checkInfo.gcQualityMain.checkName = localStorage.getItem('userName')
                self.checkInfo.gcQualityMain.checkTime =  moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
            }
            console.log(self.checkInfo.gcQualityMain);
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/appQuality/add', self.checkInfo.gcQualityMain).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.subClick=false;
                    self.$router.go(-1);
                } else {
                    Indicator.close();
                    self.subClick=false;
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        waitforipqc() {
            Indicator.open({
                text: '上传成功，请等待IPQC复核！',
                spinnerType: 'fading-circle'
            });
            this.timer = window.setInterval(() => {
                setTimeout(() => {
                    this.getMoStatus()
                }, 0)
            }, 3000)
        },
        getMoStatus() {
            let self = this
            self.$axios.get('/jeecg-boot/app/appQuality/getCheckStatus', { params: { lpId: self.items.lpId } }).then(res => {
                if (res.data.code == 200) {
                    if (res.data.message == '2') {
                        Indicator.close();
                        window.clearInterval(this.timer)
                    } else if (res.data.message == '0') {
                        Indicator.close();
                        window.clearInterval(this.timer)
                    }
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
})
</script>


<style scoped>
.order {
    background-color: #ebecf7;
    min-height: 100%;
}

.top_order_title {
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}

.top_msg {
    float: right;
}

.items_d {
    padding: 5%;
    height: 6rem;
}

.item_bg {
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}

.item_add {
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}

.itemTitle {
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sign {
    text-align: center;
}

.plotName {
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}

.plotCode {
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}

.plotCard {
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}

.addControl {
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}

.plotFactory {
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}

.plotWorkshop {
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}

.plotMitosome {
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}

.plotTime {
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.orderType {
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}

#peopleChorseT {
    position: absolute;
    width: 100%;
    top: 1.17rem;
    height: 0.6rem;
}

/**问题类型弹框样式 */
.picker-toolbar-title {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    background-color: #eee;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
}

.usi-btn-cancel,
.usi-btn-sure {
    color: #26a2ff;
    font-size: 16px;
}

.popup-div {
    width: 100%;
}

.pro-report {
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}

.sc_date {
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width: 64%;
    border-radius: 10px;
}

.rq_date {
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}

.date_work {
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}

.right_jt {
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}

.pool {
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom: 5%;
    font-size: 1rem;
    width: 90%;
}

.zbPool {
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.ybPool {
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.mid {
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}

.midPool {
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}

/deep/ .van-field__label {
    width: 10em;
}

/deep/.van-cell__title {
    text-align: left;
}
</style>