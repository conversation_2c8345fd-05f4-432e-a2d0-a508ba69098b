<template>
    <div>
        <van-cell-group>
            <van-field v-model="info.name" readonly label="名称" />
            <van-field v-model="info.brand" readonly label="品牌" />
            <van-field v-model="info.factory" readonly label="厂家" />
            <van-field v-model="info.spec" readonly label="规格" />
            <van-field v-model="info.model" readonly label="型号" />
            <van-field v-model="info.machine" readonly label="所属设备" />
            <van-field v-model="info.code" readonly label="备件编码" />
            <van-field v-model="info.category" readonly label="分类" />
            <van-field v-model="info.stock" readonly label="现存量" />
        </van-cell-group>
        <van-divider>请填写信息</van-divider>
        <van-form @submit="submit">

            <van-field v-model="info.customerId" label="批次号" />
            <van-field v-model="info.count" type="digit" label="数量" />
            <van-field v-model="remarks" label="原因" />

            <van-field name="radio" label="单选框">
                <template #input>
                    <van-radio-group v-model="info.radio" label="是否是设备" direction="horizontal" @change="raidoChange">
                        <van-radio name="1">是</van-radio>
                        <van-radio name="0">否</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="info.radio == 1" v-model="info.machineNo" label="设备编号" @blur="blur" right-icon="scan"
                @click-right-icon='scanQR' />
            <van-field v-model="info.machineName" label="设备名称" :readonly="info.radio == 1" />
            <van-field v-show="info.radio == 1" v-model="info.jobCenter" label="工作中心" readonly />
            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>


        <!-- <van-button round type="info" @click="submit" style="width:90%;margin-top:3%;">确定</van-button> -->
    </div>
</template>
<!-- /ncApp/partsUsage -->
<script>
import { Toast } from "vant";
let wx = window.wx
export default {
    data() {
        return {
            code: '',
            count: '',
            jobCenter: '',
            remarks: '',
            info: {},
        }
    },
    created() {
        this.code = this.$route.params.code
        this.$axios.get('/jeecg-boot/ncApp/parts/getPartsInfo', { params: { code: this.code } }).then(res => {
            if (res.data.code == 200) {
                this.info = res.data.result
            } else {
                Toast({
                    message: res.data.message,
                    position: 'bottom',
                    duration: 2000
                });
                this.$router.replace({ name: "SpareSetup" })
            }
        })
    },
    methods: {
        raidoChange(e) {
            console.log(e);
            this.info.radio = e
        },
        scanQR() {
            console.log('扫码');
            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function (res) {
                    // 回调
                    let result = res.resultStr;//当needResult为1时返回处理结果
                    this.$axios.get('/jeecg-boot/ncApp/parts/getMachineInfo', { params: { id: result } }).then(res => {
                        if (res.data.code == 200) {
                            this.$set(this.info, 'jobCenter', res.data.result.jobCenter)
                            this.$set(this.info, 'machineName', res.data.result.deviceName)
                            this.$set(this.info, 'machineNo', res.data.result.machineNo)
                        } else {
                            this.$set(this.info, 'jobCenter', '')
                            this.$set(this.info, 'machineName', '')
                            this.$set(this.info, 'machineNo', '')
                            Toast({
                                message: '设备编号错误',
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })

                },
                error: function (res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        Toast({
                            message: '版本过低请升级',
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }
            });
        },
        submit() {
            this.info.remarks = this.remarks
            this.info.partsId = this.info.id
            this.info.creator = localStorage.getItem('userCode')
            this.info.type = 9
            let params = this.info
            if (this.info.radio == 1) {
                if (this.info.machineName == '' ||
                    this.info.machineName == null ||
                    this.info.machineName == undefined ||
                    this.info.machineNo == '' ||
                    this.info.machineNo == null ||
                    this.info.machineNo == undefined ||
                    this.info.jobCenter == '' ||
                    this.info.jobCenter == null ||
                    this.info.jobCenter == undefined ||
                    this.info.count == '' ||
                    this.info.count == null ||
                    this.info.count == undefined ||
                    this.info.remarks == '' ||
                    this.info.remarks == null ||
                    this.info.remarks == undefined) {
                    Toast({
                        message: '请填写数量、工作中心、设备名称、设备编号和原因',
                        position: 'bottom',
                        duration: 2000
                    });
                } else {
                    if (this.info.stock * 1 >= this.info.count * 1) {
                        this.$axios.post('/jeecg-boot/ncApp/partsUsage/add', params).then(res => {
                            if (res.data.code == 200) {
                                this.$router.replace({ name: "SpareSetup" })
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    } else {
                        Toast({
                            message: '现存量不足',
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }
            } else {
                if (this.info.machineName == '' ||
                    this.info.machineName == null ||
                    this.info.machineName == undefined ||
                    this.info.count == '' ||
                    this.info.count == null ||
                    this.info.count == undefined ||
                    this.info.remarks == '' ||
                    this.info.remarks == null ||
                    this.info.remarks == undefined) {
                    Toast({
                        message: '请填写数量、设备名称和原因',
                        position: 'bottom',
                        duration: 2000
                    });
                } else {
                    if (this.info.stock * 1 >= this.info.count * 1) {
                        this.$axios.post('/jeecg-boot/ncApp/partsUsage/add', params).then(res => {
                            if (res.data.code == 200) {
                                this.$router.replace({ name: "SpareSetup" })
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    } else {
                        Toast({
                            message: '现存量不足',
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }
            }

        },
        blur(e) {
            console.log(e.target.value);
            this.$axios.get('/jeecg-boot/ncApp/parts/getMachineInfo', { params: { code: e.target.value } }).then(res => {
                if (res.data.result) {
                    this.$set(this.info, 'jobCenter', res.data.result.jobCenter)
                    this.$set(this.info, 'machineName', res.data.result.deviceName)
                    console.log(this.info);
                } else {
                    this.$set(this.info, 'jobCenter', '')
                    this.$set(this.info, 'machineName', '')
                    Toast({
                        message: '设备编号错误',
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
    },
}
</script>

<style scoped></style>