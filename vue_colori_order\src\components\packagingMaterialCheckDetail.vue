<template>
    <div style="text-align: left; background-color: #f1f1f1">
        <van-sticky :offset-top="0">
            <van-nav-bar title="包材校验" right-text="手动录入" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <van-button type="info" @click="check" style="width: 100%">
                扫码校验(共{{ allNum }}个,剩余{{ residueNum }}个)
            </van-button>
        </van-sticky>
        <div style="width: 100%; height: 100%; overflow: auto">
            <div v-for="(item, index) in dataArr" :key="index" style="
            text-align: left;
            background-color: #fff;
            padding: 3%;
            border-radius: 10px;
            width: 95%;
            margin: 0.3rem auto;
            margin-bottom: 3%;
          ">
                <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                    <van-row>
                        <van-col span="20">
                            <span style="font-size: 18px; font-weight: 700; color: #000">
                                {{ item.productName }}
                            </span>
                        </van-col>
                        <van-col span="4">
                            <span v-if="item.status == 2" style="color: #66cc00">
                                已校验
                            </span>
                            <span v-else style="color: #ff0033"> 未校验 </span>
                        </van-col>
                    </van-row>
                </div>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> ID：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.id }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 账套：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.bookName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 仓库：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.storeName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6"> 编码：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.productNo }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color: gary">
                        <van-row>
                            <van-col span="6">货位：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.rackName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="8"> 客户批次号：</van-col>
                            <van-col span="16">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.customerBatchCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="8"> NCC批次号：</van-col>
                            <van-col span="16">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.nccBatchCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 供应商：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.supplier }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 过期时间：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.expiryDay }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 生产日期：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.workDay }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 库存状态：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    <span style="color:gray" v-if='item.stockStatus == 1'>待检</span>
                                    <span style="color:green" v-if='item.stockStatus == 2'>合格</span>
                                    <span style="color:red" v-if='item.stockStatus == 3'>不合格</span>
                                    <span style="color:gray" v-if='item.stockStatus == 4'>跟踪质检</span>
                                    <span style="color:gray" v-if='item.stockStatus == 5'>返工</span>
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 备注：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.remark }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 数量：</van-col>
                            <van-col span="18">
                                <span style="
                      color: black;
                      width: 100%;
                      word-wrap: break-word;
                      word-break: break-all;
                      overflow: hidden;
                    ">
                                    {{ item.mainQuantity }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <!-- <van-row>
                    <van-col span="20"></van-col>
                    <van-col span="4"><van-button type="primary" @click="check1(item)"
                            size="small">复核</van-button></van-col>
                </van-row> -->
            </div>
        </div>
    </div>
</template>

<script>
import { DatetimePicker, Indicator, MessageBox } from "mint-ui";
import { Toast } from "vant";
export default {
    data() {
        return {
            // 配料单ID
            id: "",
            userCode: localStorage.getItem("userCode"),
            dataArr: [],
            // 共有
            allNum: 0,
            // 剩余
            residueNum: 0,
            matCheckFlag: 0,
        };
    },
    created() {
        this.matCheckFlag = localStorage.getItem("matCheckFlag");
        this.id = this.$route.params.id;
        if (this.$route.params) {
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle",
            });
            this.$axios
                .get(
                    `/jeecg-boot/app/wmsUsage/getWmsUsageList?tpId=${this.$route.params.id}`
                )
                .then((res) => {
                    if (res.data.code == 200) {
                        this.residueNum = 0;
                        console.log(res.data.result);
                        this.dataArr = res.data.result;
                        this.allNum = this.dataArr.length;
                        this.dataArr.forEach((item) => {
                            if (item.status != 2) {
                                this.residueNum++;
                            }
                        });

                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000,
                        });
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        } else {
            this.$route.go(-1);
        }
    },

    methods: {
        unlock() {
            Toast.success("已解锁");
            localStorage.setItem("matCheckFlag", "1");
            this.matCheckFlag = localStorage.getItem("matCheckFlag");
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        // 扫码
        check() {
            let self = this;
            wx.scanQRCode({
                desc: "scanQRCode desc",
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function (res) {
                    // 回调
                    var result = res.resultStr; //当needResult为1时返回处理结果
                    // 处理结果 result
                    self.checkRequest(result);
                },
                error: function (res) {
                    if (res.errMsg.indexOf("function_not_exist") > 0) {
                        alert("版本过低请升级");
                    }
                },
            });
        },
        onClickRight() {
            MessageBox.prompt("请录入ID").then(({ value, action }) => {
                if (action == "confirm") {
                    if (!value) {
                        Toast.fail("不能为空");
                        return;
                    } else {
                        this.checkRequest(value);
                    }
                }
            });
        },
        check1(item){
            this.checkRequest(item.id) 
        },
        // 校验请求
        checkRequest(code) {
            let obj = {};
            let flag = false;
            const that = this
            this.dataArr.forEach((item) => {
                if (code == item.id) {
                    obj = item;
                    flag = true;
                }
            });
            if (!flag) {
                Toast.fail("未找到");
                return;
            }
            if (obj.status == 2) {
                Toast.fail("已校验");
                return;
            }
            this.$axios
                .get(`/jeecg-boot/app/wmsUsage/checkUsageQms013?id=${code}`)
                .then((res) => {
                    if (res.data.code == 500) {
                        MessageBox.confirm(res.data.message).then(action => {
                            if (action == 'confirm') {
                                goCheck()
                            }
                        }).catch(() => {
                            Toast.fail("取消");
                        });
                    } else if (res.data.code == 200) {
                        goCheck()
                    }
                })
                .catch(() => {
                    Toast.fail("请求失败");
                    return false;
                });
            function goCheck() {
                obj.userCode = localStorage.getItem("userCode");
                obj.userName = localStorage.getItem("userName");
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle",
                });
                that.$axios
                    .get(`/jeecg-boot/app/wmsUsage/checkWmsUsageInfo?id=${obj.id}&userCode=${obj.userCode}&userName=${obj.userName}`)
                    .then((res) => {
                        if (res.data.code == 200) {
                            // 校验成功刷新列表
                            Indicator.open({
                                text: "正在加载中，请稍后……",
                                spinnerType: "fading-circle",
                            });
                            that.$axios
                                .get(
                                    `/jeecg-boot/app/wmsUsage/getWmsUsageList?tpId=${that.$route.params.id}`
                                )
                                .then((res) => {
                                    if (res.data.code == 200) {
                                        that.residueNum = 0;
                                        console.log(res.data.result);
                                        that.dataArr = res.data.result;
                                        that.allNum = that.dataArr.length;
                                        that.dataArr.forEach((item) => {
                                            if (item.status != 2) {
                                                that.residueNum++;
                                            }
                                        });
                                    } else {
                                        Toast({
                                            message: res.data.message,
                                            position: "bottom",
                                            duration: 2000,
                                        });
                                    }
                                })
                                .finally(() => {
                                    Indicator.close();
                                });
                            if (that.residueNum == that.allNum * 1 - that.residueNum * 1) {
                                Toast.success(res.data.message + ",全部校验完成");
                            } else {
                                console.log(that.residueNum * 1);
                                let aaa =
                                that.allNum * 1 - (that.allNum * 1 - that.residueNum * 1) - 1;
                                Toast.success(res.data.message + ",还剩" + aaa + "未校验");
                            }
                        } else {
                            Toast.fail(res.data.message);
                        }
                    })
                    .finally(() => {
                        Indicator.close();
                    });
            }

        },
    },
};
</script>

<style scoped></style>