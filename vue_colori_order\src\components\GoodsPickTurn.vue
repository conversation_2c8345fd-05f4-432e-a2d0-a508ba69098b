<template>
  <div style="width:100%;height:100%;text-align:left;background-color:#F1F1F1;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="转单" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-field v-model="info.checkoutNo" label="单号" readonly />
    <van-field v-model="info.type" label="单据类型" readonly />
    <van-field v-model="info.customer" label="客户名称" readonly />
    <van-field v-model="info.address" label="客户地址" readonly />

    <van-field
      readonly
      clickable
      name="picker"
      v-model="info.picker"
      label="接单人："
      placeholder="点击选择接单人"
      @click="showpickerPicker = true"
    />
    <van-popup v-model="showpickerPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="pickerColumns"
        @confirm="pickerConfirm"
        @cancel="showpickerPicker = false"
      />
    </van-popup>

    <div style="height: 2rem; width: 100%">&nbsp;</div>
    <van-row
      style="
        background-color:#F1F1F1; 
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;"
    >
      <van-col span="4"> </van-col>
      <van-col span="11"></van-col>
      <van-col span="8">
        <van-button
          round
          style="height: 25px"
          type="info"
          size="large"
          @click="submitTurn"
          loading-type="spinner"
        >
          转单
        </van-button>
      </van-col>
    </van-row>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  data() {
    return {
      info: {},
      //待转数组
      dataArr1: [],
      //转单接收人
      checkerColumns: [],
      //   转单显示
      showcheckerPicker: false,
      clickIndex: "",
      showpickerPicker: false,
      pickerColumns: []
    };
  },
  created() {
    if (this.$route.params) {
      this.info = this.$route.params;
      this.$axios
        .get(`/jeecg-boot/app/warehousePeople/getCheckerInfo?type=3`)
        .then(res => {
          if (res.data.success) {
            res.data.result.forEach(item => {
              this.pickerColumns.push(item.userName + "-" + item.userCode);
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    } else {
      this.$router.go(-1);
    }
  },
  methods: {
    pickerConfirm(value) {
      this.info.picker = value;
      this.info.pickerNo = value.split("-")[1];
      this.info.pickerName = value.split("-")[0];
      this.showpickerPicker = false;
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    showcheckerPickerFn(item, index) {
      this.clickIndex = index;
      this.showcheckerPicker = true;
    },

    checkerConfirm(value) {
      this.dataArr1[this.clickIndex].checker = value;
      this.dataArr1[this.clickIndex].checkerName = value.split("-")[0];
      this.dataArr1[this.clickIndex].checkerNo = value.split("-")[1];
      this.showcheckerPicker = false;
    },
    submitTurn() {
      let arr = [];
      this.dataArr1.forEach(item => {
        if (item.checker) {
          arr.push(item);
        }
      });
      this.info.detailList = arr;
      this.$axios
        .get(`/jeecg-boot/app/warehousePick/getPickPickerChange?id=${this.info.id}&userCode=${localStorage.getItem("userCode")}&pickerNo=${this.info.pickerNo}&pickerName=${this.info.pickerName}`)
        .then(res => {
          if (res.data.code == 200) {
            this.$router.go(-1);
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    }
  }
};
</script>

<style scoped></style>
