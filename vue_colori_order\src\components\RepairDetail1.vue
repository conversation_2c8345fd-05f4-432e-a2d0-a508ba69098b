<template>
    <div style="text-align:left;">
        <van-nav-bar v-show="active == 0 && status != 2" title="工单详情" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 1 && status != 2" title="工单详情" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 2 && status != 2" title="辅助维修工" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 0 && status == 2" title="工单详情" right-text="转单" left-arrow
            @click-left="onClickLeft" @click-right="giveOutOrder" />
        <van-nav-bar v-show="active == 1 && status == 2" title="维修记录" right-text="新增" left-arrow
            @click-left="onClickLeft" @click-right="onClickRight" />

        <!--右上角新增辅助维修工 -->
        <!-- <van-nav-bar v-show="active == 2 && status == 2" title="辅助维修工" right-text="新增" left-arrow @click-left="onClickLeft"
            @click-right="addAuxiliary" /> -->
        <van-nav-bar v-show="active == 2 && status == 2" title="辅助维修工" left-arrow @click-left="onClickLeft" />


        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">
            <!-- 是领导 填写-->
            <!-- <div v-show="isLeader == true">
                <van-field v-model="acceptNo" label="人员编号" placeholder="请输入人员编号" right-icon="search"
                    @click-right-icon='search' />
                <van-field v-model="acceptName" label="人员姓名" readonly />
            </div> -->
            <!-- 不是领导 选择领导-->

            <!-- 修改之后 --都是选择 领导选择员工列表,员工选择领导列表 -->
            <van-field readonly clickable :value="acceptName" label="接单人" placeholder="点击选择"
                @click="showPicker = true" />
            <van-popup v-model="showPicker" position="bottom">
                <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
            </van-popup>
            <van-field v-model="reason" label="转单原因：" placeholder="请输入转单原因" />

            <van-button type="info" @click="giveOut" style="width: 100%;" round>
                转单
            </van-button>
        </van-popup>
        <div v-if="status == 1">
            <van-cell-group>
                <van-cell title="维修工单号" :value="info.id" />
                <van-cell title="线体" :value="info.jobCenter" />
                <van-cell title="MO单号" :value="info.moId" />
                <van-cell title="设备名称" :value="info.machine" />
                <van-cell title="等待时长" :value="info.waitMinutes == null ? '' : info.waitMinutes + '分钟'" />
                <van-cell title="维修时长" :value="info.repairMinutes == null ? '' : info.repairMinutes + '分钟'" />
                <van-cell title="组长" :value="info.creator" />
                <van-cell title="操作工" :value="info.operatorName" />
                <van-cell title="在离线" :value="info.lineType" />
                <van-cell title="故障描述" :label="info.describe" value="" />
                <van-uploader v-if="info.pictureList.length > 0" v-model="imgList" disabled :deletable="false"
                    :show-upload="false" />
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button v-if="status == 1" round block type="info" @click="accept">接单</van-button>
                <van-button v-if="status == 2" round block type="info" @click="over">关单</van-button>
            </div>
        </div>
        <div v-else>
            <van-tabs v-model="active" color="#1989fa" sticky>
                <van-tab title="工单详情">
                    <van-cell-group>
                        <van-cell title="维修工单号" :value="info.id" />
                        <van-cell title="线体" :value="info.jobCenter" />
                        <van-cell title="MO单号" :value="info.moId" />
                        <van-cell title="设备名称" :value="info.machine" />
                        <van-cell title="等待时长" :value="info.waitMinutes == null ? '' : info.waitMinutes + '分钟'" />
                        <van-cell title="维修时长" :value="info.repairMinutes == null ? '' : info.repairMinutes + '分钟'
            " />
                        <van-cell title="分配时长" :value="info.allocateMinutes == null
                ? ''
                : info.allocateMinutes + '分钟'
            " />
                        <van-cell title="损失时长" :value="info.lossMinutes == null ? '' : info.lossMinutes + '分钟'" />
                        <van-cell title="组长" :value="info.creator" />
                        <van-cell title="操作工" :value="info.operatorName" />
                        <van-cell title="在离线" :value="info.lineType" />
                        <van-cell title="接单人" :value="info.leader" />
                        <van-cell title="故障描述" :label="info.describe" value="" />
                        <van-uploader v-if="info.pictureList.length > 0" v-model="imgList" disabled :deletable="false"
                            :show-upload="false" />
                    </van-cell-group>
                    <div style="margin: 16px;">
                        <div v-if="info.arriveTime == null">
                            <van-button v-if="userCode == info.createNo && info.detailList == null" round block
                                type="info" @click="checkArrive" style="margin-bottom: 10px;">
                                确认到达
                            </van-button>
                            <van-button v-if="info.detailList != null && status != 0" round block type="info"
                                @click="over">关单
                            </van-button>
                        </div>
                        <div v-else>
                            <div v-if="info.spStatus == null">
                                <van-button v-if="status == 1" round block type="info" @click="accept">接单</van-button>
                                <van-button v-if="status == 2" round block type="info" @click="over">关单</van-button>
                            </div>
                            <div v-else>
                                <van-button v-if="info.spStatus == 1" round block type="info" disabled ghost>审批中
                                </van-button>
                                <van-button v-if="info.spStatus == 0" round block type="info" @click="over">已驳回,点击再次关单
                                </van-button>
                            </div>
                        </div>
                    </div>
                    <!-- <div style="margin: 16px;">
                        <div v-if="department != '设备'">
                            <van-button v-if="info.detailList == null" round block type="info" @click="checkArrive"
                                style="margin-bottom: 10px;">
                                确认到达
                            </van-button>
                        </div>
                        <div v-if="department == '设备'">
                            <div v-if="info.spStatus == null">
                                <van-button v-if="status == 1" round block type="info" @click="accept">接单</van-button>
                                <van-button v-if="status == 2 && info.detailList != null" round block type="info"
                                    @click="over">关单</van-button>
                            </div>
                            <div v-else>
                                <van-button v-if="info.spStatus == 1" round block type="info" disabled ghost>审批中
                                </van-button>
                            </div>
                        </div>
                    </div> -->
                </van-tab>
                <van-tab title="维修记录">
                    <div style="padding:3%;">
                        <div v-for="(item, index) in info.detailList" :key="index" @click="goDetail(item)"
                            style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 3%;">
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                                    是否调试：{{ item.isDebug }}
                                </p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                                    是否更换配件：{{ item.isReplace }}
                                </p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                                    是否人为：{{ item.isArtificial }}
                                </p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                                    维修情况：{{ item.maintenance }}
                                </p>
                            </div>
                            <div style="">

                                <div v-for="(item, index) in item.partsList" :key="index">
                                    <p style="width:60%;overflow-x: auto;white-space:nowrap;">
                                        备件{{ index+1 }}
                                    </p>
                                    <p>
                                        备件名称:{{ item.name }}
                                    </p>
                                    <p>
                                        备件编码:{{ item.code }}
                                    </p>
                                    <p>
                                        备件数量:{{ item.partsCount }}
                                    </p>
                                    <p>
                                        生产日期:{{ item.workDay }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </van-tab>
                <van-tab title="辅助维修工">
                    <div v-for="item in allAuxiliaryList" :key="item.name">
                        <van-swipe-cell>
                            <van-cell :border="false" :title="item.userCode + '-' + item.userName"
                                :value="item.status == 1 ? '已加入' : item.status == 0 ? '未加入' : status == 0 ? '已结束' : '已离开'" />
                            <template #right v-if="status != 0">
                                <van-button @click="outPeople(item)" v-if="item.status == 1" square type="danger"
                                    text="结束" />
                                <van-button @click="inPeople(item)" v-else square type="primary" text="加入" />
                            </template>
                        </van-swipe-cell>
                    </div>
                </van-tab>
            </van-tabs>
        </div>
        <PeopleModal ref="modalForm" @ok="modalFormOk"></PeopleModal>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator } from "mint-ui";
import PeopleModal from "./list/PeopleModal.vue";
export default {
    components: { PeopleModal },
    data() {
        return {
            // beginDay: this.getDate(-300),
            // endDay: this.getDate(0),
            active: 0,
            // 0 已完成 1未接单 2已接单
            status: "",
            // 工单信息
            info: {},
            // 图片
            imgList: [],
            // 转单 弹框显隐
            show: false,
            // 转单 判断是否是领导
            isLeader: false,
            // 转单原因
            reason: "",
            // 领导列表
            leaderList: [],
            // 接单人 姓名
            acceptName: "",
            // 接单人 工号
            acceptNo: "",
            // 领导名字列表
            columns: [],
            // 选择领导弹框显隐
            showPicker: false,
            // 登录人
            userName: localStorage.getItem("userName"),
            // 登录人ID
            userCode: localStorage.getItem("userCode"),
            // 部门
            department: localStorage.getItem("department"),
            // 已存在的 辅助维修工
            atAuxiliaryList: [],
            // 所有 辅助维修工
            allAuxiliaryList: [],
            lpId: '',
            id: '',
        };
    },
    created() {
        this.status = this.$route.params.status;
        this.lpId = this.$route.params.item.lpId
        this.id = this.$route.params.item.id
        if (this.$route.params.active) {
            this.active = this.$route.params.active;
        }
        this.info = JSON.parse(localStorage.getItem("repair"));
        console.log(this.info);
        if (this.status == "2") {
            this.$axios.get(`/jeecg-boot/app/mac/getAppRecordList?id=${this.$route.params.item.id}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.info = res.data.result[0];
                    } else {
                        Toast({
                            message: "失败",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
        this.info.pictureList.forEach(item => {
            this.imgList.push({ url: item.picUrl });
        });
        // 判断登录人是否是领导
        this.$axios
            .get(
                `/jeecg-boot/app/mac/getIsLeader?userCode=${localStorage.getItem(
                    "userCode"
                )}`
            )
            .then(res => {
                if (res.data.code == 200) {
                    // 重置columns
                    this.columns = [];
                    if (res.data.message == 0) {
                        this.isLeader = false;
                        // 不是领导 获取领导列表
                        this.$axios
                            .get(`/jeecg-boot/app/mac/getLeaderList?userCode=${localStorage.getItem("userCode")}`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.leaderList = res.data.result;
                                    this.leaderList.forEach(item => {
                                        this.columns.push(item.lead);
                                    });
                                } else {
                                    Toast({
                                        message: "查询失败",
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        this.isLeader = true;
                        // 是领导 获取员工列表
                        this.$axios
                            .get(
                                `/jeecg-boot/app/mac/getWorkerList?userCode=${localStorage.getItem(
                                    "userCode"
                                )}`
                            )
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.leaderList = res.data.result;
                                    this.leaderList.forEach(item => {
                                        this.columns.push(item.lead);
                                    });
                                } else {
                                    Toast({
                                        message: "查询失败",
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    }
                } else {
                    Toast({
                        message: "查询失败",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });

        // this.$axios
        //     .get(
        //         `/jeecg-boot/app/mac/getRepairAssist?id=${this.$route.params.item.id}`
        //     )
        //     .then(res => {
        //         if (res.data.code == 200) {
        //             this.atAuxiliaryList = res.data.result;
        //         } else {
        //             Toast({
        //                 message: res.data.message,
        //                 position: "bottom",
        //                 duration: 2000
        //             });
        //         }
        //     });
        this.getallAuxiliaryList(this.$route.params.item.lpId, this.$route.params.item.id)
    },
    methods: {
        getallAuxiliaryList(id, repairId) {
            this.$axios
                .get(
                    `/jeecg-boot/app/mac/getRepairClass?id=${id}&repairId=${repairId}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.allAuxiliaryList = res.data.result;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        outPeople(item) {
            let params = {
                repairId: this.id,
                userCode: item.userCode,
                userName: item.userName,
                type: 2
            }
            this.$axios
                .post(`/jeecg-boot/app/mac/addRepairAssist`, params)
                .then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.getallAuxiliaryList(this.lpId, this.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        inPeople(item) {
            let params = {
                repairId: this.id,
                userCode: item.userCode,
                userName: item.userName,
                type: 1
            }
            this.$axios
                .post(`/jeecg-boot/app/mac/addRepairAssist`, params)
                .then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.getallAuxiliaryList(this.lpId, this.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        //新增辅助维修工
        addAuxiliary() {
            let self = this
            if (localStorage.getItem("userCode") == this.info.leaderNo) {
                self.$refs.modalForm.edit(this.lpId, this.id);
                self.$refs.modalForm.title = "添加辅助维修工";
                self.$refs.modalForm.disableSubmit = true;
            } else {
                Toast({
                    message: "不能操作非自己的工单",
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        //添加成功回调
        modalFormOk(item, type) {

        },

        // 确认到达
        checkArrive() {
            this.$axios
                .get(
                    `/jeecg-boot/app/mac/checkArrive?userCode=${localStorage.getItem(
                        "userCode"
                    )}&id=${this.info.id}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.info.arriveTime = res.data.result;
                        // this.$router.replace({
                        //     name: "RepairOrder",
                        // });
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 关单时 输入userCode 搜寻人员名称
        search() {
            this.$axios
                .get(
                    `/jeecg-boot/app/utils/getStaffNameByCode?userCode=${this.acceptNo}`
                )
                .then(res => {
                    if (res.data.message != null) {
                        console.log(res.data.message);
                        this.acceptName = res.data.message;
                    } else {
                        Toast({
                            message: "人员编号错误,请重新输入",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 转单 弹框显示
        giveOutOrder() {
            if (localStorage.getItem("userCode") == this.info.leaderNo) {
                if (this.info.spStatus == 1 || this.info.spStatus == 2) {
                    Toast({
                        message: "该工单不能转单",
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    if (this.info.changeStatus == 1) {
                        this.show = true;
                    } else {
                        Toast({
                            message: "已达转单次数上限,不能转单",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                }
            } else {
                Toast({
                    message: "不能操作非自己的工单",
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        // 转单 提交
        giveOut() {
            if (
                this.acceptNo != null &&
                this.acceptName != null &&
                this.reason != null
            ) {
                this.$axios
                    .get(
                        `/jeecg-boot/app/mac/giveOutOrder?id=${this.info.id}&leader=${this.info.leader}&leaderNo=${this.info.leaderNo}&acceptNo=${this.acceptNo}&acceptName=${this.acceptName}&reason=${this.reason}`
                    )
                    .then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: "转单成功",
                                position: "bottom",
                                duration: 2000
                            });
                            this.show = false;
                        } else {
                            Toast({
                                message: "转单失败",
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            } else {
                Toast({
                    message: "请选择转单人,填写转单原因",
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        //接单
        accept() {
            Dialog.confirm({
                message: "确定接单吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then(() => {
                    if (localStorage.getItem("department") == "设备") {
                        Indicator.open({
                            text: "处理中，请稍后……",
                            spinnerType: "fading-circle"
                        });
                        this.$axios
                            .get(
                                `/jeecg-boot/app/mac/changeOrders?id=${this.info.id
                                }&status=2&leader=${localStorage.getItem(
                                    "userName"
                                )}&leaderNo=${localStorage.getItem("userCode")}`
                            )
                            .then(res => {
                                if (res.data.code == 200) {
                                    Indicator.close();
                                    Toast({
                                        message: "接单成功",
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.$router.push({
                                        name: "RepairOrder"
                                    });
                                } else {
                                    Indicator.close();
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        Toast({
                            message: "非设备部人员不能接单",
                            position: "bottom",
                            duration: 1000
                        });
                    }
                })
                .catch(() => {
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
        // 关单
        over() {
            if (localStorage.getItem("userCode") == this.info.leaderNo) {
                let flag = false;
                if (this.info.detailList) {
                    this.info.detailList.forEach(item => {
                        if (item.maintenance == "已修好") {
                            flag = true;
                        } else if (item.maintenance == "委外维修") {
                            flag = true;
                        }
                    });
                }
                if (flag) {
                    Dialog.confirm({
                        message: "确定关单吗?",
                        theme: "round-button",
                        confirmButtonColor: "#1989fa",
                        cancelButtonColor: "#CCCCCC"
                    }).then(() => {
                        Indicator.open({
                            text: "处理中，请稍后……",
                            spinnerType: "fading-circle"
                        });
                        this.$axios
                            .get(
                                `/jeecg-boot/app/mac/changeOrders?id=${this.info.id}&status=0`
                            )
                            .then(res => {
                                if (res.data.code == 200) {
                                    Indicator.close();
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.$router.push({
                                        name: "RepairOrder"
                                    });
                                } else {
                                    Indicator.close();
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    });
                } else {
                    Toast({
                        message: "设备未修好，不能关单",
                        position: "bottom",
                        duration: 1500
                    });
                }
            } else {
                Toast({
                    message: "不能操作非自己的工单",
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        //维修记录 新增
        onClickRight() {
            if (this.info.arriveTime == null) {
                // 如果没有到达时间，看是否有维修记录，有的话就继续可以新增维修记录
                if (this.info.detailList != null) {
                    if (localStorage.getItem("userCode") == this.info.leaderNo) {
                        if (this.info.spStatus == 1 || this.info.spStatus == 2) {
                            Toast({
                                message: "该工单不能新增维修记录",
                                position: "bottom",
                                duration: 2000
                            });
                        } else {
                            // 如果工单已修好,不能新增维修记录
                            let info = this.info;
                            let maintenance = false;
                            if (this.info.detailList) {
                                this.info.detailList.forEach(item => {
                                    if (item.maintenance == "已修好") {
                                        maintenance = true;
                                    }
                                });
                            }

                            if (maintenance) {
                                Toast({
                                    message: "该工单已修好",
                                    position: "bottom",
                                    duration: 2000
                                });
                            } else {
                                this.$router.push({
                                    name: "RepairAdd",
                                    params: { info }
                                });
                            }
                        }
                    } else {
                        Toast({
                            message: "不能操作非自己的工单",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                } else {
                    Toast({
                        message: "未确认到达不能新增维修记录",
                        position: "bottom",
                        duration: 2000
                    });
                }
            } else {
                if (localStorage.getItem("userCode") == this.info.leaderNo) {
                    if (this.info.spStatus == 1 || this.info.spStatus == 2) {
                        Toast({
                            message: "该工单不能新增维修记录",
                            position: "bottom",
                            duration: 2000
                        });
                    } else {
                        // 如果工单已修好,不能新增维修记录
                        let info = this.info;
                        let maintenance = false;
                        if (this.info.detailList) {
                            this.info.detailList.forEach(item => {
                                if (item.maintenance == "已修好") {
                                    maintenance = true;
                                }
                            });
                        }

                        if (maintenance) {
                            Toast({
                                message: "该工单已修好",
                                position: "bottom",
                                duration: 2000
                            });
                        } else {
                            this.$router.push({
                                name: "RepairAdd",
                                params: { info }
                            });
                        }
                    }
                } else {
                    Toast({
                        message: "不能操作非自己的工单",
                        position: "bottom",
                        duration: 2000
                    });
                }
            }
        },
        onClickLeft() {
            this.$router.push({
                name: "RepairOrder"
            });
        },
        getDate(day) {
            var date1 = new Date(),
                time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
        },
        // 维修记录 详情
        goDetail(item) {
            this.$router.push({
                name: "RepairDetail2",
                params: { item, info: this.info, status: this.status }
            });
        },
        // 选择领导确认方法
        onConfirm(value) {
            this.acceptName = value;
            this.showPicker = false;
            this.leaderList.forEach(item => {
                if (this.acceptName == item.lead) {
                    this.acceptNo = item.leaderNo;
                }
            });
            console.log(this.acceptName, this.acceptNo);
        }
    }
};
</script>

<style scoped></style>
