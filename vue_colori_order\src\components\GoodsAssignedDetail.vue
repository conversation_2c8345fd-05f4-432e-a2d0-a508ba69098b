<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="指派" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <!-- <van-field v-model="info.customer" label="客户" readonly/>
        <van-field v-model="info.type" label="单据类型" readonly/>
        <van-field v-model="info.checkoutNo" label="单据号" readonly/>
        <van-field v-model="info.totalCount" label="数量" readonly/>
        <van-field v-model="info.address" label="地址" readonly/>
        <van-field v-model="info.workDay" label="开单日期" readonly/>
        <van-field v-model="info.leader" label="调度员" readonly/> -->
    </div>
</template>

<script>
import { Toast } from 'mint-ui';
export default {
    data() {
        return {
            info: {},
        }
    },
    created() {
        if (this.$route.params) {
            this.info = this.$route.params
            console.log(this.$route.params);
        } else {
            this.$router.go(-1)
        }
    },
    methods: {

        onClickLeft() {
            this.$router.go(-1)
        },
    },
}
</script>

<style scoped>

</style>