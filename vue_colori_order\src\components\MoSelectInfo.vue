<template>
    <div style="background:#F0F0F0;min-height:100%">
        <div v-for="(item,index) in colloidList" :key="index" style="text-align:left;padding:5px;margin:5px;background:#FFF;" @click="selectItem(item)">
            <div style="width:100%">
                <div style="float:left;font-size:16px;font-weight:800;width:70%;color:black;">{{item.moId}}</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:green" v-if="item.matchFlag=='1'">匹配</div>
                <div style="float:left;font-size:16px;font-weight:800;width:30%;text-align:right;color:red" v-else>不符</div>
                <div style="clear:both"></div>
            </div>
            
            <div style="background:#f0f0f0;height:1px;width:100%"></div>

            <div style="width:100%;font-size:14px;color:#888888">产品编号：{{item.code}}</div>
            <div style="width:100%;font-size:14px;color:#888888">产品名称：{{item.name}}</div>
            <div style="width:100%;font-size:14px;color:#888888">线体组长：{{item.leaderNo}}</div>
            <div style="width:100%;font-size:14px;color:#888888">工作中心：{{item.mitosome}}</div>


            <div style="clear:both"></div>
        </div>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox,Indicator } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            colloidList:[{
                tankNo:'',
                code:'',
                name:'',
                customer:'',
                volume:'',
                pullUser:'',
                receiver:'',
                checker:'',
                status:'',
                pullStatus:''
            }],
            jobCenter:"",
            code:"",
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.jobCenter=this.$route.params.jobCenter
        this.code=this.$route.params.code
        this.result=this.$route.params.result

        this.getMoInfoByJobCenter();
    },
    methods: {
        getMoInfoByJobCenter(){
            let self=this
            Indicator.open({
                text: '数据加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/tank/check/getMoInfoByJobCenter',{params:{jobCenter:self.jobCenter,code:self.code}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.colloidList=res.data.result;
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        selectItem(item){
            console.log(item);

            if(item.matchFlag=='2'){
                Toast({
                    message: "当前订单与该胶体不符，无法加料！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }else{
                this.$route.params.moItem = item;
                this.$route.params.result = this.result;
                this.$router.back();
            }


        },
    }
}
</script>
<style scoped>

</style>