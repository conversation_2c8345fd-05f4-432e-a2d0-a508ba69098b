<template>
  <div style="text-align: left;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="详细信息" left-arrow @click-left="onClickLeft" />
    </van-sticky>
        <div style="margin-left:2%;margin-top:2%;">
          <div class="photo">
            <img :src="fileList" width="100%" height="100%" />
          </div>
          <p style="font-weight: 900;font-size: 16px;margin-top:20px;">基本信息</p>
          <van-cell title="员工编号:" :value="info.llqUserCode" />
          <van-cell title="姓名:" :value="info.staffName" />
          <van-cell title="性别:" :value="info.sex" />
          <van-cell title="所属部门:" :value="info.departmentName" />
          <van-cell title="体系:" :value="info.nature" />
          <van-cell title="当前岗位:" :value="info.positionName" />
          <van-cell title="当前状态:" :value="info.quit" />
          <van-cell title="职级:" :value="info.ncLevel" />
          <van-cell title="职等:" :value="info.grading" />
          <van-cell title="年龄:" :value="info.age" />
          <van-cell title="婚育情况:" :value="info.marriageorno" />
          <van-cell title="生日:" :value="info.birthday" />
          <van-cell title="籍贯:" :value="info.comefrom" />
          <van-cell title="学历:" :value="info.education" />
          <van-cell title="毕业学校:" :value="info.school" />
          <van-cell title="专业:" :value="info.professional" />
          <van-cell title="进司时间:" :value="info.joinDt" />
          <van-cell title="入职时间:" :value="info.arrDt" />
          <van-cell title="政治面貌:" :value="info.ncFace" />
          <van-cell title="联系电话:" :value="info.telPhone" />
          <van-cell title="一月:" :value="info.jan" />
          <van-cell title="二月:" :value="info.feb" />
          <van-cell title="三月:" :value="info.mar" />
          <van-cell title="四月:" :value="info.apr" />
          <van-cell title="五月:" :value="info.may" />
          <van-cell title="六月:" :value="info.jun" />
          <van-cell title="七月:" :value="info.jul" />
          <van-cell title="八月:" :value="info.aug" />
          <van-cell title="九月:" :value="info.sept" />
          <van-cell title="十月:" :value="info.oct" />
          <van-cell title="十一月:" :value="info.nov" />
          <van-cell title="十二月:" :value="info.dec" />
          <van-cell title="月均:" :value="info.avgs" />
          
        </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeName: "1",
      info: {},
      family: [],
      fileList: [],
      assess: [],
      workRecord: [],
      eduBackground: [],
      workExperience: [],
      appointment: [],
      contract: []
    };
  },
  created() {
    this.info = this.$route.params.item;
    //照片
    this.fileList = `http://service.colori.com/jeecg-boot/sys/common/static/temp/${this.info.llqUserCode}.JPG`;

    //家庭关系
    this.$axios
      .get(`/jeecg-boot/app/staff/getFamily?paperNum=${this.info.paperNum}`)
      .then(res => {
        if ((res.data.code = 200)) {
          this.family = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //奖惩情况
    this.$axios
      .get(`/jeecg-boot/app/staff/getRp?llqUserCode=${this.info.llqUserCode}`)
      .then(res => {
        if ((res.data.code = 200)) {
          this.assess = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //相关合同
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getContract?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code = 200)) {
          this.contract = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //工作记录
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getWorkExperience?paperNum=${this.info.paperNum}`
      )
      .then(res => {
        if ((res.data.code = 200)) {
          this.workExperience = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //教育水平
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getEduBackground?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code = 200)) {
          this.eduBackground = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //工作历程
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getEduBackground?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code = 200)) {
          this.eduBackground = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //聘书记录
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getAppointment?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code = 200)) {
          this.appointment = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
  },
  methods: {
    dateQuitDiff(sDate1) {
      let date2 = new Date();
      let date1 = new Date(Date.parse(sDate1.replace(/-/g, "/")));
      let iDays = parseInt(
        Math.abs(date2.getTime() - date1.getTime()) / 1000 / 60 / 60 / 24
      );
      let workday = "";
      if (iDays > 365) {
        let year = Math.floor(iDays / 365);
        let days = iDays - year * 365;
        workday = year + "年" + days;
      } else {
        workday = iDays;
      }
      return workday;
    },
    onClickLeft() {
      // this.$router.push({
      //   name: "rosterStaff"
      // });
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.photo {
  width: 320px;
  height: 380px;
  margin: 0 auto;
}
</style>
