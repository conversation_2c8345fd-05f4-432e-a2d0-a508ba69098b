<template>
  <van-popup
    v-model="visible"
    position="bottom"
    :style="{ height: '90%', width: '100%' }"
    closeable
    close-icon-position="top-right"
  >
    <div style="padding: 20px;">
      <h3 style="margin: 0 0 20px 0; text-align: center;">新增排班</h3>
      
      <!-- 表单 -->
      <van-form>
        <van-field
          v-model="formData.workDay"
          readonly
          label="日期"
          placeholder="请选择日期"
          required
          @click="showDatePicker = true"
        />
        
        <van-field
          v-model="formData.work"
          readonly
          label="工单"
          placeholder="请选择工单"
          required
          @click="showWorkPicker = true"
        />
        
        <van-field
          v-model="formData.book"
          readonly
          label="账套"
          placeholder="自动填充"
        />
        
        <van-field
          v-model="formData.workshop"
          readonly
          label="车间"
          placeholder="自动填充"
        />
        
        <van-field
          v-model="formData.jobCenter"
          readonly
          label="工作中心"
          placeholder="自动填充"
        />
        
        <van-field
          v-model="formData.category"
          readonly
          label="班次"
          placeholder="自动填充"
        />
        
        <!-- 选择人员按钮 -->
        <div style="margin: 20px 0;">
          <van-button 
            type="info" 
            @click="selectWorkers" 
            style="width: 100%;"
            icon="friends"
          >
            选择人员 (已选择 {{ selectedWorkers.length }} 人)
          </van-button>
        </div>
        
        <!-- 已选择的人员列表 -->
        <div v-if="selectedWorkers.length > 0" style="margin-bottom: 20px;">
          <h4 style="margin: 10px 0;">已选择人员：</h4>
          <div
            v-for="(worker, index) in selectedWorkers"
            :key="index"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px;
              margin-bottom: 8px;
              background-color: #f7f8fa;
              border-radius: 6px;
            "
          >
            <div>
              <div style="font-weight: 500;">{{ worker.name }}</div>
              <div style="color: #666; font-size: 14px;">工号：{{ worker.code }}</div>
            </div>
            <van-button 
              size="mini" 
              type="danger" 
              @click="removeWorker(index)"
            >
              移除
            </van-button>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div style="margin-top: 30px;">
          <van-button 
            type="primary" 
            native-type="submit" 
            style="width: 100%; margin-bottom: 10px;"
            :loading="loading"
            @click="handleSubmit"
          >
            确认提交
          </van-button>
          <van-button 
            @click="handleCancel" 
            style="width: 100%;"
          >
            取消
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 工单选择器 -->
    <van-popup v-model="showWorkPicker" position="bottom">
      <van-picker
        :columns="workColumns"
        title="选择工单"
        @confirm="onWorkConfirm"
        @cancel="showWorkPicker = false"
        show-toolbar
      />
    </van-popup>

    <!-- 选择人员弹窗 -->
    <WorkerSelectModal 
      ref="workerModal" 
      @confirm="onWorkersSelected"
    />
  </van-popup>
</template>

<script>
import { Toast } from 'vant';
import WorkerSelectModal from './WorkerSelectModal.vue';

export default {
  name: "SchedulingAddModal",
  components: {
    WorkerSelectModal
  },
  data() {
    return {
      visible: false,
      loading: false,
      
      // 日期选择
      showDatePicker: false,
      currentDate: new Date(),
      
      // 工单选择
      showWorkPicker: false,
      workColumns: [],
      orderList: [],
      
      // 表单数据
      formData: {
        workDay: '',
        work: '',
        book: '',
        workshop: '',
        jobCenter: '',
        category: ''
      },
      
      // 选择的人员
      selectedWorkers: [],
      
      // API URLs
      url: {
        getLeadCategoryList: "/jeecg-boot/app/gcWorkCategory/getLeadCategoryList",
        addWorkCategoryList: "/jeecg-boot/app/gcWorkCategory/addWorkCategoryList"
      }
    }
  },
  
  methods: {
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    // 显示弹窗
    add() {
      this.resetForm();
      this.visible = true;
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        workDay: '',
        work: '',
        book: '',
        workshop: '',
        jobCenter: '',
        category: ''
      };
      this.selectedWorkers = [];
      this.orderList = [];
      this.workColumns = [];
    },
    
    // 日期确认
    onDateConfirm(date) {
      const dateStr = this.formatDate(date);
      this.formData.workDay = dateStr;
      this.showDatePicker = false;
      
      // 获取排班信息
      this.getLeadCategoryList(dateStr);
    },
    
    // 获取排班信息
    getLeadCategoryList(workDay) {
      this.$axios.get(this.url.getLeadCategoryList, { params: { workDay,userCode:localStorage.getItem('userCode') } })
        .then(res => {
          if (res.data.success) {
            this.orderList = res.data.result.map(item => {
              return {
                id: item.jobCenter + '-' + item.category,
                text: `${item.jobCenter} - ${item.category}`,
                ...item
              }
            });
            this.workColumns = this.orderList.map(item => item.text);
          } else {
            this.orderList = [];
            this.workColumns = [];
            Toast({
              message: '该日期暂无可用的排班信息',
              position: 'bottom'
            });
          }
        })
        .catch(error => {
          Toast({
            message: '获取排班信息失败',
            position: 'bottom'
          });
          console.error('API Error:', error);
        });
    },
    
    // 工单确认
    onWorkConfirm(value, index) {
      this.formData.work = value;
      const selectedOrder = this.orderList[index];
      if (selectedOrder) {
        this.formData.book = selectedOrder.book;
        this.formData.workshop = selectedOrder.workshop;
        this.formData.jobCenter = selectedOrder.jobCenter;
        this.formData.category = selectedOrder.category;
      }
      this.showWorkPicker = false;
    },
    
    // 选择人员
    selectWorkers() {
      if (!this.formData.workDay) {
        Toast({
          message: '请先选择日期',
          position: 'bottom'
        });
        return;
      }
      this.$refs.workerModal.show();
    },
    
    // 人员选择确认
    onWorkersSelected(workers) {
      console.log("🚀 ~ onWorkersSelected ~ workers:", workers)
      // 通过id去重合并数组
      const newWorkers = [...this.selectedWorkers];
      workers.forEach(worker => {
        if (!newWorkers.some(existingWorker => existingWorker.id === worker.id)) {
          newWorkers.push(worker);
        }
      });
      this.selectedWorkers = newWorkers;
      
    },
    
    // 移除人员
    removeWorker(index) {
      this.selectedWorkers.splice(index, 1);
    },
    
    // 提交表单
    handleSubmit() {
      if (!this.formData.workDay || !this.formData.work) {
        Toast({
          message: '请填写完整信息',
          position: 'bottom'
        });
        return;
      }
      
      if (this.selectedWorkers.length === 0) {
        Toast({
          message: '请至少选择一个工作人员',
          position: 'bottom'
        });
        return;
      }
      
      this.loading = true;
      
      const submitData = {
        ...this.formData,
          detailList: this.selectedWorkers.map(item => ({
          pid: item.id,
          userCode: item.code,
          userName: item.name,
          station: item.station,
          creator: localStorage.getItem('userCode'),
          createName: localStorage.getItem('userName')
        }))
      };
      
      this.$axios.post(this.url.addWorkCategoryList, submitData)
        .then(res => {
          this.loading = false;
          if (res.data.success) {
            Toast({
              message: res.data.message || '新增成功',
              position: 'bottom'
            });
            this.$emit('ok');
            this.handleCancel();
          } else {
            Toast({
              message: res.data.message || '新增失败',
              position: 'bottom'
            });
          }
        })
        .catch(error => {
          this.loading = false;
          Toast({
            message: '网络错误，请重试',
            position: 'bottom'
          });
          console.error('API Error:', error);
        });
    },
    
    // 取消
    handleCancel() {
      this.visible = false;
      this.resetForm();
    }
  }
}
</script>

<style scoped>
.van-popup {
  border-radius: 20px 20px 0 0;
}
</style>
