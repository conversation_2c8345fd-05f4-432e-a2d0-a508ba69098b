<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="配料前检查" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <div v-for="item in dataArr" :key="item.id">
            <van-cell>
                <template #title>
                    <div v-html="item.label+'：'+item.content" style="white-space:pre-wrap"></div>
                </template>
            </van-cell>
            <van-cell>
                <template #title>
                    <span class="custom-title">结果</span>
                </template>
                <template #default>
                    <div class="custom-title">
                        <van-radio-group v-model="item.result" direction="horizontal" @change="(e)=>radioChage(e,item)">
                            <van-radio name="Y" value="Y">Y</van-radio>
                            <van-radio name="N" value="N">N</van-radio>
                        </van-radio-group>
                    </div>
                </template>
            </van-cell>
        </div>
        <div style="height: 3rem;"></div>
        <van-button type="info" @click="submit" style="width:100%;position: fixed;bottom: 0;">提交</van-button>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                data: '',
                showDate: false,
                isEdit: '1',
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                console.log(this.info);
                this.search(this.$route.params.id)
            }
        },
        methods: {

            search(id) {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${id}`).then(res => {
                    if (res.data.code == 200) {
                        this.dataArr = res.data.result.plzyList
                        console.log(this.dataArr);
                    }
                });
            },
            submit() {
                let flag = false
                this.dataArr.forEach(item => {
                    if (item.id != null) {
                        flag = true
                    }
                });
                if (flag) {
                    Toast({
                        message: "请勿重复提交",
                        position: "bottom",
                        duration: 2000

                    });
                    return
                }
                let params = {
                    userCode: localStorage.getItem("userCode"),
                    userName: localStorage.getItem("userName"),
                    mixId: this.info.id,
                    batchId: this.info.batchId,
                    resultList: this.dataArr
                }
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios.post(`/jeecg-boot/app/batchRecord/addPlzy`, params).then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.onClickLeft()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            },
            radioChage(e, item) {
                if (item.id == null) {
                    return
                }
                let params = {
                    mixId: this.info.id,
                    batchId: this.info.batchId,
                    plzyId: item.plzyId,
                    result: item.result,
                    id: item.id
                }
                this.$axios.put(`/jeecg-boot/app/batchRecord/editPlzy`, params).then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search(this.info.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
           
            },
            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>