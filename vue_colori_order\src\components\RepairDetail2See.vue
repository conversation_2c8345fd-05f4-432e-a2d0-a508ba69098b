<template>
    <div style="text-align:left;">
        <van-nav-bar title="维修记录详情" left-text="返回" left-arrow @click-left="onClickLeft" />
        <van-cell-group>
            <van-cell title="是否调试" :value="item.isDebug" />
            <van-cell title="是否更换配件" :value="item.isReplace" />
            <van-cell title="是否人为" :value="item.isArtificial" />
            <van-cell title="维修情况" :value="item.maintenance" />
            <van-cell title="故障描述" :label="item.content" value="" />
            <van-uploader v-model="imgList" disabled :deletable='false' :show-upload='false' />
        </van-cell-group>
    </div>
</template>

<script>
export default {
    data() {
        return {
            // 维修记录
            info: {},
            // 图片
            imgList: [],
            // 维修工单
            item: {},
            status: ''
        }
    },
    created() {
        this.item = this.$route.params.item
        this.status = this.$route.params.status
        this.info = this.$route.params.info

        console.log(this.$route.params)
        this.item.pictureList.forEach(item => {
            this.imgList.push({ url: item.picUrl })
        });
    },
    methods: {
        onClickLeft() {
            this.$router.push({
                name: "RepairDetail1",
                params: { item: this.info, status: this.status, active: 1 }
            });
        },
    },
}
</script>

<style scoped>
</style>