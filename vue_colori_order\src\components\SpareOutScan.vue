<template>
    <div>
        <van-cell-group>
            <van-field v-model="info.name" readonly label="名称" />
            <van-field v-model="info.brand" readonly label="品牌" />
            <van-field v-model="info.factory" readonly label="厂家" />
            <van-field v-model="info.spec" readonly label="规格" />
            <van-field v-model="info.model" readonly label="型号" />
            <van-field v-model="info.machine" readonly label="所属设备" />
            <van-field v-model="info.code" readonly label="备件编码" />
            <van-field v-model="info.category" readonly label="分类" />
            <van-field v-model="info.stock" readonly label="现存量" />
        </van-cell-group>
        <van-divider>请填写信息</van-divider>
        <van-field v-model="info.customerId" label="批次号" />
        <van-field v-model="info.count" type="digit" label="数量" @blur='check' />
        <van-field v-model="remarks" label="原因" />

        <van-button round type="info" @click="submit" style="width:90%;margin-top:3%;">确定</van-button>
    </div>
</template>
<!-- /ncApp/partsUsage -->
<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            code: '',
            count: '',
            remarks: '',
            info: {},
        }
    },
    created() {
        this.code = this.$route.params.code
        this.$axios.get('/jeecg-boot/ncApp/parts/getPartsInfo', { params: { code: this.code } }).then(res => {
            if (res.data.code == 200) {
                this.info = res.data.result
            } else {
                Toast({
                    message: res.data.message,
                    position: 'bottom',
                    duration: 2000
                });
                this.$router.replace({ name: "SpareOut" })
            }
        })
    },
    methods: {
        check(e) {
            if (e.target.value <= 0) {
                Toast({
                    message: '请输入大于0的数',
                    position: 'bottom',
                    duration: 2000
                });
                e.target.value = ''
            }
        },
        submit() {
            this.info.remarks = this.remarks
            this.info.partsId = this.info.id
            this.info.creator = localStorage.getItem('userCode')
            this.info.type = 2
            let params = this.info
            if (this.info.count == '' ||
                this.info.count == null ||
                this.info.count == undefined ||
                this.info.remarks == '' ||
                this.info.remarks == null ||
                this.info.remarks == undefined) {
                Toast({
                    message: '请填写原因和数量',
                    position: 'bottom',
                    duration: 2000
                });
            } else {

                if (this.info.stock * 1 >= this.info.count * 1) {

                    this.$axios.post('/jeecg-boot/ncApp/partsUsage/add', params).then(res => {
                        if (res.data.code == 200) {
                            this.$router.replace({ name: "SpareOut" })
                        } else {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                } else {
                    Toast({
                        message: '现存量不足',
                        position: 'bottom',
                        duration: 2000
                    });
                }

            }
        }
    },
}
</script>

<style scoped>
</style>