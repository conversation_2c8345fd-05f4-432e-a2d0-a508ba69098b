<template>
    <div class="content">
        <img :src="top_image" width="100%"/>
        <img :src="logo" class="logo"/>
        
        <div class="setting" @click="changeLanguage">
            <img :src="change" width="20%"/>
            {{language}}
        </div>
        <div class="menu_top">
            <div class="menu_item" @click="pushToNext(1)">
                <img :src="order" class="menu_pic"/><label class="menu_text">{{$t('taskList')}}</label>
            </div>
            <div class="menu_item" @click="pushToNext(2)">
                <img :src="train" class="menu_pic"/><label class="menu_text">{{$t('lineData')}}</label>
            </div>
        </div>
        <div class="menu_bottom">
            <div class="menu_item"  @click="pushToNext(3)">
                <img :src="service" class="menu_pic"/><label class="menu_text">{{$t('employee')}}</label>
            </div>
            <div class="menu_item">
                <img :src="okr" class="menu_pic"/><label class="menu_text">{{$t('okr')}}</label>
            </div>
        </div>
        <div style="margin-top:10px;color:#475b66;">
            Create unlimited possibilities for ibeauty
        </div>
        <div style="margin-top:10px;color:#0076cc;">
            COLORI INC.
        </div>
        <img :src="company_pic" class="factory"/>
    </div>
</template>
<script>
import { Locale } from 'vant';
import { setLanguage } from "../utils/cookie";
export default {
    data(){
        return{
            top_image:require('../../static/images/top_image.png'),
            logo:require('../../static/images/colori_logo.png'),
            setting:require('../../static/images/setting.png'),
            company_pic:require('../../static/images/factory.png'),
            order:require('../../static/images/order.png'),
            train:require('../../static/images/train.png'),
            service:require('../../static/images/service.png'),
            okr:require('../../static/images/okr.png'),
            change:require('../../static/images/change.png'),
            langs: [
                {
                    text: '中文',
                    value: 'zh-CN'
                },
                {
                    text: 'English',
                    value: 'en-US'
                },
            ],
            language:"",
        }
    },
    created:function(){
        let index=this.langs.findIndex(item => item.value === this.$i18n.locale) || 0;
        console.log("index:"+index)
        if(index<0){
            index=0
        }
        this.language=this.langs[index].text
    },
    methods:{
        changeLanguage(){
            let index=this.langs.findIndex(item => item.value === this.$i18n.locale) || 0;

            if(index<0){
                index=0
            }

            let value=this.langs[index].value;
            console.log("value:"+value)
            if(index==0){
                value=this.langs[1].value;
                this.language=this.langs[1].text
            }else{
                value=this.langs[0].value;
                this.language=this.langs[0].text
            }

            Locale.use(value, this.$i18n.messages[value]);
            // Business component
            this.$i18n.locale = value;
            // Cookie
            setLanguage(value);

        },
        pushToNext(num){
            if(num==1){
                this.$router.push({path:'/orderMenu'});
            }else if(num==2){
                this.$router.push({path:'/productData'});
            }else if(num==3){
                this.$router.push({path:'/empMenu'});
            }
        }
    }
}
</script>
<style scoped>
.content{
    background: #f3f4f6;
}
.logo{
    position: absolute;
    top:13%;
    left: 37%;
    width: 26%;
    height: 8%;
}
.menu_top{
    position: relative;
    height: 4.5rem;
    background: rgba(255, 255, 255, 0.75);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    margin-top: -4.5rem;
    margin-left: 5%;
    margin-right: 5%;
}
.menu_bottom{
    position: relative;
    background: #fdfdfd;
    margin-left: 5%;
    margin-right: 5%;
    height: 4rem;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.factory{
    border-radius: 10px;
    width: 90%;
    margin: 5%;
}
.menu_item{
    height:100%;
    float: left;
    width: 49%;
}
.menu_text{
    font-size: 0.9rem;
    font-weight: 800;
    float: left;
    margin-left: 3%;
    margin-top: 10%;
}
.menu_pic{
    margin-top: 3%;
    float: left;
    margin-left: 18%;
}
.setting{
    position: absolute;
    top: 1%;
    right: 1%;
    color: #fff;
    padding: 2%;
}
</style>