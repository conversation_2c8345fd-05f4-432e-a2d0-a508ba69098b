<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div style="background:#f3f4f6;height:10px;"></div>
        
        <div class="search">
            <div class="search_type" @click="openQuestionType">{{searchType}}</div>
            <div class="search_input">
                <mt-field placeholder="请输入" v-model="searchKey"></mt-field>
            </div>
            <div class="search_btn" @click="searchModel()">
                搜索
                <!-- <mt-button type="primary" @click="searchModel()">搜索</mt-button> -->
            </div>
        </div>

        <mt-popup class="popup-div" v-model="popupVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="popupSlots" @change="onValuesChange"  showToolbar @touchmove.native.stop.prevent>
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="popupVisible = !popupVisible">取消</div>
                    <div class="">请选择筛选条件</div>
                    <div class="usi-btn-sure" @click="popupOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>


        <div v-for="(item,index) in personList" :key="index">
            <div class="sch_order_order" @click="handleClick(item)">
                <div class="person_item_left">
                    <div class="person_name">{{item.name}}_{{item.code}}</div>
                    <div class="person_station">{{item.station}}</div>
                </div> 
                <div class="person_item_right">
                    <label><input type="checkbox" value="" class="person_checkbox" @change="handleChange(item)" v-model="item.selected"/></label>
                </div> 
            </div>
        </div>

        <div style="height:5rem;"></div>


        <div class="user_bottom">
            <div class="user_select_all" @click="selectAll">
                {{selectedAll}}
            </div>
            <div class="user_selected">
                {{selected}}
            </div>
            <div class="user_add" @click="userAdd">
                {{$t('add')}}
            </div>
        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Toast,Indicator } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            edateVal:'',
            c_show:false,
            date:'',
            preCategory:'',
            selectedAll:'全选',
            selected:'已选择(0)',
            begin:'',
            end:'',
            selectedValue: this.formatDate(new Date()),
            my_schedual:require('../../static/images/mySchedual.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            schedualList:[],
            personList:[],
            fee:{},
            item:{},
            searchType:'按组长',
            questionType:'',
            questionTypeVal:'',
            searchKey:'',
            popupVisible:false,
            popupSlots:[
                {
                    values:[
                        '按组长','自定义组名','按线体','员工编号'
                    ]
                }
            ],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        sessionStorage.setItem('workList','')
        this.item=JSON.parse(sessionStorage.getItem('item'))
        console.log(this.item)
        this.begin=this.$route.params.begin
        this.end=this.$route.params.end

        console.log(this.begin)
        console.log(this.end)
    },
    methods: {
        getCategory(){

        },
        userAdd(){
            let self=this
            let workList=[]
            for(var i=0;i<self.personList.length;i++){
                let item=self.personList[i]
                if(item.selected){
                    workList.push(item);
                }
            }

            if(workList==null || workList==''){
                Toast({
                    message: "请返回后重新进入页面！",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                sessionStorage.setItem('workList',JSON.stringify(workList))
                self.$router.go(-1);
            }

            
        },
        handleClick(item){
            let self=this
            if(item.selected){
                item.selected=false
            }else{
                item.selected=true
            }
            let num=0;
            for(var i=0;i<self.personList.length;i++){
                let item=self.personList[i]
                if(item.selected){
                    num++
                }
            }
            self.selected='已选择('+num+')'
        },
        handleChange(item){
            let self=this
            console.log('selected:'+item.selected)
            let num=0;
            for(var i=0;i<self.personList.length;i++){
                let item=self.personList[i]
                if(item.selected){
                    num++
                }
            }
            self.selected='已选择('+num+')'
        },
        selectAll(){
            let self=this
            if(self.selectedAll=="全选"){
                for(var i=0;i<self.personList.length;i++){
                    let item=self.personList[i]
                    item.selected=true
                    self.$set(this.personList,i,item)
                }
                console.log(self.personList)
                self.selected='已选择('+self.personList.length+')'
                self.selectedAll='取消全选'
            }else{
                for(var i=0;i<self.personList.length;i++){
                    let item=self.personList[i]
                    item.selected=false
                    self.$set(self.personList,i,item)
                }
                console.log(self.personList)
                self.selected='已选择(0)'
                self.selectedAll='全选'
            }


            
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.searchType = this.questionTypeVal;
            this.popupVisible = false;
            // this.getOrderInfo();
        },
        openQuestionType(){
            this.popupVisible = true;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        getBeginTime(){
            console.log("getBeginTime")
            // if (this.item.begin) {
            //     this.dateVal = this.item.begin
            // } else {
            //     this.dateVal = new Date()
            // }
            this.$refs['picker'].open()
        },
        getEndTime(){
            console.log("getEndTime")
            this.$refs['epicker'].open()
        },
        addPerson(){

        },
        searchModel(){
            let self = this
            let type='1'
            if(self.searchType=='按组长'){
                type='1'
            }else if(self.searchType=='自定义组名'){
                type='2'
            }else if(self.searchType=='按线体'){
                type='3'
            }else if(self.searchType=='员工编号'){
                type='4'
            }
            let params={
                department:this.item.department,
                workshop:this.item.workshop,
                mitosome:this.item.mitosome,
                secinfo:this.searchKey,
                type:type,
                begin:this.begin,
                end:this.end,
                createTime:this.item.createTime,
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcLeadPlanApp/getFreeWorker',{params:params}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.personList=res.data.result
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            console.log(this.dateVal)
            this.item.begin = this.dateVal
            this.begin=this.dateVal
            console.log(this.item)
        },
        edateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.item.end = this.edateVal
            this.end=this.edateVal
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.search{
    width: 86%;
    margin-left: 5%;
    height: 3rem;
    display: flex;
    background: #fff;
    align-items: center;
    padding: 2%;
}
.search_type{
    float: left;
    width: 25%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.search_input{
    float: left;
    width: 55%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.search_btn{
    float: left;
    width: 25%;
    height: 100%;
    background: cornflowerblue;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}
.user_bottom{
    height: 4rem;
    width: 100%;
    position:fixed;
    bottom:0;
    background-color: #fff;
}
.user_select_all{
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    width: 25%;
    height: 100%;
}
.user_selected{
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    width: 25%;
    height: 100%;
}
.user_add{
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    background: crimson;
    color: #fff;
    width: 50%;
    height: 100%;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.sch_order_order{
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 4.5rem;
    border-radius: 10px;
    background: #fff;
}
.person_item_left{
    float: left;
    width: 58%;
    height: 4.5rem;
    text-align: left;
}
.person_item_right{
    float: left;
    width: 42%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.person_name{
    width: 100%;
    padding: 5%;
}
.person_station{
    padding: 5%;
    width: 100%;
}
.person_checkbox{
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
}
</style>