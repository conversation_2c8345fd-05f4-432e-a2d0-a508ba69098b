<template>
  <div>
    <van-nav-bar title="异常" left-arrow @click-left="onClickLeft" />
    <div style="text-align: left; background-color: #fff; padding: 1rem">
      <van-cell title="创建人" :value="info.createName" />
      <van-cell title="创建编码" :value="info.creator" />
      <van-cell title="异常信息" :value="info.errorInfo" />

      <van-field v-model="info.reason" rows="3" autosize label="备注" type="textarea" maxlength="100"
        placeholder="请输入备注" />

      <van-button type="primary" @click="unlock" style="width: 100%; margin: 0 auto">解锁</van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  data() {
    return {
      info: {},
    };
  },
  created() {
    this.info = this.$route.params;
    console.log(this.info);
  },
  methods: {
    unlock() {
      if (!this.info.reason) {
        Toast({
          message: '原因不能为空',
          position: "bottom",
          duration: 2000,
        });
        return
      }
      this.$axios
        .post(`/jeecg-boot/app/gcMixError/check`, {
          id: this.info.id,
          mainId: this.info.mainId,
          errorInfo: this.info.errorInfo,
          userCode: localStorage.getItem("userCode"),
          userName: localStorage.getItem("userName"),
          reason: this.info.reason
        })
        .then((res) => {
          if (res.data.code == 200) {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000,
            });
            localStorage.setItem("matCheckFlag", '1')
            this.$router.go(-1);
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000,
            });
          }
        });
    },
    onClick(name, title) {
      console.log(name, title);
    },
    onClickLeft() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped></style>
