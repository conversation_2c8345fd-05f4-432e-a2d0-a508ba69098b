<template>
    <div style="text-align: left;padding: 0.5rem;">
        <!-- type == '1' -->
        <van-tabbar v-model="active" v-if="false">
            <van-tabbar-item icon="label-o">记录填写</van-tabbar-item>
            <van-tabbar-item icon="manager">个人中心</van-tabbar-item>
        </van-tabbar>
        <template v-if="active == 0">
            <div class="equipment-info"
                style="padding: 20px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%); border-radius: 12px; margin-bottom: 16px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); height: 8vh;">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                    <div
                        style="width: 48px; height: 48px; background: #1989fa; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                        <van-icon name="setting-o" size="24" color="#fff" />
                    </div>
                    <div>
                        <div style="font-size: 18px; font-weight: bold; color: #323233;">{{ info.modelName }}</div>
                        <div style="font-size: 14px; color: #969799;">{{ info.modelCode }}</div>
                    </div>
                </div>
                <!-- <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
                    <div style="padding: 8px; background: #f7f8fa; border-radius: 6px;">
                        <div style="font-size: 12px; color: #969799;">设备类型</div>
                        <div style="font-size: 14px; color: #323233; margin-top: 4px;">{{ info.type }}</div>
                    </div>
                    <div style="padding: 8px; background: #f7f8fa; border-radius: 6px;">
                        <div style="font-size: 12px; color: #969799;">品牌</div>
                        <div style="font-size: 14px; color: #323233; margin-top: 4px;">{{ info.brand }}</div>
                    </div>
                    <div style="padding: 8px; background: #f7f8fa; border-radius: 6px;">
                        <div style="font-size: 12px; color: #969799;">安装区域</div>
                        <div style="font-size: 14px; color: #323233; margin-top: 4px;">{{ info.installArea }}</div>
                    </div>
                </div> -->
            </div>
            <div :style="{ height: type === '1' ? '80vh' : '40vh', overflow: 'scroll', backgroundColor: '#f5f7fa' }">
                <div v-for="item in list" :key="item.id"
                    style="margin-bottom: 16px; border: 1px solid #ebedf0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 12px;">
                    <van-cell :title="`${item.deviceName} - ${item.partName}`"
                        title-style="font-weight: bold;font-size: 0.9rem;" :border="false" :is-link="false" />
                    <div
                        style="width: 100%;height: 2rem;background-color: #eaf7ff;padding-left: 2px;line-height: 2rem;border-radius: 4px;">
                        {{ item.title }}</div>
                    <van-field label="检查结果">
                        <template #input>
                            <van-radio-group v-model="item.result" direction="horizontal" @change="resultChange(item)">
                                <van-radio name="正常">正常</van-radio>
                                <van-radio name="异常">异常</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                    <template v-if="item.result == '异常'">
                        <van-field label="异常描述" type="textarea" v-model="item.content" :maxlength="200"
                            :show-word-limit="true"> </van-field>
                        <van-field name="uploader" label="异常照片">
                            <template #input>
                                <van-uploader v-model="item.pictureList" :max-size="10000 * 1024"
                                    :after-read="(file, name) => afterRead(file, name, item)" @oversize="onOversize"
                                    :max-count="5" />
                            </template>
                        </van-field>
                        <van-field label="是否免费">
                            <template #input>
                                <van-radio-group v-model="item.free" direction="horizontal" @change="freeChange(item)">
                                    <van-radio name="是">是</van-radio>
                                    <van-radio name="否">否</van-radio>
                                </van-radio-group>
                            </template>
                        </van-field>
                        <template v-if="item.free == '是'">
                            <van-field label="备注" type="textarea" v-model="item.remarks" :maxlength="200"
                                :show-word-limit="true"> </van-field>
                        </template>
                        <template v-if="item.free == '否'">
                            <van-cell title="点击添加备件" value="+" @click="addPart(item)" value-class="add-btn" />
                            <template v-for="(x, i) in item.infoList">
                                <van-cell :title="`备件${i + 1}`" value="删除" @click="delPart(item, i)"
                                    style="margin-top: 16px;" value-class="delete-btn" />
                                <van-field label="备件名称" v-model="x.name"> </van-field>
                                <van-field label="备件品牌" v-model="x.brand"> </van-field>
                                <van-field label="备件型号" v-model="x.spec"> </van-field>
                                <van-field label="备件数量" type="number" v-model="x.qty"> </van-field>
                            </template>
                        </template>
                    </template>
                    <!-- 添加初效和中效输入框，只在dataType为2时显示 -->
                    <template v-if="item.dataType == 2">
                        <van-field label="初效＜200pa" v-model="item.actualPa1" type="number" placeholder="请输入初效值"
                            :rules="[{ required: true, message: '请输入初效值' }]">
                        </van-field>
                        <van-field label="中效＜350pa" v-model="item.actualPa2" type="number" placeholder="请输入中效值"
                            :rules="[{ required: true, message: '请输入中效值' }]">
                        </van-field>
                    </template>
                </div>
            </div>
            <div v-if="type === '2'" style="margin-top: 20px; height:40vh; padding: 12px; background-color: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div style="font-weight: bold; font-size: 16px;">问题列表</div>
                    <van-button type="primary" size="small" @click="addProblem">添加问题</van-button>
                </div>
                
                <div style="flex: 1; overflow-y: auto; overflow-x: hidden; padding-right: 4px;">
                    <template v-if="problemList.length > 0">
                        <div v-for="(problem, index) in problemList" :key="index" 
                            style="margin-bottom: 12px; background-color: #f8f8f8; padding: 10px; border-radius: 8px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <div style="font-weight: bold;">问题 {{ index + 1 }}</div>
                                <van-button size="mini" type="danger" @click="deleteProblem(index)">删除</van-button>
                            </div>
                            <van-field label="是否是重大问题" label-width="7rem">
                                <template #input>
                                    <van-radio-group v-model="problem.type" direction="horizontal">
                                        <van-radio name="否">否</van-radio>
                                        <van-radio name="是">是</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                            <van-field label="问题内容" type="textarea" v-model="problem.content" placeholder="请输入问题内容"
                                :rules="[{ required: true, message: '请输入问题内容' }]">
                            </van-field>
                        </div>
                    </template>
                    <div v-else style="color: #999; text-align: center; padding: 20px;">
                        暂无问题，请点击添加问题按钮
                    </div>
                </div>
            </div>
            <van-button type="primary" style="width:100%;margin-top:1rem;" @click="onSubmit">提交</van-button>
        </template>
        <template v-if="active == 1">
            <div class="profile-container">
                <div class="profile-card">
                    <div class="avatar-section">
                        <div class="avatar">
                            <van-icon name="contact" size="50" color="#1989fa" />
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="info-item">
                            <label>账号</label>
                            <span>{{ userCode }}</span>
                        </div>
                        <div class="info-item">
                            <label>姓名</label>
                            <span>{{ userName }}</span>
                        </div>
                    </div>

                    <div class="action-section">
                        <van-button type="primary" block round icon="lock" @click="showPasswordDialog = true"
                            style="margin-bottom: 16px;">
                            修改密码
                        </van-button>

                        <van-button type="danger" block round icon="close" @click="handleLogout">
                            退出登录
                        </van-button>
                    </div>
                </div>
            </div>

            <!-- 修改密码弹窗 -->
            <van-dialog v-model="showPasswordDialog" title="修改密码" show-cancel-button :before-close="handleBeforeClose">
                <van-form>
                    <van-field v-model="passwordForm.newPassword" type="password" label="新密码" placeholder="请输入新密码"
                        :rules="[{ required: true, message: '请输入新密码' }]" />
                    <van-field v-model="passwordForm.confirmPassword" type="password" label="确认密码"
                        placeholder="请再次输入新密码" :rules="[{ required: true, message: '请确认新密码' }]" />
                </van-form>
            </van-dialog>
        </template>
    </div>
</template>
<script>
import Vue from "vue";
import { Toast, Dialog } from "vant";
import { Indicator } from 'mint-ui';
export default {
    name: 'equiSpotCheck',
    data() {
        return {
            active: 0,
            message: '',
            info: {},
            list: [],
            id: '',
            type: '',
            problemList: [], // 添加顶级问题列表
            userCode: localStorage.getItem("userCode"),
            userName: localStorage.getItem("userName"),
            showPasswordDialog: false,
            passwordForm: {
                newPassword: '',
                confirmPassword: ''
            }
        }
    },
    created() {
        const { id, type } = this.$route.query
        this.id = id
        if ((type == '1' && (!localStorage.getItem("equiUserCode") || localStorage.getItem("equiUserCode") == "")) ||
            (type == '2' && (!this.userCode || this.userCode == ""))) {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000
            });
            if (type == '2') {
                this.$router.push({
                    name: "Login",
                    query: {
                        route: 'equiSpotCheck',
                        id: id,
                        type: type
                    }
                });
            } else if (type == '1') {
                this.$router.push({
                    name: "equiSpotLogin",
                    query: {
                        route: 'equiSpotCheck',
                        id: id,
                        type: type
                    }
                });
            }
        } else {
            this.type = type
            this.$axios.get(`/jeecg-boot/app/ncGeneralEquiSpotInfo/getEquiSpotInfo?id=${id}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.info = res.data.result.ncGeneralEquiPmInfo;
                        this.list = res.data.result.ncGeneralEquiPmDetailList.map(item => {
                            item.result = '正常'
                            item.title = item.content
                            item.detailId = item.id
                            delete item.createTime
                            delete item.creator
                            let obj = {
                                detailId: item.id,
                                title: item.content,
                                result: '正常',
                                partName: item.partName,
                                deviceName: item.deviceName,
                                dataType: item.dataType,
                                deviceId: id
                            };
                            if (item.dataType == 2) {
                                obj.actualPa1 = '';
                                obj.actualPa2 = '';
                            }
                            return obj;
                        })
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }

    },
    methods: {
        resultChange(item) {
            Vue.set(item, 'content', '')
            Vue.set(item, 'contents', '')
            Vue.set(item, 'picUrl', '')
            Vue.set(item, 'free', '')
            Vue.set(item, 'infoList', [])
        },
        freeChange(item) {
            Vue.set(item, 'contents', '')
            Vue.set(item, 'infoList', [])
        },
        onSubmit() {
            this.list.forEach(item => {
                item.type = this.type
                item.picUrl = ''
                if (Array.isArray(item.pictureList) && item.pictureList.length > 0) {
                    item.pictureList.forEach(x => {
                        item.picUrl += x.picUrl + ','
                    })
                }
            })

            for (let i = 0; i < this.list.length; i++) {
                const item = this.list[i];

                if (!item.result) {
                    Toast({
                        message: "请选择检查结果",
                        position: "bottom",
                        duration: 2000
                    });
                    return;
                }

                if (item.result === '异常') {
                    if (!item.content) {
                        Toast({
                            message: "请填写异常描述",
                            position: "bottom",
                            duration: 2000
                        });
                        return;
                    }

                    if (!item.picUrl && (!Array.isArray(item.pictureList) || item.pictureList.length === 0)) {
                        Toast({
                            message: "请上传异常照片",
                            position: "bottom",
                            duration: 2000
                        });
                        return;
                    }

                    if (!item.free) {
                        Toast({
                            message: "请选择是否免费",
                            position: "bottom",
                            duration: 2000
                        });
                        return;
                    }

                    if (item.free === '是') {
                        if (!item.remarks) {
                            Toast({
                                message: "请填写备注",
                                position: "bottom",
                                duration: 2000
                            });
                            return;
                        }
                    }

                    if (item.free === '否') {
                        if (!Array.isArray(item.infoList) || item.infoList.length === 0) {
                            Toast({
                                message: "请至少添加一条备件信息",
                                position: "bottom",
                                duration: 2000
                            });
                            return;
                        }


                        for (let j = 0; j < item.infoList.length; j++) {
                            const part = item.infoList[j];
                            // 检查每个字段是否为空字符串或0
                            console.log(part)
                            if (!part.name || !part.brand || !part.spec || !part.qty || part.qty === '0' ||
                                part.name.trim() === '' || part.brand.trim() === '' || part.spec.trim() === '') {
                                Toast({
                                    message: `${item.partName}的第${j + 1}条备件信息不完整`,
                                    position: "bottom",
                                    duration: 2000
                                });
                                return;
                            }
                        }
                    }
                }
                if (item.dataType == 2) {
                    if (!item.actualPa1 || !item.actualPa2) {
                        Toast({
                            message: "请填写" + item.deviceName + "-" + item.partName + "的初效和中效",
                            position: "bottom",
                            duration: 2000
                        });
                        return;
                    }
                }


            }
            if (Array.isArray(this.problemList) && this.problemList.length > 0) {
                for (let k = 0; k < this.problemList.length; k++) {
                    const problem = this.problemList[k];
                    // 不需要再检查问题类型是否为空，因为现在是单选按钮
                    if (!problem.content || problem.content.trim() === '') {
                        Toast({
                            message: `第${k + 1}个问题内容不能为空`,
                            position: "bottom",
                            duration: 2000
                        });
                        Indicator.close();
                        return;
                    }
                }
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            this.$axios.post(`/jeecg-boot/app/ncGeneralEquiSpotInfo/pushEquiPmInfo`, {
                resultList: this.list,
                type: this.type,
                problemList: this.problemList.map(item => {
                    return {
                        deviceId: this.id,
                        type: item.type,
                        content: item.content,
                        creator: localStorage.getItem('userCode'),
                        createName: localStorage.getItem('userName')
                    }
                }),
                paList: this.list.filter(item => item.dataType == 2).map(item => ({
                    deviceId: this.id,
                    actualPa1: item.actualPa1,
                    actualPa2: item.actualPa2,
                    creator: localStorage.getItem('userCode'),
                    createName: localStorage.getItem('userName'),
                    remarks: item.remarks
                })),
                creator: localStorage.getItem('userCode'),
                createName: localStorage.getItem('userName')
            })
                .then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: "提交成功",
                            position: "bottom",
                            duration: 2000
                        });
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                }).finally(() => {
                    Indicator.close();
                });
        },
        delPart(item, i) {
            Dialog.confirm({
                message: '确定删除吗?',
                theme: 'round-button',
                confirmButtonColor: '#1989fa',
                cancelButtonColor: '#CCCCCC',
            }).then(() => {
                item.infoList.splice(i, 1)
            }).catch(() => {
                Toast({
                    message: '取消',
                    position: "bottom",
                    duration: 1000
                });
            });
        },
        addPart(item) {
            if (!Array.isArray(item.infoList)) {
                this.$set(item, 'infoList', [])
                item.infoList.push({
                    name: '',
                    brand: '',
                    qty: '',
                })
            } else {
                item.infoList.push({
                    name: '',
                    brand: '',
                    qty: '',
                })
            }
            console.log(item.infoList)
        },

        //上传图片
        afterRead(file, name, item) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("biz", "016");
            this.$axios.post(`/jeecg-boot/sys/common/upload`, param).then(res => {
                if (res.data.success) {
                    file.picUrl = res.data.message
                } else {
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        handleBeforeClose(action, done) {
            if (action === 'confirm') {
                // 验证密码
                if (!this.passwordForm.newPassword || !this.passwordForm.confirmPassword) {
                    Toast('请填写完整密码信息');
                    done(false);
                    return;
                }

                if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
                    Toast('两次输入的新密码不一致');
                    done(false);
                    return;
                }
                // 调用修改密码接口
                this.$axios.get(`/jeecg-boot/app/external/changePassword?password=${this.passwordForm.newPassword}&id=${localStorage.getItem('userId')}&username=${this.userCode}`).then(res => {
                    if (res.data.code === 200) {
                        Toast.success('密码修改成功');
                        this.passwordForm = {
                            newPassword: '',
                            confirmPassword: ''
                        };
                        done();
                    } else {
                        Toast.fail(res.data.message || '修改失败');
                        done(false);
                    }
                }).catch(() => {
                    Toast.fail('修改失败');
                    done(false);
                });
            } else {
                done();
            }
        },
        handleLogout() {
            Dialog.confirm({
                title: '提示',
                message: '确定要退出登录吗？',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonColor: '#ee0a24'
            }).then(() => {
                // 清除相关缓存
                localStorage.removeItem('equiUserCode');
                localStorage.removeItem('equiUserId');
                localStorage.removeItem('equiUserName');

                // 跳转到登录页
                this.$router.replace({
                    name: 'equiSpotLogin'
                });

                Toast.success('已退出登录');
            }).catch(() => {
                // 取消退出
            });
        },
        addProblem() {
            this.problemList.push({
                type: '否',
                content: '',
            })
            console.log(this.problemList)
        },
        deleteProblem(index) {
            Dialog.confirm({
                message: '确定删除吗?',
                theme: 'round-button',
                confirmButtonColor: '#1989fa',
                cancelButtonColor: '#CCCCCC',
            }).then(() => {
                this.problemList.splice(index, 1)
            }).catch(() => {
                Toast({
                    message: '取消',
                    position: "bottom",
                    duration: 1000
                });
            });
        }
    },
}
</script>

<style scoped>
.delete-btn {
    color: red;
}

.add-btn {
    color: rgb(5, 120, 197);
}

.profile-container {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 50px);
}

.profile-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.avatar-section {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
}

.avatar {
    width: 80px;
    height: 80px;
    background: #f5f7fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-section {
    margin-bottom: 24px;
}

.info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f5f7fa;
}

.info-item label {
    color: #969799;
    width: 60px;
}

.info-item span {
    color: #323233;
    flex: 1;
}

.action-section {
    margin-top: 32px;
}

.action-section .van-button {
    margin-bottom: 12px;
}

.action-section .van-button:last-child {
    margin-bottom: 0;
}
</style>