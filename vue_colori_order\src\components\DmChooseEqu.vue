<template>
    <div class="order">
        <van-nav-bar
            title="宿舍-选择器材"
            left-text="返回"
            right-text="筛选"
            left-arrow
            @click-left="onClickLeft"
            @click-right="onClickRight"
        />


        <van-popup v-model="show" position="top" :style="{ height: '35%' }">
            <van-nav-bar
                title="查询条件"
            />
            <van-field
                v-model="queryParam.code"
                clearable
                label="器材编号"
                placeholder="请输入器材编号"
            />
            <van-field
                v-model="queryParam.name"
                clearable
                label="器材名称"
                placeholder="请输入器材名称"
            />
            <van-field
                v-model="queryParam.spec"
                clearable
                label="规格"
                placeholder="请输入规格"
            />
            <van-field
                v-model="queryParam.unit"
                clearable
                label="单位"
                placeholder="请输入单位"
            />
            
            <van-row  gutter="8" style="margin-top:4px;">
                <van-col span="12">
                    <van-button @click="resentQuery" style="width: 80%;">
                        重置
                    </van-button>
                </van-col>
                <van-col span="12">
                    <van-button type="info" @click="searchQuery" style="width: 80%;">
                        查询
                    </van-button>
                </van-col>
            </van-row>
            
        </van-popup>
        <!-- <van-row gutter="20">
            
        </van-row> -->
        <van-divider content-position="left" margin="12px 0 20px">选中器材</van-divider>
        <!-- <van-grid :column-num="2">
            <van-grid-item v-for="(item,index) in showList" :key="index">
                <van-button size="small" plain type="primary" style="width:100%;border:1px solid red;">{{item.code}}{{item.name}}</van-button>
                
            </van-grid-item>
        </van-grid> -->

        <van-row>
            <van-col span="8" v-for="(item,index) in showList" :key="index">
                <van-button plain type="primary" @click="cancelChoose(item,index)">
                    {{item.code}} {{item.name}}
                    <!-- <van-icon name="cross" /> -->
                </van-button>
            </van-col>
        </van-row>
        <van-button type="primary" @click="submit()"  style="margin:5px;width:40%;border-radius:10px;">提交</van-button>

        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            >
            
            

            <van-cell v-for="item,index in listData"
                :key="index" 
                :title="item.code+' '+item.name"  clickable @click="radio = 1">

                <van-checkbox slot="right-icon" v-model="item.checked" @change="changeCheck(item,index)"></van-checkbox>
            </van-cell>
        </van-list>

        

        

        
        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            queryParam:{
                code:null,
                name:null,
                spec:null,
                unit:null,
                pageNo:1,
                pageSize:10,
                total:0,
            },
            listData: [],
            show:false,
            loading: false,
            finished: false,

            showList:[],
            checked:false,
            rForm: {}
        }
    },
    created:function(){
        let that = this;
        that.$axios.get('/dormApi/maintain/app/getEquipmentList',{params:that.queryParam}).then(res=>{
            console.log('res:',res);
            if(res.data.success){
                that.queryParam.total = res.data.result.total;
                for(let i = 0; i < res.data.result.records.length; i++) {
                    
                    that.listData.push(res.data.result.records[i])
                    
                }
                console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.listData)
            }
        })
        Object.assign(this.rForm, this.$route.params.rForm);
        console.log("🚀 ~ this.rForm:", this.rForm)
    },
        
    methods:{
        onClickLeft() {
            let self = this
            this.$router.replace({name:"dormRepairComplate",params:{rForm:self.rForm}})
        },
        onClickRight() {
            this.show = true;
        },
        onLoad() {
            // 异步更新数据
            let that = this;
            that.queryParam.pageNo = that.queryParam.pageNo + 1;
            that.$axios.get('/dormApi/maintain/app/getEquipmentList',{params:that.queryParam}).then(res=>{
                console.log('res:',res.data);
                console.log('res:',res.data.success == true);
                if(res.data.success == true){
                    that.queryParam.total = res.data.result.total;
                    
                    for(let i = 0; i < res.data.result.records.length; i++) {
                        
                        that.listData.push(res.data.result.records[i])
                    }

                    console.log("🚀 ~ that.$axios.get ~ that.listData:", that.listData.length)
                    console.log("🚀 ~ that.$axios.get ~ that.queryParam:", that.queryParam)
                    // 加载状态结束
                    that.loading = false;
                    // 数据全部加载完成
                    if (that.listData.length >= that.queryParam.total) {
                        that.finished = true;
                    }
                    // that.finished = true;
                }
                
            })
        },
        resentQuery() {
            let that = this;
            that.queryParam.code = null;
            that.queryParam.name = null;
            that.queryParam.spec = null;
            that.queryParam.unit = null;
            that.searchQuery();
        },
        searchQuery() {
            let that = this;

            that.queryParam.pageNo = 1;
            that.listData = [];
            that.$axios.get('/dormApi/maintain/app/getEquipmentList',{params:that.queryParam}).then(res=>{
                console.log('res:',res);
                if(res.data.success){
                    that.queryParam.total = res.data.result.total;
                    for(let i = 0; i < res.data.result.records.length; i++) {
                        
                        that.listData.push(res.data.result.records[i])
                        
                    }
                    that.show = false;
                    console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.listData)
                }
            })
        },
        changeCheck(item,index) {
            console.log("🚀 ~ file: DmChoosePerson.vue:193 ~ changeCheck ~ item,index:", item,index)
            if(item.checked == true) {
                let site = -1;
                for(let i = 0; i < this.showList.length; i++) {
                    if(this.showList[i].code == item.code) {
                        site = i;
                        break;
                    }
                }
                if(site == -1) {
                    this.showList.push(item);
                }
            } else {
                let site = -1;
                for(let i = 0; i < this.showList.length; i++) {
                    if(this.showList[i].code == item.code) {
                        site = i;
                        break;
                    }
                }
                if(site != -1) {
                    this.showList.splice(site,1)
                }
            }
        },
        cancelChoose(item,index) {
            
            for(let i = 0; i < this.listData.length; i++) {
                if(this.listData[i].code == item.code) {
                    this.listData[i].checked = false;
                    break;
                }
            }
            this.showList.splice(index,1);
        },
        submit(){
            let self=this
            if(self.showList.length == 0) {
                Toast({
                    message: "至少选择一项！",
                    position: 'bottom',
                    duration: 1500
                });
                return;
            }
            self.$router.replace({
                name:"dormRepairComplate",
                params:{
                    chooseEquList:self.showList,
                    rForm:self.rForm
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>

</style>