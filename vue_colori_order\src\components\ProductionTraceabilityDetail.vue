<template>
  <div style="background-color: #ebecf7;height: 99vh;">
    <van-sticky :offset-top="0">
      <van-nav-bar v-if="type == '1'" title="成品 使用记录" left-arrow @click-left="onClickLeft" />
      <van-nav-bar v-if="type == '2'" title="胶体 使用记录" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <div v-for="(item, index) in dataSource" :key="index"
      style="text-align: left;background-color: #fff;border-radius: 10px;padding:2%;width: 95%;margin: 0.5rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="20">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{ item.moId }}
            </span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="12">
          <span style="color:gary">坦克编号：
            <span style="color:black;">
              {{ item.tankNo }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary">胶体名称：
            <span style="color:black;">
              {{ item.name }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="12">
          <span style="color:gary">胶体编码：
            <span style="color:black;">
              {{ item.code }}
            </span>
          </span>
        </van-col>
        <van-col span="12">
          <span style="color:gary">胶体批次号：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.customer }}
            </span>
          </span>
        </van-col>
      </van-row>

      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary">灌装产品名称：
            <span style="color:black;">
              {{ item.gbname }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24">
          <span style="color:gary">灌装产品批号：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.batchNumber }}
            </span>
          </span>
        </van-col>
      </van-row>

      <van-row>
        <van-col span="24">
          <span style="color:gary">灌装产品编号：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.gbcode }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="14">
          <span style="color:gary">灌装线体：&emsp;&emsp;
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.jobCenter }}
            </span>
          </span>
        </van-col>
        <van-col span="10">
          <span style="color:gary">灌装组长：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.leaderNo }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="14">
          <span style="color:gary">灌装时间：&emsp;&emsp;
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.acceptTime }}
            </span>
          </span>
        </van-col>
        <van-col span="10">
          <span style="color:gary">使用容量：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.volume }}kg
            </span>
          </span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
import { Indicator } from 'mint-ui';
import { Toast } from 'vant';
export default {
  data() {
    return {
      dataSource: [],
      info: {},
      type: '',
    }
  },
  created() {
    console.log(this.$route.params);
    if (this.$route.params.type) {
      this.info = this.$route.params.item
      this.type = this.$route.params.type
      this.search()
    }
  },
  methods: {
    search() {
      this.$axios.get('/jeecg-boot/app/tank/check/colloidReviewDetail', { params: { code: this.info.code, name: this.info.name, customerId: this.info.customer, lpId: this.info.lpId, type: this.type } }).then(res => {
        if (res.data.code == 200) {
          console.log(res);
          this.dataSource = res.data.result
        } else {
          Toast({
            message: res.data.message,
            position: 'bottom',
            duration: 2000
          });
        }
      })
    },
    onClickLeft() {
      this.$router.go(-1)
    },
  },
}
</script>

<style>

</style>