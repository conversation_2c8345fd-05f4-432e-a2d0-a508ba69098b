<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <a-form-item
              :labelCol="labelCol"
              label="客户序列号"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入客户序列号" disabled="true" v-decorator="['customer',{rules:[{required:true,message:'请输入客户序列号'}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="取样类型"
              :wrapperCol="wrapperCol">
              <a-select placeholder="请输入取样类型" v-decorator="['type',{rules:[{required:true,message:'请输入取样类型'}]}]">
                <!-- <a-select-option value="2">公司取样</a-select-option> -->
                <a-select-option value="11">理化检测</a-select-option>
                <a-select-option value="12">微生物检测</a-select-option>
                <a-select-option value="13">公司留样</a-select-option>
                <a-select-option value="3">客户取样</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="reportName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入质检取样" v-decorator="['output',{rules:[{required:true,message:'请输入质检取样'},{validator: this.validateNum}]}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import pick from 'lodash.pick'
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "PostRecordModal",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        moIds:[],
        expandedRowKeys: [],
        id: '',
        unit:'',
        reportName:'',
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      
      add() {
        // 新增
        this.edit({});
      },

      edit(id,unit) {  
        this.visible = true;
        this.id=id;
        this.unit=unit
        this.reportName="质检取样数("+this.unit+")"
        this.getCustomer(this.id)
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      getCustomer(id){
        let self=this
        self.$axios.get('/jeecg-boot/app/gcWorkshop/getCustomerInfoNew',{params:{lpId:id}}).then(res=>{
          if(res.data.success){
            self.form.setFieldsValue({customer: res.data.message ? res.data.message : null})
            self.form.setFieldsValue({type: "11"})
          }
        })
      },
      handleOk() {
        let self=this
        this.form.validateFields((err, values) => {
          if (!err) {
            self.confirmLoading = true;
            let params={
              customer:self.form.getFieldValue("customer"),
              output:self.form.getFieldValue("output"),
              lpId:self.id,
              type:self.form.getFieldValue("type"),
              creator:localStorage.getItem('userCode')
            }
          
            self.$axios.post('/jeecg-boot/app/gcWorkshop/addLeadOutput',params).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                this.visible=false;
                this.$emit('ok');
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            }).finally(() => {
                  self.confirmLoading = false;
                  self.close();
            });
          }
        });
        
      },
      handleCancel() {
        this.close()
      },
      // 验证数字
      validateNum(rule, value, callback) {
        if (!value || new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,5})?$/).test(value)) {
          callback();
        } else {
          callback("请输入数字，小数最多5位!");
        }
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>