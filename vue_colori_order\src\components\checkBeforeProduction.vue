<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="制胶前检查复核" left-arrow @click-left="onClickLeft" right-text="复核" @click-right="check" />
        </van-sticky>
        <div v-for="(item, index) in dataArr" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <div v-if="index==0">
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
                <van-radio-group v-model="item.result" disabled>
                    <van-cell-group>
                        <van-cell title="清洁消毒结果合格" clickable @click="radio = '清洁消毒结果合格'">
                            <template #right-icon>
                                <van-radio name="清洁消毒结果合格" value="清洁消毒结果合格" />
                            </template>
                        </van-cell>
                        <van-cell title="连续生产无需清洗" clickable @click="radio = '连续生产无需清洗'">
                            <template #right-icon>
                                <van-radio name="连续生产无需清洗" value="连续生产无需清洗" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>

                <van-field readonly v-model="item.lastProduct" label-width="6.5rem" label="上批产品名称：" placeholder="请输入"
                    @blur="(e)=>lastProductfieldBlur(e,item)" />
                <van-field readonly label="消毒有效期：" :value="item.cleanDate" />
            </div>
            <div v-else-if="index==dataArr.length-1">
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
            </div>
            <div v-else>
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
                <van-cell>
                    <template #title>
                        <span class="custom-title">结果</span>
                    </template>
                    <template #default>
                        <div class="custom-title">
                            <van-radio-group disabled v-model="item.result" direction="horizontal">
                                <van-radio name="Y" value="Y">Y</van-radio>
                                <van-radio name="N" value="N">N</van-radio>
                            </van-radio-group>
                        </div>
                    </template>
                </van-cell>
            </div>
        </div>

    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator, MessageBox } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                data: '',
                showDate: false,
                isEdit: '1',
                checkStatus:'',
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                if(!this.$route.params){
                    this.$router.go(-1)
                }
                this.info = this.$route.params
                console.log(this.info);
                this.search()
            }
        },
        methods: {
            check() {
                if (this.info.checkStatus == 5) {
                    Toast({
                        message: "已复核",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                
                if (this.info.checkStatus != 4) {
                    Toast({
                        message: "检查记录未填写,不能复核",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                MessageBox.confirm('是否确认复核？').then(action => {
                    if (action == "confirm") {
                        this.$axios.get(`/jeecg-boot/app/batchRecord/approveZjzy?mixId=${this.info.id}&checkNo=${localStorage.getItem("userCode")}&checkName=${localStorage.getItem("userName")}`).then(res => {
                            if (res.data.success) {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                this.$router.push({name:"OrderStart"})
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                })
            },
            search() {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${this.info.id}`).then(res => {
                    if (res.data.code == 200) {
                        this.isEdit = res.data.result.checkStatus
                        this.dataArr = res.data.result.zjzyList
                        this.info.checkStatus=res.data.result.checkStatus
                    }
                });
            },
            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>