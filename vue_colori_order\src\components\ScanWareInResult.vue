<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div v-if="result!='' && result!=null">
            
            <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">扫描信息</div>
            <van-field label="托码号" :value="result" readonly/>
            <van-field label="产品编号" :value="itemParams.code" readonly/>
            <van-field label="产品名称" :value="itemParams.name" readonly/>
            <van-field label="客户批次号" :value="itemParams.customer" readonly/>
            <van-field label="入库单号" :value="itemParams.lpId" readonly/>
            <van-field label="入库箱数" :value="itemParams.cxOutput" readonly/>
            <van-field label="入库状态" value="未入库" v-if="itemParams.ckStatus=='0'" readonly/>
            <van-field label="入库状态" value="已入库" v-if="itemParams.ckStatus=='1'" readonly/>
            <van-field label="仓库名称" v-model="itemParams.warehouse"/>
            <div style="width:100%;position: relative;">
                <van-field label="货位号" v-model="itemParams.hwh"   style="width:70%;float:left;"/>
                <van-button type="info"  @click="changeHwh" style="height:35px;margin:5px;width:25%;border-radius:10px;float:left;">扫码</van-button>
                <div style="clear:both;"></div>
            </div>

            <van-button type="primary" @click="grounding('1')" v-if="itemParams.ckStatus!='1'"  style="margin-top:50px;width:40%;border-radius:10px;">确认</van-button>
            <van-button type="info" @click="grounding('2')" v-if="itemParams.ckStatus=='1'"  style="margin-top:50px;width:40%;border-radius:10px;">更改</van-button>

        </div>
        <div v-else>
            二维码已失效，请退出后重新扫描！
        </div>
    
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            result:'',
            type:'',
            itemParams:{},
            title1:'',
            title2:'',
            title3:'',
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.result=this.$route.params.result
        this.getSticker();
    },
    methods: {
        changeHwh(){
            let self=this
            MessageBox.confirm('',{
                message: '请选择扫码或者录入',
                title: '提示',
                confirmButtonText: '扫码',
                cancelButtonText: '录入'
                }).then(action => {
                    if(action=="confirm"){
                        wx.scanQRCode({
                            desc: 'scanQRCode desc',
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function(res) {
                                // 回调
                                var result = res.resultStr;//当needResult为1时返回处理结果
                                self.$axios.get('/jeecg-boot/app/sticker/getWarehouseById',{params:{id:result}}).then(res=>{
                                    if(res.data.code==200){
                                        self.itemParams.hwh=res.data.message
                                    }
                                })
                            },
                            error: function(res) {
                                if (res.errMsg.indexOf('function_not_exist') > 0) {
                                    alert('版本过低请升级')
                                }
                            }
                        });
                    }
                }).catch((res)=>{
                    if(res=="cancel"){
                        MessageBox.prompt('请录入货位号').then(({ value, action }) => {
                            if(action=="confirm"){
                                self.itemParams.hwh=value
                            }
                        });
                    }        
                });
        },
        inputOutput(e){
            let self=this;
            self.itemParams.cxOutput=0
        },
        changeOutput(value){
            let self=this;
            var vcxMinOutput=value*self.itemParams.exchange;
            self.itemParams.cxMinOutput=parseInt(vcxMinOutput)
        },
        changeMinOutput(value){
            let self=this;
            var vcxOutput=value/self.itemParams.exchange;
            if(vcxOutput==parseInt(vcxOutput)){
                vcxOutput = parseInt(vcxOutput);
            }else{
                vcxOutput = vcxOutput.toFixed(3)
            }
            self.itemParams.cxOutput=vcxOutput
        },
        cancelWareHouseIn(){
            let self=this;

            MessageBox.confirm('',{
                message: '请确认是否撤销入库？',
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.get('/jeecg-boot/app/sticker/cancelWareHouseIn',{params:{id:self.result,userCode:localStorage.getItem('userCode')}}).then(res=>{
                            if(res.data.code==200){
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                        position: 'bottom',
                                        duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });


            
        },
        grounding(num){
            let self=this;

            if(self.itemParams.hwh==null || self.itemParams.hwh==''){
                Toast({
                    message: '货位号不得为空！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            self.itemParams.type=num;
            self.itemParams.ckChecker=localStorage.getItem('userCode')
            self.itemParams.ckStatus='1'

            var message="是否确认提交产品上架的货位号为"+self.itemParams.hwh+"?";
            if(num=='2'){
                message="是否确认更改产品上架的货位号为"+self.itemParams.hwh+"?";
            }


            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/gcQrCode/updateWarehouseInInfo',self.itemParams).then(res=>{
                            if(res.data.code==200){
                                this.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });

        },
        submit(num){
            let self=this;
            let params={
                id:this.result,
                type:this.type,
                flag:num,
                plot:self.itemParams.plot,
                customer:self.itemParams.customer,
                exchange:self.itemParams.exchange,
                cxOutput:self.itemParams.cxMinOutput,
                userCode:localStorage.getItem('userCode')
            }

            var message="是否确认提报报工数量为"+self.itemParams.cxOutput+self.itemParams.unit+"?";
            if(num=='2'){
                message="是否更改提报报工数量为"+self.itemParams.cxOutput+self.itemParams.unit+"?";
            }

            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/gcQrCode/addSticker',params).then(res=>{
                            if(res.data.code==200){
                                this.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });

        },
        check(num){
            let self=this;

            let params={
                id:this.result,
                type:this.type,
                qcStatus:num,
                userCode:localStorage.getItem('userCode')
            }

            var message="是否确认此托码设为合格？";
            if(num=='2'){
                message="是否确认此托码设为不合格？";
            }

            MessageBox.confirm('',{
                message: message,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.post('/jeecg-boot/app/gcQrCode/addSticker',params).then(res=>{
                            if(res.data.code==200){
                                this.$router.go(-1);
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                            
                });
        },
        getSticker(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcQrCode/getWarehouseInInfoById',{params:{id:self.result}}).then(res=>{
                if(res.data.code==200){
                    self.itemParams=res.data.result
                    self.itemParams.warehouse="成品仓库";
                }else{
                     Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sign{
    width:100%;
    height:5rem;
}
.sign-type{
    height:100%;
    width:100%;
    color:white;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>