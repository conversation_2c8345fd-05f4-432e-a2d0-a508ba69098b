<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="异常记录填写" left-arrow @click-left="onClickLeft" right-text="新增" @click-right="addRecord" />
        </van-sticky>

        <van-dialog v-model="dialogshow" title="请输入异常内容" show-cancel-button showConfirmButton @confirm="confirm"
            @cancel="cancel">
            <van-field labelWidth="3rem" v-model="result" label="内容：" type="textarea" rows="3" autosize
                placeholder="请输入" />
        </van-dialog>
        <div v-for="(item, index) in dataArr" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <van-row>
                <van-col span="24">
                    <van-row>
                        <van-col span="6"> {{`异常记录${index+1}：`}}</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.result }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>

        </div>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator, MessageBox } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                dialogshow: false,
                result: '',
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                this.search()
            }
        },
        methods: {
            addRecord() {
                this.dialogshow = true
            },
            cancel() {
                this.dialogshow = false
            },
            confirm() {
                let params = {
                    mixId: this.info.id,
                    batchId: this.info.batchId,
                    creator: localStorage.getItem("userCode"),
                    result: this.result,
                }
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios
                    .post(`/jeecg-boot/app/batchRecord/editYcjl`, params)
                    .then(res => {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        if (res.data.code == 200) {
                            this.search()
                        }
                    })
                    .finally(() => {
                        Indicator.close();
                    });
            },
            search() {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${this.info.id}`).then(res => {
                    if (res.data.code == 200) {
                        this.dataArr = res.data.result.ycjlList
                        this.result = ''
                    }
                });
            },
            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>