<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <!-- <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select placeholder="请输入部门" v-decorator="['department',{rules:[{required:true,message:'请选择部门'}]}]" @change="deptChange">
                <a-select-option v-for="d in depart" :key="d">{{d}}</a-select-option>
              </a-select>
            </a-form-item> -->
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-select placeholder="请输入车间" v-decorator="['workshop',{rules:[{required:true,message:'请选择车间'}]}]">
                <a-select-option v-for="c in currentCheJian" :key="c.name">{{c.name}}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入离开原因" v-decorator="['reason',{}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "OutModal",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: "/gc/gcTechnologyPlan/addGcPlan",
          edit: "/test/order/edit",
          quota: "/gc/gcLead/getTechnology",
          getMoPlot: "/gc/gcTechnologyPlan/getRestCount",
          mode: "/gc/gcLead/getWorkmode",
          order: "/gc/gcLead/getOrderInfo",
          worker: "/gc/gcLead/getWorker",
          material: "/gc/gcLead/getMaterial",
          preplot:"/gc/gcLead/getPrePlot",
          restcount:"/gc/gcLead/getRestCount",
          getOrderNo: "/gc/gcTechnologyPlan/getOrderNo",
          orderCustomerList: "/test/order/listOrderCustomerByMainId",
          orderTicketList: "/test/order/listOrderTicketByMainId",
          toSelectEmp:"/gc/gcPeople/getPeopleInfo"
        },
        moIds:[],
        expandedRowKeys: [],
        id: ' ',
        item:[],
        depart:[],
        currentCheJian:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },

      edit(item,department) {  
        this.visible = true;
        this.item=item;
        console.log(department)
        this.deptChange(department);
      },
      deptChange(department){
        this.form.setFieldsValue({
          workshop:undefined
        })
        this.$axios.get('/jeecg-boot/app/gcWorkshop/getFactoryInfoByDepartment',{params:{department:department}}).then(res=>{
          if(res.data.code=200){
            // Toast({
            //   message: res.data.message,
            //   position: 'bottom',
            //   duration: 2000
            // });
            this.currentCheJian=res.data.result;
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        })
      },
      getDepartment(){
        this.$axios.get('/jeecg-boot/app/gcWorkshop/getDepartmentInfo',{params:{type:'1'}}).then(res=>{
          if(res.data.code=200){
            // Toast({
            //   message: res.data.message,
            //   position: 'bottom',
            //   duration: 2000
            // });
            this.depart=res.data.result;
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
        this.dataSource = []
        this.flag = 0
        this.saveCode = ''
        this.saveStation = ''
      },
      handleOk() {
        let self=this
        this.form.validateFields((err, values) => {
          if (!err) {
            for(var i=0;i<self.item.length;i++){
              console.log(self.item[0])
              self.item[i].departmentTem=self.form.getFieldValue("department")
              self.item[i].workshopTem=self.form.getFieldValue("workshop")
              self.item[i].reason=self.form.getFieldValue("reason")
            }
            let params={
              lpId:self.item[0].lpId,
              gcWorkOperationList:self.item,
              type:'6',
              createNo:localStorage.getItem('userCode'),
              creator:localStorage.getItem('userName')
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                this.visible=false;
                this.$emit('ok');
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            });
          }
        })
      },
      handleCancel() {
        this.close()
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>