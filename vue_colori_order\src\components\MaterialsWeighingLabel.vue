<template>
    <div style="width: 100%;height:100;overflow: hidden;">
        <button @click="type=1">type=1</button>
        <button @click="type=2">type=2</button>
        <button @click="type=3">type=3</button>
        <button @click="logtype">logtype</button>
        <button @click="logtype">logtype</button>


        <button @click="searchPrint">开始扫描</button>
        <button @click="stopBluetoothDevicesDiscovery">停止扫描</button>
        <div class="devices_summary">已发现 {{devices.length}} 个外围设备：</div>
        <div class="connected_info">
            <div>
                <div><span>已连接到: {{bindDevice.name}} 是否可读: {{bindDevice.read}} 是否可写: {{bindDevice.write}}</span></div>
                <div class="operation">
                    <button @click="clickPrint">打印订单及二维码</button>
                    <button size="mini" @click="closeBLEConnection">断开连接</button>
                </div>
            </div>
        </div>

        <div class="device_list">
            <div v-for="(item,index) in devices" :key="index" @click="setBindDevice(item)" class="device_item">
                <div style="font-size: 16px; color: #333;">{{item.name}}</div>
                <div style="font-size: 10px">信号强度: {{item.RSSI*1+100}}%</div>
                <div style="font-size: 10px">UUID: {{item.deviceId}}</div>
                <div style="font-size: 10px">Service数量: {{item.advertisServiceUUIDs.length}}</div>
            </div>
        </div>


        <table id="tableToCavas" border="2"
            style="width:100%;height:100%;font-family:Microsoft YaHei;color: #000;font-weight: 700;text-align: left;">
            <tr>
                <td colspan="4">
                    <van-row>
                        <van-col :span="4">
                            胶体编码:
                        </van-col>
                        <van-col :span="8">
                            {{ info.glueCode }}
                        </van-col>
                        <van-col :span="4">
                            胶体批次:
                        </van-col>
                        <van-col :span="8">
                            {{ info.glueBatchCode }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <van-row>
                        <van-col :span="4">
                            胶体名称:
                        </van-col>
                        <van-col :span="20">
                            {{ info.glueName }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <van-row>
                        <van-col :span="4">
                            原料编码:
                        </van-col>
                        <van-col :span="8">
                            {{ info.materialCode }}
                        </van-col>
                        <van-col :span="4">
                            接收批号:
                        </van-col>
                        <van-col :span="8">
                            {{ info.customer }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <van-row>
                        <van-col :span="4">
                            原料名称:
                        </van-col>
                        <van-col :span="20">
                            {{ info.materialName }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <van-row>
                        <van-col :span="4">
                            供应商:
                        </van-col>
                        <van-col :span="20">
                            {{ info.supplier }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <van-row>
                        <van-col :span="6">
                            理论(KG):
                        </van-col>
                        <van-col :span="6">
                            {{ info.respondWeight }}
                        </van-col>
                        <van-col :span="5">
                            称量秤:
                        </van-col>
                        <van-col :span="7">
                            {{ info.balance }}
                        </van-col>
                    </van-row>
                </td>
                <td rowspan="3">
                    <div>
                        <div id="qrcodeaa" ref="qrcodeaa"></div>
                        <div style="text-align: center;">{{info.id}}</div>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <van-row>
                        <van-col :span="6">
                            皮重(KG):
                        </van-col>
                        <van-col :span="6">
                            {{ info.tareWeight }}
                        </van-col>
                        <van-col :span="6">
                            实配(KG):
                        </van-col>
                        <van-col :span="6">
                            {{ info.actualWeight }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <van-row>
                        <van-col :span="5">
                            配料员:
                        </van-col>
                        <van-col :span="7">
                            {{ info.batcherName }}
                        </van-col>
                        <van-col :span="3">
                            时间:
                        </van-col>
                        <van-col :span="9">
                            {{ info.batcherTime }}
                        </van-col>
                    </van-row>
                </td>
            </tr>
        </table>
        <img style="width: 100%;height: 100%;margin-top: 1rem;" :src="imgsrc" alt="">


    </div>
</template>

<script>
    import QRCode from 'qrcodejs2';

    import html2canvas from 'html2canvas'

    // import VConsole from 'vconsole';
    // let vConsole = new VConsole();
    let wx = window.wx
    import Buffer from 'buffer'
    import { encode } from '../utils/gbk.js';

    export default {
        data() {
            return {
                info: {},


                imgsrc: '',

                devices: [],
                bindDevice: '',
                platform: 'android',
                type: 1,
            };
        },
        created() {
            this.info = this.$route.params.result;
            console.log(this.info);

            this.platform = this.getEquipmentInfo()
            console.log(this.platform);
        },
        mounted() {
            const that = this
            this.$nextTick(function () {
                new QRCode('qrcodeaa', {
                    width: 80,
                    height: 80,
                    text: that.info.id, // 需要二维码跳转的地址
                    colorDark: "#333333", //二维码颜色
                    colorLight: "#ffffff", //二维码背景色
                    correctLevel: QRCode.CorrectLevel.L//容错率，L/M/H
                })
            })
            // 直接生成图片
            this.takeScreenshot()
        },
        methods: {
            logtype() {
                console.log(this.type);
            },
            takeScreenshot() {
                console.log('test');
                setTimeout(() => {
                    html2canvas(document.getElementById('tableToCavas')).then((canvas) => {
                        const src = canvas.toDataURL()
                        this.imgsrc = src
                    });
                }, 0)
            },
            getEquipmentInfo() {
                var u = navigator.userAgent

                if (!!u.match(/compatible/i) || u.match(/Windows/i)) {
                    return 'windows'
                } else if (!!u.match(/Macintosh/i) || u.match(/MacIntel/i)) {
                    return 'macOS'
                } else if (!!u.match(/iphone/i) || u.match(/Ipad/i)) {
                    return 'ios'
                } else if (u.match(/android/i)) {
                    return 'android'
                } else if (u.match(/Ubuntu/i)) {
                    return 'Ubuntu'
                } else {
                    return 'other'
                }
            },






            //重复搜索时，判断设备是否已经存在过滤掉
            findArrItem(arr, id) {
                let index = -1;
                for (let i in arr) {
                    if (arr[i].deviceId == id) {
                        index = i;
                    }
                }
                return index;
            },

            //断开与低功耗蓝牙设备的连接
            closeBLEConnection() {
                let that = this,
                    bindDevice = that.bindDevice;
                wx.closeBLEConnection({
                    deviceId: bindDevice.deviceId
                })
                //重置属性
                that.bindDevice = ''
            },

            //停止蓝牙搜索
            stopBluetoothDevicesDiscovery() {
                let that = this;
                wx.stopBluetoothDevicesDiscovery({
                    success() {
                        console.log('stopBluetoothDevicesDiscovery success')
                    },
                    fail() {
                        console.log('stopBluetoothDevicesDiscovery fail')
                    }
                })
            },

            //开始搜寻附近的蓝牙设备
            startBluetoothDevicesDiscovery() {
                let that = this;
                //停止蓝牙搜索
                that.stopBluetoothDevicesDiscovery();
                //开启蓝牙搜索
                wx.startBluetoothDevicesDiscovery({
                    allowDuplicatesKey: false,
                    success: (res) => {
                        console.log('开启蓝牙搜索成功', res)
                        //搜索，监听返回结果
                        that.onBluetoothDeviceFound()
                    },
                    fail: (res) => {
                        console.log("开启蓝牙搜索失败", res);
                    }
                });
            },

            //寻找到新设备的事件的回调函数
            onBluetoothDeviceFound() {
                let that = this;
                wx.onBluetoothDeviceFound((res) => {
                    console.log('找到新的蓝牙设备', res);
                    res.forEach(device => {
                        if (!device.name && !device.localName) {
                            return
                        }
                        const foundDevices = that.devices;
                        const idx = that.findArrItem(foundDevices, device.deviceId);
                        const data = {};
                        if (idx === -1) {
                            foundDevices.push(device);
                        } else {
                            foundDevices[idx] = device;
                        }
                        console.log('过滤后的蓝牙设备', that.devices);
                        that.devices = foundDevices
                    })
                })
            },

            //查询蓝牙设备
            searchPrint() {
                let that = this;
                //避免连接过多,先断开所有连接,每次初始化才能确保稳定，想跳过的，自己在踩坑44.
                wx.closeBluetoothAdapter({
                    complete: () => {
                        //初始化蓝牙
                        wx.openBluetoothAdapter({
                            success: (res) => {
                                that.startBluetoothDevicesDiscovery();
                            },
                            fail: (res) => {
                                if (res.errCode === 10001) {
                                    console.log('蓝牙未开启');
                                } else {
                                    console.log('蓝牙初始化失败');
                                }
                            }
                        })
                    }
                });
            },

            //绑定设备
            setBindDevice(item) {
                let that = this;
                console.log('点击的绑定设备', item);
                //创建连接，测试设备是否可以读，可写
                that.createBLEConnection(item);
            },

            //低功耗蓝牙设备连接
            createBLEConnection(item, callback) {
                console.log('低功耗蓝牙设备开始连接');
                let that = this;
                wx.createBLEConnection({
                    deviceId: item.deviceId,
                    timeout: 15000,
                    success: (res) => {
                        console.log('蓝牙连接成功');
                        that.bindDevice = item
                        //如果蓝牙设备write没定义，说明是新设备需要执行
                        if (that.platform == 'android') {
                            if (item.write == undefined) {
                                //检查该蓝牙设备是否有写入权限，并保存参数，以便发送数据
                                that.getBLEDeviceServices(item.deviceId, callback);
                            } else {
                                callback ? callback() : ''
                            }
                        } else {
                            that.getBLEDeviceServices(item.deviceId, callback);
                        }

                    },
                    fail: (res) => {
                        console.log("蓝牙连接失败:", res);
                    }
                })
            },

            //获取蓝牙设备所有服务(service)
            getBLEDeviceServices(deviceId, callback) {
                let that = this;
                wx.getBLEDeviceServices({
                    deviceId,
                    success: (res) => {
                        console.log('蓝牙设备所有服务', res.services)
                        for (let i = 0; i < res.services.length; i++) {
                            if (res.services[i].isPrimary) {
                                that.getBLEDeviceCharacteristics(deviceId, res.services[i].uuid, callback)
                                return
                            }
                        }
                    },
                    fail: (res) => {
                        console.log("获取蓝牙服务失败：" + JSON.stringify(res))
                    }
                })
            },

            //获取蓝牙设备某个服务中所有特征值(characteristic)
            getBLEDeviceCharacteristics(deviceId, serviceId, callback) {
                let that = this;
                console.log('开始获取某个服务中所有特征值', deviceId, serviceId)
                wx.getBLEDeviceCharacteristics({
                    deviceId,
                    serviceId,
                    success: (res) => {
                        console.log('蓝牙设备某个服务中所有特征值 获取success', res.characteristics)
                        for (let i = 0; i < res.characteristics.length; i++) {
                            let item = res.characteristics[i];
                            let _uuid
                            if (that.type == 1) {
                                _uuid = item.uuid;
                            } else if (that.type == 2) {
                                _uuid = '0000FF02-0000-1000-8000-00805F9B34FB'
                                item.properties.write = true
                            } else {
                                _uuid = item.uuid;
                            }
                            let bindDevice = that.bindDevice;
                            // 读取低功耗蓝牙设备的特征值的二进制数据值 注意：必须设备的特征值支持 read 才可以成功调用。
                            if (item.properties.read) {
                                bindDevice.read = true;
                            }
                            //向低功耗蓝牙设备特征值中写入二进制数据。注意：必须设备的特征值支持 write 才可以成功调用。
                            if (item.properties.write) {
                                bindDevice.serviceId = serviceId;
                                bindDevice.characteristicId = _uuid;
                                bindDevice.write = true;
                                callback ? callback() : ''
                            }
                            //启用低功耗蓝牙设备特征值变化时的 notify 功能，使用characteristicValueChange事件
                            if (item.properties.notify || item.properties.indicate) {
                                wx.notifyBLECharacteristicValueChange({
                                    deviceId,
                                    serviceId,
                                    characteristicId: _uuid,
                                    state: true,
                                })
                            }
                            //设置当前选中的蓝牙设备，包括是否可读写属性采集
                            that.bindDevice = bindDevice
                            console.log("🚀bindDevice:", that.bindDevice)
                        }
                    },
                    fail(res) {
                        console.error('获取特征值失败：', res)
                    }
                })
            },

            //点击打印
            clickPrint() {
                let that = this
                //避免连接过多，先断开所有连接
                wx.closeBluetoothAdapter({
                    success: () => {
                        console.log('点击打印');
                        //初始化蓝牙
                        wx.openBluetoothAdapter({
                            success: (res) => {
                                wx.startBluetoothDevicesDiscovery({
                                    allowDuplicatesKey: false,
                                    complete: (res) => {
                                        callbackOpenBluetooth();
                                        that.stopBluetoothDevicesDiscovery();
                                    }
                                });
                            },
                            fail: (res) => {
                                if (res.errCode === 10001) {
                                    console.log('蓝牙未开启!');
                                } else {
                                    console.log('蓝牙初始化失败!');
                                }
                            }
                        })
                    }
                });

                //直接打印
                let callbackConnected = function () {
                    console.log('数据生成中...');
                    that.writeBLECharacteristicValue(that.bindDevice);
                };

                //检查设备是否连接
                let callbackOpenBluetooth = function () {
                    console.log('检查设备是否连接...');
                    that.getConnectedBluetoothDevices(that.bindDevice, callbackConnected);
                };
            },

            //获取已经连接的蓝牙设备,并开始打印
            getConnectedBluetoothDevices(device, callback) {
                console.log('获取已经连接的蓝牙设备', device, callback);
                let that = this;
                wx.getConnectedBluetoothDevices({
                    services: [device.serviceId],
                    success(res) {
                        console.log('获取已经连接的蓝牙设备success', res, device);
                        let devices = res.devices;
                        let index = that.findArrItem(devices, device.deviceId);
                        if (index === -1) {
                            //如果该设备不是连接的，断开所有蓝牙连接
                            for (let i in devices) {
                                that.closeBLEConnection(devices[i]);
                            }
                            //重新连接该设备，重新点击打印，再次发送数据给打印机
                            that.createBLEConnection(device, callback);
                        } else {
                            callback ? callback() : ''
                        }
                    }
                })
            },

            //小票打印机打印
            writeBLECharacteristicValue(device) {
                let that = this;
                let arrPrint = [];
                //初始化打印机
                arrPrint.push(that.sendDirective([0x1B, 0x40])); //16进制
                //居中对齐
                arrPrint.push(that.sendDirective([0x1B, 0x61, 0x01])); //居中
                //正文
                arrPrint.push(that.sendDirective([0x1B, 0x0E]));
                arrPrint.push(that.hexStringToBuff("\ttttttTTTTTttttt\n\n"));
                arrPrint.push(that.sendDirective([0x1B, 0x14]));
                arrPrint.push(that.hexStringToBuff('tttttttttttttttt' + "\n"));
                arrPrint.push(that.hexStringToBuff("单号" + 'W20154221212222' + "\n"));
                arrPrint.push(that.hexStringToBuff("--------------------------------\n"));
                arrPrint.push(that.hexStringToBuff(that.printThreeData('a', 'b', 'c')));
                arrPrint.push(that.hexStringToBuff(that.printThreeData('测试', 10, '3元')))
                arrPrint.push(that.hexStringToBuff("--------------------------------\n"));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'aaaaaaaaaa')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'aaaaaaaaaaaaaaaaaaaa')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'aaaaaaaaaaaaaaa')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", '2019-10-20 12:20:20')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'asdasdadfasfasf')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'asffffffffffffffffffffffffffffffffffffffffffffffffffff')));
                arrPrint.push(that.hexStringToBuff(that.printTwoData("名称:", 'asfasfasfafa')));
                arrPrint.push(that.sendDirective([0x1B, 0x61, 0x00]));
                arrPrint.push(that.hexStringToBuff("1.asdasddddddddddd；\n"));
                arrPrint.push(that.hexStringToBuff("2.asdasddddddddddd；asdasddddddddddd；asdasddddddddddd；asdasddddddddddd；asdasddddddddddd；\n"));
                arrPrint.push(that.hexStringToBuff("\n\n\n"));

                let printImgT = function () {
                    that.printImgT(device, 'W20154221212222');
                };

                //只测试文字
                //that.printInfo(device, arrPrint);
                //只测试二维码
                //that.printImgT(device, 'W20154221212222');
                //测试文字和二维码
                that.printInfo(device, arrPrint, printImgT);

            },

            //打印图片
            async printImgT(device, text) {
                console.log('Buffer', Buffer);
                console.log('打印图片', device, text);
                let that = this;
                html2canvas(document.getElementById('tableToCavas')).then((canvas) => {
                    const src = canvas.toDataURL()
                    let arr = that.convert4to1(src);
                    console.log('打印图片arr', arr);
                    let data = that.convert8to1(arr);
                    const cmds = [].concat([27, 97, 1], [29, 118, 48, 0, 20, 0, 160, 0], data, [27, 74, 3], [27, 64]);
                    const buffer = toArrayBuffer(Buffer.from(cmds, 'gb2312'));
                    let arrPrint = [];
                    arrPrint.push(that.sendDirective([0x1B, 0x40]));
                    // arrPrint.push(that.sendDirective([0x1B, 0x61, 0x01])); //居中
                    for (let i = 0; i < buffer.byteLength; i = i + 20) {
                        arrPrint.push(buffer.slice(i, i + 20));
                    }
                    arrPrint.push(that.hexStringToBuff("\n"));
                    arrPrint.push(that.sendDirective([0x1B, 0x61, 0x01])); //居中
                    arrPrint.push(that.hexStringToBuff("扫码识别订单号\n"));
                    arrPrint.push(that.hexStringToBuff("\n"));
                    arrPrint.push(that.hexStringToBuff("\n"));
                    that.printInfo(device, arrPrint);
                });

            },

            printInfo(device, arr, callback) {
                let that = this;
                if (arr.length > 0) {
                    that.sendStr(device, arr[0], function (success) {
                        arr.shift();
                        that.printInfo(device, arr, callback);
                    }, function (error) {
                        console.log(error);
                    });
                } else {
                    callback ? callback() : '';
                }
            },

            //发送数据
            sendStr(device, bufferstr, success, fail) {
                let that = this;
                console.log('发送数据', device);
                wx.writeBLECharacteristicValue({
                    deviceId: device.deviceId,
                    serviceId: device.serviceId,
                    characteristicId: device.characteristicId,
                    value: bufferstr,
                    success: function (res) {
                        success(res);
                        console.log('发送成功', bufferstr)
                    },
                    failed: function (res) {
                        fail(res)
                        console.log("数据发送失败:" + JSON.stringify(res))
                    },
                    // complete: function(res) {
                    // 	console.log("发送完成:" + JSON.stringify(res))
                    // }
                })
            },

            //4合1
            convert4to1(res) {
                let arr = [];
                for (let i = 0; i < res.length; i++) {
                    if (i % 4 == 0) {
                        let rule = 0.29900 * res[i] + 0.58700 * res[i + 1] + 0.11400 * res[i + 2];
                        if (rule > 200) {
                            res[i] = 0;
                        } else {
                            res[i] = 1;
                        }
                        arr.push(res[i]);
                    }
                }
                return arr;
            },

            //8合1
            convert8to1(arr) {
                let data = [];
                for (let k = 0; k < arr.length; k += 8) {
                    let temp = arr[k] * 128 + arr[k + 1] * 64 + arr[k + 2] * 32 + arr[k + 3] * 16 + arr[k + 4] * 8 + arr[k + 5] * 4 +
                        arr[k + 6] * 2 + arr[k + 7] * 1
                    data.push(temp);
                }
                return data;
            },


            formatTime(data) {
                const year = date.getFullYear()
                const month = date.getMonth() + 1
                const day = date.getDate()
                const hour = date.getHours()
                const minute = date.getMinutes()
                const second = date.getSeconds()

                return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
            },

            formatNumber(n) {
                n = n.toString()
                return n[1] ? n : '0' + n
            },


            hexStringToBuff(str) { //str='中国：WXHSH'
                //const buffer = new ArrayBuffer((sumStrLength(str)) * 4)
                const buffer = new ArrayBuffer((this.sumStrLength(str)) + 1);
                const dataView = new DataView(buffer)
                var data = str.toString();
                var p = 0; //ArrayBuffer 偏移量
                for (var i = 0; i < data.length; i++) {
                    if (this.isCN(data[i])) { //是中文
                        //调用GBK 转码
                        // console.log(29,data[i])
                        var t = encode(data[i]);
                        for (var j = 0; j < 2; j++) {
                            //var code = t[j * 2] + t[j * 2 + 1];
                            var code = t[j * 3 + 1] + t[j * 3 + 2];
                            var temp = parseInt(code, 16)
                            //var temp = strToHexCharCode(code);
                            dataView.setUint8(p++, temp)
                        }
                    } else {
                        var temp = data.charCodeAt(i);
                        dataView.setUint8(p++, temp)
                    }
                }
                return buffer;
            },

            toUnicode(s) {
                var str = "";
                for (var i = 0; i < s.length; i++) {
                    str += "\\u" + s.charCodeAt(i).toString(16) + "\t";
                }
                return str;
            },

            strToHexCharCode(str) {
                if (str === "")
                    return "";
                var hexCharCode = [];
                hexCharCode.push("0x");
                for (var i = 0; i < str.length; i++) {
                    hexCharCode.push((str.charCodeAt(i)).toString(16));
                }
                return hexCharCode.join("");
            },

            sumStrLength(str) {
                var length = 0;
                var data = str.toString();
                for (var i = 0; i < data.length; i++) {
                    if (this.isCN(data[i])) { //是中文
                        length += 2;
                    } else {
                        length += 1;
                    }
                }
                return length;
            },

            isCN(str) {
                let characterA = /^[\u4e00-\u9fa5]+$/
                let characterB = /^[\uFF01]|[\uFF0C-\uFF0E]|[\uFF1A-\uFF1B]|[\uFF1F]|[\uFF08-\uFF09]|[\u3001-\u3002]|[\u3010-\u3011]|[\u201C-\u201D]|[\u2013-\u2014]|[\u2018-\u2019]|[\u2026]|[\u3008-\u300F]|[\u3014-\u3015]+$/
                if (characterA.test(str) || characterB.test(str)) {
                    return true;
                } else {
                    return false;
                }
            },

            //汉字转码
            hexStringToArrayBuffer(str) {
                const buffer = new ArrayBuffer((str.length / 2) + 1)
                const dataView = new DataView(buffer)
                for (var i = 0; i < str.length / 2; i++) {
                    var temp = parseInt(str[i * 2] + str[i * 2 + 1], 16)
                    dataView.setUint8(i, temp)
                }
                dataView.setUint8((str.length / 2), 0x0a)
                return buffer;
            },

            //返回八位数组
            subString(str) {
                var arr = [];
                if (str.length > 8) { //大于8
                    for (var i = 0;
                        (i * 8) < str.length; i++) {
                        var temp = str.substring(i * 8, 8 * i + 8);
                        arr.push(temp)
                    }
                    return arr;
                } else {
                    return str
                }
            },

            //不带有汉字
            hexStringToArrayBufferstr(str) {
                let val = ""
                for (let i = 0; i < str.length; i++) {
                    if (val === '') {
                        val = str.charCodeAt(i).toString(16)
                    } else {
                        val += ',' + str.charCodeAt(i).toString(16)
                    }
                }
                val += "," + "0x0a";
                console.log(val)
                // 将16进制转化为ArrayBuffer
                return new Uint8Array(val.match(/[\da-f]{2}/gi).map(function (h) {
                    return parseInt(h, 16)
                })).buffer
            },

            //换行符号
            send0X0A() {
                const buffer = new ArrayBuffer(1)
                const dataView = new DataView(buffer)
                dataView.setUint8(0, 0x0a)
                return buffer;
            },

            sendDirective(arr) {
                const buffer = new ArrayBuffer(arr.length)
                const dataView = new DataView(buffer)
                for (let i in arr) {
                    dataView.setUint8(i, arr[i])
                }
                return buffer;
            },

            replaceStr(str) {
                str = str.toString();
                // console.log(147, str)
                if (str) {
                    let len = this.getBytesLength(str);
                    let minLen = 4;
                    if (len < minLen) {
                        let nstr = '';
                        for (let i = 0; i < minLen - len; i++) {
                            nstr = nstr + ' ';
                        }
                        str = nstr + str;
                    } else {
                        str = str;
                    }
                } else {
                    str = ''
                }
                console.log(163, str);
                return str
            },

            //计算打印的位置
            printTwoData(leftText, rightText) {
                let sb = '';
                let maxLen = 32;
                let leftTextLength = this.getBytesLength(leftText);
                let rightTextLength = this.getBytesLength(rightText);
                let spaceLen = maxLen - leftTextLength - rightTextLength;
                sb = sb + leftText;
                for (let i = 0; i < spaceLen; i++) {
                    sb = sb + ' ';
                }
                sb = sb + rightText + '\n';
                return sb.toString();
            },

            printThreeData(leftText, centerText, rightText) {
                let sb = '';
                let maxLen = 32;

                leftText = this.replaceStr(leftText);
                centerText = this.replaceStr(centerText);
                rightText = this.replaceStr(rightText);

                let leftTextLength = this.getBytesLength(leftText);
                let centerTextLength = this.getBytesLength(centerText);
                let rightTextLength = this.getBytesLength(rightText);
                let spaceLeft = maxLen / 2 - leftTextLength - centerTextLength / 2;
                let spaceRight = maxLen / 2 - centerTextLength / 2 - rightTextLength;

                console.log(168, leftTextLength, centerTextLength, rightTextLength, spaceLeft, spaceRight);
                sb = sb + leftText;
                for (let i = 0; i < spaceLeft; i++) {
                    sb = sb + ' ';
                }
                sb = sb + centerText;
                for (let i = 0; i < spaceRight; i++) {
                    sb = sb + ' ';
                }
                sb = sb + rightText + '\n';
                console.log('printThreeData', sb);
                return sb.toString();
            },

            getBytesLength(val) {
                var str = new String(val);
                var bytesCount = 0;
                for (var i = 0, n = str.length; i < n; i++) {
                    var c = str.charCodeAt(i);
                    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                        bytesCount += 1;
                    } else {
                        bytesCount += 2;
                    }
                }
                return bytesCount;
            }
        },
    };
</script>

<style lang="less" scoped>
    .devices_summary {
        margin-top: 30px;
        padding: 10px;
        font-size: 16px;
    }

    .device_list {
        overflow: scroll;
        height: 300px;
        margin: 50px 5px;
        margin-top: 0;
        border: 1px solid #EEE;
        border-radius: 5px;
        width: auto;
    }

    .device_item {
        border-bottom: 1px solid #EEE;
        padding: 10px;
        color: #666;
    }

    .device_item_hover {
        background-color: rgba(0, 0, 0, .1);
    }

    .connected_info {
        width: 100%;
        background-color: #F0F0F0;
        padding: 10px;
        margin-bottom: env(safe-area-inset-bottom);
        font-size: 14px;
        min-height: 100px;
        box-shadow: 0px 0px 3px 0px;
    }

    .connected_info .operation {
        display: inline-block;
    }

    .connected_info .operation button {
        margin: 5px;
    }
</style>