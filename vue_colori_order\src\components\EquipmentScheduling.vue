<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="设备部排班" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <van-button round type="info" @click="schedulingAdd" style="width: 90%;">
                新增排班
            </van-button>
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '35%' }">
            <!-- 时间 -->
            <van-field readonly clickable label="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />

            <!-- 班次 -->
            <van-field readonly clickable :value="category" label="班次" placeholder="点击选择班次"
                @click="showCategoryPicker = true" />
            <van-popup v-model="showCategoryPicker" position="bottom">
                <van-picker show-toolbar :columns="categoryColumns" @confirm="categoryConfirm"
                    @cancel="showCategoryPicker = false" />
            </van-popup>
            <!-- 车间 -->
            <van-field v-model="workshop" label="车间" placeholder="请输入车间" />
            <van-field v-model="worker" label="工人" placeholder="请输入工人" />

            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <van-checkbox-group v-model="result" ref="checkboxGroup">
            <van-cell-group>
                <!-- @click="toggle(index)" -->
                <van-cell v-for="(item, index) in schedulingList" clickable :key="index">
                    <template #default>
                        <div style="text-align: left;margin-left:5% ;">

                            <van-row gutter="20">
                                <van-col span="8" style="fontSize:18px;font-weight: 700;">{{ item.leaderNo }}
                                </van-col>
                                <van-col span="8">{{ item.workDay }}
                                </van-col>
                                <van-col span="8">{{ item.category }}
                                </van-col>
                            </van-row>
                            <van-row gutter="0">
                                <van-col span="16">{{ item.book }}</van-col>
                                <van-col span="8">
                                    <van-button style="height:100%" plain type="info" @click="edit(item)">编辑
                                    </van-button>
                                </van-col>
                            </van-row>
                            <van-row gutter="0">
                                <van-col span="12">{{ item.workshop }}</van-col>
                                <van-col span="4">{{ item.type }}</van-col>
                                <van-col span="8">
                                    <van-button style="height:100%" plain type="info" @click="detail(item)">删除
                                    </van-button>
                                </van-col>
                            </van-row>
                        </div>
                    </template>
                    <template #icon>
                        <van-checkbox :name="item" ref="checkboxes" />
                    </template>

                </van-cell>
            </van-cell-group>
        </van-checkbox-group>
        <div style="height:5%;width: 100%;">&nbsp;</div>
        <van-row style="background-color:#fff;position: fixed;bottom: 0;right: 0;z-index: 99;width: 100%;height: 5%;"
            gutter="30">
            <van-col span="4">
                <van-button :plain="plain" icon="success" type="info" round size="mini" @click="toggleAll">
                </van-button>
            </van-col>
            <van-col span="11"></van-col>
            <van-col span="8">
                <van-button round style="height:25px" type="info" size="large" @click="deleteBatch">批量删除</van-button>
            </van-col>
        </van-row>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            schedulingList: [],
            beginDay: this.getDate(0),
            show: false,
            // 时间选择框是否显示
            show1: false,
            endDay: this.getDate(0),

            categoryColumns: ['全部', '白班', '晚班'],
            showCategoryPicker: false,
            // 搜索条件 时间
            date: this.getDate(0) + ' ~ ' + this.getDate(0),
            // 班次
            category: '',
            // 车间
            workshop: '',
            // 工人
            worker: '',
            // 选中数组
            result: [],
            // 全选按钮
            plain: true,
        }
    },
    created() {
        this.search()
    },
    methods: {
        schedulingAdd() {
            this.$router.push({
                name: 'EquipmentSchedulingAdd'
            })
        },
        search() {
            this.result = []
            this.$axios
                .get(`/jeecg-boot/app/device/getClassList?userCode=${localStorage.getItem('userCode')}&beginDay=${this.beginDay}&endDay=${this.endDay}&category=${this.category}&workshop=${this.workshop}&worker=${this.worker}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.schedulingList = res.data.result.records
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        onClickRight() {
            this.show = true;
        },
        getDate(day) {
            var date1 = new Date(),
                time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
        },
        // 时间
        onConfirm(date) {
            const [start, end] = date;
            this.beginDay = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
            this.endDay = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
            this.date = `${this.beginDay}~${this.endDay}`
            this.show1 = false;
        },
        // 班次
        categoryConfirm(value) {
            this.category = value == '全部' ? '' : value;
            console.log(this.category);
            this.showCategoryPicker = false;
        },

        // 选中方法
        // checkAll() {
        //     this.$refs.checkboxGroup.toggleAll(true);
        // },
        toggleAll() {
            if (this.result.length != this.schedulingList.length) {
                this.plain = false
                this.$refs.checkboxGroup.toggleAll(true);
            } else {
                this.plain = true
                this.$refs.checkboxGroup.toggleAll();
            }
        },
        // toggle(index) {
        //     this.$refs.checkboxes[index].toggle();
        // },

        
        // 批量删除
        deleteBatch() {
            if (this.result.length <= 0) {
                Toast({
                    message: '请选择一条记录！',
                    position: "bottom",
                    duration: 2000
                });
                return;
            } else {
                let ids = "";
                this.result.forEach(item => {
                    ids += item.id + ','
                });
                ids = ids.substring(0, ids.length - 1);
                Dialog.confirm({
                    title: '',
                    message: '确认全部删除吗?',
                })
                    .then(() => {
                        this.$axios
                            .delete(`/jeecg-boot/app/device/deleteBatch?ids=${ids}&creator=${localStorage.getItem('userCode')}`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.search()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    })
                    .catch(() => {
                        Toast({
                            message: '取消',
                            position: "bottom",
                            duration: 2000
                        });
                    });
            }
        },
        // 删除 
        detail(item) {

            this.result = []
            Dialog.confirm({
                title: '',
                message: '确认删除吗?',
            })
                .then(() => {
                    this.$axios
                        .delete(`/jeecg-boot/app/device/deleteById?id=${item.id}&creator=${localStorage.getItem('userCode')}`)
                        .then(res => {
                            if (res.data.code == 200) {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.search()
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                })
                .catch(() => {
                    Toast({
                        message: '取消',
                        position: "bottom",
                        duration: 2000
                    });
                });
        },
        // 编辑 
        edit(item) {
            this.$router.push({ name: 'EquipmentSchedulingEdit', params: { item: item } });
        },
    },
}
</script>

<style scoped>
</style>