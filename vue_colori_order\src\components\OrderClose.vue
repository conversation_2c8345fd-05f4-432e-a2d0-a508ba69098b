<template>
    <div class="order">

        <div class="top-title">
            <div class="title_order_user">
                <span>订单数据</span>
            </div>
            <div class="top-message">
                <img :src="message" width="70%" />
            </div>
        </div>

        <div style="margin-top:1rem;">
            <div class="data">
                <div class="data_label">MO单号</div><div class="data_value">{{item.moId}}</div>
            </div>
            <div class="data1">
                <div class="data_label">产品名称</div><div class="data_value">{{item.name}}</div>
            </div>
            <div class="data">
                <div class="data_label">定额产量</div><div class="data_value">{{item.prePlot}}</div>
            </div>
            <div class="data">
                <div class="data_label">工艺定额</div><div class="data_value">{{item.tquota}}</div>
            </div>
            <div class="data">
                <div class="data_label">实际产量</div><div class="data_value">{{item.count}}</div>
            </div>
            <div class="data">
                <div class="data_label">完成率</div><div class="data_value">{{item.comPercent}}</div>
            </div>
            <div class="data">
                <div class="data_label">单位工时人均产量</div><div class="data_value">{{item.perCountHour}}</div>
            </div>
            <div class="data">
                <div class="data_label">备注</div><div class="data_value">{{item.comments}}</div>
            </div>
        </div>


        <div>
            <a-button @click="close" style="margin:10%;width:80%;height:3rem" type="primary" icon="close" >关闭</a-button>
        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            search:require('../../static/images/search.png'),
            add:require('../../static/images/userAdd.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            value:false,
            personList:[],
            addUserInfo:[],
            searchKey:'',
            userInfo:[],
            item:{},
            questionType:'',
            questionTypeVal:'',
            status:'',
            lpId:'',
            createTime:'',
            popupVisible:false,
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let self=this;
        self.item=this.$route.params
    },
    methods:{
        close(){
            this.$router.push({name:"OrderStart"})
        },
        addUserToServe(){
            let self=this;
            if(self.addUserInfo.length<=0){
                Toast({
                    message: "请先添加人员！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            console.log(self.status)
            if(self.status=='0'){
                self.notStartToAdd();
            }else{
                self.startedToAdd();
            }
            
        },
        notStartToAdd(){
            let self=this;
            let userCode=localStorage.getItem('userCode')
            let params={
                type:'1',
                gcWorkPlanAppList:self.addUserInfo,
                creator:userCode
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changePlanInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        startedToAdd(){
            let self=this;
            let itemType='1';
            if(self.item.lastStatus='2'){
                itemType='8'
            }
            let params={
                type:itemType,
                lpId:self.lpId,
                gcWorkOperationList:self.addUserInfo,
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        searchUser(){
            let self=this;
            let url="";
            if(self.status=='0'){
                //未开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreeWorker";
            }else{
                //开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreePeopleInfo";
            }

            self.$axios.get(url,{params:{secinfo:self.searchKey,createTime:self.createTime,lpId:self.lpId}}).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.userInfo=res.data.result
                    console.log(self.userInfo)
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })

        },
        openQuestionType(){
            this.popupVisible = true;
        },
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        addPerson(item){
            let self=this;
            item.lpId=self.lpId;
            // item.pid=item.id;
            console.log(item)
            this.addUserInfo.push(item);
            this.userInfo.splice(this.userInfo.indexOf(item),1);
        },
        selectData(){
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.title_order_user{
    font-size: 1.6rem;
    font-weight: 600;
    float: left;
    width: 60%;
    text-align:left;
    padding:8%;
}
.top-message{
    float: left;
    display:flex;
    align-items:center;
    width:20%;
    margin:0 auto;
}
.searchField{
    width: 70%;
    margin-left: 8%;
    margin-top:5%;
    float: left;
}
.searchBtn{
    margin-top:5%;
    float: left;
}
.selectBottom{
    height: 4rem;
    width: 100%;
    position:fixed;
    bottom:0;
    background-color: #fff;
}
.top-title{
    height:fit-content;
    width:100%;
    display:flex;
}
.userItem{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 2rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 5%;
    font-size: 1.2rem;
}
.userName-st{
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}
.userName-add{
    float: left;
    width: 28%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
.user_List{
    float: left;
    width: 60%;
    height: 100%;
    display: flex;
    align-items: center;
}
.addBtn{
    float: left;
    width: 40%;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    justify-content: center;
    height: 100%;
    background: #5032f2;
    color: #fff;
}
.data_label{
    font-size: 1.2rem;
    float: left;
    width: 40%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.data_value{
    font-size: 1.2rem;
    float: left;
    width: 59%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.data{
    width: 100%;
    background: #fff;
    height: 3rem;
    margin-bottom: 1px;
}
.data1{
    width: 100%;
    background: #fff;
    height: 6rem;
    margin-bottom: 1px;
}
</style>