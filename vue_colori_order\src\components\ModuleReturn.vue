<template>
    <div>
        <!-- 归还 -->
        <van-form @submit="onSubmit">
            <van-field label="数量" v-model="info.count" placeholder="请输入"
                :rules="[{ required: true, message: '请填写数量' }]" />


            <van-field readonly clickable name="picker" :value="info.moldStatus" label="模具状态" placeholder="请选择模具状态"
                @click="showPicker = true" :rules="[{ required: true, message: '请选择模具状态' }]" />
            <van-popup v-model="showPicker" position="bottom">
                <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
            </van-popup>

            <van-field label="备注" v-model="info.remarks" placeholder="请输入" />

            <van-radio-group v-model="radio" style="height: 450px;overflow:auto ;">
                <van-cell-group v-for="(item) in receiveArr" :key="item.id">
                    <van-cell clickable @click="radio = item.id">
                        <p style="font-size:large;font-weight:700;"> {{ item.name }} </p>
                        <p> 数量:{{ item.count }} </p>
                        <p> 已归还数:{{ item.backNumber }}</p>
                        <p> 备注:{{ item.remarks }}</p>
                        <template #right-icon>
                            <van-radio :name="item.id" />
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>

            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast } from 'mint-ui';
export default {
    data() {
        return {
            info: {},
            columns: ['完好', '损坏'],
            showPicker: false,

            // 领用的
            receiveArr: [],
            radio: '',
        }
    },
    created() {
        this.info = this.$route.params
        if (!this.info.id) {
            this.$router.replace({
                name: "ModuleInfoDetail",
            });
        } else {
            this.info.moldsId = this.info.id
            this.info.remarks = ''
            this.info.type = '3'

            this.$axios
                .get(`/jeecg-boot/ncApp/moldsUsage/getUsageList?product=${this.info.code}&workshop=${this.info.workshop}&type=${2}&flag=${1}&id=${this.info.id}&userCode=${localStorage.getItem('userCode')}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.receiveArr = res.data.result.records
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
    },
    methods: {
        onConfirm(value) {
            this.info.moldStatus = value;
            this.showPicker = false;
        },
        addInput() {
            this.items.push({ jobCenter: '', productNo: '', productName: '', remakrs: "" });
        },
        onSubmit() {
            let flag = true
            if (flag) {
                flag = false
                this.info.type = 3
                this.info.creator = localStorage.getItem('userCode')
                this.info.createName = localStorage.getItem('userName')
                let index = ''

                
                this.receiveArr.forEach((v, i) => {
                    if (v.id == this.radio) {
                        index = i
                    }
                })
                this.info.parentId = this.receiveArr[index].id

                if (this.info.count > this.receiveArr[index].count - this.receiveArr[index].backNumber) {
                    Toast({
                        message: '归还数量不能大于领用数量',
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    // 数量只能是正整数
                    if (/^[1-9]\d*$/.test(this.info.count)) {
                        this.$axios
                            .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.$router.replace({
                                        name: "ModuleInfo",
                                    });
                                    flag = true
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        Toast({
                            message: '请输入正整数',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                }
            }
        },
    },
}
</script>

<style  scoped>
</style>