<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_order_title">模板选择</div>
        </div>
        <!-- <van-field label="MO单号" placeholder="请输入MO单号" v-model="moId" style="background:white"/>
        <van-field label="日期" :value="date" style="background:white" readonly @click="c_show = true"/>
        <van-button type="primary" @click="search()" style="margin-top:10px;margin-bottom:10px;border-radius:10px;width:50%">查询</van-button>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" /> -->


        <div v-for="(item,index) in leadAppInfo" :key="index" class="report_item">

            <div style="width:100%;text-align:left;padding-left:2%">
                <div style="float:left;width:25%;">模板名称</div>
                <div style="float:left;width:75%;">{{item.tableName}}</div>
                <div style="clear:both;"></div>
            </div>
            <div style="width:100%;text-align:left;padding-left:2%">
                <div style="float:left;width:25%;">模板区域</div>
                <div style="float:left;width:75%;">{{item.areaName}}</div>
                <div style="clear:both;"></div>
            </div>
            <div class="report_line"></div>
            <div class="bottom_button" @click="selected(item)">选择</div>
            
            <!-- <div class="report_item_text">
                <div class="item_left">模板名称</div>
                <div class="item_right">{{item.tableName}}</div>
                <div style="clear:both;"></div>
            </div>
            <div class="report_item_text">
                <div class="item_left">模板区域</div>
                <div class="item_right">{{item.areaName}}</div>
                <div style="clear:both;"></div>
            </div>
            <div class="report_line"></div>
            <div class="bottom_button" @click="selected(item)">选择</div> -->
        </div>

        <update-modal ref="modalForms" @ok="modalFormOk"></update-modal>
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
import UpdateModal from './list/UpdateModal.vue';
export default {
  components: { UpdateModal },
    data(){
        return{
            moId:'',
            itemId:'',
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal:'',
            minDate:'',
            maxDate:'',
            date:'',
            category:'',
            userName:'',
            userCode:'',
            popupVisible:false,
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            questionType:'',
            c_show:false,
            questionTypeVal:'',
            clickNum:0,
            isfirst:'',
            popupVisible:false,
            leadAppInfo:[],
            popupSlots:[
                {
                    values:[
                        '全部','白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let self=this
        var qaModel=localStorage.getItem('qaModel');
        console.log(qaModel)
        if(qaModel=="true"){
            console.log("run here")
            self.$router.go(-1);
        }
        this.search()
    },
    methods:{
        search(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/appBos/getMainInfo',null).then(res=>{
                if(res.data.code==200){
                    self.leadAppInfo=res.data.result
                }
            })
        },
        selected(item){
            let self=this
            self.$router.push({name:"WorkDuringForQa",params:{id:item.id,type:"1"}})
            // self.$axios.get('/jeecg-boot/app/appQuality/getConnect',{params:{tableId:item.id,tableName:item.tableName,mainId:self.itemId}}).then(res=>{
            //     if(res.data.code==200){
            //         self.$router.push({name:"WorkDuring",params:{id:item.id,type:'1',itemId:self.itemId,childId:'0'}})
            //     }
            // })
        },
        hiddenToUpdateOrder(){
            this.clickNum=this.clickNum+1;
            if(this.clickNum<5){
                var lastClick=5-this.clickNum
                var msg="再点击"+lastClick+"下即可打开管理员权限！"
                Toast({
                    message: msg,
                    position: 'bottom',
                    duration: 1000
                });
            }else{
                this.clickNum=0;
                if(this.userCode=='HI1606270001' || this.userCode=='HI2002250004'){
                    this.$router.push({name:"OrderUpdate"})
                }else{
                    Toast({
                        message: "对不起，您暂无权限！",
                        position: 'bottom',
                        duration: 1000
                    });
                } 
            }

            
        },
        modalFormOk(){
            this.getLeadAppInfo()
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
        },
        updateCount(item){
            let self=this;
            self.$refs.modalForms.edit(item);
            self.$refs.modalForms.title="产量更改";
            self.$refs.modalForms.disableSubmit = true;
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType(){
            this.popupVisible = true;
        },
        dateConfirm(value){
            this.newSelectedValue=this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getLeadAppInfo()
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getLeadAppInfo()
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    min-height: 100%;
    background: #eeeeee;
}
.top_order_title{
    font-size: 2em;
    font-weight: 800;
    text-align: left;
    padding-left: 5%;
}
.report_item{
    margin-left: 5%;
    width: 90%;
    background: #ffffff;
    margin-bottom: 5%;
}
.report_line{
    width:100%;
    height:1px;
    background: #808080;
}
.bottom_button{
    color: #ff0000;
    font-size: 16px;
}
</style>