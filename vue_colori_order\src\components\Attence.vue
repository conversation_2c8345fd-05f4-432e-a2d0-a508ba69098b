<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_attence" width="100%"/>
        <van-cell title="员工姓名" :value="userName" @click="openManager()" style="margin:5%;width:90%;border-radius:10px;"/>
        <van-cell title="当前日期" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />

        <div style="margin:1rem;color:blue;" @click="pushToSpNo" :hidden="isManager">未补卡成功管理员验证</div>

        <div class="attence" v-if="attenceList.dayType=='休息'">
            <div class="attence_title">{{attenceList.type}}</div>

            <div class="attence_item4">
                <div class="attence_circle">
                    <div class="item_circle4"></div>
                </div>
                
                <div class="item_top" style="height:100%">
                    <div>今日休息</div>
                </div>
            </div>
        </div>




        <div class="attence" :class="attenceList.type!='管理人员'?'attence_pg':''" v-if="attenceList.dayType=='工作日'">
            <div class="attence_title">{{attenceList.type}}</div>

            <div class="attence_item" v-if="attenceList.type=='管理人员'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state0=='0'"></div>
                    <div class="item_circle1" v-if="attenceList.state0=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state0=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state0=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state0=='0'">正常</div>
                    <div class="item_status1" v-if="attenceList.state0=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state0=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state0=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state0=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record0}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>



            <div class="attence_item_bottom" v-if="attenceList.type=='管理人员'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state1=='0'"></div>
                    <div class="item_circle1" v-if="attenceList.state1=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state1=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state1=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state1=='0'">正常</div>
                    <div class="item_status1" v-if="attenceList.state1=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state1=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state1=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state1=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record1}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>

            <div class="attence_item" v-if="attenceList.type=='非管理人员白班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state11=='0' || attenceList.state11==null"></div>
                    <div class="item_circle1" v-if="attenceList.state11=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state11=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state11=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>上午上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state11=='0' || attenceList.state11==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state11=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state11=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state11=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state11=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record11}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>


            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员白班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state12=='0' || attenceList.state12==null"></div>
                    <div class="item_circle1" v-if="attenceList.state12=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state12=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state12=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>上午下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state12=='0' || attenceList.state12==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state12=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state12=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state12=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state12=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record12}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>


            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员白班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state13=='0' || attenceList.state13==null"></div>
                    <div class="item_circle1" v-if="attenceList.state13=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state13=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state13=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>下午上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state13=='0' || attenceList.state13==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state13=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state13=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state13=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state13=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record13}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>

            <!-- <div class="attence_item_bottom" v-if="attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state14=='0' || attenceList.state14==null"></div>
                    <div class="item_circle1" v-if="attenceList.state14=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state14=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state14=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>下午下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state14=='0' || attenceList.state14==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state14=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state14=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state14=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state14=='3'">缺卡</div>
                <div class="item_bottom" v-else>{{attenceList.record14}}</div>
            </div>



            <div class="attence_item_bottom" v-if="attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state15=='0' || attenceList.state15==null"></div>
                    <div class="item_circle1" v-if="attenceList.state15=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state15=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state15=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>晚上上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state15=='0' || attenceList.state15==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state15=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state15=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state15=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state15=='3'">缺卡</div>
                <div class="item_bottom" v-else>{{attenceList.record15}}</div>
            </div> -->


            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员白班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工白班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state16=='0' || attenceList.state16==null"></div>
                    <div class="item_circle1" v-if="attenceList.state16=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state16=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state16=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>晚上下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state16=='0' || attenceList.state16==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state16=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state16=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state16=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state16=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record16}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>


            <div class="attence_item" v-if="attenceList.type=='非管理人员晚班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工晚班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state17=='0' || attenceList.state17==null"></div>
                    <div class="item_circle1" v-if="attenceList.state17=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state17=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state17=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>上半夜上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state17=='0' || attenceList.state17==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state17=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state17=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state17=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state17=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record17}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>


            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员晚班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工晚班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state18=='0' || attenceList.state18==null"></div>
                    <div class="item_circle1" v-if="attenceList.state18=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state18=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state18=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>上半夜下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state18=='0' || attenceList.state18==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state18=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state18=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state18=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state18=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record18}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>



            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员晚班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工晚班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state19=='0' || attenceList.state19==null"></div>
                    <div class="item_circle1" v-if="attenceList.state19=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state19=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state19=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>下半夜上班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state19=='0' || attenceList.state19==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state19=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state19=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state19=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state19=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record19}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>


            <div class="attence_item_bottom" v-if="attenceList.type=='非管理人员晚班' || attenceList.type=='非管理人员全班' || attenceList.type=='普通员工晚班' || attenceList.type=='普通员工全班'">
                <div class="attence_circle">
                    <div class="item_circle" v-if="attenceList.state20=='0' || attenceList.state20==null"></div>
                    <div class="item_circle1" v-if="attenceList.state20=='1'"></div>
                    <div class="item_circle2" v-if="attenceList.state20=='2'"></div>
                    <div class="item_circle3" v-if="attenceList.state20=='3'"></div>
                </div>
                
                <div class="item_top">
                    <div>下半夜下班打卡时间：</div>
                    <div class="item_status" v-if="attenceList.state20=='0' || attenceList.state20==null">正常</div>
                    <div class="item_status1" v-if="attenceList.state20=='1'">迟到</div>
                    <div class="item_status2" v-if="attenceList.state20=='2'">早退</div>
                    <div class="item_status3" v-if="attenceList.state20=='3'">缺卡</div>
                </div>
                <div class="item_bottom" style="color:#ff0000" v-if="attenceList.state20=='3'">缺卡</div>
                <!-- <div class="item_bottom" v-else>{{attenceList.record20}}</div> -->
                <div class="item_bottom" v-else>-</div>
            </div>

        </div>

        

        <div style="background:#f3f4f6;height:10px;"></div>


        
    </div>
</template>
<script>
import { DatetimePicker } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            clickNum:0,
            isManager:true,
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_attence:require('../../static/images/myAttence.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            attenceList:[],
            fee:{}
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        sessionStorage.setItem('workList','')
        this.date=this.formatDate(new Date)
        // this.userCode=localStorage.getItem('userCode')
        // this.userName=localStorage.getItem('userName')
        this.getAttecdanceInfo()
    },
    methods: {
        getAttecdanceInfo(){
            let self=this
            let userCode=localStorage.getItem('userCode')
            self.$axios.get('/jeecg-boot/app/gcAttendance/getAttendanceInfo',{params:{userCode:userCode,workDay:this.date}}).then(res=>{
                if(res.data.code==200){
                    self.attenceList=res.data.result
                }
            })
        },
        pushToSpNo(){
            this.$router.push({path:'/checkSpNo'})
        },
        openManager(){
            let self=this
            self.clickNum=self.clickNum+1;
            if(self.userCode=="HI0901071284" || self.userCode=="HI1308030001" 
            || self.userCode=="HI2002250004" || self.userCode=="HI2102220002"){
                if(self.clickNum==5){
                    self.isManager=false;
                    self.clickNum=0
                }
            }
        },
        getDetail(item){
            console.log("item:"+item)
            sessionStorage.setItem('item',JSON.stringify(item))
            this.$router.push({path:'/schedualDetail'})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getAttecdanceInfo()
        },
        
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
</style>