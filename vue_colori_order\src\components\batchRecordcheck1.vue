<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="制胶前检查" right-text="筛选" @click-right="onClickRight" left-arrow
                @click-left="onClickLeft" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">

            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />

            <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
                @click="showbookNamePicker = true" />
            <van-popup v-model="showbookNamePicker" position="bottom">
                <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                    @cancel="showbookNamePicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="workshop" label="车间：" placeholder="点击选择车间"
                @click="showworkshopPicker = true" />
            <van-popup v-model="showworkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                    @cancel="showworkshopPicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="jobCenter" label="工作中心：" placeholder="点击选择工作中心"
                @click="showjobCenterPicker = true" />
            <van-popup v-model="showjobCenterPicker" position="bottom">
                <van-picker show-toolbar :columns="jobCenterColumns" @confirm="jobCenterConfirm"
                    @cancel="showjobCenterPicker = false" />
            </van-popup>
            <van-field v-model="moId" clearable label="MO单：" placeholder="请输入MO单号" />
            <van-field v-model="glue" clearable label="胶体：" placeholder="请输入胶体名称或编码" />

            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in dataArr" :key="index" @click="detail(item)"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
                <van-row>
                    <van-col span="24">
                        <span style="font-size:18px;font-weight: 700;color: #000;">
                            {{ item.remark }}-{{ item.glueBatchCode }}
                        </span>
                    </van-col>
                </van-row>
            </div>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 账套：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.book }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 车间：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.workshop }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 胶体编码：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.glueCode }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 胶体名称：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.glueName }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 工作中心：</van-col>
                        <van-col span="18">
                            <span style="color:black;">
                                {{ item.jobCenter }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 重量：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.expectWeight }}KG </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator } from "mint-ui";
    export default {
        data() {
            return {
                date: this.getDate(-1) + " ~ " + this.getDate(0),
                beginDay: this.getDate(-1),
                endDay: this.getDate(0),

                active: 0,
                userCode: localStorage.getItem("userCode"),
                //   
                dataArr: [],


                show: false,
                show1: false,

                showbookNamePicker: false,
                bookName: "",
                bookNameColumns: [],

                showworkshopPicker: false,
                workshop: "",
                workshopColumns: [],

                showjobCenterPicker: false,
                jobCenter: "",
                jobCenterColumns: [],
                moId: "",
                glue: ""
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach(item => {
                            this.bookNameColumns.push(item.name);
                        });
                    } else {
                    }
                });
                this.bookName = localStorage.getItem('feedingCheckBook')==null?'': localStorage.getItem('feedingCheckBook')
                this.bookNameConfirm(this.bookName)
                this.search();
            }
        },
        methods: {
            bookNameConfirm(value) {
                this.bookName = value;
                this.workshop = '';
                this.jobCenter = '';
                localStorage.setItem('feedingCheckBook', this.bookName)
                this.showbookNamePicker = false;
                //查找车间
                this.$axios
                    .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                        params: { code: value }
                    })
                    .then(res => {
                        if ((res.data.code = 200)) {
                            this.workshopColumns = []
                            res.data.result.forEach(item => {
                                this.workshopColumns.push(item.name);
                            });
                            this.workshop = localStorage.getItem('feedingCheckWorkshop')==null?'':localStorage.getItem('feedingCheckWorkshop')
                            this.workshopConfirm(this.workshop)
                        } else {
                        }
                    });
            },
            workshopConfirm(value) {
                this.workshop = value;
                this.jobCenter = '';
                this.showworkshopPicker = false;
                localStorage.setItem('feedingCheckWorkshop', this.workshop)
                // 查找工作中心
                this.$axios
                    .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
                        params: { book: this.bookName, workshop: value }
                    })
                    .then(res => {
                        if (res.data.code == 200) {
                            console.log(res.data.result);
                            res.data.result.forEach(item => {
                                this.jobCenterColumns.push(item.jobCenter);
                            });
                        } else {
                        }
                    });
            },
            jobCenterConfirm(value) {
                this.jobCenter = value;
                this.showjobCenterPicker = false;
            },
            onClickRight() {
                this.show = true;
            },
            search() {
                this.show = false;
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios
                    .get(
                        `/jeecg-boot/app/gcMix/getBatchMixMainList?checkStatus=3&startDate=${this.beginDay}&endDate=${this.endDay}&moId=${this.moId}&glue=${this.glue}&book=${this.bookName}&workshop=${this.workshop}&jobCenter=${this.jobCenter}&userCode=${this.userCode}&status=6`
                    )
                    .then(res => {
                        if (res.data.code == 200) {
                            console.log(res.data.result.records);
                            this.dataArr = res.data.result.records;
                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    })
                    .finally(() => {
                        Indicator.close();
                    });
            },
            onClickLeft() {
                this.$router.go(-1);
            },
            onClick(name, title) {
                console.log(name, title);
            },
            detail(item) {
                this.$router.push({
                    name: "batchRecordcheck1detail",
                    params: item
                });
            },
            onConfirm(date) {
                const [start, end] = date;
                this.show1 = false;
                this.beginDay =
                    start.getFullYear() +
                    "-" +
                    (start.getMonth() + 1 < 10
                        ? "0" + (start.getMonth() + 1)
                        : start.getMonth() + 1) +
                    "-" +
                    (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
                this.endDay =
                    end.getFullYear() +
                    "-" +
                    (end.getMonth() + 1 < 10
                        ? "0" + (end.getMonth() + 1)
                        : end.getMonth() + 1) +
                    "-" +
                    (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
                this.date = `${this.beginDay}~${this.endDay}`;
            },
            getDate(day) {
                var date1 = new Date(),
                    time1 =
                        date1.getFullYear() +
                        "-" +
                        (date1.getMonth() + 1) +
                        "-" +
                        date1.getDate(); //time1表示当前时间
                var date2 = new Date(date1);
                date2.setDate(date1.getDate() + day);
                return (
                    date2.getFullYear() +
                    "-" +
                    (date2.getMonth() + 1 < 10
                        ? "0" + (date2.getMonth() + 1)
                        : date2.getMonth() + 1) +
                    "-" +
                    (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
                );
            },
        }
    };
</script>

<style scoped></style>