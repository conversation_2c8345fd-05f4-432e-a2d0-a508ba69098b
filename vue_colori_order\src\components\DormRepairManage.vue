<template>
<div class="pageRepairManage">
    <van-nav-bar fixed
        title="维修列表"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
    >
    <template #right>
        <van-popover
            v-model="popoverShow"
            trigger="click"
            placement="bottom-end"
            :actions="popoverActions"
            @select="onPopoverSelect">
            <template #reference>
                <van-button icon="more-o"></van-button>
            </template>
        </van-popover>
    </template>
</van-nav-bar>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getRepairList">
            <div v-for="(repair, index) in repairList" :key="index" style="margin-top: 15px;">
                <van-cell-group inset>
                    <van-cell class="vanCellClass" title="登记人" :value="repair.creator" />
                    <van-cell class="vanCellClass" title="登记人工号" :value="repair.userCode" />
                    <van-cell class="vanCellClass" title="登记时间" :value="repair.createTime" />
                    <van-cell class="vanCellClass" title="宿舍信息" :value="`${repair.areaName}-${repair.buildingName}-${repair.roomNumber}`" />
                    <van-cell class="vanCellClass" title="维修状态" :value="repair.maintainStatus|fMStatus" />
                    <van-cell class="vanCellClass" title="维修类型" :value="repair.type" />
                    <van-cell class="vanCellClass" title="维修内容" value=" ">
                        <template #label>
                            <div v-html="repair.content.replace(/\n/g,'<br>')" class="repairContent"></div>
                        </template>
                    </van-cell>
                    <van-cell class="vanCellClass" title="操作">
                        <template #default>
                            <van-button type="default" size="small" @click="onItemCheck(repair)">查看</van-button>
                            <van-button v-if="repair.maintainStatus=='1'"
                                type="info" size="small" @click="debounce(onItemRepair(repair),500)">报修</van-button>
                            <van-button v-if="repair.maintainStatus=='2'"
                                type="primary" size="small" @click="debounce(onItemComplete(repair),500)">完成</van-button>
                            <van-button v-if="repair.maintainStatus=='1'"
                                type="danger" size="small" @click="debounce(onItemClose(repair),500)">关闭</van-button>
                        </template>
                    </van-cell>
                </van-cell-group>
            </div>
        </van-list>
    </van-pull-refresh>
<!-- 筛选条件 -->
<van-popup v-model="popFilterShow" position="top" :style="{ height: '50%' }" :close-on-click-overlay="false">
    <h2>筛选条件</h2>
    <van-cell-group>
        <van-field v-model="queryParams.userCode" label="员工工号" placeholder="请输入员工工号" />
        <van-field readonly v-model="queryParams.startTime" label="开始时间" placeholder="请输入开始时间" @focus="onSelectDate" />
        <van-field readonly v-model="queryParams.endTime" label="结束时间" placeholder="请输入结束时间" @focus="onSelectDate" />
        <van-field v-model="queryParams.areaName" label="区域名称" placeholder="请输入区域名称" />
        <van-field v-model="queryParams.buildingName" label="建筑名称" placeholder="请输入建筑名称" />
        <van-field v-model="queryParams.roomNumber" label="房间号" placeholder="请输入房间号" />
        <!-- <van-field v-model="queryParams.maintainStatus" label="状态" placeholder="请输入状态" /> -->
        <van-cell title="状态" :value="queryParams.maintainStatus|fMStatus" is-link style="text-align: left;" @click="onSelectStatus"></van-cell>
    </van-cell-group>
    <div style="margin: 0 auto; padding-top: 10px;">
        <van-button round type="default" size="small" style="width: 30%;" @click="searchReset">重置</van-button>
        <van-button round type="primary" size="small" style="width: 30%;margin-left: 2rem;" @click="searchQuery">查询</van-button>
    </div>
</van-popup>
<!-- 关闭 -->
<van-popup v-model="popCloseShow" position="bottom" :style="{ height: '30%' }" :close-on-click-overlay="false">
    <van-nav-bar
        title="关闭维修登记"
        left-text="取消"
        left-arrow
        @click-left="onCloseLeft"
        />
        <van-form @submit="onCloseSubmit">
            <van-field name="关闭原因" label="关闭原因"
                v-model.trim="closeReason"
                placeholder="关闭原因"
                rows="3" autosize type="textarea"
                :rules="[{ required: true, message: '请填写关闭原因' }]"
            />
            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
</van-popup>
<!-- 日期 -->
<van-calendar v-model="showDate" type="range" @confirm="onDateConfirm" 
    :min-date="new Date('2023-01-01')" />
<!-- 状态 -->
<van-popup v-model="popStatusShow" position="bottom" :style="{ height: '45%' }">
    <van-picker title="维修状态" show-toolbar
        :columns="statusColumns"
        @confirm="onStatusConfirm"
        @cancel="onStatusCancel" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            queryParams: {
                userCode: '',
                startTime: '',
                endTime: '',
                areaName: '',
                buildingName: '',
                roomNumber: '',
                maintainStatus: '',
            },
            popFilterShow: false,
            iPage: {
                current: 1,
                size: 5,
                total: 1,
            },
            refreshing: false, // 是否下拉刷新
            repairList: [],   // 修理列表
            loading: false, // 是否加载
            finished: false, // 是否完成  停止触底加载
            opRepair: {},
            popCloseShow:false,
            closeReason: "",
            opDate: '',
            showDate: false,
            popStatusShow: false,
            statusColumns: ['待处理','已报修','已完成','已撤销','已关闭'],
            popoverShow: false,
            popoverActions: [
                { text: '筛选', icon: 'filter-o' },
                { text: '新增', icon: 'add-o' },
            ],
        }
    },
    methods:{
        onPopoverSelect(pAction, pIndex){
            if(pIndex===0){
                this.popFilterShow=true
            }else if(pIndex===1){
                this.$router.push({ path: "/addRepair"})
            }
        },
        onStatusConfirm(pName, pIndex){
            this.queryParams.maintainStatus=pIndex+1+""
            this.popStatusShow=false
        },
        onStatusCancel(){
            this.queryParams.maintainStatus=""
            this.popStatusShow=false
        },
        onSelectStatus(){
            this.popStatusShow=true
        },
        onDateConfirm(pDate){
            console.log("onDateConfirm",pDate)
            const [start, end] = pDate;
            this.queryParams.startTime=this.formatDate(start)
            this.queryParams.endTime=this.formatDate(end)
            console.log(this.queryParams)
            this.showDate=false
        },
        onSelectDate(){
            this.showDate=true
        },
        onCloseSubmit(){
            let rParams={
                id: this.opRepair.id,
                reason: this.closeReason,
                userCodeRequest:localStorage.getItem('userCode'),
            }
            console.log("onCloseSubmit", rParams)
            this.$axios.get("/dormApi/maintain/app/closeRecord", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.closeReason=""
                        this.popCloseShow=false
                        Toast.success(res.message)
                        this.searchReset()
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        onCloseLeft(){
            this.closeReason=""
            this.popCloseShow=false
        },
        requestStatus(params){
            this.$axios.get("/dormApi/maintain/app/maintainOrFinish",{params: params}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.searchReset()
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        onItemClose(pRow){// 关闭
            this.opRepair=pRow
            this.popCloseShow=true
        },
        onItemRepair(pRow){// 报修
            console.log('onItemRepair',pRow)
            let rParams={
                id: pRow.id,
                maintainStatus: '2',
                userCodeRequest:localStorage.getItem('userCode'),
            }
            console.log('onItemRepair',rParams)
            Dialog.confirm({
                title: '提示',
                message: '确认已报修？',
            }).then(()=>{
                this.requestStatus(rParams)
            })
        },
        onItemComplete(pRow){// 完成
            let rParams={
                id: pRow.id,
                maintainStatus: '3',
                userCodeRequest:localStorage.getItem('userCode'),
            }
            console.log('onItemComplete',rParams)
            this.$router.push({path:'/dormRepairComplate', query: { id: pRow.id, rId: pRow.roomId, maintainStatus: '3', }});
            // Dialog.confirm({
            //     title: '提示',
            //     message: '确认已完成？',
            // }).then(()=>{
            //     this.requestStatus(rParams)
            // })
        },
        onItemCheck(pRow){
            this.$router.push({path:'/ssRepairDetail', query: { rId: pRow.id}});
        },
        searchQuery(){
            this.repairList=[]
            this.iPage.current=1
            this.popFilterShow=false
            this.onRefresh()
        },
        searchReset(){
            this.repairList=[]
            this.iPage.current=1
            this.queryParams={}
            this.popFilterShow=false
            this.onRefresh()
        },
        onRefresh(){
            // 处于刷新状态
            this.refreshing=true;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            // 加载数据
            this.getRepairList();
        },
        getRepairList(){
            if (this.refreshing) {
                this.repairList = [];
                this.iPage.current = 1;
                this.refreshing = false;
                this.finished=false
            }
            let rParams=this.queryParams
            rParams.pageNo=this.iPage.current
            rParams.pageSize=this.iPage.size
            this.$axios.get("/dormApi/maintain/app/getManageMaintainRecordList", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.repairList.push(...res.result.records);
                        this.iPage={
                            current: res.result.current,
                            size: res.result.size,
                            total: res.result.pages,
                        }
                        // 关闭loading状态
                        this.loading = false;
                        // 判断是否到底了
                        if(this.iPage.current*1>=this.iPage.total*1){
                            this.finished = true;
                        }else{
                            this.iPage.current++;
                        }
                    }else{
                        Toast({
                            message: res.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: '发生错误',
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onClickLeft(){
            this.$router.go(-1)
        },
        formatDate (secs) {// date format
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        },
        debounce(fn, delay = 100) {
            let timer = null
            return function () {
                let args = arguments
                if (timer) {
                    clearTimeout(timer)
                }
                timer = setTimeout(() => {
                    fn.apply(this, args)
                }, delay)
            }
        },
    },
    created(){},
    filters: {
        fStatus(status){
            switch(status){
                case "0": return "正常";
                case "1": return "失效";
                default: return status;
            }
        },
        fMStatus(mStatus){
            switch(mStatus){
                case "1": return "待处理";
                case "2": return "已报修";
                case "3": return "已完成";
                case "4": return "已撤销";
                case "5": return "已关闭";
                default: return mStatus;
            }
        }
    }
}
</script>
<style scoped>
.pageRepairManage{
    background: #eee;
    padding-top: 50px;
}
.vanCellClass{
  text-align: left;
}
</style>