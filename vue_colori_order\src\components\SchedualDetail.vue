<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div style="background:#f3f4f6;height:10px;"></div>
        <div class="sch_d_list">

            <div class="tag">{{$t('orderInfo')}}</div>

            <div class="sch_d_order">
                <mt-cell :title="moId" :value="item.moId"></mt-cell>
                <div class="sch_d_order_line">
                    <div class="sch_d_order_left">{{$t('productName')}}</div>
                    <div class="sch_d_order_right">{{item.name}}</div>
                </div>
                <mt-cell :title="mitosome" :value="item.mitosome"></mt-cell>
                <mt-cell :title="plannedStaff" :value="item.tpnum"></mt-cell>
                <mt-cell :title="quota" :value="item.tquota"></mt-cell>
                <mt-cell :title="plandate" :value="item.createTime"></mt-cell>
                <mt-cell :title="plot" :value="item.plot"></mt-cell>
            </div>

        </div>

        <div style="background:#f3f4f6;height:10px;"></div>

        <div class="sch_order_list">

            <div class="class-tag">{{$t('shiftInfo')}}</div>
            <div class="sch_order_order">
                <div class="sch_d_order_line" @click="openQuestionType">
                    <div class="sch_d_order_left">{{$t('shiftInfo')}}</div>
                    <div class="sch_d_order_right">{{preCategory}}</div>
                </div>
                <!-- <mt-cell title="班别" :value="item.preCategory" @click="getCategory()"></mt-cell> -->
                <mt-popup class="popup-div" v-model="popupVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
                    <mt-picker :slots="popupSlots" @change="onValuesChange"  showToolbar @touchmove.native.stop.prevent>
                        <div class="picker-toolbar-title">
                            <div class="usi-btn-cancel" @click="popupVisible = !popupVisible">{{$t('cancel')}}</div>
                            <div class="">请选择班别</div>
                            <div class="usi-btn-sure" @click="popupOk()">{{$t('ok')}}</div>
                        </div>
                    </mt-picker>
                </mt-popup>
                <div class="sch_d_order_line" @click="getBeginTime()">
                    <div class="sch_d_order_left">{{$t('startTime')}}</div>
                    <div class="sch_d_order_right">{{begin}}</div>
                </div>
                <!-- <mt-cell title="开始时间" :value="item.begin" @click="getBeginTime()"></mt-cell> -->
                <mt-datetime-picker
                    ref="picker"
                    @touchmove.native.stop.prevent
                    type="time"
                    @confirm="dateConfirm()"
                    v-model="dateVal">
                </mt-datetime-picker>
                <div class="sch_d_order_line" @click="getEndTime()">
                    <div class="sch_d_order_left">{{$t('endTime')}}</div>
                    <div class="sch_d_order_right">{{end}}</div>
                </div>
                <!-- <mt-cell title="结束时间" :value="end" @click="getEndTime()"></mt-cell> -->
                <mt-datetime-picker
                    ref="epicker"
                    @touchmove.native.stop.prevent
                    type="time"
                    @confirm="edateConfirm()"
                    v-model="edateVal">
                </mt-datetime-picker>
                <mt-cell :title="yj" :value="prePlot"></mt-cell>
            </div>
        </div>


        <div style="background:#f3f4f6;height:10px;"></div>

        

        <div class="sch_person_list">
            <div class="person-tag">{{$t('userInfo')}}</div>
            <div class="person_add" @click="addPerson()" v-if="status=='1'">{{$t('add')}}</div>
        </div>

        <div v-for="(item,index) in workPlanList" :key="index">
            <div class="sch_order_order">
                <div class="person_item_left">
                    <div class="person_name">{{item.name}}_{{item.code}}</div>
                    <div class="person_station">{{item.station}}</div>
                </div> 
                <div class="person_item_mid" @click="edit(index)">
                    {{$t('edit')}}
                </div>  
                <div class="person_item_end" @click="remove(index)">
                    {{$t('delete')}}
                </div>  
            </div>
        </div>

        <div style="background:#f3f4f6;height:2rem;"></div>

        <mt-button type="primary" size="large" style="margin:1rem;width:90%" @click="submitInfo" v-if="status=='1'">{{$t('submit')}}</mt-button>

        <div style="background:#f3f4f6;height:100px;"></div>
        
    </div>
</template>
<script>
import { DatetimePicker,Toast,Indicator } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            edateVal:'',
            c_show:false,
            date:'',
            prePlot:'',
            action:'add',
            preCategory:this.$t('select'),
            begin:this.$t('select'),
            end:this.$t('select'),
            selectedValue: this.formatDate(new Date()),
            my_schedual:require('../../static/images/mySchedual.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            schedualList:[],
            fee:{},
            item:{},
            yj:this.$t('estOutput'),
            moId:this.$t('moId'),
            name:this.$t('productName'),
            mitosome:this.$t('mitosome'),
            status:'1',
            workId:'',
            plannedStaff:this.$t('plannedStaff'),
            quota:this.$t('quota'),
            plandate:this.$t('workDay'),
            plot:this.$t('plot'),
            questionType:'',
            questionTypeVal:'',
            popupVisible:false,
            leadPlanAppList:[],
            workPlanList:[],
            workList:[],
            popupSlots:[
                {
                    values:[
                        '白班','晚班','培训'
                    ]
                }
            ],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.item=JSON.parse(sessionStorage.getItem('item'))
        console.log(this.workList)
        if(this.item.begin!=null && this.item.begin !=''){
            this.begin=this.item.begin
        }
        if(this.item.end!=null && this.item.end !=''){
            this.end=this.item.end
        }
        if(this.item.preCategory!=null && this.item.preCategory !=''){
            this.preCategory=this.item.preCategory
        }
        if(this.item.prePlot!=null && this.item.prePlot !=''){
            this.prePlot=this.item.prePlot
        }
        this.leadPlanAppList=this.item.gcLeadPlanAppList
        if(this.leadPlanAppList.length>0){
            this.action='update'
            this.workPlanList=this.leadPlanAppList[0].gcWorkPlanList
            this.workId=this.leadPlanAppList[0].id
            this.begin=this.leadPlanAppList[0].begin
            this.end=this.leadPlanAppList[0].end
            this.prePlot=this.leadPlanAppList[0].prePlot
            this.status=this.leadPlanAppList[0].status
            this.preCategory=this.leadPlanAppList[0].preCategory
            if(this.preCategory=='1'){
                this.preCategory='白班'
            }else if(this.preCategory=='4'){
                this.preCategory='晚班'
            }else if(this.preCategory=='6'){
                this.preCategory='培训'
            }
        }
        
        let planList=[]
        console.log(this.workPlanList.length)
        if(this.workPlanList.length>0){
            for(var z=0;z<this.workPlanList.length;z++){
                planList.push(this.workPlanList[z])
            }
        }

        if(sessionStorage.getItem('workList')==null || sessionStorage.getItem('workList')==''){
            this.workList=[]
        }else{
            this.workList=JSON.parse(sessionStorage.getItem('workList'))
        }
        

        if(this.workList.length>0){
            for(var n=0;n<this.workList.length;n++){
                if(this.workPlanList.length>0){
                    for(var m=0;m<this.workPlanList.length;m++){
                        if(this.workList[n].code==this.workPlanList[m].code){
                            this.workList.splice(n,1)
                        }
                    }
                }
            }
        }
        if(this.workList.length>0){
            for(var n=0;n<this.workList.length;n++){
                this.workList[n].pid=this.workList[n].id
                planList.push(this.workList[n])
            }
        }

        this.workPlanList=planList
        console.log(this.workPlanList)

    },
    methods: {
        getCategory(){

        },
        edit(index){
            Toast({
                message: '功能开发中',
                position: 'bottom',
                duration: 5000
            });
        },
        remove(index){
            if(this.status!='1'){
                Toast({
                    message: '当前订单已开始，严禁再次操作订单',
                    position: 'bottom',
                    duration: 5000
                });
            }else{
                this.workPlanList.splice(index,1)
            } 
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.item.preCategory = this.questionTypeVal;
            this.preCategory = this.questionTypeVal;
            this.popupVisible = false;
            // this.getOrderInfo();
        },
        openQuestionType(){
            this.popupVisible = true;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        getBeginTime(){
            console.log("getBeginTime")
            this.$refs['picker'].open()
        },
        getEndTime(){
            console.log("getEndTime")
            this.$refs['epicker'].open()
        },
        getPrePlot(){
            let self=this
            let params = {
                id:this.item.id,
                did:this.item.moId,
                begin:this.begin,
                end:this.end,
                tquota:this.item.tquota
            }
            self.$axios.get('/jeecg-boot/app/gcLeadPlanApp/getPrePlot',{params:params}).then((res)=>{
                if(res.data.code==200){
                    this.prePlot=res.data.result
                } else {
                    this.prePlot=''
                }
            })
        },
        addPerson(){
            sessionStorage.setItem('item',JSON.stringify(this.item))
            this.$router.push({name:'SchedualUser',params:{begin:this.begin,end:this.end}})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
        },
        submitInfo(){
            let self=this
            let userCode=localStorage.getItem('userCode')
            let category=''
            if(self.preCategory=='白班'){
                category='1'
            }else if(self.preCategory=='晚班'){
                category='4'
            }else if(self.preCategory=='培训'){
                category='6'
            }else{
                Toast({
                    message: "班次不得为空",
                    position: 'bottom',
                    duration: 5000
                });
                return;
            }

            if(self.begin=='请选择'){
                Toast({
                    message: "开始时间不得为空",
                    position: 'bottom',
                    duration: 5000
                });
                return;
            }

            if(self.end=='请选择'){
                Toast({
                    message: "结束时间不得为空",
                    position: 'bottom',
                    duration: 5000
                });
                return;
            }

            let params={
                id:self.workId,
                tpId:self.item.id,
                createTime:self.item.createTime,
                preCategory:category,
                begin:self.begin,
                end:self.end,
                prePlot:self.prePlot,
                username:userCode,
                gcWorkPlanList:self.workPlanList,
                action:self.action
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcLeadPlanApp/addLeadPlan',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.$router.go(-1);
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            console.log(this.dateVal)
            this.item.begin = this.dateVal
            this.begin=this.dateVal
            this.getPrePlot()
        },
        edateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.item.end = this.edateVal
            this.end=this.edateVal
            this.getPrePlot()
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.person_name{
    width: 100%;
    padding: 5%;
}
.person_station{
    padding: 5%;
    width: 100%;
}
.person_item_left{
    float: left;
    width: 58%;
    height: 4.5rem;
    text-align: left;
}
.person_item_mid{
    float: left;
    width: 20%;
    color: #fcfcfc;
    background: cornflowerblue;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.person_item_end{
    float: left;
    width: 20%;
    height: 100%;
    background: crimson;
    color: #fcfcfc;
    display: flex;
    align-items: center;
    justify-content: center;
}
.sch_d_list{
    height: 28rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-left: 5%;
}
.sch_order_list{
    height: 16rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-left: 5%;
}
.sch_person_list{
    height: 3rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-left: 5%;
    display: flex;
    align-items: center;
}
.tag{
    background: #0077cb;
    color: #ffffff;
    width: 5rem;
    padding: 0.3rem;
    position: absolute;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    top: 5%;
}
.class-tag{
    background: #0077cb;
    color: #ffffff;
    width: 5rem;
    padding: 0.3rem;
    position: relative;
    top: 5%;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}
.person-tag{
    background: #0077cb;
    padding: 0.3rem;
    color: #ffffff;
    width: 6rem;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}
.sch_d_order{
    padding-top: 4rem;
}
.sch_order_order{
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 4.5rem;
    border-radius: 10px;
    background: #fff;
}
.sch_d_order_line{
    height: 2.5rem;
}
.sch_d_order_left{
    float: left;
    width: 25%;
    display: flex;
    text-align: left;
    align-items: center;
    height: 100%;
    padding-left: 3%;
}
.sch_d_order_right{
    float: left;
    width: 69%;
    height: 100%;
    color: #888;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: right;
}
.mint-cell{
    text-align: left;
}
.person_add{
    color: #0077cb;
    width: 60%;
    padding: 3%;
    text-align: right;
    float: left;
}
.person_top{
    width: 100%;
    position: relative;
    top: 25%;
    height: 2.5rem;
}
.mint-cell-wrapper{
    background-image: unset;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
</style>