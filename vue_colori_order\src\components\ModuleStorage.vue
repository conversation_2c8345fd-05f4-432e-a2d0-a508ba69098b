<template>
    <div>
        <!-- 入库 -->
        <van-form @submit="onSubmit">
            <van-field label="入库编号" v-model="info.rkNo" placeholder="请输入"
                :rules="[{ required: true, message: '请填写入库编号' }]" />
            <van-field label="入库数量" v-model="info.count" placeholder="请输入"
                :rules="[{ required: true, message: '请填写入库数量' }]" />
            <van-field label="备注" v-model="info.remarks" placeholder="请输入" />
            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast } from 'mint-ui';
export default {
    data() {
        return {
            info: {},
        }
    },
    created() {
        this.info = this.$route.params
        if (!this.info.id) {
            this.$router.replace({
                name: "ModuleInfoDetail",
            });
        } else {
            this.info.moldsId = this.info.id
            this.info.remarks = ''
            this.info.type = '1'
        }
    },
    methods: {
        onSubmit() {
            let flag = true
            // 数量只能是正整数
            if (flag) {
                flag = false
                if (/^[1-9]\d*$/.test(this.info.count)) {
                    this.info.creator = localStorage.getItem('userCode')
                    this.info.createName = localStorage.getItem('userName')
                    this.$axios
                        .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                        .then(res => {
                            if (res.data.code == 200) {
                                Toast({
                                    message: '操作成功',
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.$router.replace({
                                    name: "ModuleInfo",
                                });
                                flag = true
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                } else {
                    Toast({
                        message: '请输入正整数',
                        position: "bottom",
                        duration: 2000
                    });
                }
            }
        }
    },
}
</script>

<style  scoped>
</style>