<template>
    <div style="background:#f3f4f6;min-height:100%">

        <img :src="sc" width="100%"/>
        <van-cell title="当前日期" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />
        <van-cell title="当前班次" :value="category" @click="popupVisible = true"  style="margin:5%;width:90%;border-radius:10px;"/>

        <mt-popup class="popup-div" v-model="popupVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="popupSlots" @change="onValuesChange"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="popupVisible = !popupVisible">取消</div>
                    <div class="">请选择车间</div>
                    <div class="usi-btn-sure" @click="popupOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>

        
        <div>

            <div style="width:100%;">

                <div v-for="(item,index) in info" :key="index">
                    <div class="bg_item" @click="getWorkInfo('1',index)">
                        <p>
                            <span style="font-size:0.8rem">{{item.zjTitle}}</span>
                            <span style="font-size:0.4rem">&nbsp;&nbsp;总:{{item.zjs}}</span>
                        </p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">开锅数：{{item.zjKgs}}</p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">派单数：{{item.zjSendNum}}</p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">完工数：{{item.zjWgNum}}</p>
                    </div>

                    <div class="bg_item" @click="getWorkInfo('2',index)">
                        <p>
                            <span style="font-size:0.8rem">{{item.gbTitle}}</span>
                            <span style="font-size:0.4rem">&nbsp;&nbsp;总:{{item.gbs}}</span>
                        </p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">开线数：{{item.gbKgs}}</p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">派单数：{{item.gbSendNum}}</p>
                        <p style="font-size:0.6rem;text-align:left;padding-left:18%">完工数：{{item.gbWgNum}}</p>
                    </div>
                </div>

            </div>


        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            category:'白班',
            popupVisible:false,
            questionTypeVal:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            sc:require('../../static/images/sc.png'),
            fee:{},
            popupSlots:[
                {
                    values:[
                        {
                            id:0,
                            name:"白班"
                        },
                        {
                            id:1,
                            name:"晚班"
                        }
                    ]
                }
            ],
            info:"",
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.date = this.$route.query.date
        this.category = this.$route.query.category

        console.log(this.date)

        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        
        this.sendShopToUser()
    },
    methods: {
        sendShopToUser(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/sendShopToUser',{params:{date:this.date,category:this.category}}).then(res=>{
                if(res.data.code==200){
                    self.info=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getWorkInfo(type,index){
            let self=this
            let params={
                type:type,
                index:index,
                date:this.date,
                category:this.category
            }
            localStorage.setItem('workInfo',JSON.stringify(params));
            self.$router.push({name:"WorkInfoDetail",params:{params}})
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.sendShopToUser()
        },
        popupOk(){
            this.category = this.questionTypeVal.name;
            this.popupVisible = false;
            this.sendShopToUser()
        },
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year  +''+ month  +''+ date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png');
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
.menu_order2{
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png') no-repeat;
}
</style>