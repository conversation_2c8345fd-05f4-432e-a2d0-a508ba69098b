<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_title">产线管理</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div>
        </div>
        <div class="sign">
            <!-- <img :src="plotTop" width="90%"/>
            <div class="plotName">{{peopleInfo.name}}</div>
            <div class="plotCode">{{peopleInfo.code}}</div>
            <div class="plotId">{{item.id}}-{{item.workshop}}_{{item.createTime}}</div>
            <div class="plotFactory">{{item.name}}</div>
            <div class="plotWorkshop"><span :class="item.predoes=='是'?'ycl-style':''">{{item.moId}}</span>-<span style="color:#33FF33;">{{lastNum}}</span>-{{preCategory}}-{{item.mitosome}}</div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div> -->
            <div style="width:100%;">
                <div style="float:left;text-align:left;width:70%;">
                    <span style="font-size:22px;">{{peopleInfo.name}}</span>
                    <br />
                    <span style="font-size:16px;">{{peopleInfo.code}}</span>
                </div>
                <div style="float:left;width:30%;">
                    <img :src="card" width="60%" />
                </div>
                <div style="clear:both"></div>
            </div>
            <div style="margin-top:50px;text-align:left">
                {{item.id}}-{{item.workshop}}_{{item.createTime}}
                <br />
                <span :class="item.predoes=='是'?'ycl-style':''">{{item.moId}}</span>-<span style="color:#FF4500">{{preCategory}}</span>-{{item.mitosome}}
                <br />
                {{item.name}}
                <br />
                {{item.code}}<span style="color:orange" v-if="item.opocType=='1'">(一物一码)</span>-<span style="color:#33FF33;">{{lastNum}}{{item.mainUnit}}</span>-<span style="color:#FF00FF;">{{lastFNum}}{{item.unit}}</span>
            </div>
            
        </div>

        <div class="menu_order" :hidden="start">
            <div class="menu_order_item" @click="startMode('1')">
                <img :src="startMite" width="90%" />
            </div>
            <div class="menu_order_item" @click="startMode('4')">
                <img :src="finishMo" width="90%" />
            </div>
            
        </div>

        <div class="menu_order" :hidden="end">
            <div class="menu_order_item" @click="startMode('2')" v-show="!stop">
                <img :src="endMite" width="90%"/>
            </div>
            <!-- @click="startMode('3')" 取消连接关线 -->
            <div class="menu_order_item" @click="stopMode()">
                <img :src="lxendMite" width="90%"/>
            </div>
        </div>

        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="workMode('1')">
                <img src="../../static/images/begin_record.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p>开线检查表</p>
            </div>

            <div class="menu_order_more_item" @click="workMode('2')">
                <img src="../../static/images/work_check.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p>物料核对</p>
            </div>

            <div class="menu_order_more_item" @click="workMode('3')">
                <img src="../../static/images/work_time.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p>产量报工</p>
            </div>

            <div class="menu_order_more_item" @click="workMode('4')">
                <img src="../../static/images/order_more.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p>其他状态</p>
            </div>
        </div>

        <div class="gl_bg">
            <div class="gld_bg">关联申请单</div>
            <div class="gld_title"  @click="selectGld">{{gld}}</div>
            <div class="gld_cancel" v-if="gld != '' && gld != '请选择' && gld != null && item.lastStatus!='4' && item.lastStatus!='2'"  @click="cancelGl">取消</div>
        </div>

        <!-- <div class="other" @click="pushOther()" v-if="item.workType=='1' || item.workType=='4'">
            <p>其他状态</p>
        </div> -->


        <div class="pl" @click="pushPl()" v-if="item.workType=='3'">
            <p>配料清单</p>
        </div>

        
        <div class="allOpera">
            <a-dropdown>
                <a-menu slot="overlay" @click="handleMenuClick">
                    <a-menu-item key="1" v-if="lastStatus!='0'"> <a-icon type="user" />调入蓄水池</a-menu-item>
                    <a-menu-item key="2" v-if="lastStatus!='0'"> <a-icon type="user" />调出至其他车间</a-menu-item>
                    <a-menu-item key="3" v-if="lastStatus!='0'"> <a-icon type="user" />临时离开</a-menu-item>
                    <a-menu-item key="4" :hidden="menuTrainVis"> <a-icon type="user"/>{{train}}</a-menu-item>
                    <a-menu-item key="5" :hidden="menuSelectVis"> <a-icon type="user" />{{select}}</a-menu-item>
                </a-menu>
                <div class="more" v-if="personList.length>0">
                    批量操作
                </div>
            </a-dropdown>
            <div class="person_num">
                    当前人数：{{personList.length}}人
            </div>

        </div>    

        <div v-if="personList.length>0">
            <div class="person_item" v-for="(item,index) in personList" :key="index">
                <div class="person-top">
                    <div class="person_name" :class="item.type=='5'?'leave-style':''" v-if="item.type!=14 && item.type!=16">{{item.code}}_{{item.name}}</div>
                    <div class="person_name" :class="item.type=='14'?'leave-style2':''" v-if="item.type==14">{{item.code}}_{{item.name}}_培训中</div>
                    <div class="person_name" :class="item.type=='16'?'leave-style3':''" v-if="item.type==16">{{item.code}}_{{item.name}}_挑拣中</div>
                    <div class="person_menu">
                        <mt-switch v-model="item.selected"></mt-switch>
                    </div>
                </div>
                <div class="person_line"></div>
                <div style="height:12rem;width:100%">
                    <div class="person-left">
                        <div class="person_menu_item" @click="addLabel(item)"><div class="circle"></div>{{item.label}}</div>
                        <div class="person_line"></div>
                        <div class="person_menu_item" @click="pushPool(item)" v-if="lastStatus!='0'"><div class="circle"></div>调入蓄水池</div>
                        <div class="person_line" v-if="lastStatus!='0'"></div>
                        <div class="person_menu_item" @click="outWorkshop(item)" v-if="lastStatus!='0'"><div class="circle"></div>调出至其他车间</div>
                        <div class="person_line" v-if="lastStatus!='0'"></div>
                        <div class="person_menu_item" @click="goback(item)" v-if="item.type=='5'"><div class="circle"></div>归产</div>
                        <div class="person_menu_item" @click="deleteUser(item)" v-else-if="lastStatus=='0'"><div class="circle"></div>删除</div>
                        <div class="person_menu_item" @click="leave(item)" v-else><div class="circle"></div>临时离开</div>
                        <div class="person_line"></div>
                    </div>
                    <div class="person-right">
                        <img :src="personLogo" width="90%"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="addUser" @click="addUser">
            <img :src="add" width="10%"/>
        </div>

        <reason-modal ref="modalForms" @ok="modalFormOk"></reason-modal>
        <out-modal ref="modalForm" @ok="modalFormOk"></out-modal>
        <close-modal ref="modalFormc" @ok="modalCloseFormOk"></close-modal>
        <close-new-modal ref="modalNewForm" @ok="modalNewCloseFormOk"></close-new-modal>
        <!-- <finish-modal ref="modalFinishForm" @ok="modalFinishFormOk"></finish-modal> -->
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox,Indicator   } from 'mint-ui';

import ReasonModal from './list/ReasonModal.vue';
import OutModal from './list/OutModal.vue';
import CloseModal from './list/CloseModal.vue';
import CloseNewModal from './list/CloseNewModal.vue';
// import finishModal from './list/finishModal.vue';
export default {
//   components: { ReasonModal,OutModal,CloseModal, CloseNewModal,finishModal},
  components: { ReasonModal,OutModal,CloseModal, CloseNewModal},
      
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            startMite:require('../../static/images/startMite.png'),
            endMite:require('../../static/images/endMite.png'),
            lxendMite:require('../../static/images/stopline_start.png'),
            finishMo:require('../../static/images/finishMo.png'),
            menuPic:require('../../static/images/menu_pic.png'),
            personLogo:require('../../static/images/user_logo.png'),
            add:require('../../static/images/add.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            lastNum:0,
            lastFNum:0,
            show:false,
            value:false,
            menuTrainVis:true,
            menuSelectVis:true,
            reason:'',
            personList:[],
            start:false,
            stop:false,
            end:true,
            item:[],
            workDay:'',
            finishFlag:true,
            questionType:'',
            questionTypeVal:'',
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            popupVisible:false,
            lastStatus:'0',
            openOrClose:false,
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
            train:'培训',
            select:'挑拣',
            itemParams:{},
            preCategory:'',
            gld:'请选择',
            worker:''
        }
    },
    created:function(){
        
    },
    mounted() {
        let self=this;
        self.itemParams=JSON.parse(localStorage.getItem("params"))
        self.worker=self.itemParams.userCode;
        self.getInfo(self.itemParams)

        // if(this.item.status=='2'){
        //     self.end=false;
        //     self.start=true;
        // }
        // this.personList=this.item.gcWorkPlanList
    },
    methods:{
        stopMode(){
            let self = this
            //停线
            if(self.item.lastStatus=='18'){
                //停线结束
                let params = {
                    lpId: self.item.id,
                    type: "28",
                    createNo: localStorage.getItem('userCode'),
                    creator: localStorage.getItem('userName')
                }
                self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        self.getInfo(self.itemParams)
                    }else{
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                });
            }else{
                //开始停线
                MessageBox.confirm('',{
                            message: '是否确认线体停线？',
                            title: '提示',
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        }).then(action => {
                            //启动停线页面
                            self.$router.push({
                                name: "StopLineInfo",
                                params: {
                                    moId: self.item.moId,
                                    lpId: self.item.id,
                                    preCategory: self.item.preCategory,
                                    workType: self.item.workType
                                }
                            });
                        })
            }
        },
        workMode(num){
            let self=this;
            if(num==1){
                // Toast({
                //     message: "功能暂未开启，敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                // return
                if(self.item.lastStatus=='4'){
                    Toast({
                        message: "当前订单已完工！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
                if(self.item.lastStatus=='0' || self.item.lastStatus=='2'){
                    Toast({
                        message: "请先开线",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
                self.$router.push({name:"BeginRecord",params:{lpId:self.item.id,checkFlag:self.item.checkFlag}})
            }else if(num==2){
                if(self.item.workType=='4'){
                    if(self.item.lastStatus=='0' || self.item.lastStatus=='2'){
                        Toast({
                            message: "请先开线",
                            position: 'bottom',
                            duration: 2000
                        });
                        return
                    }else if(self.item.lastStatus>='11' && self.item.lastStatus<='17'){
                        Toast({
                            message: "请先结束维修、换产等操作",
                            position: 'bottom',
                            duration: 2000
                        });
                        return
                    }
                    self.$router.push({name:"OrderMixture",params:{lpId:self.item.id}})
                }else{
                    self.$router.push({name:"ColloidUsageInfo",params:{lpId:self.item.id,tpId:self.item.tpId}})
                }
                return
            }else if(num==3){

                if(self.item.checkFlag=='1'){
                    Toast({
                        message: "请等待IPQC复核！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }

                if(self.item.lastStatus=='4'){
                    Toast({
                        message: "当前订单已完工！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }

                if(self.item.lastStatus=='0' || self.item.lastStatus=='2'){
                    if(self.item.workType!='4'){
                        Toast({
                            message: "请先开线",
                            position: 'bottom',
                            duration: 2000
                        });
                        return
                    }
                }
                localStorage.setItem("bgItem",JSON.stringify(self.item));
                if(self.item.workType=='4' || self.item.workType=='1'){
                    self.$router.push({name:"HourReport"})
                }else{
                    Toast({
                        message: "该工单暂无报工权限！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
            }else if(num==4){

                if(self.item.checkFlag=='1'){
                    Toast({
                        message: "请等待IPQC复核！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }

                    self.pushOther()
                // if(self.item.workType=='1' || self.item.workType=='4'){
                    // self.pushOther()
                // }else{
                //     Toast({
                //         message: "非灌包/制胶订单无法进入其他状态！",
                //         position: 'bottom',
                //         duration: 2000
                //     });
                //     return
                // }
            }
        },
        modalCloseFormOk(){

        },
        getPrepareInfo(){
            let self=this;
            // let workDay='2021-07-31'
            let workDay=this.formatDate(new Date())
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getPrepareInfo',{params:{workDay:workDay,lpId:self.item.id}}).then(res=>{
                if(res.data.code==200){
                    self.gld=res.data.result.laId
                    if(self.gld==null || self.gld==''){
                        self.gld="请选择"
                    }
                }
            })
        },
        getInfo(item){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo',{params:item}).then(res=>{
                console.log(res)
                if(res.data.code==200){
                    Indicator.close();
                    self.peopleInfo=res.data.result.peopleInfo
                    self.newOrderList=res.data.result.newOrderList
                    self.workOrderList=res.data.result.workOrderList
                    self.errorOrderList=res.data.result.errorOrderList
                    self.finishOrderList=res.data.result.finishOrderList
                    if(self.newOrderList.length>0){
                        self.item=self.newOrderList[0]
                    }else if(self.workOrderList.length>0){
                        self.item=self.workOrderList[0]
                    }else if(self.errorOrderList.length>0){
                        self.item=self.errorOrderList[0]
                    }else if(self.finishOrderList.length>0){
                        self.item=self.finishOrderList[0]
                    }

                    console.log(self.item)

                    self.getLastNum(self.item.moId,self.item.plot,self.item.book,self.item.exchange);

                    if(self.item.preCategory=='1'){
                        self.preCategory='白班'
                    }else if(self.item.preCategory=='4'){
                        self.preCategory='晚班'
                    }else if(self.item.preCategory=='6'){
                        self.preCategory='培训'
                    }else{
                        self.preCategory='无班次'
                    }
                    self.personList=self.item.gcWorkPlanList
                    console.log(self.item.status)
                    self.lastStatus=self.item.lastStatus
                    if(self.lastStatus==null || self.lastStatus==''){
                        self.item.lastStatus=='1'
                    }
                    if(self.item.lastStatus=='1' || self.item.lastStatus=='3' || self.item.lastStatus=='9'){
                        self.end=false;
                        self.start=true;
                        self.stop=false;
                        self.menuTrainVis=false;
                        self.menuSelectVis=false;
                        self.lxendMite = require('../../static/images/stopline_start.png')
                    }else if(self.item.lastStatus=='2'){
                        self.end=true;
                        self.start=false;
                        self.menuTrainVis=true;
                        self.menuSelectVis=true;
                    }else if(self.item.lastStatus=='0'){
                        self.end=true;
                        self.start=false;
                        self.menuTrainVis=true;
                        self.menuSelectVis=true;
                    }else if(self.item.lastStatus=='14'){
                        self.end=true;
                        self.start=true;
                        self.menuTrainVis=false;
                    }else if(self.item.lastStatus=='16'){
                        self.end=true;
                        self.start=true;
                        self.menuSelectVis=false;
                    }else if(self.item.lastStatus=='18'){
                        self.end=false;
                        self.start=true;
                        self.stop=true;
                        self.lxendMite = require('../../static/images/stopline_end.png')
                    }else{
                        self.end=true;
                        self.start=true;
                    }

                    for(var i=0;i<self.personList.length;i++){
                        if(self.personList[i].type=='14'){
                            self.train='取消培训';
                        }
                        if(self.personList[i].type=='16'){
                            self.select='取消挑拣';
                        }
                    }
                    self.getPrepareInfo()
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getLastNum(moId,plot,book,exchange){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getRestCount',{params:{moId:moId,plot:plot,book:book}}).then(res=>{
                if(res.data.code==200){
                    self.lastNum=res.data.message;
                    if(self.lastNum==null || self.lastNum==0){
                        self.lastNum=0;
                        self.lastFNum=0;
                    }else{
                        self.lastFNum=self.lastNum/exchange
                    }
                }
            });
        },
        startToNext(){

        },
        modalFormOk(){
            let self=this
            this.getInfo(this.itemParams)
            if(self.personList.length==0){
                MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                    if(action=="confirm"){
                        let params={
                            lpId:self.item.id,
                            gcWorkOperationList:self.personList,
                            type:'2',
                            preCategory:self.item.preCategory,
                            workType:self.item.workType,
                            createNo:localStorage.getItem('userCode'),
                            creator:localStorage.getItem('userName'),
                            repCreator:self.worker
                        }
                        // if(self.item.department.indexOf('苏州')!=-1){
                            this.$refs.modalNewForm.edit(params);
                            this.$refs.modalNewForm.title="关线备注";
                            this.$refs.modalNewForm.disableSubmit = true;
                        // }else{
                        //     this.$refs.modalFormc.edit(params);
                        //     this.$refs.modalFormc.title="关线录入";
                        //     this.$refs.modalFormc.disableSubmit = true;
                        // } 
                        // this.$refs.modalFormc.edit(params);
                        // this.$refs.modalFormc.title="关线录入";
                        // this.$refs.modalFormc.disableSubmit = true;
                    }
                })
            }
        },
        modalNewCloseFormOk(){
            let self=this
            self.getInfo(self.itemParams)
        },
        handleMenuClick(e) {
            let self=this
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let itemList=[]
            for(var i=0;i<self.personList.length;i++){
                if(self.personList[i].type=='5'){
                    Toast({
                        message: '所选之人存在人员临时离开，请先归产',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
                if(self.personList[i].selected){
                    itemList.push(self.personList[i]) 
                }
            }
            console.log("itemList:"+itemList);
            if(itemList.length<=0){
                Toast({
                    message: "至少选择一名员工操作！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }

            if(e.key==1){
                MessageBox.prompt('调入蓄水池原因').then(({ value, action }) => {
                    if(action=="confirm"){
                        self.sendToWorkDeploy('9',itemList)
                    }
                });
                
            }else if(e.key==2){
                for(var i=0;i<itemList.length;i++){
                    itemList[i].lpId=self.item.id
                    
                }
                self.$refs.modalForm.edit(itemList,self.item.department);
                self.$refs.modalForm.title="调离";
                self.$refs.modalForm.disableSubmit = true;
            }else if(e.key==3){
                for(var i=0;i<itemList.length;i++){
                    itemList[i].lpId=self.item.id
                }
                self.$refs.modalForms.edit(itemList);
                self.$refs.modalForms.title="离开原因";
                self.$refs.modalForms.disableSubmit = true;
            }else if(e.key==4){
                if(self.train=="取消培训"){
                    self.sendToWorkDeploy('24',itemList)
                }else{
                    self.sendToWorkDeploy('14',itemList)
                }
            }else if(e.key==5){
                if(self.select=="取消挑拣"){
                    self.sendToWorkDeploy('26',itemList)
                }else{
                    self.sendToWorkDeploy('16',itemList)
                }
                
            }
            
        },
        sendToWorkDeploy(num,itemList){
            let self=this;
            let params={
                lpId:self.item.id,
                gcWorkOperationList:itemList,
                type:num,
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName'),
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    if(num=='9'){
                        for(var a=0;a<itemList.length;a++){
                            self.personList.splice(this.personList.indexOf(itemList[a]),1);
                        }
                        if(self.personList.length==0){
                            MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                                if(action=="confirm"){
                                    let params={
                                        lpId:self.item.id,
                                        gcWorkOperationList:self.personList,
                                        type:'2',
                                        preCategory:self.item.preCategory,
                                        workType:self.item.workType,
                                        createNo:localStorage.getItem('userCode'),
                                        creator:localStorage.getItem('userName'),
                                        repCreator:self.worker
                                    }
                                    // if(self.item.department.indexOf('苏州')!=-1){
                                        this.$refs.modalNewForm.edit(params);
                                        this.$refs.modalNewForm.title="关线备注";
                                        this.$refs.modalNewForm.disableSubmit = true;
                                    // }else{
                                    //     this.$refs.modalFormc.edit(params);
                                    //     this.$refs.modalFormc.title="关线录入";
                                    //     this.$refs.modalFormc.disableSubmit = true;
                                    // } 
                                    // this.$refs.modalFormc.edit(params);
                                    // this.$refs.modalFormc.title="关线录入";
                                    // this.$refs.modalFormc.disableSubmit = true;
                                }
                            })
                        }
                    }else if(num=='14'){
                        self.train="取消培训"
                        this.getInfo(this.itemParams)
                    }else if(num=='24'){
                        self.train="培训"
                        this.getInfo(this.itemParams)
                    }else if(num=='16'){
                        self.select="取消挑拣"
                        this.getInfo(this.itemParams)
                    }else if(num=='26'){
                        self.select="挑拣"
                        this.getInfo(this.itemParams)
                    }else if(num=='1'){
                        self.getInfo(self.itemParams);
                    }
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            });
        },
        chargeBtn(action, done) {
            let self=this
            console.log(action)
            if (action === 'cancel') {//取消按钮
                done();
            } else if (action === 'confirm') {//确定按钮
                //向后端传值并关闭dialog弹出框
                this.show = false;
                done();
            }
            
        },
        leave(item){
            let self=this
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let list=[]
            item.lpId=this.item.id
            list.push(item);
            this.$refs.modalForms.edit(list);
            this.$refs.modalForms.title="离开原因";
            this.$refs.modalForms.disableSubmit = true;
            // Toast({
            //     message: "功能已关闭",
            //     position: 'bottom',
            //     duration: 2000
            // });
        },
        goback(item){
            let self=this;
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            let itemList=[]
            itemList.push(item)
            self.sendToWorkDeploy('1',itemList)
        },
        outWorkshop(item){
            let self = this
            let list=[]
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if(item.type=='5'){
                Toast({
                    message: '所选之人存在人员临时离开，请先归产',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            item.lpId=this.item.id
            list.push(item);
            this.$refs.modalForm.edit(list,self.item.department);
            this.$refs.modalForm.title="调离";
            this.$refs.modalForm.disableSubmit = true;
        },
        pushPool(item){
            let self=this;
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if(item.type=='5'){
                Toast({
                    message: '所选之人存在人员临时离开，请先归产',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            
            
            MessageBox.prompt('调入蓄水池原因').then(({ value, action }) => {
                    if(action=="confirm"){
                        let person=[];
                        item.reason=value;
                        person.push(item);
                        let params={
                            lpId:self.item.id,
                            gcWorkOperationList:person,
                            type:'9',
                            createNo:localStorage.getItem('userCode'),
                            creator:localStorage.getItem('userName')
                        }
                        self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
                            if(res.data.code==200){
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.personList.splice(this.personList.indexOf(item),1);
                                if(self.personList.length==0){
                                    MessageBox.confirm('当前人员列表中已无人员，是否关线？').then(action => {
                                        if(action=="confirm"){
                                            let params={
                                                lpId:self.item.id,
                                                gcWorkOperationList:self.personList,
                                                type:'2',
                                                preCategory:self.item.preCategory,
                                                workType:self.item.workType,
                                                createNo:localStorage.getItem('userCode'),
                                                creator:localStorage.getItem('userName'),
                                                repCreator:self.worker
                                            }
                                            // if(self.item.department.indexOf('苏州')!=-1){
                                                this.$refs.modalNewForm.edit(params);
                                                this.$refs.modalNewForm.title="关线备注";
                                                this.$refs.modalNewForm.disableSubmit = true;
                                            // }else{
                                            //     this.$refs.modalFormc.edit(params);
                                            //     this.$refs.modalFormc.title="关线录入";
                                            //     this.$refs.modalFormc.disableSubmit = true;
                                            // } 
                                            // this.$refs.modalFormc.edit(params);
                                            // this.$refs.modalFormc.title="关线录入";
                                            // this.$refs.modalFormc.disableSubmit = true;
                                        }
                                    })
                                }
                            }else{
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        });
                    }
            });
        },
        addLabel(item){
            let self=this;
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            console.log(item)
            MessageBox.prompt('请对其输入技能标签').then(({ value, action }) => {
                if(action=="confirm"){
                    self.$axios.get('/jeecg-boot/app/gcWorkshop/getLabelInfo',{params:{id:item.pid,label:value,createNo:localStorage.getItem('userCode'),creator:localStorage.getItem('userName')}}).then(res=>{
                        if(res.data.code==200){
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            item.label=value;
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                }
            });
        },
        addUser(){
            let self=this;
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            this.$router.push({name:"OrderUser",params:{status:self.item.status,lastStatus:self.item.lastStatus,createTime:self.item.createTime,lpId:self.item.id,preCategory:self.item.preCategory}})
        },
        pushPl(){
            let self=this;
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            this.$router.push({name:"AddPl",params:{workDay:this.item.createTime,lpId:this.item.id,materialList:this.item.gcMaterialList}})
        },
        pushOther(){
            let self=this
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if(self.item.lastStatus=='0' || self.item.lastStatus=='2'){
                Toast({
                    message: "请先开线",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            
            console.log("item:"+self.item);
            self.item.userName=self.peopleInfo.name
            self.item.userCode=self.peopleInfo.code
            localStorage.setItem('orderOtherItem',JSON.stringify(self.item));
            this.$router.push({name:"OrderOther",params:self.item})
        },
        openQuestionType(){
            this.popupVisible = true;
        },
        startMode(num){
            let self=this;
            let params={
                lpId:self.item.id,
                gcWorkOperationList:self.personList,
                type:num,
                preCategory:self.item.preCategory,
                workType:self.item.workType,
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName'),
                repCreator:self.worker
            }
            if(num=="2" || num=="3"){
                if(self.item.checkFlag=='1'){
                    Toast({
                        message: "请等待IPQC复核！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }

                console.log("personList:"+JSON.stringify(self.personList))

                let title="确认是否关线？";
                for(var i=0;i<self.personList.length;i++){
                    let type=self.personList[i].type
                    if(type=='16'){
                        title="有人员正在挑拣中，确认是否关线？";
                    }
                    if(type=='14'){
                        title="有人员正在培训中，确认是否关线？";
                    }
                }

                
                MessageBox.confirm(title).then(action => {
                    if(action=="confirm"){
                        // console.log(self.item.department.indexOf('苏州'))
                        // if(self.item.department.indexOf('苏州')!=-1){
                            this.$refs.modalNewForm.edit(params);
                            this.$refs.modalNewForm.title="关线备注";
                            this.$refs.modalNewForm.disableSubmit = true;
                        // }else{
                        //     this.$refs.modalFormc.edit(params);
                        //     this.$refs.modalFormc.title="关线录入";
                        //     this.$refs.modalFormc.disableSubmit = true;
                        // } 
                    }
                })


                
                // MessageBox.prompt('请输入产量').then(({ value, action }) => {
                //     if(action=="confirm"){
                //         if(value==null || value==""){
                //             Toast({
                //                 message: "产量不得为空",
                //                 position: 'bottom',
                //                 duration: 2000
                //             });
                //             return
                //         }else{
                //             params.count=value
                //             self.sendMode(num,params)
                //         } 
                //     }
                // });
            }else if(num=="4"){
                // this.$refs.modalFinishForm.edit(params);
                // this.$refs.modalFinishForm.title="确认完工";
                // this.$refs.modalFinishForm.disableSubmit = true;
                if(self.item.mitosome!=null && self.item.mitosome.indexOf("-GB-")!=-1){
                    self.$axios.get('/jeecg-boot/app/gcWorkshop/getUncheckCount',{params:{tpId:self.item.tpId}}).then(res=>{
                        if(res.data.code==200){
                            self.$router.push({name:"WmsUsageLoss",params:{tpId:self.item.tpId,book:self.item.book,plot:self.item.plot,worker:self.worker,
                    lpId:self.item.id,personList:self.personList,workshop:self.item.workshop,moId:self.item.moId,code:self.item.code,mainUnit:self.item.mainUnit,
                    preCategory:self.item.preCategory,workType:self.item.workType}})
                        }else{
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                    
                }else{
                    MessageBox.confirm('确认是否完工？').then(action => {
                        if(action=="confirm"){
                            // if(self.item.department.indexOf('苏州')!=-1){
                                self.getOutputRecords(num,params)
                                // self.sendFinishMode(num,params)
                            // }else{
                            //     self.sendMode(num,params)
                            // }
                            
                        }
                    })
                }
            }else{

                if(self.item.workType=='1'){

                    if(self.item.checkFlag=='0'){
                        MessageBox.confirm('',{
                            message: '是否进入换产模式？',
                            title: '提示',
                            confirmButtonText: '开线',
                            cancelButtonText: '换产'
                        })
                        .then(action => {
                            if(action=="confirm"){
                                self.checkMaterialWarnInfo(params);
                                // self.$router.push({name:"WorkBegin",params:{items:params,type:1,lpId:self.item.id,checkFlag:self.item.checkFlag}})
                                // self.$router.push({name:"WorkBegin",params:{items:params,type:1,moId:self.item.moId,workDay:self.item.createTime,checkFlag:self.item.checkFlag}})
                            }
                        }).catch((res)=>{
                            // console.log("res:"+res)
                            if(res=="cancel"){
                                params.type='99'
                                self.sendMode('99',params)
                            }
                        });
                    }else if(self.item.checkFlag=='1'){
                        self.getMoStatus(num,params)
                    }else{
                        self.sendMode(num,params)
                    }
                    
                }else if(self.item.workType=='4'){
                    MessageBox.confirm('',{
                            message: '是否进入换产模式？',
                            title: '提示',
                            confirmButtonText: '开线',
                            cancelButtonText: '换产'
                    }).then(action => {
                        if(action=="confirm"){
                            self.checkMixture(num,params);
                            // self.$router.push({name:"WorkBegin",params:{items:params,type:1,moId:self.item.moId,workDay:self.item.createTime,checkFlag:self.item.checkFlag}})
                        }
                    }).catch((res)=>{
                        // console.log("res:"+res)
                        if(res=="cancel"){
                            params.type='99'
                            self.sendMode('99',params)
                        }
                    });
                    // self.sendMode(num,params)
                }else{
                    self.sendMode(num,params)
                }
            } 
        },
        checkMaterialWarnInfo(params){
            let self=this
            self.$axios.get('/jeecg-boot/app/gcWorkshop/checkMaterialWarnInfo',{params:{tpId:self.item.tpId}}).then(res=>{
                if(res.data.success){
                    if(res.data.result.length>0){
                        //存在异常物料
                        var msg = "";

                        for(var i=0;i<res.data.result.length;i++){
                            msg+="<p>物料："+res.data.result[i].code+res.data.result[i].name+",批次："+res.data.result[i].batchCode+"</p>";
                        }

                        msg+="<p>存在质量异常，请仔细核对，详情至物料核对菜单内查看！</p>"


                        MessageBox.confirm('',{
                            message:msg,
                            title:'温馨提示',
                            showConfirmButton:true,
                            dangerouslyUseHTMLString: true,
                            showCancelButton:false
                        }).then(action => {
                            if(action=="confirm"){
                                self.$router.push({name:"WorkBegin",params:{items:params,type:1,lpId:self.item.id,checkFlag:self.item.checkFlag}})
                            }
                        })
                    }else{
                        self.$router.push({name:"WorkBegin",params:{items:params,type:1,lpId:self.item.id,checkFlag:self.item.checkFlag}})
                    }
                }else{
                    self.$router.push({name:"WorkBegin",params:{items:params,type:1,lpId:self.item.id,checkFlag:self.item.checkFlag}})
                }
            })
        },
        checkMixture(num,params){
            let self=this
            self.$axios.get('/jeecg-boot/app/mix/checkMixture',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.success){
                    // 未绑定配料单
                    if(res.data.message == '1'){
                        if(self.item.workshop=='漱口水制胶车间'){
                            self.checkTimes(num,params);
                        }else{
                            self.$router.push({name:"MixtureSelect",params:{lpId:self.item.id}})
                        }
                    }else{
                        if (self.workOrderList.length == 0) {
                            // 未开过线
                            if (self.newOrderList[0].batchFlag == 1) {
                                Toast({
                                    message: "请确认是否绑定批记录",
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }else if (self.newOrderList[0].batchFlag == 99 || self.newOrderList[0].batchFlag == null || self.newOrderList[0].batchFlag == 5) {
                                // 暂不绑定配料单
                                self.sendMode(num, params)
                            } else {
                                // 已绑定配料单 且 绑定批记录 且 未复核
                                let obj = {
                                    id: self.newOrderList[0].mixId,
                                    lpId:self.newOrderList[0].lpId
                                }
                                self.$router.push({
                                    name: "checkBeforeProduction",
                                    params: obj
                                });
                            }
                        } else {
                            // 已经开过线
                            if (self.workOrderList[0].batchFlag == 1) {
                                Toast({
                                    message: "请确认是否绑定批记录",
                                    position: 'bottom',
                                    duration: 2000
                                });
                            } else if (self.workOrderList[0].batchFlag == null || self.workOrderList[0].batchFlag == 99 || self.workOrderList[0].batchFlag == 5) {
                                // 暂不绑定配料单
                                self.sendMode(num, params)
                            } else {
                                // 已绑定配料单 且 绑定批记录 且 未复核
                                let obj = {
                                    id: self.workOrderList[0].mixId,
                                    checkStatus: self.workOrderList[0].batchFlag,
                                    lpId: self.workOrderList[0].lpId
                                }
                                self.$router.push({
                                    name: "checkBeforeProduction",
                                    params: obj
                                });
                            }
                        }
                    }
                }else{
                    self.sendMode(num,params)
                }
            })
        },
        checkTimes(num,params){
            let self = this
            self.$axios.get('/jeecg-boot/app/mix/checkTimes',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.success){
                    MessageBox.confirm('是否在已绑定配料单上继续开线').then(action => {
                        if(action=="confirm"){
                            self.sendMode(num,params)
                        }
                    }).catch((res) => {
                        if (res == "cancel") {
                            self.$router.push({name:"MixtureSelect",params:{lpId:self.item.id}})
                        }
                    })
                }else{
                    self.$router.push({name:"MixtureSelect",params:{lpId:self.item.id}})
                }
            });
        },
        getOutputRecords(num,params){
            let self=this
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getOutputRecords',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.success){
                    MessageBox.confirm('当前报工数量为'+res.data.result.output+self.item.mainUnit+',是否确认完工？').then(action => {
                        if(action=="confirm"){
                            self.sendFinishMode(num,params)
                        }
                    })
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getMoStatus(num,params){
            let self=this
            self.$axios.get('/jeecg-boot/app/appQuality/getCheckStatus',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.code==200){
                    if(res.data.message=='2'){
                        Indicator.close();
                        self.sendMode(num,params)
                    }else if(res.data.message=='0'){
                        // self.$router.push({name:"WorkBegin",params:{items:params,type:1,moId:self.item.moId,workDay:self.item.createTime,checkFlag:self.item.checkFlag}})
                        self.$router.push({name:"WorkBegin",params:{items:params,type:1,lpId:self.item.id,checkFlag:self.item.checkFlag}})
                    }else{
                        Toast({
                            message: "请等待IPQC复核！",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendFinishMode(num,params){
            let self=this;
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            if(this.finishFlag){
                this.finishFlag=false
            }else{
                return;
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    this.finishFlag=true
                    if(num=='1'){
                        self.end=false;
                        self.start=true;
                    }else if(num=='2'){
                        self.end=true;
                        self.start=false;
                    }else if(num=='99'){
                        self.end=false;
                        self.start=true;
                    }
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    // self.getInfo(self.itemParams)
                    self.$router.push({name:"OrderClose",params:res.data.result});
                }else{
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendMode(num,params){
            let self=this;
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            if(this.openOrClose){
                Toast({
                    message: "正在等待网络相应，请勿重复点击！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            self.openOrClose=true
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.openOrClose=false
                    if(num=='1'){
                        self.end=false;
                        self.start=true;
                    }else if(num=='2'){
                        self.end=true;
                        self.start=false;
                    }else if(num=='99'){
                        self.end=false;
                        self.start=true;
                    }
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getInfo(self.itemParams)
                }else{
                    Indicator.close();
                    self.openOrClose=false
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        deleteUser(item){
            let self=this;
            let person=[]
            item.lpId=self.item.id
            person.push(item)
            let params={
                gcWorkPlanAppList:person,
                type:'2',
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName'),
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changePlanInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getInfo(self.itemParams)
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        selectGld(){
            // Toast({
            //     message: '暂未开放，敬请期待！',
            //     position: 'bottom',
            //     duration: 2000
            // });
            let self=this
            if(self.item.lastStatus=='4'){
                Toast({
                    message: "当前订单已完工！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            if(self.item.lastStatus=='0' || self.item.lastStatus=='2'){
                Toast({
                    message: "请先开线",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            this.$router.push({name:"OrderPre",params:{id:this.item.id}})
        },
        cancelGl(){
            //取消关联
            let self=this
            MessageBox.confirm('是否确认取消此单关联？').then(action => {
                if(action=="confirm"){
                    self.cancelSelected()
                }
            })
        },
        cancelSelected(){
            let self=this;
            let num='9';
            let params={
                lpId:self.item.id,
                laId:'',
                type:num,
                createNo:localStorage.getItem('userCode'),
                creator:localStorage.getItem('userName')
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close()
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.gld="请选择"
                }else{
                    Indicator.close()
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>

<style scoped>
.order{
    background-color: #ebecf7;
}
.top_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
}
.sign{
    text-align: center;
    margin: 10px;
    color: white;
    padding: 15px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url('../../static/images/plat_top.png');
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotId{
    position: absolute;
    top: 26%;
    left: 10%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    width: 85%;
    text-align: left;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 36%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 38%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 90%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
.menu_order_item{
    padding: 3%;
    float: left;
    width: 48%;
}
.menu_order{
    width: 100%;
    height: 11rem;
}
.menu_order_more_item{
    float: left;
    height: 100%;
    width: 25%;
}
.menu_order_more{
    background: white;
    border-radius: 10px;
    margin-bottom: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}
.more{
    background-image: url('../../static/images/more.png');
    background-size: 100%,100%;
    width: 33%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5%;
    color: #fff;
    background-repeat: no-repeat;
}
.other{
    background-image: url('../../static/images/other.png');
    background-size: 100%,100%;
    width: 90%;
    margin: 5%;
    height: 12rem;
    background-repeat: no-repeat;
}
.other p{
    font-weight: 600;
    text-align: left;
    padding: 10%;
}
.person_item{
    background-image: url('../../static/images/person_item.png');
    background-repeat: no-repeat;
    background-size: 100%,100%;
    margin: 5%;
    height: 18rem;
}
.person_name{
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}
.person_menu{
    float: right;
    width: 26%;
    position: absolute;
    right: 0.5rem;
}
.person_line{
    background-color: #d8d2f7;
    height: 1px;
    width: 90%;
    margin-left: 5%;
}
.person-top{
    padding: 5%;
    height: 2rem;
}
.person-left{
   float: left; 
   width: 65%;
   height: 100%;
   text-align: left;
}
.leave-style{
    color: crimson;
}
.leave-style2{
    color: teal;
}
.leave-style3{
    color: tomato;
}
.person-right{
   float: left; 
   display: flex;
   align-items: center;
   width: 33%;
   height: 100%;
}
.circle{
    width: 10px;
    height: 10px;
    background-color: #4a1bf6;
    border-radius: 50%; 
    display: inline-block;
    margin-right: 5px;
}
.person_menu_item{
    padding: 5%;
}
.addUser{
    background-image: url('../../static/images/addUser.png');
    background-size: 100% 100%;
    width: 90%;
    margin: 5%;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pl{
    background-image: url('../../static/images/item_bg.png');
    background-size: 100%,100%;
    width: 90%;
    margin: 5%;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background-repeat: no-repeat;
}
.allOpera{
    width: 100%;
    height: 3rem;
}
.person_num{
    color: #4a1bf6;
    font-weight: 800;
    width: 33%;
    height: 100%;
    float: right;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5%;
    background-repeat: no-repeat;
}
.gl_bg{
    width: 90%;
    height: 3rem;
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 5%;
    margin-top: 5%;
    margin-bottom: 5%;
    display: flex;
    align-items: center;
}
.gld_bg{
    background: url('../../static/images/gl_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42%;
    color: #fff;
    float: left;
}
.gld_title{
    width: 42%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.gld_cancel{
    width: 15%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: blue;
}
.ycl-style{
    color: crimson;
}
</style>