<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">坦克数据</div>

        <van-field label="储罐编号" v-model="tankNo"/>

        <van-button type="info" @click="searchTank()"  style="margin:10px;width:40%;border-radius:10px;">查询</van-button>
        <van-button type="primary" @click="scanTank()"  style="margin:10px;width:40%;border-radius:10px;">扫码</van-button>

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px;margin-top:20px;">数据检索</div>

        <van-field label="查询条件" placeholder="可输入胶体编号、名称、批次等信息进行检索" v-model="key"/>

        <van-button type="info" @click="searchKey()"  style="margin:10px;width:40%;border-radius:10px;">检索</van-button>


        <div v-for="(item,index) in dataArr1" :key="index" style="margin-bottom:10px;margin-top:10px;padding:5px;text-align:left;background:#fff" @click="handleTank(item)">
            <div style="font-weight:800;font-size:16px;">{{item.tankNo}}</div>
            <div style="width:100%;height:1px;background:#888"></div>
            <div>产品编号：{{item.realProductNo}}</div>
            <div>产品名称：{{item.realProductName}}</div>
            <div>产品批次：{{item.realCode}}</div>
            <div>剩余容量：{{item.tankVolume-item.realVolume}}KG</div>
        </div>

        
        
    </div>
</template>
<script>

import { DatetimePicker,Indicator,Toast,MessageBox } from 'mint-ui';

let wx=window.wx

export default {
    data(){
        return{
            bookList:[],
            workShopList:[],
            jobCenterList:[],
            tankList:[],
            dataArr1:[],
            pullList:[],
            showBook:false,
            showWorkshop:false,
            showJobCenter:false,
            book:"",
            workshop:"",
            key:"",
            jobCenter:"",
            tankNo:"",
        }
    },
    methods: {
        searchTank(){
            let self = this
            self.$router.push({name:"ScanTankOptInfo",params:{id:self.tankNo}})
        },
        handleTank(item){
            let self = this
            self.$router.push({name:"ScanTankOptInfo",params:{id:item.id}})
        },
        searchKey(){
            let self = this
            self.$axios.get('/jeecg-boot/app/tank/check/getTankInfoByKey', { params: { key: self.key } }).then(res => {
                if (res.data.success) {
                    this.dataArr1 = res.data.result
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        scanTank(){
            let self=this

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$router.push({name:"ScanTankOptInfo",params:{id:result}})
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}

</style>