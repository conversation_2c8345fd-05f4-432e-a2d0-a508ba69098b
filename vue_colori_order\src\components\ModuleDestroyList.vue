<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="报损记录" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">
            <van-field v-model="product" clearable label="备件：" placeholder="请输入备件" />
            <van-field v-model="workshop" clearable label="车间：" placeholder="请输入车间" />
            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in list" :key="index"
            style="color: rgb(112 100 100 / 85%);text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="24" style="display:flex;color: rgb(38 37 37 / 85%);">
                    <span style="font-size:large;font-weight:700;text-align:left">
                        {{ item.name }}
                    </span>
                    <!-- <span style="flex:1;text-align:right">
                        <van-button style="height: 100%;" plain type="info" @click="imgPreview(item)">查看图片
                        </van-button>
                    </span> -->
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24">备件编号: {{ item.code }}</van-col>
            </van-row>
            <van-row>
                <van-col span="24">操&nbsp;作&nbsp;人 : {{ item.createName }}</van-col>
            </van-row>
            <van-row>
                <van-col span="24">入库数量: {{ item.count }}</van-col>
            </van-row>
            <van-row>
                <van-col span="24">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注: {{ item.remarks }}</van-col>
            </van-row>
        </div>

    </div>
</template>

<script>
import { Toast, ImagePreview } from "vant";
export default {
    components: {
        [ImagePreview.Component.name]: ImagePreview.Component,
    },
    data() {
        return {
            list: [],
            show: false,
            product: "",
            workshop: "",
            id: "",
        }
    },
    created() {
        this.search()
    },
    methods: {
        imgPreview(item) {
            let images = []
            if (item.pictureList) {
                item.pictureList.forEach(item => {
                    images.push(item.picUrl)
                });
                console.log(images);
                ImagePreview({
                    images,
                    closeable: true,
                });
            } else {
                Toast({
                    message: '暂无图片',
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        search() {
            this.$axios
                .get(`/jeecg-boot/ncApp/moldsUsage/getUsageList?type=4&product=${this.product}&workshop=${this.workshop}&id=${this.id}&userCode=${localStorage.getItem('userCode')}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.list = res.data.result.records
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "ModuleOperation"
            });
        },
        onClickRight() {
            this.show = true;
        }
    },
}
</script>

<style scoped>
</style>