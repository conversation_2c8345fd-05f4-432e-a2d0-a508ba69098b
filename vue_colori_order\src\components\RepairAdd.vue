<template>
    <div>
        <van-nav-bar v-show="active == 0" title="维修录入" left-text="返回" left-arrow @click-left="onClickLeft" />

        <van-form validate-first @submit="onSubmit">
            <van-field readonly name="isDebug" :value="isDebug" label="维修类型" placeholder="点击选择"
                @click="showPicker2 = true" />
            <van-popup v-model="showPicker2" position="bottom">
                <van-picker show-toolbar :columns="columns2" @confirm="onConfirm2" @cancel="showPicker2 = false" />
            </van-popup>

            <van-field readonly :clickable="false" name="isReplace" :value="isReplace" label="是否更换配件" placeholder="点击选择"
                @click="replaceClick" :disabled="isDebug == '调试'" />
            <van-popup v-model="showPicker1" position="bottom">
                <van-picker show-toolbar :columns="columns1" @confirm="onConfirm1" @cancel="showPicker1 = false" />
            </van-popup>
            <div v-if="isReplace == '是'">
                <van-cell title="点击添加备件" @click="handleAdd" style="width: 90%; " />
                <div v-for="(v, index) in partsListRes" :key="index">
                    <van-row>
                        <van-col span="12">
                            <van-cell title="点击扫码" @click="handleSearch(v)" style="width: 90%; " />
                        </van-col>
                        <van-col span="12">
                            <van-cell title="删除" @click="handleDel(index)" style="width: 90%; " />
                        </van-col>
                    </van-row>
                    <a-row>
                        <a-col :span="6" style="line-height: 2rem"> 备件 </a-col>
                        <a-col :span="16">
                            <a-select placeholder="请选择备件" style="width: 100%" v-model="v.partsId" :dropdown-match-select-width="false" :dropdown-style="{ maxWidth: '260px', wordWrap: 'break-word', whiteSpace: 'normal', overflow: 'hidden' }" class="custom-select">
                                <a-select-option v-for="(item, index) in v.arr" :value="item.id" :key="index" :disabled="partsListRes.some(v => v.partsId === item.id)" class="custom-option">
                                    {{ item.name }}--数量:({{ item.stock }})
                                </a-select-option>
                            </a-select>
                        </a-col>
                    </a-row>
                    <van-field v-model="v.partsCount" name="partsCount" label="备件数量" placeholder="请输入" />
                </div>
            </div>

            <van-field readonly clickable name="maintenance" :value="maintenance" label="维修情况" placeholder="点击选择"
                @click="showPicker3 = true" />
            <van-popup v-model="showPicker3" position="bottom">
                <van-picker show-toolbar :columns="columns3" @confirm="onConfirm3" @cancel="showPicker3 = false" />
            </van-popup>

            <div v-if="info.detailList == null">
                <van-field readonly clickable name="isArtificial" :value="isArtificial" label="是否人为" placeholder="点击选择"
                    @click="showPicker4 = true" />
                <van-popup v-model="showPicker4" position="bottom">
                    <van-picker show-toolbar :columns="columns4" @confirm="onConfirm4" @cancel="showPicker4 = false" />
                </van-popup>
            </div>
            <div v-else>
                <van-field v-model="isArtificial" disabled name="isArtificial" label="是否人为" />
            </div>
            <van-field v-model="content" name="content" rows="3" autosize label="维修内容" type="textarea"
                placeholder="请输入维修内容" />
            <van-field v-model="remarks" name="remarks" rows="3" autosize label="备注" type="textarea"
                placeholder="请输入备注" />

            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="uploader" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" :max-count="1" />
                </template>
            </van-field>

            <div style="margin: 16px">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
export default {
    data() {
        return {
            code: "",
            active: 0,
            info: {},
            // 是否更换配件
            isReplace: "",
            // 是否调试
            isDebug: "",
            // 维修情况
            maintenance: "",
            // 维修内容
            content: "",
            // 备件
            partsId: "",
            // 备件数量
            partsCount: 0,
            // 是否人为
            isArtificial: "",
            //备注
            remarks: "",
            // 展示图片
            pictureList: [],
            // 上传图片
            uploader: [],

            columns1: ["是", "否"],
            columns2: ["维修", "调试"],
            columns3: ["未修好", "已修好", "委外维修"],
            columns4: ["是", "否"],

            partsList: [],
            showPicker1: false,
            showPicker2: false,
            showPicker3: false,
            showPicker4: false,
            partsListRes: [],
        };
    },
    created() {
        this.info = this.$route.params.info;
        if (this.info.detailList != null) {
            this.isArtificial = this.info.detailList[
                this.info.detailList.length - 1
            ].isArtificial;
        }
    },
    methods: {
        handleDel(i){
            console.log(i);
            this.partsListRes.splice(i, 1);
        },
        handleAdd() {
            this.partsListRes.push({})
        },
        handleSearch(v) {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果
                                self.code = result;
                                self.getList(v, self.code)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入产品编号").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getList(v, self.code)
                            }
                        });
                    }
                });
        },
        getList(v, code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/ncApp/parts/getPartsListByCode?code=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        self.$set(v, 'arr', res.data.result)
                        console.log(v);
                        console.log(self.partsListRes);
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        handleChange(e,v) {
            this.$set(v,'count',v.stock)
            console.log(e);
            console.log(v);
        },
        replaceClick() {
            if (this.isDebug == "维修") {
                this.showPicker1 = true;
            }
        },
        isDebugChange(e) {
            console.log(e);
        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios.post(`/jeecg-boot/app/mac/uploadPic`, param).then(res => {
                if (res.data.code == 200) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/app/mac/deletePic?id=${""}&picUrl=${this.pictureList[name.index].picUrl
                            }`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                            } else {
                                Indicator.close();
                                this.uploader.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                    this.uploader.splice(name.index, 1);
                    this.pictureList.splice(name.index, 1);
                })
                .catch(() => {
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
        // 提交
        onSubmit(values) {
            if (this.info.detailList != null) {
                // 如果不是第一条维修记录,是否人为与一条记录保持一致
                values.isArtificial = this.isArtificial;
            }

            if (this.pictureList.length <= 0) {
                Toast({
                    message: "请对维修情况进行拍照处理！",
                    position: "bottom",
                    duration: 2000
                });
                return;
            }

            values.pictureList = this.pictureList;
            values.creator = localStorage.getItem("userCode");
            values.repairId = this.info.id;
            values.partsList = this.partsListRes;
            console.log(values);
            if (
                values.content == "" ||
                values.isArtificial == "" ||
                values.isDebug == "" ||
                values.isReplace == "" ||
                values.maintenance == ""
            ) {
                Toast({
                    message: "请填写相关信息",
                    position: "bottom",
                    duration: 2000
                });
            } else {
                if (values.isReplace == "是" && values.partsId == "") {
                    Toast({
                        message: "请选择备件",
                        position: "bottom",
                        duration: 2000
                    });
                }
                Indicator.open({
                    text: "处理中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios
                    .post(`/jeecg-boot/app/mac/addRepairDetail`, values)
                    .then(res => {
                        if (res.data.code == 200) {
                            Indicator.close();
                            this.$router.push({
                                name: "RepairDetail1",
                                params: { item: this.info, status: "2", active: 1 }
                            });
                        } else {
                            Indicator.close();
                            Toast({
                                message: "操作失败",
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            }
        },
        onClickLeft() {
            this.$router.push({
                name: "RepairDetail1",
                params: { item: this.info, status: "2", active: 1 }
            });
        },
        onConfirm1(value) {
            this.isReplace = value;
            this.showPicker1 = false;
        },
        onConfirm2(value) {
            console.log(value);
            this.partsId = "";
            this.partsCount = 0;
            this.isReplace = "";
            if (value == "调试") {
                this.isReplace = "否";
            }
            this.isDebug = value;
            this.showPicker2 = false;
        },
        onConfirm3(value) {
            this.maintenance = value;
            this.showPicker3 = false;
        },
        onConfirm4(value) {
            this.isArtificial = value;
            this.showPicker4 = false;
        }
    }
};
</script>

<style>
/* 添加自定义样式使 select option 能够固定宽度并换行 */
.custom-option {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100% !important;
  display: block !important;
  line-height: 1.5;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 专门针对Ant Design的样式 */
.ant-select-dropdown-menu-item, 
.ant-select-dropdown-menu-item-selected {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow: hidden !important;
  padding: 5px 8px !important;
  height: auto !important;
  min-height: 32px !important;
}

.ant-select-dropdown {
  width: 300px !important;
}

/* 禁用水平滚动 */
.ant-select-dropdown-menu {
  overflow-x: hidden !important;
}

/* 控制下拉选项的样式 */
.ant-select-dropdown-menu-item-content,
.ant-select-dropdown-menu-item-content span {
  white-space: normal !important;
  word-break: break-all !important;
  display: inline-block !important;
  width: 100% !important;
}

/* 修改a-select-option的样式 */
.ant-select-dropdown-menu-item div {
  white-space: normal !important;
  word-break: break-all !important;
}

/* 针对 Ant Design Vue 1.x 的特殊处理 */
.ant-select-dropdown-menu-item {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: normal !important;
}

/* 处理实际文本内容 */
.ant-select-dropdown ul li {
  white-space: normal !important;
}
</style>
