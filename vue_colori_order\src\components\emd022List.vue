<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="emd022" right-text="筛选" @click-right="onClickRight" left-arrow
                @click-left="onClickLeft" />
            <van-button round type="info" @click="add" style="width: 100%;">
                新增
            </van-button>
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />

            <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
                @click="showbookNamePicker = true" />
            <van-popup v-model="showbookNamePicker" position="bottom">
                <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                    @cancel="showbookNamePicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="workshop" label="车间：" placeholder="点击选择车间"
                @click="showworkshopPicker = true" />
            <van-popup v-model="showworkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                    @cancel="showworkshopPicker = false" />
            </van-popup>



            <van-field v-model="leader" label="部门主管" placeholder="请输入部门主管" />

            <van-button type="info" @click="search" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>
        <div v-for="(item, index) in dataArr1" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
                <van-row>
                    <van-col span="24" style="font-size:18px;font-weight: 700;color: #000;">
                        <span v-if="item.spStatus == null"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            新工单
                        </span>
                        <span v-if="item.spStatus == '1'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            审批中
                        </span>
                        <span v-if="item.spStatus == '2'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            已通过
                        </span>
                        <span v-if="item.spStatus == '3'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            已驳回
                        </span>
                        <span v-if="item.spStatus == '4'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            已撤销
                        </span>
                        <span v-if="item.spStatus == '6'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            通过后撤销
                        </span>
                        <span v-if="item.spStatus == '7'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            已删除
                        </span>
                        <span v-if="item.spStatus == '10'"
                            style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                            已支付
                        </span>
                    </van-col>
                </van-row>
            </div>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 账套：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.bookName }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 部门：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.deptName }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
         
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 维修结果：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.repairResult }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 备注：</van-col>
                        <van-col span="18">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.remark }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="8"> 是否使用备件：</van-col>
                        <van-col span="16">
                            <span
                                style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                {{ item.isUseParts }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row gutter="5">
                <van-col span="6" v-if="item.spStatus != 1 && item.spStatus != 2 && item.spStatus != 10"><van-button 
                        type="info" style="width:100%" size="mini" @click="detail(item)">编辑</van-button></van-col>
                <van-col span="6" v-if="item.spStatus != 1 && item.spStatus != 2 && item.spStatus != 10"><van-button 
                        type="info" style="width:100%" size="mini" @click="del(item)">删除</van-button></van-col>
                <van-col span="6" v-if="item.spStatus != 1 && item.spStatus != 2 && item.spStatus != 10"><van-button 
                        type="info" style="width:100%" size="mini"
                        @click="initiateApproval(item)">发起审批</van-button></van-col>
                <van-col span="6" v-if="item.spStatus == 2"><van-button  type="info" style="width:100%" size="mini"
                        @click="detailes(item)">详情</van-button></van-col>
                <van-col span="6" v-if="item.spStatus == 2"><van-button  type="info" style="width:100%" size="mini"
                        @click="push(item)">推送</van-button></van-col>
                <van-col span="6" v-if="item.spStatus == 2 && item.nccRollBackStatus == 0"><van-button  type="info" style="width:100%" size="mini"
                        @click="handleReturn(item)">退库</van-button></van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";

import { Indicator } from "mint-ui";
export default {
    data() {
        return {
            date: this.getDate(-1) + " ~ " + this.getDate(0),
            startDate: this.getDate(-1),
            endDate: this.getDate(0),

            active: 0,
            userCode: localStorage.getItem("userCode"),
            dataArr1: [],
            loading: false,
            finished: false,
            refreshing: false,

            show: false,
            show1: false,

            showbookNamePicker: false,
            bookName: "",
            bookNameColumns: [],

            showworkshopPicker: false,
            workshop: "",
            workshopColumns: [],

            showstatusPicker: false,
            type: "",
            statusColumns: ['点检', '巡检', '维保'],
            leader: "",
            glue: "",
            initiatorName: '',
        };
    },
    created() {
        if (this.userCode == null || this.userCode == "") {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000
            });
            this.$router.push({
                name: "LoginIndex"
            });
        } else {
            this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
                if (res.data.code == 200) {
                    console.log(res.data.result);
                    res.data.result.forEach(item => {
                        this.bookNameColumns.push(item.name);
                    });
                } else {
                }
            });
            this.search();
        }
    },
    methods: {
        handleReturn(item) {
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .get("/jeecg-boot/app/inspection/handSendNccEmdTwentyTwo", { params: { id: item.id, type: -1, userCode: localStorage.getItem('userCode') } })
                .then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        push(item) {
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .post("/jeecg-boot/app/inspection/handSendNccEmdTwentyTwo", { id: item.id, userCode: localStorage.getItem('userCode') })
                .then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        initiateApproval(item) {
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .post("/jeecg-boot/app/inspection/infoApproval", { id: item.id, userCode: localStorage.getItem('userCode') })
                .then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        del(item) {
            Dialog.confirm({
                message: '确定要删除吗?',
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .get("/jeecg-boot/app/inspection/nonProductionDeptPartsDelete", {
                            params: { id: item.id, userCode: localStorage.getItem('userCode') }
                        })
                        .then(res => {
                            Indicator.close();
                            if (res.data.code == 200) {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.search()
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                })
                .catch(() => {
                    Toast({
                        message: `您点击了取消`,
                        position: 'bottom',
                        duration: 1000
                    });
                });

        },
        setup(item) {
            this.$router.push({
                name: "eqImprovementSetup",
                params: item
            });
        },
        bookNameConfirm(value) {
            this.bookName = value;
            this.workshop = '';
            this.jobCenter = '';
            localStorage.setItem('feedingCheckBook', this.bookName)
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value }
                })
                .then(res => {
                    if (res.data.code == 200) {
                        this.workshopColumns = []
                        res.data.result.forEach(item => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.workshop = value;
            this.showworkshopPicker = false;
            // this.jobCenter = '';
            // // 查找工作中心
            // this.$axios
            //     .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
            //         params: { book: this.bookName, workshop: value }
            //     })
            //     .then(res => {
            //         if (res.data.code == 200) {
            //             console.log(res.data.result);
            //             res.data.result.forEach(item => {
            //                 this.jobCenterColumns.push(item.jobCenter);
            //             });
            //         } else {
            //         }
            //     });
        },
        statusConfirm(value) {
            this.type = value;
            this.showstatusPicker = false;
        },
        onClickRight() {
            this.show = true;
        },
        search() {
            this.show = false;
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .get(
                    `/jeecg-boot/app/inspection/getNonProductionDeptPartsList?startDate=${this.startDate}&endDate=${this.endDate}&bookName=${this.bookName ? this.bookName : ''}&deptName=${this.deptName ? this.deptName : ''}&leaderName=${this.leader ? this.leader : ''}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.dataArr1 = res.data.result;
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        onClick(name, title) {
            console.log(name, title);
        },
        detail(item) {
            this.$router.push({
                name: "emd022Add",
                params: { ...item, editType: 'edit' }
            });
        },
        detailes(item) {
            this.$router.push({
                name: "emd022Detail",
                params: item
            });
        },
        add() {
            this.$router.push({
                name: "emd022Add",
            });
        },
        onConfirm(date) {
            const [start, end] = date;
            this.show1 = false;
            this.startDate =
                start.getFullYear() +
                "-" +
                (start.getMonth() + 1 < 10
                    ? "0" + (start.getMonth() + 1)
                    : start.getMonth() + 1) +
                "-" +
                (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
            this.endDate =
                end.getFullYear() +
                "-" +
                (end.getMonth() + 1 < 10
                    ? "0" + (end.getMonth() + 1)
                    : end.getMonth() + 1) +
                "-" +
                (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
            this.date = `${this.startDate}~${this.endDate}`;
        },
        getDate(day) {
            var date1 = new Date(),
                time1 =
                    date1.getFullYear() +
                    "-" +
                    (date1.getMonth() + 1) +
                    "-" +
                    date1.getDate(); //time1表示当前时间
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return (
                date2.getFullYear() +
                "-" +
                (date2.getMonth() + 1 < 10
                    ? "0" + (date2.getMonth() + 1)
                    : date2.getMonth() + 1) +
                "-" +
                (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
            );
        },
    }
};
</script>

<style scoped></style>