<template>
  <div>
    <img src="../../static/images/batchRecord_check.png" width="100%" />
    <div class="menu_order_more">
      <div class="menu_order_more_item" @click="goDetail('5')">
        <img src="../../static/images/FeedingCheckDetail5.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>配料前检查</p>
      </div>
      <div class="menu_order_more_item" @click="goDetail('1')">
        <img src="../../static/images/FeedingCheckDetail4.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>制胶前检查</p>
      </div>

      <div class="menu_order_more_item" @click="goDetail('2')">
        <img src="../../static/images/FeedingCheckDetail1.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>胶体操作记录</p>
      </div>

    </div>
    <div class="menu_order_more">

      <div class="menu_order_more_item" @click="goDetail('3')">
        <img src="../../static/images/FeedingCheckDetail2.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>HACCP关键控制点</p>
      </div>
      <div class="menu_order_more_item" @click="goDetail('4')">
        <img src="../../static/images/FeedingCheckDetail3.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>质量控制指标</p>
      </div>
      <div class="menu_order_more_item" @click="goDetail('6')">
        <img src="../../static/images/FeedingCheckDetail6.png"
          style="display:block;margin:0 auto;height:50%;padding-top:10%" />
        <p>审查过程异常记录表</p>
      </div>
    </div>
  </div>
</template>

<script>
  import { Toast } from "vant";
  export default {
    data() {
      return {};
    },
    created() { },
    methods: {
      goDetail(i) {
        if (i == 1) {
          this.$router.push({ name: "batchRecordcheck1" });
        } else if (i == 2) {
          this.$router.push({ name: "batchOptionrecord" });
        } else if (i == 3) {
          this.$router.push({ name: "batchHACCP" });
        } else if (i == 4) {
          let self=this
            self.$axios.get('/jeecg-boot/app/tank/check/getPermissionByColloid',{params:{userCode:localStorage.getItem("userCode"),type:1}}).then(res=>{
                if(res.data.success){
                    if(res.data.result>=1){
                      this.$router.push({ name: "BatchQualityControl" });
                    }else{
                        Toast({
                            message: "对不起，您没有权限！",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        } else if (i == 5) {
          this.$router.push({ name: "batchBeforeCheck" })
        } else if (i == 6) {
          this.$router.push({ name: "batchAbnormalRecord" })
        }
      }
    }
  };
</script>

<style scoped>
  .menu_order_more_item {
    float: left;
    height: 100%;
    width: 33%;
  }

  .menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
  }
</style>