<template>
<div class="order">
    <van-tabs v-model="tabActive" @change="onTabChange">
        <van-tab name="1" title="维修反馈"></van-tab>
        <van-tab name="2" title="维修记录"></van-tab>
        <van-tab name="3" title="维修登记">
            <van-form @submit="recordSubmit">
                <van-field readonly right-icon="arrow" input-align="right"
                    label="维修类型" name="维修类型"
                    v-model="type"
                    placeholder="维修类型"
                    @focus="onSelectType"
                    :rules="[{ required: true, message: '维修类型必选' }]"
                />
                <van-field label="维修内容" name="维修内容"
                    v-model="content"
                    placeholder="维修内容"
                    rows="3" autosize type="textarea"
                    :rules="[{ required: true, message: '请填写维修内容' }]"
                />
                <van-cell title="照片" value="损坏物件照片" title-class="vanCellClass">
                    <template #label>
                        <van-uploader v-model="imgFiles"
                            accept="image/*"
                            :after-read="afterReadFiles"
                            @click-preview="onClickPreview" />
                    </template>
                </van-cell>
                <van-cell title="固定设施" title-class="vanCellClass">
                    <van-checkbox-group v-model="articleId" ref="checkboxGroup">
                        <van-row>
                            <van-col span="24">
                                <van-checkbox style="margin-top:5%;" v-for="(item, index) in articleList" :key="index"
                                    :name="item.value" shape="square">
                                    {{ item.label }}
                                </van-checkbox>
                            </van-col>
                        </van-row>
                        
                    </van-checkbox-group>
                </van-cell>
                <div style="margin: 16px;">
                    <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
                </div>
            </van-form>
        </van-tab>
    </van-tabs>

    <div v-if="tabActive!='3'">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="getRepairList">
                <div v-for="(repair, index) in repairList" :key="index" style="margin-top: 10px;">
                    <div class="repairItem">
                        <div>登记人：<b>{{ repair.creator }}</b></div>
                        <div>登记人工号：<b>{{ repair.userCode }}</b></div>
                        <div>登记时间：<b>{{ repair.createTime }}</b></div>
                        <div>固定设施：<b>{{ repair.articleName }}</b></div>
                        <div>维修状态：<b>{{ repair.maintainStatus|fMStatus }}</b></div>
                        <div>维修类型：<b>{{ repair.type }}</b></div>
                        <div>维修内容：</div>
                        <div v-html="repair.content.replace(/\n/g,'<br>')" class="repairContent"></div>
                        <div style="text-align: right;">
                            <van-button type="default" size="small" @click="onCheck(repair)">查看</van-button>
                            <van-button v-if="repair.maintainStatus=='1'"
                                style="margin-left: 8px;"
                                type="warning" size="small" @click="debounce(onRollBack(repair,500))">撤销</van-button>
                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
    </div>
    


<van-popup v-model="popTypeShow" position="bottom" :style="{ height: '45%' }">
    <van-picker title="维修类型" show-toolbar
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="onTypeCancel" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            tabActive: '1',
            content: '', //维修内容
            imgFiles:[], //照片上传
            popTypeShow: false,
            typeColumns: ['设施维修','水电维修'],
            type: '',
            sign: 'response',
            iPage: {
                currentPage: 1,
                TotalPage: 0,
                pageSize: 5
            },
            refreshing: false, // 是否下拉刷新
            repairList: [],   // 维修登记列表
            loading: false, // 是否加载
            finished: false, // 是否完成  停止触底加载
            confirmLoading: false,
            articleId: [],
            articleList: []
        }
    },
    methods:{
        onCheck(pRow){// 跳转详情页
            this.$router.push({path:'/ssRepairDetail', query: { rId: pRow.id}});
        },
        onRollBack(pRow){
            const _THIS=this
            Dialog.confirm({
                title: '提示',
                message: '确认撤销？',
            }).then(()=>{
                let rParams={
                    id: pRow.id,
                    userCodeRequest:localStorage.getItem('userCode'),
                }
                _THIS.$axios.get("/dormApi/maintain/app/rollBackRecord",{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 1000
                            });
                            this.onRefresh()
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                            this.onRefresh()
                        }
                    }else{
                        Toast({
                            message: '发生错误',
                            position: 'bottom',
                            duration: 2000
                        });
                        this.onRefresh()
                    }
                })
            })
        },
        getRepairList(){ // 获取维修列表
            if (this.refreshing) {
                this.repairList = [];
                this.iPage.currentPage = 1;
                this.refreshing = false;
                this.finished=false
            }
            let rParams={
                userCode: localStorage.getItem('userCode'),
                sign: this.sign,
                pageNo: this.iPage.currentPage,
                pageSize: this.iPage.pageSize
            }
            this.$axios.get("/dormApi/maintain/app/getMaintainRecordList", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.repairList.push(...res.result.records);
                        this.iPage={
                            currentPage: res.result.current,
                            TotalPage: res.result.pages,
                            pageSize: res.result.size,
                        }
                        // 关闭loading状态
                        this.loading = false;
                        // 判断是否到底了
                        if(this.iPage.currentPage*1>=this.iPage.TotalPage*1){
                            this.finished = true;
                        }else{
                            this.iPage.currentPage++;
                        }
                    }else{
                        Toast({
                            message: res.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: '发生错误',
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onRefresh() { // 列表刷新
            // 处于刷新状态
            this.refreshing=true;
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            // 加载数据
            this.getRepairList();
        },
        onTabChange(pName, pTitle){
            if(pName==='1'){
                this.sign='response'
                this.onRefresh()
            }else if(pName==='2'){
                this.sign='record'
                this.onRefresh()
            }else if(pName==='3'){
                this.clearForm()
                this.iPage={
                    currentPage: 1,
                    TotalPage: 0,
                    pageSize: 5
                }
                this.repairList=[]

                if(this.articleList.length == 0) {
                    let that = this;
                    this.$axios.get('/dormApi/dormitory/app/getArticleByRoom', {params:{userCode:localStorage.getItem('userCode')}}).then(rtn=>{
                        console.log('rtn',rtn);
                        if(rtn.status===200){
                            const res=rtn.data
                            if(res.success){
                                that.articleList = []
                                for(let i = 0; i < res.result.length; i++) {
                                    that.articleList.push({
                                        label: res.result[i].article,
                                        value: res.result[i].id,
                                    });
                                }
                                Toast.success(res.message)
                                
                            }else{
                                Toast.fail(res.message)
                                
                            }
                        }else{
                            Toast.fail("发生错误")
                        }
                    })
                }
            }
        },
        onTypeConfirm(pValue, pIndex){
            this.type=pValue
            this.popTypeShow=false
        },
        onTypeCancel(){
            this.type=''
            this.popTypeShow=false
        },
        onSelectType(){
            this.popTypeShow=true
        },
        clearForm(){
            this.$nextTick(()=>{
                this.type=''
                this.content=''
                this.articleId = []
                this.imgFiles=[]
                this.confirmLoading=false
            })
        },
        recordSubmit(){
            this.confirmLoading=true
            for(let img of this.imgFiles){
                if(img.status=='failed'){
                    Toast("请删除上传失败文件后再提交")
                    return false
                }
                if(img.status=='uploading'){
                    Toast("请在上传完成后再提交")
                    return false
                }
            }

            let ids = this.articleId.join(',');
            let nameList = [];
            if(this.articleId && this.articleId.length > 0) {
                
                for(let i = 0; i < this.articleId.length; i++) {
                    for(let j = 0; j < this.articleList.length; j++) {
                        if(this.articleId[i] == this.articleList[j].value) {
                            nameList.push(this.articleList[j].label)
                        }
                    }
                }
            }
            let articleName = nameList.join(',');
            let params={
                type: this.type,
                userCode: localStorage.getItem('userCode'),
                content: this.content,
                pictureUrl: this.imgFiles.map(item=>item.url).join(","),
                userCodeRequest:localStorage.getItem('userCode'),
                articleId: ids,
                articleName: articleName
            }
            if(!params.type || params.type==""){
                Toast.fail("请选择维修类型")
                return false;
            }
            this.$axios.post('/dormApi/maintain/app/maintainRecordAdd', params).then(res => {
                const result=res.data
                if(res.status==200 && result.success){
                    Toast({
                        message: result.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    this.clearForm()
                    this.tabActive="1"
                    this.onTabChange("1","维修反馈")
                }else{
                    Toast({
                        message: result.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            }).finally(()=>{
                this.confirmLoading=true
            })
        },
        afterReadFiles(file){
            console.log(file)
            let rParams=new FormData();
            if(file.constructor != Array){
                if(file.file.type.indexOf('image')==-1){
                    Toast.fail('请上传图片文件')
                    file.status='failed'
                    file.message='上传失败'
                    return
                }
                file.status='uploading'
                file.message='点击可取消'
                rParams.append("file",file.file)
            }else{
                for(var i=0;i<file.length;i++){
                    let tmpFile=file[i]
                    if(tmpFile.file.type.indexOf('image')==-1){
                        Toast.fail('请上传图片文件')
                        tmpFile.status='failed'
                        tmpFile.message='上传失败'
                        continue
                    }
                    file.status='uploading'
                    file.message='点击可取消'
                    rParams.append("file",tmpFile.file)
                }
            }
            this.$axios.post('/dormApi/sys/common/upload', rParams).then(res => {
                console.log("upload Result", res)
                const result=res.data
                if(res.status==200 && result.success){
                    if(file.constructor != Array){
                        file.status=''
                        file.message=''
                        file.url=result.message
                    }else{

                    }
                }else{
                    Toast({
                        message: result.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onClickPreview(file){
            if(file.status==='uploading'){
                Dialog.confirm({
                title: '提示',
                message: '确认取消上传？',
                }).then(()=>{
                    file.status='failed'
                    file.message='取消上传'
                })
            }else if(file.status===''){
                if(file.file.type.indexOf('image')!=-1){
                    ImagePreview({
                        images: [`/dormApi/${file.url}`],
                        closeable: true
                    })
                }
            }
        },
        debounce(fn, delay = 100) {
            let timer = null
            return function () {
                let args = arguments
                if (timer) {
                    clearTimeout(timer)
                }
                timer = setTimeout(() => {
                    fn.apply(this, args)
                }, delay)
            }
        },
    },
    created(){
        this.onTabChange("1","维修反馈")
    },
    filters: {
        fStatus(status){
            switch(status){
                case "0": return "正常";
                case "1": return "失效";
                default: return status;
            }
        },
        fMStatus(mStatus){
            switch(mStatus){
                case "1": return "待处理";
                case "2": return "已报修";
                case "3": return "已完成";
                case "4": return "已撤销";
                case "5": return "已关闭";
                default: return mStatus;
            }
        }
    }
}
</script>
<style scoped>
.vanCellClass{
  color: #646566;
  text-align: left;
}
.repairItem{
    width: 92%;
    padding: 10px;
    margin: 0 auto;
    border-radius: 8px;
    background: #eee;
    text-align: left;
}
.repairContent{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-align: left;
    font-weight: bold;
}
</style>