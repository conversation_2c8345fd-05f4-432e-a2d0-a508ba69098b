<template>
  <div style="text-align:left;padding-bottom: 1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar
        title="拣货"
        left-text="返回"
        right-text="筛选"
        left-arrow
        @click-left="onClickLeft"
        @click-right="onClickRight"
      />
    </van-sticky>
    <van-popup v-model="show" position="bottom" :style="{ height: '35%' }">
      <van-cell title="选择时间" :value="date" @click="show1 = true" />
      <van-calendar
        v-model="show1"
        type="range"
        @confirm="onConfirm"
        :min-date="new Date(2022)"
        color="#1989fa"
      />
      <van-field
        readonly
        clickable
        name="picker"
        v-model="bookName"
        label="账套："
        placeholder="点击选择账套"
        @click="showbookNamePicker = true"
      />
      <van-popup v-model="showbookNamePicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="bookNameColumns"
          @confirm="bookNameConfirm"
          @cancel="showbookNamePicker = false"
        />
      </van-popup>

      <van-field
        v-model="checkoutNo"
        clearable
        label="出库单号："
        placeholder="请输入出库单号"
      />
      <van-field
        v-model="checker"
        clearable
        label="复核员："
        placeholder="请输入复核员"
      />
      <van-field
        v-model="customer"
        clearable
        label="客户："
        placeholder="请输入客户"
      />

      <van-button type="info" @click="search1" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>
    <div
      v-for="(item, index) in dataArr"
      :key="index"
      style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
    >
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="20">
            <span style="font-size:18px;font-weight: 700;color: #000;">{{
              item.checkoutNo
            }}</span>
          </van-col>
          <van-col span="4">
            <span v-if="item.childStatus == 1" style="color: #FF9933;">
              待处理
            </span>
            <span v-if="item.childStatus == 2" style="color: #009966;">
              已完成
            </span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="16">
          <span style="color:gary"
            >单据类型：<span style="color:black;">{{ item.type }}</span></span
          >
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24">
          <span style="color:gary"
            >客户名称：<span
              style="color:black;overflow-x: auto;white-space:nowrap;"
              >{{ item.customer }}</span
            ></span
          >
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary;"
            >客户地址：<span style="color:black">{{ item.address }}</span></span
          >
        </van-col>
      </van-row>

      <van-row>
        <van-col span="16"></van-col>
        <van-col span="4">
          <!-- <van-button
            size="mini"
            icon="exchange"
            color="linear-gradient(to right, #667eea, #764ba2)"
            round
            hairline
            type="primary"
            @click="turnSingle(item)"
            >转单
          </van-button> -->
        </van-col>
        <van-col span="4">
          <van-button
            size="mini"
            icon="share-o"
            round
            hairline
            type="primary"
            @click="checkDetail(item)"
            >拣货
          </van-button>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      date: this.getDate(-1) + " ~ " + this.getDate(0),
      beginDay: this.getDate(-1),
      endDay: this.getDate(0),
      userCode: localStorage.getItem("userCode"),
      active: 0,
      // 搜索弹出层是否显示
      show: false,
      // 时间选择显示隐藏
      show1: false,
      // 指派保管员
      show2: false,
      // 指派复核员
      show3: false,
      dataArr: [],
      checkerNo: "",
      checkerName: "",
      // 指派数组
      arrCheck: [],

      // 区域选择显示隐藏
      showCheckPicker: false,
      // 区域数组
      checkColumns: [],
      clickIndex: "",

      showbookNamePicker: false,
      bookName: "",
      bookNameColumns: [],

      checker: "",
      checkoutNo: "",
      customer: ""
    };
  },
  created() {
    this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
      if (res.data.code == 200) {
        console.log(res.data.result);
        res.data.result.forEach(item => {
          this.bookNameColumns.push(item.name);
        });
      } else {
      }
    });
    this.search(this.beginDay, this.endDay);
  },
  methods: {
    // 选择账套确认
    bookNameConfirm(value) {
      this.bookName = value;
      this.showbookNamePicker = false;
    },
    // 查看详情
    checkDetail(item) {
      this.$router.push({
        name: "GoodsPickDetail",
        params: item
      });
    },
    overDetail(item) {
      this.$router.push({
        name: "GoodsPickOverDetail",
        params: item
      });
    },

    search1() {
      this.search(this.beginDay, this.endDay);
      this.show = false;
    },
    search(beginDay, endDay) {
      this.$axios
        .get(
          `/jeecg-boot/app/warehousePick/getPickMainList?userCode=${this.userCode}&status=2&beginDay=${beginDay}&endDay=${endDay}&book=${this.bookName}&checkoutNo=${this.checkoutNo}&checker=${this.checker}&customer=${this.customer}&type=2`
        )
        .then(res => {
          if (res.data.code == 200) {
            this.dataArr = res.data.result.records;
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    onClickLeft() {
      this.$router.replace({
        name: "GoodsOutbound"
      });
    },
    onClickRight() {
      this.show = true;
    },
    // 切换tab栏请求列表
    onClick(name) {
      localStorage.setItem("goodsActive", name + 2);
      this.dataArr = [];
      if (name == 3) {
        // 已完结
        this.search(name, this.beginDay, this.endDay);
      } else {
        // 其他
        this.search(name, "", "");
      }
    },
    getDate(day) {
      var date1 = new Date(),
        time1 =
          date1.getFullYear() +
          "-" +
          (date1.getMonth() + 1) +
          "-" +
          date1.getDate(); //time1表示当前时间
      var date2 = new Date(date1);
      date2.setDate(date1.getDate() + day);
      return (
        date2.getFullYear() +
        "-" +
        (date2.getMonth() + 1 < 10
          ? "0" + (date2.getMonth() + 1)
          : date2.getMonth() + 1) +
        "-" +
        (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
      );
    },
    onConfirm(date) {
      const [start, end] = date;
      this.show1 = false;
      this.beginDay =
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1 < 10
          ? "0" + (start.getMonth() + 1)
          : start.getMonth() + 1) +
        "-" +
        (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
      this.endDay =
        end.getFullYear() +
        "-" +
        (end.getMonth() + 1 < 10
          ? "0" + (end.getMonth() + 1)
          : end.getMonth() + 1) +
        "-" +
        (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
      this.date = `${this.beginDay}~${this.endDay}`;
    },
    searchPeople() {
      this.$axios
        .get(
          `/jeecg-boot/app/utils/getStaffNameByCode?userCode=${this.checkerNo}`
        )
        .then(res => {
          if (res.data.message != null) {
            this.checkerName = res.data.message;
          } else {
            this.checkerName = "";
            Toast({
              message: "编号错误,请重新输入",
              position: "bottom",
              duration: 2000
            });
          }
        });
    },

    // 区域确认
    onCheckConfirm(value) {
      this.arrCheck[this.clickIndex].user = value;
      this.arrCheck[this.clickIndex].userName = value.split("-")[0];
      this.arrCheck[this.clickIndex].userCode = value.split("-")[1];
      this.showCheckPicker = false;

      console.log(this.arrCheck);
    },
    showCheckPickerFn(item, index) {
      this.clickIndex = index;
      this.showCheckPicker = true;
      this.checkColumns = item.data;
    },
    goCheck(item) {
      this.$router.push({
        name: "GoodsPickCheck",
        params: item
      });
    },
    turnSingle(item) {
      this.$router.push({
        name: "GoodsPickTurn",
        params: item
      });
      // this.$axios
      //   .get(`/jeecg-boot/app/warehousePick/getPickMainSun?id=${item.id}`)
      //   .then(res => {
      //     if (res.data.success) {
      //       console.log(res);
      //       for (let i = 0; i < res.data.result.length; i++) {
      //         if (res.data.result[i].pickerNo != this.userCode) {
      //           Toast({
      //             message: "您非此单保管员,不能操作转单",
      //             position: "bottom",
      //             duration: 2000
      //           });
      //           return;
      //         }
      //       }
      //       this.$router.push({
      //         name: "GoodsPickTurn",
      //         params: item
      //       });
      //     } else {
      //       Toast({
      //         message: res.data.message,
      //         position: "bottom",
      //         duration: 2000
      //       });
      //     }
      //   });
    }
  }
};
</script>

<style lang="scss" scoped></style>
