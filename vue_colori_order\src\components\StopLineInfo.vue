<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div style="text-align:left;font-weight:800;padding:10px;font-size:18px;">停线情况描述</div>
        <van-form validate-first @submit="onSubmit" style="background:white">
            <van-field-Pick v-model="stopType"  label="停线类型" required
                :columns="['原材料质量异常', '胶体待出料问题', '停工待料']" />

            <van-field-Pick v-model="qualityType"  label="质量异常" required v-if="stopType=='原材料质量异常'"
                :columns="['材料异常', '原料异常']" />

            <van-field v-model="describe" type="textarea" label="情况描述" required row="5" maxlength="200" show-word-limit/>

            <div style="margin: 16px;">
                <van-button round block type="info" :loading="loading" native-type="submit">提交</van-button>
            </div>
        </van-form>


        

    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

export default {
    data(){
        return{
            loading:false,
            stopType:"",
            qualityType:"",
            describe:"",
            moId:"",
            lpId:"",
            workType:"",
        }
    },
    components:{
        DatetimePicker
    },
    async created() {
        this.moId = this.$route.params.moId
        this.lpId = this.$route.params.lpId
        this.workType = this.$route.params.workType
        this.preCategory=this.$route.params.preCategory
        console.log(this.workType)
        console.log(this.preCategory)
    },
    methods: {
        onSubmit(values){
            let self = this
            if(self.stopType=="" || self.describe==""){
                Toast({
                    message: '相关信息不完整',
                    position: "bottom",
                    duration: 2000
                });
            }else if(self.stopType=="原材料质量异常" && self.qualityType==""){
                Toast({
                    message: '请选择质量异常',
                    position: "bottom",
                    duration: 2000
                });
            }else{
                if(self.stopType!="原材料质量异常"){
                    self.qualityType="";
                }
                let params = {
                    stopType: self.stopType,
                    describe: self.describe,
                    qualityType: self.qualityType,
                    lpId: self.lpId,
                    type: "18",
                    createNo: localStorage.getItem('userCode'),
                    creator: localStorage.getItem('userName')
                }
                self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        self.$router.go(-1);
                    }else{
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                });
            }
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.star-rating {
  display: flex;
  margin-right:20px;
  justify-content: right;
}
.star {
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s;
}
.star::before {
  content: '☆';
}
.star.filled::before,
.star:hover::before {
  content: '★';
  color: gold;
}
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
/deep/ .van-field__label{
    width: 12em;
}
/deep/ .van-field__control{
    text-align: left;
    color: #00f;
    border: 1px solid #555;
}
/deep/ .mint-radiolist-title{
    font-size: 18px;
    color: #000;
}
</style>