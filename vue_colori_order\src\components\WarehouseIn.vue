<template>
    <div>

        <van-button type="primary" @click="qrCodeScan()" style="margin:10%;width:80%;border-radius:10px;">扫描托码</van-button>


        <div style="margin-left:10%;width:80%;height:50px;">
            <div class="rktitle">入库记录</div>
            <div class="rkdate" @click="c_show = true">{{date}}</div>
        </div>

        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false" position="right" />


        <div v-for="(item,index) in detailInfoList" :key="index" @click="getCodeInfo(item.id)" style="margin-top:15px;" class="report_item">
            <div class="report_line"></div>
            <div class="report_item_text">
                <div class="item_left">MO单号</div>
                <div class="item_right">{{item.moId}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">产品编号</div>
                <div class="item_right">{{item.code}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">产品名称</div>
                <div class="item_right">{{item.name}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">货位号</div>
                <div class="item_right">{{item.location}}</div>
            </div>
            <div class="report_item_text">
                <div class="item_left">入库时间</div>
                <div class="item_right">{{item.localTime}}</div>
            </div>
        </div>

    </div>
</template>

<script>
import { DatetimePicker,Toast,Indicator,MessageBox } from 'mint-ui';
export default ({
    data() {
        return{
            itemParams:{},
            detailInfoList:{},
            allQuantity:0,
            show:true,
            date:'',
            minDate:'',
            status:'放行',
            pass_show:false,
            c_show:false,
            workTitle:'设为不合格'
        }
        
    },
    created:function(){
        this.date=this.formatDate(new Date)
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 30 * 24 * 3600 * 1000);
        // this.setWxInfo();
        this.getAddHouseInfo();
        // this.getCodeInfo("2112090001")
    },
    methods: {
        getAddHouseInfo(){
            let self=this
            self.$axios.get('/jeecg-boot/app/appQuality/getAddHouseInfo',{params:{createTime:self.date,userCode:localStorage.getItem('userCode')}})
                .then(res=>{
                if(res.data.code==200){
                    // self.$router.push({name:"CargoPerch",params:{item:JSON.stringify(res.data.result)}})
                    self.detailInfoList=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        setWxInfo(){
            let self=this
            let url=window.location.href.split("#")[0];
            self.$axios.get('/jeecg-boot/app/wx/getWxInfo',{params:{url:url}})
                .then(res=>{
                wx.config({
                    beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                    debug: false,
                    appId: res.data.result.appId,
                    timestamp: res.data.result.timestamp,
                    nonceStr: res.data.result.noncestr,
                    signature: res.data.result.signature,
                    jsApiList : [
                        'getLocalImgData',
                        'chooseImage',
                        'previewImage',
                        'uploadImage',
                        'downloadImage',
                        'downloadImage',
                        'openBluetoothAdapter',
                        'closeBluetoothAdapter',
                        'getBluetoothAdapterState',
                        'startBluetoothDevicesDiscovery',
                        'onBluetoothDeviceFound',
                        'createBLEConnection',
                        'closeBLEConnection',
                        'getBLEDeviceServices',
                        'getBLEDeviceCharacteristics',
                        'writeBLECharacteristicValue',
                        'onBLECharacteristicValueChange',
                        'scanQRCode'
                    ]
                });
            })
        },
        selectMoId(){
            this.$router.push({name:"SearchMoId"})
        },
        qrCodeScan(){
            let self=this;
            wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                success: function(res) {
                  console.log(res.resultStr);
                  self.getCodeInfo(res.resultStr);
                }
            });
        },
        getCodeInfo(resultStr){
            let self=this;
            self.$axios.get('/jeecg-boot/app/appQuality/getCodeInfoById',{params:{id:resultStr}})
                .then(res=>{
                if(res.data.code==200){
                    self.$router.push({name:"CargoPerch",params:{item:JSON.stringify(res.data.result)}})
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getAddHouseInfo();
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    },
    
})
</script>


<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}
.addControl{
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
.rktitle{
    float:left;
    width:40%;
    height: 100%;
    font-size:20px;
    display: flex;
    align-items: center;
    justify-content: left;
}
.rkdate{
    float:left;
    width:60%;
    height: 100%;
    font-size:20px;
    display: flex;
    align-items: center;
    justify-content: right;
}
.report_line{
    background: #cfcfcf;
    height: 1px;
}
.report_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.report_item{
    width: 90%;
    border-radius: 10px;
    margin: 0 auto;
    margin-bottom: 20px;
    background: #fff;
}
/deep/ .van-field__label{
    width: 10em;
}

</style>