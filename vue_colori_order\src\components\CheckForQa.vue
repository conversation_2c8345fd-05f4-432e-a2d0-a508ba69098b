<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_title">巡检整改审核单</div>
            <!-- <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div> -->
        </div>
        <van-field label="异常项目" rows="3" autosize type="textarea" :value="itemParams.content" readonly />
        <van-field label="异常描述" rows="3" autosize type="textarea" :value="itemParams.reason" readonly />
        <van-field label="巡检日期" :value="itemParams.createTime" readonly />

        <div class="add_upload_imgBox float_left all_width">
            <div class="add_upload_imgDiv float_left" v-for="(item, index) in itemParams.pictureInfoList" :key="index">
                <img v-image-preview :src="item.picUrl"/>
            </div>
        </div>


        <van-field label="整改描述" rows="3" autosize type="textarea" v-model="itemParams.changeResult" readonly/>
        
        <div class="add_upload_imgBox float_left all_width">
            <div class="add_upload_imgDiv float_left" v-for="(item, index) in itemParams.changeInfoList" :key="index">
                <img @click="showImg(item.picUrl)" :src="item.picUrl"/>
            </div>
        </div>

        <div style="height:5vh;"></div>
        <van-button type="primary" @click="checkInfo" style="margin:10px;width:80%;border-radius:10px;">审核</van-button>

        <change-people-modal ref="modalNewForm" @ok="modalNewCloseFormOk"></change-people-modal>
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox,Indicator   } from 'mint-ui';
import PicModal from './list/PicModal.vue'
import ChangePeopleModal from './list/ChangePeopleModal.vue';
export default {
  components: { PicModal, ChangePeopleModal},
      
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            startMite:require('../../static/images/startMite.png'),
            endMite:require('../../static/images/endMite.png'),
            lxendMite:require('../../static/images/lx_endMite.png'),
            finishMo:require('../../static/images/finishMo.png'),
            menuPic:require('../../static/images/menu_pic.png'),
            personLogo:require('../../static/images/user_logo.png'),
            add:require('../../static/images/add.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            lastNum:0,
            show:false,
            value:false,
            menuTrainVis:true,
            menuSelectVis:true,
            reason:'',
            personList:[],
            urls:[],
            pictureInfoList:[],
            start:false,
            end:true,
            item:[],
            workDay:'',
            questionType:'',
            questionTypeVal:'',
            peopleInfo:{},
            workDetaiList:[],
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            popupVisible:false,
            lastStatus:'0',
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
            train:'培训',
            select:'挑拣',
            itemParams:{
                id:undefined,
                moId:undefined,
                workshop:undefined,
                mitosome:undefined,
                leaderNo:undefined,
                description:undefined
            },
            preCategory:'',
            gld:'请选择',
            worker:'',
            params:{
                id:'',
                localIds:[],
                serverIds:[]
            },
        }
    },
    created:function(){
        let self=this;
        self.itemParams=JSON.parse(localStorage.getItem("checkItem"))
    },
    
    methods:{
        checkInfo(){
            let self=this

            self.$axios.get('/jeecg-boot/app/appBos/checkDetail',{params:{id:self.itemParams.id,userCode:localStorage.getItem('userCode')}}).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        changeInfo(){
            this.$refs.modalNewForm.edit();
            this.$refs.modalNewForm.title="转派";
            this.$refs.modalNewForm.disableSubmit = true;
        },
        handleDeleteImage(id) {
            let that = this;
            MessageBox.confirm('确定删除该图片吗?').then(action => {
                console.log(action)
                if(action == 'confirm') {
                    deleteImage()
                }
            });
            function deleteImage() {
                for(let i = 0; i < that.pictureInfoList.length; i+=1) {
                    if(that.pictureInfoList[i].id === id) {
                        self.pictureInfoList.splice(i, 1);
                        break;
                    }
                }
            }
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        },
        showImg(url){
            this.$refs.modalForm.show(url);
            this.$refs.modalForm.disableSubmit = true;
        },
        fileUpload(){
            let that = this;
            let file = document.getElementById('upfile');
            let fileName = file.value;
            let files = file.files;
            console.log(files[0])
            if(fileName == null || fileName==""){
                alert("请选择文件");
            }else{
                let fileType = fileName.substr(fileName.length-4,fileName.length);
                console.log("fileType:"+fileType)
                if(fileType == ".jpg" || fileType == ".png"){
                if (files[0]) {
                    let formData = new window.FormData()
                    formData.append('file', files[0])
                    formData.append('id', this.itemParams.id)
                    formData.append('childId', "")
                    formData.append('description', "")
                    formData.append('type', "3")

                    that.$axios.post('jeecg-boot/app/appBos/uploadPic',formData).then(res=>{
                        if(res.data.code==200){
                            let timestamp = (new Date()).valueOf();
                            that.pictureInfoList.push({id: timestamp, picUrl: res.data.message})
                        }else{
                            Toast({
                                message: "上传失败，请稍候再试！",
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })


                    // fetch('jeecg-boot/app/gcWorkshop/uploadPic', {
                    // method: 'POST',
                    // body: formData,
                    // headers: {
                    //     // Auth: 'token'
                    //     'Access-Control-Allow-Origin': '*',
                    //     Authorization: 'Bearer ',
                    // },
                    // }).then((res) => {
                    //     console.log(res);
                    //     let timestamp = (new Date()).valueOf();
                    //     that.imgs.push({id: timestamp, base64: res.url,flag:true})
                    // })

                    // let reader = new FileReader();
                    // reader.readAsDataURL(files[0]);
                    // reader.onload = function (e) {
                    // let timestamp = (new Date()).valueOf();
                    // that.imgs.push({id: timestamp, base64: this.result,flag:true})
                    // console.log(that.imgs)
                    // }
                } else {
                    alert("请选择要上传的图片");
                }
                }else{
                alert("上传文件类型错误！");
                }
            }
        },
        uploadPic(){
            let self=this;
            self.params.description=self.itemParams.description
            if(self.params.serverIds.length<=0){
                Toast({
                    message: "请上传一张照片！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            self.$axios.post('/jeecg-boot/app/wx/uploadPic',self.params)
            .then(res=>{
                if(res.data.code=='200'){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.uploadFlag=true;
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            }).catch(res=>{
                Toast({
                    message: res.data.message,
                    position: 'bottom',
                    duration: 2000
                });
            });
        },
        uploadImgToWx(localIds){
              let self=this;
              var localId=localIds.pop();
              console.log("localId:"+localId);
              wx.uploadImage({
                localId: localId,
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                    var serverId=res.serverId;
                    console.log("serverId:"+serverId);
                    self.params.serverIds.push(serverId);
                    self.getLocation('uploadImg');
                },
                fail:err=>{
                    alert("上传失败");
                }
              });
        },
        previewImg(index){
            let that = this;
            console.log("index:"+index)
            wx.previewImage({
                current:that.urls[index],
                urls:that.urls
            });
        },
    }
}
</script>

<style scoped>
.van-cell{
    background: #fff;
}
.order{
    background-color: #ebecf7;
}
.top_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}
.plotId{
    position: absolute;
    top: 26%;
    left: 10%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    width: 85%;
    text-align: left;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 36%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 38%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 90%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    border-radius: 10px;
    margin: 5%;
    width: 90%;
    height: 5.5rem;
}
.more{
    background-image: url('../../static/images/more.png');
    background-size: 100%,100%;
    width: 33%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5%;
    color: #fff;
    background-repeat: no-repeat;
}
.other{
    background-image: url('../../static/images/other.png');
    background-size: 100%,100%;
    width: 90%;
    margin: 5%;
    height: 12rem;
    background-repeat: no-repeat;
}
.other p{
    font-weight: 600;
    text-align: left;
    padding: 10%;
}
.person_item{
    background-image: url('../../static/images/person_item.png');
    background-repeat: no-repeat;
    background-size: 100%,100%;
    margin: 5%;
    height: 5.5rem;
}
.person_name{
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}
.person_menu{
    float: right;
    width: 26%;
    position: absolute;
    right: 0.5rem;
}
.person_line{
    background-color: #d8d2f7;
    height: 1px;
    width: 90%;
    margin-left: 5%;
}
.person-top{
    padding: 5%;
    height: 2rem;
}
.person-left{
   float: left; 
   width: 65%;
   height: 100%;
   text-align: left;
}
.leave-style{
    color: crimson;
}
.leave-style2{
    color: teal;
}
.leave-style3{
    color: tomato;
}
.person-right{
   float: left; 
   display: flex;
   align-items: center;
   width: 33%;
   height: 100%;
}
.circle{
    width: 10px;
    height: 10px;
    background-color: #4a1bf6;
    border-radius: 50%; 
    display: inline-block;
    margin-right: 5px;
}
.person_menu_item{
    padding: 5%;
}
.addUser{
    background-image: url('../../static/images/addUser.png');
    background-size: 100% 100%;
    width: 90%;
    margin: 5%;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pl{
    background-image: url('../../static/images/item_bg.png');
    background-size: 100%,100%;
    width: 90%;
    margin: 5%;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background-repeat: no-repeat;
}
.allOpera{
    width: 100%;
    height: 3rem;
}
.person_num{
    color: #4a1bf6;
    font-weight: 800;
    width: 33%;
    height: 100%;
    float: right;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5%;
    background-repeat: no-repeat;
}
.gl_bg{
    width: 90%;
    height: 3rem;
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 5%;
    margin-top: 5%;
    display: flex;
    align-items: center;
}
.gld_bg{
    background: url('../../static/images/gl_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42%;
    color: #fff;
    float: left;
}
.gld_title{
    width: 42%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.gld_cancel{
    width: 15%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: blue;
}
.ycl-style{
    color: crimson;
}
.sudoku_row{
    display: flex;
    align-items: center;
    width:90%;
    flex-wrap: wrap;
    margin-left: 5%;
    margin-top: 5vh;
    margin-bottom: 5vh;
}
.sudoku_item{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width:33%;
    padding-top: 10px;
    padding-bottom: 10px;
}
.opacity{
    opacity: 0.4;
    background: #e5e5e5;
}
.sudoku_item img{
    margin-bottom: 3px;
    display: block;
}
.j-pic-upload{
    margin-top:2vh;
    margin-bottom:1vh;
    padding: 10rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}
.j-upload-btn{
    border: 1px dashed #ddd;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    width:32%;
    height:30vw;
}
.j-upload-add{
    font-size: 80rpx;
    font-weight: 500;
    color:#C9C9C9;
}
</style>
<style scoped>
.float_left {
  float: left;
}
.all_width{
  width: 100%;
}
.add_width{
  width: 29%;
}
.add_upload .add_upload_button {
  position: relative;
  width: 100%;
  height: 8rem;
  border: none;
  background: rgb(236,236,236);
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload .add_upload_icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.add_upload .add_upload_file {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  font-size: 0;
}

.add_upload_imgBox .add_upload_imgDiv {
  position: relative;
  width: 29%;
  height: 8rem;
  margin: 0.5rem 0.5rem 0.5rem 0.5rem;
}
.add_upload_imgBox .add_upload_imgDiv img {
  width: 100%;
  height: 100%;
}
.add_upload_imgBox .add_upload_close {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 30%;
}
.add_upload_imgBox .add_upload_close img {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
</style>