<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="新增021" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-form validate-first @submit="onSubmit" ref="form">

            <van-field readonly v-model="formData.bookName" name="content" rows="1" label="账套：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.workshopName" name="content" rows="1" label="车间：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.workCenter" name="content" rows="1" label="工作中心：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipId" name="content" rows="1" label="设备ID" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipCode" name="content" rows="1" label="设备编码" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.equipName" name="content" rows="1" label="设备名称" type="text"
                :rules="validateRule" />


            <van-field readonly clickable name="picker" v-model="formData.type" label="类型：" placeholder="点击选择类型"
                :rules="validateRule" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="typeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>
            <van-field readonly  name="picker" v-model="formData.dateChoose" label="日期：" placeholder="点击选择日期"
                :rules="validateRule" />
            <van-calendar v-model="show1" type="single" @confirm="onDateChooseConfirm" :min-date="new Date(2022)"
                color="#1989fa" />

            <van-field readonly v-model.trim="formData.groupLeader" label="组长" placeholder="请输入组长编号" :rules="[
                { validator: value => validator(value), message: '必填' }
            ]" />
            <van-field v-model="formData.groupLeaderName" label="组长姓名" readonly :rules="validateRule" />

            <van-popup v-model="show2" position="bottom" :style="{ height: '30%' }">
                <van-datetime-picker type="datetime" title="选择开始时间" @confirm="onStartTimeConfirm" :min-date="new Date()" @cancel="show2 = false" />
            </van-popup>
            <van-field v-model="formData.abnormalDescribe" name="abnormalDescribe" rows="3" autosize label="异常描述"
                type="textarea" placeholder="请输入" :rules="validateRule" />

            <van-field v-model="formData.remark" name="remark" rows="3" autosize label="备注" type="textarea"
                placeholder="请输入" :rules="validateRule" />
     
            <van-field readonly clickable name="picker" v-model="formData.startTime" label="开始时间："
                placeholder="点击选择开始时间" @click="show2 = true" :rules="validateRule" />

            <van-field readonly clickable name="picker" v-model="formData.isFinish" label="处理完成：" placeholder="点击选择"
                @click="showIsFinishPicker = true" :rules="validateRule" />
            <van-popup v-model="showIsFinishPicker" position="bottom">
                <van-picker show-toolbar :columns="isFinishColumns" @confirm="isFinishConfirm"
                    @cancel="showIsFinishPicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="formData.isUseParts" label="使用备件：" placeholder="点击选择"
                @click="showIsUsePartsPicker = true" :rules="validateRule" />
            <van-popup v-model="showIsUsePartsPicker" position="bottom">
                <van-picker show-toolbar :columns="isUsePartsColumns" @confirm="isUsePartsConfirm"
                    @cancel="showIsUsePartsPicker = false" />
            </van-popup>
        </van-form>

        <template v-if="formData.isUseParts == '是'">
            <van-button type="info" @click="addParts" size="mini" style="width:100%">扫码添加备件</van-button>
            <div v-for="(v, index) in detailList" :key="index" style="margin-top:2rem;">
                <van-row>

                    <van-col span="24">
                        <van-button style="width: 100%;" size="mini" @click="handleDel(index)" plain>删除</van-button>
                    </van-col>
                </van-row>
                <a-row>
                    <a-col :span="6" style="line-height: 2rem;margin-left:1rem;"> 备件 </a-col>
                    <a-col :span="16" ref="selectBox"   >
                        <a-select placeholder="请选择备件" style="width: 100%" v-model="v.partsId" :getPopupContainer="()=>this.$refs.selectBox"
                            :dropdown-match-select-width="false" :dropdown-style="{ maxWidth: '260px', wordWrap: 'break-word', whiteSpace: 'normal', overflow: 'hidden' }" class="custom-select">
                            <!-- :disabled="partsListRes.some(v => v.partsId === item.id)" -->
                            <a-select-option v-for="(item, index) in v.arr" :value="item.id" :key="index" class="custom-option">
                                {{ item.name }}--数量:({{ item.stock }})
                            </a-select-option>
                        </a-select>
                    </a-col>
                </a-row>
                <van-field v-model="v.partsUseNumber" name="partsUseNumber" label="实际数量" placeholder="请输入" type="number" />
            </div>
        </template>



        <van-button type="primary" style="width:100%;margin-top:2rem;" @click="onSubmit">提交</van-button>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
import moment from "moment";
export default {

    data() {
        return {
            info: {},
            formData: {},

            validateRule: [
                { required: true, message: '不能为空', trigger: 'onBlur' },
            ],

            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenterColumns: [],
            showTypePicker: false,
            typeColumns: ['点检', '巡检'],
            showIsFinishPicker: false,
            isFinishColumns: ['是', '否'],
            showIsUsePartsPicker: false,
            isUsePartsColumns: ['是', '否'],

            pictureList: [],

            detailList: [],
            show1: false,
            show2: false,
        };
    },
    created() {
        console.log("🚀 ~ created ~   this.$route.params:", this.$route.params)
        this.formData = { ...this.$route.params ,type:'维保',maintenanceId:this.$route.params.id}
    },
    methods: {
        // 通过编号搜索名字
        validator(value) {
            console.log(value);
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        this.$set(this.formData, "leaderName", res.data.message)
                        console.log("🚀 ~ validator ~ this.formData.leaderName:", this.formData)
                        return true;
                    } else {
                        this.$set(this.formData, "leaderName", '')
                        Toast({
                            message: "请检查组长编码是否正确",
                            position: "bottom",
                            duration: 2000
                        });
                        return false;
                    }
                });
        },
        handleDel(i) {
            this.detailList.splice(i, 1);
        },
        addParts() {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果

                                self.getParts(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入备件编号").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getParts(self.code)
                            }
                        });
                    }
                });
        },
        getParts(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/ncApp/parts/getPartsListByCode?code=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log("🚀 ~ getParts ~ res:",  self.detailList)
                        self.detailList.push({
                            arr: res.data.result
                        })
                    } else {
                        console.log(res.data);
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        handleSearch(v) {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果
                                self.getList(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入设备ID").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getList(self.code)
                            }
                        });
                    }
                });
        },

        getList(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/app/device/list?id=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.$set(self.formData, "machineName", res.data.result.records[0].deviceName)
                        this.$set(self.formData, "equipId", res.data.result.records[0].id)
                        this.$set(self.formData, "machineNo", res.data.result.records[0].deviceNo)
                        this.$set(self.formData, "bookName", res.data.result.records[0].bookName)
                        this.$set(self.formData, "workshopName", res.data.result.records[0].workshopName)
                        this.$set(self.formData, "workCenter", res.data.result.records[0].jobCenter)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        bookNameConfirm(value) {
            this.formData.book = value;
            this.formData.workshop = '';
            this.formData.jobCenter = '';
            localStorage.setItem('feedingCheckBook', this.bookName)
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value }
                })
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.workshopColumns = []
                        res.data.result.forEach(item => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.formData.workshop = value;
            this.formData.mitosome = '';
            this.showworkshopPicker = false;
            // 查找工作中心
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
                    params: { book: this.bookName, workshop: value }
                })
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach(item => {
                            this.jobCenterColumns.push(item.jobCenter);
                        });
                    } else {
                    }
                });
        },
        onDateChooseConfirm(data) {
            this.show1 = false
            this.formData.dateChoose = moment(data).format('YYYY-MM-DD')
        },
        onStartTimeConfirm(data) {
            console.log(data);
            this.show2 = false
            this.formData.startTime = moment(data).format('YYYY-MM-DD HH:mm:ss')
        },
        jobCenterConfirm(value) {
            this.formData.mitosome = value;
            this.showjobCenterPicker = false;
        },
        typeConfirm(value) {
            this.formData.type = value;
            this.showTypePicker = false;
        },
        isFinishConfirm(value) {
            this.formData.isFinish = value;
            this.showIsFinishPicker = false;
        },
        isUsePartsConfirm(value) {
            this.formData.isUseParts = value;
            this.showIsUsePartsPicker = false;
        },
        onSubmit() {
            this.$refs.form
                .validate()
                .then((res) => {

                    // if (this.detailList.length == 0) {
                    //     Toast({
                    //         message: "至少添加一个备件",
                    //         position: "bottom",
                    //         duration: 2000
                    //     });
                    //     return
                    // } else {
                    if (this.formData.isUseParts == '是') {
                        let flag = false
                        this.detailList.forEach(item => {
                            if (!/^[0-9]*[1-9][0-9]*$/.test(item.partsUseNumber)) {
                                flag = true
                            }
                            console.log(flag);
                        })
                        if (flag) {
                            Toast({
                                message: "请输入大于0的整数！",
                                position: "bottom",
                                duration: 2000
                            });
                            return
                        }
                    }

                    let param = {
                        ...this.formData,
                        type:"维保",
                        macInspectionPartsList: this.detailList.map(item => {
                            return {
                                partsId: item.partsId,
                                partsUseNumber: item.partsUseNumber
                            }
                        }),
                        initiator: localStorage.getItem('userCode'),  //发起人
                        userCode: localStorage.getItem('userCode'),  //发起人
                    }
                    delete param.spStatus
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .post(`/jeecg-boot/app/inspection/infoAdd`, param)
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                this.$router.go(-1);
                            } else {
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                    // }

                })
                .catch((err) => {
                    Toast({
                        message: "未填写完成",
                        position: "bottom",
                        duration: 2000
                    });
                });

        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios.post(`/jeecg-boot/app/improve/uploadPic`, param).then(res => {
                if (res.data.code == 200) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/app/mac/deletePic?id=${""}&picUrl=${this.pictureList[name.index].picUrl
                            }`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                            } else {
                                Indicator.close();
                                this.formData.uploader.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });

                })
                .catch(() => {
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
    }
};
</script>

<style>
/* 添加自定义样式使 select option 能够固定宽度并换行 */
.custom-option {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100% !important;
  display: block !important;
  line-height: 1.5;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 专门针对Ant Design的样式 */
.ant-select-dropdown-menu-item, 
.ant-select-dropdown-menu-item-selected {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  overflow: hidden !important;
  padding: 5px 8px !important;
  height: auto !important;
  min-height: 32px !important;
}

.ant-select-dropdown {
  width: 300px !important;
}

/* 禁用水平滚动 */
.ant-select-dropdown-menu {
  overflow-x: hidden !important;
}

/* 控制下拉选项的样式 */
.ant-select-dropdown-menu-item-content,
.ant-select-dropdown-menu-item-content span {
  white-space: normal !important;
  word-break: break-all !important;
  display: inline-block !important;
  width: 100% !important;
}

/* 修改a-select-option的样式 */
.ant-select-dropdown-menu-item div {
  white-space: normal !important;
  word-break: break-all !important;
}

/* 针对 Ant Design Vue 1.x 的特殊处理 */
.ant-select-dropdown-menu-item {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: normal !important;
}

/* 处理实际文本内容 */
.ant-select-dropdown ul li {
  white-space: normal !important;
}
</style>