<template>
  <div>
    <van-button
      type="primary"
      @click="checkTime('1', 'add')" v-if="item.lastStatus=='1'"
      style="margin:5%;width:60%;border-radius:10px;"
      >提报产量</van-button
    >

    <div style="width:100%;">

        <van-button
            v-if="item.workType == '1'"
            type="danger"
            @click="checkTime('2', 'add2')"
            style="margin:5%;width:40%;border-radius:10px;float:left;"
            >质检取样</van-button
        >
        <van-button
            v-if="item.workType == '1'"
            type="info"
            @click="scanPost"
            style="margin:5%;width:40%;border-radius:10px;float:left;"
            >扫码报工</van-button
        >

        <!-- <van-field v-model="codeValue" label="托码" placeholder="" />
        <van-button
            type="info"
            @click="scanCodeValue"
            >扫码</van-button
        > -->

        
        <div style="clear:both;"></div>

    </div>

    

    <van-button
      v-if="item.workType == '4'"
      :type="bottonType"
      @click="startWork"
      style="margin:5%;width:60%;border-radius:10px;"
      >{{ colloid }}</van-button
    >
    <div style="margin-left:5%;margin-top:5%;text-align:left;font-size:18px;" v-if="item.workType == '1'">
      当前效率:{{ xl }}
    </div>

    
    <template v-if="item.workType=='4'">
      <div style="margin:5%;" v-for="(items, index) in record" :key="index" >
        <div style="width:100%;position: relative;">
            <div style="float:left;width:50%;font-size:18px;font-weight:600;text-align:left;display: inline-block;bottom: 0;">
                {{ items.mainId }}
            </div>
            <div style="float:left;width:50%;color:#808080;font-size:14px;text-align:right;display: inline-block;bottom: 0;">
                {{ items.createTime }}
            </div>
            <div style="clear:both;"></div>
        </div>
        <div style="height:1px;width:100%;background:#5f5f5f;"></div>
        <div style="width:100%;margin-top:2%">
          <div style="float:left;width:75%;color:#808080;font-size:16px;text-align:left;">
            {{ items.type }} | {{ items.customer }}
          </div>
            
            <div style="float:left;width:25%;color:#808080;font-size:16px;text-align:right;">
                {{ items.output }} {{item.mainUnit}}
            </div>
        </div>
        <div style="width:100%;margin-top:2%">
          <div style="float:right;width:50%;color:#808080;font-size:16px;text-align:left;">
          </div>
            <div v-if='items.cancel' style="float:right;width:20%;color:red;font-size:16px;text-align:right;" @click="delRecord(items)">
                撤销
            </div>
            <div style="float:right;width:10%;color:#808080;font-size:16px;text-align:left;">
            </div>
            <div style="float:right;width:20%;color:blue;font-size:16px;text-align:right;" @click="checkTime(items, 'edit')">
                修改
            </div>
        </div>
        <div style="clear:both;"></div>
    </div>
    </template>
    <template v-else>
      <div style="margin:5%;" v-for="(items, index) in record" :key="index"  @click="checkTime(items, 'edit')">
        <div style="width:100%;position: relative;">
            <div style="float:left;width:50%;font-size:18px;font-weight:600;text-align:left;display: inline-block;bottom: 0;">
                {{ items.mainId }}
            </div>
            <div style="float:left;width:50%;color:#808080;font-size:14px;text-align:right;display: inline-block;bottom: 0;">
                {{ items.createTime }}
            </div>
            <div style="clear:both;"></div>
        </div>
        <div style="height:1px;width:100%;background:#5f5f5f;"></div>
        <div style="width:100%;margin-top:2%">
          <div style="float:left;width:75%;color:#808080;font-size:16px;text-align:left;">
            {{ items.type }} | {{ items.customer }}
          </div>
            
            <div style="float:left;width:25%;color:#808080;font-size:16px;text-align:right;">
                {{ items.output }} {{item.mainUnit}}
            </div>
        </div>
        <div style="clear:both;"></div>
    </div>
    </template>
 


    <!-- <table width="90%" style="margin:5%">
      <tr style="height:40px;font-size:18px">
        <th>提报类型</th>
        <th>提报时间</th>
        <th>提报数量({{ item.mainUnit }})</th>
      </tr>
      <tr
        v-for="(item, index) in record"
        :key="index"
        style="height:60px"
        @click="checkTime(item, 'edit')"
      >
        <td>
          {{ item.type }}
        </td>
        <td>
          {{ item.createTime }}
        </td>
        <td>
          {{ item.output }}
        </td>
      </tr>
    </table> -->

    <audit-modal ref="modalForm" @ok="modalFormOk"></audit-modal>
    <post-reocrd-modal ref="modalRForm" @ok="modalFormOk"></post-reocrd-modal>

    <div style="clear:both;"></div>
  </div>
</template>

<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from "mint-ui";
import AuditModal from "./list/AuditModal.vue";
import PostReocrdModal from "./list/PostReocrdModal.vue";
import { Dialog } from "vant";

export default {
  components: {
    AuditModal,
    PostReocrdModal
  },
  data() {
    return {
      item: [],
      record: [],
      xl: "",
      colloid: "开始制胶",
      workStatus: "2",
      bottonType: "primary",

      codeValue:''
    };
  },
  created: function() {
    let self = this;
    self.item = JSON.parse(localStorage.getItem("bgItem"));
    console.log('@',this.item);

    if (self.item == null || self.item == "" || self.item == undefined) {
      Toast({
        message: "工单类型获取失败，请重试！",
        position: "bottom",
        duration: 2000
      });
      self.$router.go(-1);
    }
    if (self.item.workType == "4") {
      self.item.unit = "KG";
    }
    self.getRecord();
    self.getCurrentPresent("0");
    self.getWorkStatus();
    self.getCurStatus();
  },
  methods: {
    getCurStatus(){
      let self = this;
      self.$axios.get("/jeecg-boot/app/gcWorkshop/getCurStatus", {
          params: { lpId: self.item.id }
        }).then(res => {
          if (res.data.code == 200) {
            if(res.data.message=='2' || res.data.message=='4'){
              self.item.lastStatus = '1';
            }
            
          }
        });
    },
    getRecord() {
      let self = this;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getOutputInfo", {
          params: { lpId: self.item.id }
        })
        .then(res => {
          if (res.data.code == 200) {
            self.record = res.data.result.map(item=>{
              return{
                ...item,
                cancel:self.item.workType==4 && item.type=='常规' && new Date().getTime() -  new Date(item.createTime).getTime()<86400000 
              }
            });
            console.log('self.record@',self.record);
          }
        });
    },
    scanCodeValue(){
      if(!this.codeValue){
        return
      }
      this.$router.push({name:"ScanResult",params:{result:this.codeValue,type:'2'}})
    },
    scanPost(){
        let self=this

        // self.$router.push({name:"ScanResult",params:{result:'T25032700139',type:'2'}})

        wx.scanQRCode({
          desc: 'scanQRCode desc',
          needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
          success: function(res) {
            // 回调
            var result = res.resultStr;//当needResult为1时返回处理结果
            self.$router.push({name:"ScanResult",params:{result:result,type:'2'}})
          },
          error: function(res) {
            if (res.errMsg.indexOf('function_not_exist') > 0) {
              alert('版本过低请升级')
            }
          }
        });

        
    },
    getWorkStatus() {
      let self = this;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getGlueStatus", {
          params: { id: self.item.id }
        })
        .then(res => {
          if (res.data.code == 200) {
            self.workStatus = res.data.message;
            if (self.workStatus == "2") {
              self.colloid = "开始制胶";
              self.bottonType = "primary";
            } else {
              self.colloid = "结束制胶";
              self.bottonType = "danger";
            }
          }
        });
    },
    startWork() {
      let self = this;
      let params = {
        lpId: self.item.id,
        creator: localStorage.getItem("userCode")
      };
      if (self.colloid == "开始制胶") {
        params.type = "1";
      } else {
        params.type = "2";
      }
      self.$axios
        .post("/jeecg-boot/app/gcWorkshop/addGlueOperation", params)
        .then(res => {
          if (res.data.code == 200) {
            self.getWorkStatus();
          }
        });
    },
    getCurrentPresent(output) {
      let self = this;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getCurrentPresent", {
          params: { lpId: self.item.id, output: output }
        })
        .then(res => {
          if (res.data.code == 200) {
            self.xl = res.data.message;
          }
        });
    },
    add2() {
      let self = this;
      this.$refs.modalForm.edit(self.item.id, self.item.mainUnit);
      this.$refs.modalForm.title = "质检取样";
      this.$refs.modalForm.disableSubmit = true;
    },
    modalFormOk() {
      let self = this;
      self.getRecord();
      self.getCurrentPresent("0");
      self.getWorkStatus();
    },
    edit(item) {
      let self = this;
      console.log(item);
      if (self.item.workType == "4") {
        // MessageBox.prompt('请输入胶体产量('+self.item.unit+')').then(({ value, action }) => {
        //     if(action=="confirm"){
        //         let num=parseFloat(value)
        //         MessageBox.confirm('请确认是否修改提报产量为'+num+self.item.unit+'?').then(action => {
        //             if(action=="confirm"){
        //                 self.editLeadOutput(num,item);
        //             }
        //         });
        //     }
        // });
        // if (this.item.department == "苏州") {
          localStorage.setItem("updateTankItem", JSON.stringify(item));
          this.$router.push({ path: "/hourTank" });
        // } else {
        //   this.$refs.modalRForm.update(item, self.item);
        //   this.$refs.modalRForm.title = "胶体产量修改";
        //   this.$refs.modalRForm.disableSubmit = true;
        // }
      } else if (self.item.workType == "1") {
        this.$refs.modalRForm.update(item, self.item);
        this.$refs.modalRForm.title = "灌包产量修改";
        this.$refs.modalRForm.disableSubmit = true;

        // MessageBox.prompt('请输入灌包产量('+self.item.unit+')').then(({ value, action }) => {
        //     if(action=="confirm"){
        //         let num=parseInt(value)
        //         MessageBox.confirm('请确认是否修改提报产量为'+num+self.item.unit+'?').then(action => {
        //             if(action=="confirm"){
        //                 self.editLeadOutput(num,item);
        //             }
        //         });
        //     }
        // });
      }
    },
    add() {
      let self = this;
      if (self.item.workType == "4") {
        // MessageBox.prompt('请输入胶体产量('+self.item.unit+')').then(({ value, action }) => {
        //     if(action=="confirm"){
        //         try{
        //             let num=parseFloat(value)
        //             MessageBox.confirm('请确认是否提报产量为'+num+self.item.unit+'?').then(action => {
        //                 if(action=="confirm"){
        //                     self.addLeadOutput(num);
        //                 }
        //             });
        //         }catch(e){
        //             Toast({
        //                 message: e,
        //                 position: 'bottom',
        //                 duration: 2000
        //             });
        //         }

        //     }
        // });
        // this.$refs.modalRForm.edit(self.item.id,self.item);
        // this.$refs.modalRForm.title="胶体产量录入";
        // this.$refs.modalRForm.disableSubmit = true;
        // if (this.item.department == "苏州" || this.item.department == "南通") {
          localStorage.setItem("updateTankItem", null);
          this.$router.push({ path: "/hourTank" });
        // } else {
        //   this.$refs.modalRForm.edit(self.item.id, self.item);
        //   this.$refs.modalRForm.title = "胶体产量录入";
        //   this.$refs.modalRForm.disableSubmit = true;
        // }
      } else if (self.item.workType == "1") {
        this.$refs.modalRForm.edit(self.item.id, self.item);
        this.$refs.modalRForm.title = "灌包产量录入";
        this.$refs.modalRForm.disableSubmit = true;

        // MessageBox.prompt('请输入灌包产量('+self.item.unit+')').then(({ value, action }) => {
        //     if(action=="confirm"){
        //         try{
        //             let num=parseInt(value)
        //             MessageBox.confirm('请确认是否提报产量为'+num+self.item.unit+'?').then(action => {
        //                 if(action=="confirm"){
        //                     self.addLeadOutput(num);
        //                 }
        //             });
        //         }catch(e){
        //             Toast({
        //                 message: e,
        //                 position: 'bottom',
        //                 duration: 2000
        //             });
        //         }

        //     }
        // });
      }
    },
    delRecord(item) {
      const that  = this
      MessageBox.prompt("请录入秘钥").then(({ value, action }) => {
        if (action == "confirm") {
          Indicator.open({
            text: "处理中，请稍后……",
            spinnerType: "fading-circle"
          });
          this.$axios
            .get("/jeecg-boot/app/gcQrCode/cancelQrCode", {
              params: { id: item.id, userCode: localStorage.getItem('userCode') ,secretKey:value}
            })
            .then(res => {
              Indicator.close();
              if (res.data.code == 200) {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
                that.getRecord()
              } else {
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
              }
            });
        }
      });
    },
    isWithin24Hours(inputTime) {
      const now = new Date().getTime();
      const inputTimestamp = new Date(inputTime).getTime();
      const oneDay = 24 * 60 * 60 * 1000;
      return inputTimestamp >= (now - oneDay);
    },
    checkTime(item, workType) {
      let self = this;

      if(workType == "edit"){
        if(item.mainId.startsWith('T')){
          Toast({
            message: "托码请扫码修改",
            position: "bottom",
            duration: 2000
          });
          return;
        }
      }

      if (workType == "add") {
        self.add();
      } else if (workType == "add2") {
        self.add2();
      } else if (workType == "edit") {
        self.edit(item);
      } 
      

      // Indicator.open({
      //   text: "正在加载中，请稍后……",
      //   spinnerType: "fading-circle"
      // });
      // self.$axios
      //   .get("/jeecg-boot/app/gcWorkshop/checkTime", {
      //     params: { lpId: self.item.id }
      //   })
      //   .then(res => {
      //     if (res.data.code == 200) {
      //       Indicator.close();
      //       //1可以新增，0可以修改
      //       let t = res.data.message;
      //       if (t == "1" && workType == "add") {
      //         self.add();
      //       } else if (t == "1" && workType == "add2") {
      //         self.add2();
      //       } else if (t == "0" && workType == "edit") {
      //         self.edit(item);
      //       } else {
      //         if (workType == "add") {
      //           Toast({
      //             message: "未过5分钟，无法新增",
      //             position: "bottom",
      //             duration: 2000
      //           });
      //         } else if (workType == "add2") {
      //           Toast({
      //             message: "未过5分钟，无法新增",
      //             position: "bottom",
      //             duration: 2000
      //           });
      //         } else {
      //           Toast({
      //             message: "5分钟已过，无法修改",
      //             position: "bottom",
      //             duration: 2000
      //           });
      //         }
      //       }
      //     } else {
      //       Indicator.close();
      //       Toast({
      //         message: res.data.message,
      //         position: "bottom",
      //         duration: 2000
      //       });
      //     }
      //   });
    },
    addLeadOutput(num) {
      let self = this;
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });
      let params = {
        lpId: self.item.id,
        output: num,
        creator: localStorage.getItem("userCode"),
        type: "1"
      };
      self.$axios
        .post("/jeecg-boot/app/gcWorkshop/addLeadOutput", params)
        .then(res => {
          if (res.data.code == 200) {
            Indicator.close();
            Toast({
              message: "提报成功！",
              position: "bottom",
              duration: 2000
            });
            self.getRecord();
          } else {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    editLeadOutput(num, item) {
      let self = this;
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });
      item.output = num;
      self.$axios
        .post("/jeecg-boot/app/gcWorkshop/changeOutput", item)
        .then(res => {
          if (res.data.code == 200) {
            Indicator.close();
            Toast({
              message: "提报成功！",
              position: "bottom",
              duration: 2000
            });
            self.getRecord();
          } else {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    }
  }
};
</script>
<style scoped></style>
