<template>
    <div class="order">
        <van-nav-bar
            title="宿舍-消息通知"
            left-text="返回"
            left-arrow
            @click-left="onClickLeft"
        />
        <van-row gutter="20">
            <van-col span="24">

                <!-- <van-cell-group>
                    <van-cell title="通知对象" reverse-color>
                        <van-radio-group v-model="info.radio" class="flex">
                            <van-radio name="1" @click="chooseRadio('1')">选择人员</van-radio>
                            <van-radio name="2" @click="chooseRadio('2')" class="margin-left30">选择宿舍</van-radio>
                        </van-radio-group>
                    </van-cell>
                </van-cell-group> -->
                <van-field
                    readonly
                    clickable
                    label="通知对象"
                    required
                >
                    <van-radio-group slot="input" v-model="info.radio">
                        <van-radio name="1" @click="chooseRadio('1')">人员</van-radio>
                        <van-radio name="2" @click="chooseRadio('2')">宿舍</van-radio>
                    </van-radio-group>
                </van-field>
                <!-- <van-radio-group v-model="info.radio">
                    <van-cell-group>
                        <van-cell title="选择人员" clickable @click="chooseRadio('1')">
                            <van-radio slot="right-icon" name="1" />
                        </van-cell>
                        <van-cell title="选择宿舍" clickable @click="chooseRadio('2')">
                            <van-radio slot="right-icon" name="2" />
                        </van-cell>
                    </van-cell-group>
                </van-radio-group> -->
            </van-col>
            <van-col span="24" v-if="info.radio == '1'">
                <van-divider content-position="left" margin="12px 0 20px">选中人员</van-divider>
                <van-row>
                    <van-col span="8" v-for="(item,index) in showPerson" :key="index">
                        <van-button plain type="primary" @click="cancelPerson(item,index)">
                            {{item.userCode}} {{item.userName}}
                            <!-- <van-icon name="cross" /> -->
                        </van-button>
                    </van-col>
                </van-row>
            </van-col>
            <van-col span="24" v-if="info.radio == '2'">
                <van-field
                    readonly
                    clickable
                    label="区域"
                    :value="area"
                    placeholder="选择区域"
                    @click="showAreaPicker = true"
                    required
                />

                <van-popup v-model="showAreaPicker" position="bottom">
                    <van-picker
                        show-toolbar
                        :columns="areaColumns"
                        @cancel="showAreaPicker = false"
                        @confirm="onAreaConfirm"
                    />
                
                </van-popup>
            </van-col>
            <van-col span="24" v-if="info.radio == '2'">
                <van-field
                    readonly
                    clickable
                    label="楼栋"
                    :value="build"
                    placeholder="选择楼栋"
                    @click="showBuildPicker = true"
                    required
                />

                <van-popup v-model="showBuildPicker" position="bottom">
                <van-picker
                    show-toolbar
                    :columns="buildColumns"
                    @cancel="showBuildPicker = false"
                    @confirm="onBuildConfirm"
                />
                </van-popup>
            </van-col>
            <van-col span="24" v-if="info.radio == '2'">
                <van-field
                    readonly
                    clickable
                    label="房间"
                    :value="room"
                    placeholder="选择房间"
                    @click="showRoomPicker = true"
                    required
                />

                <van-popup v-model="showRoomPicker" position="bottom">
                <van-picker
                    show-toolbar
                    :columns="roomColumns"
                    @cancel="showRoomPicker = false"
                    @confirm="onRoomConfirm"
                />
                </van-popup>
            </van-col>
            <van-col span="24">
                <van-field
                    readonly
                    clickable
                    label="发送类型"
                    :value="info.sendType"
                    placeholder="选择发送类型"
                    @click="showTypePicker = true"
                    required
                />

                <van-popup v-model="showTypePicker" position="bottom">
                <van-picker
                    show-toolbar
                    :columns="typeColumns"
                    @cancel="showTypePicker = false"
                    @confirm="onTypeConfirm"
                />
                </van-popup>
            </van-col>
            <van-col span="24">
                <van-cell-group>
                    <van-field
                        type="textarea"
                        v-model="info.content"
                        label="内容"
                        placeholder="请输入内容"
                        rows="3"
                        autosize
                        maxlength="50"
                        show-word-limit
                        required
                    />
                </van-cell-group>
            </van-col>
        </van-row>

        

        

        <van-button type="primary" @click="submit()"  style="margin:5px;width:40%;border-radius:10px;">提交</van-button>
        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            info:{
                oldPwd:"",
                newPwd:"",
                checkPwd:"",
                radio:'2',
                sendType:'立刻',
                areaId:'',
                buildId:'',
                roomId:'',
                content:''
            },


            showAreaPicker:false,
            showBuildPicker:false,
            showRoomPicker:false,
            areaColumns: [],
            areaResColumns: [],
            buildColumns: [],
            buildResColumns: [],
            roomColumns: [],
            roomResColumns: [],
            area:'',
            build:'',
            room:'',
            showTypePicker:false,
            typeColumns:['立刻','定时'],
            showPerson:[],
        }
    },
    created:function(){
        let that = this;
        let params = {userCodeRequest:localStorage.getItem('userCode')};
        that.$axios.get('/dormApi/dormitory/app/getAreaListAll',params).then(res=>{
            console.log('res:',res);
            if(res.data.code==200){
                for(let i = 0; i < res.data.result.length; i++) {
                    
                    that.areaColumns.push(res.data.result[i].areaName)
                    that.areaResColumns.push(res.data.result[i]);
                }
            }
        })
        console.log('route：',that.$route.params)
        let resPerson = that.$route.params.choosePerson;
        
        if(resPerson != null) {
            that.info.radio = '1';


            for(let i = 0; i < resPerson.length; i++) {
                let repeatFlag = 0;
                for(let j = 0; j < that.showPerson.length; j++) {
                    if(that.showPerson[j].paperNum == resPerson[i].paperNum) {
                        repeatFlag = 1;
                        break;
                    }
                }
                if(repeatFlag == 0) {
                    that.showPerson.push(resPerson[i]);
                }
            }
            
            
        }
    },
    methods:{
        onClickLeft() {
            this.$router.go(-1);
        },
        submit(){
            let self=this
            
            if(self.info.oldPwd==null || self.info.oldPwd==""){
                Toast({
                    message: "原密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd==null || self.info.newPwd==""){
                Toast({
                    message: "新密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd!=self.info.checkPwd){
                Toast({
                    message: "新密码和确认密码不一致！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            let params={
                userCode:localStorage.getItem('userCode'),
                oldPwd:self.info.oldPwd,
                newPwd:self.info.newPwd
            }

            self.$axios.post('/jeecg-boot/app/user/updatePwd',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })


        },
        onAreaConfirm(value) {
            console.log("🚀 ~ file: DmNotify.vue:188 ~ onAreaConfirm ~ value:", value)
            this.area = value;
            let resId = '';
            for(let i = 0; i < this.areaResColumns.length; i++) {
                if(this.areaResColumns[i].areaName == value) {
                    resId = this.areaResColumns[i].id;
                    break;
                }
            }
            if(resId != null && resId != undefined && resId != '') {
                this.buildColumns = [];
                this.buildResColumns = [];
                this.roomColumns = [];
                this.roomResColumns = [];
                this.build = '';
                this.room = '';
                this.info.buildId = '';
                this.info.roomId = '';
                this.info.areaId = resId;
                let that = this;
                let params = {
                    areaId:resId,
                    userCodeRequest:localStorage.getItem('userCode')
                }
                that.$axios.get('/dormApi/dormitory/app/getBuildingListAll',params).then(res=>{
                    console.log('res:',res);
                    if(res.data.code==200){
                        for(let i = 0; i < res.data.result.length; i++) {
                            
                            that.buildColumns.push(res.data.result[i].buildingName)
                            that.buildResColumns.push(res.data.result[i]);
                        }
                    }
                })
            }
            this.showAreaPicker = false;
        },
        onBuildConfirm(value) {
            this.build = value;

            let resId = '';
            for(let i = 0; i < this.buildResColumns.length; i++) {
                if(this.buildResColumns[i].buildingName == value) {
                    resId = this.buildResColumns[i].id;
                    break;
                }
            }
            if(resId != null && resId != undefined && resId != '') {

                this.roomColumns = [];
                this.roomResColumns = [];
                this.room = '';
                this.info.roomId = '';
                this.info.buildId = resId;
                let that = this;
                let params = {
                    buildingId:resId,
                    userCodeRequest:localStorage.getItem('userCode')
                }
                that.$axios.get('/dormApi/dormitory/app/getRoomInfoListAll',params).then(res=>{
                    console.log('res:',res);
                    if(res.data.code==200){
                        for(let i = 0; i < res.data.result.length; i++) {
                            
                            that.roomColumns.push(res.data.result[i].roomNumber)
                            that.roomResColumns.push(res.data.result[i]);
                        }
                    }
                })
            }
            this.showBuildPicker = false;
        },
        onRoomConfirm(value) {
            this.room = value;

            let resId = '';
            for(let i = 0; i < this.roomResColumns.length; i++) {
                if(this.roomResColumns[i].roomNumber == value) {
                    resId = this.roomResColumns[i].id;
                    break;
                }
            }
            if(resId != null && resId != undefined && resId != '') {

                this.roomColumns = [];
                this.roomResColumns = [];
                this.info.roomId = resId;
                console.log("🚀 ~ file: DmNotify.vue:278 ~ onRoomConfirm ~ this.info:", this.info)
            }
            this.showRoomPicker = false;
        },
        onTypeConfirm(value) {
            this.info.sendType = value;

            
            this.showTypePicker = false;
        },
        chooseRadio(type) {
            if(type == '1') {
                this.info.radio = '1';

                // 结果id置空
                this.info.areaId = '';
                this.info.buildId = '';
                this.info.roomId = '';
                // 展示名称置空
                this.area = '';
                this.build = '';
                this.room = '';
                // 选择列置空
                this.buildColumns = [];
                this.buildResColumns = [];
                this.roomColumns = [];
                this.roomResColumns = [];
                this.$router.push({path:'/dmChoosePerson'});
            } else if(type == '2') {
                this.info.radio = '2';
                
            }
        },
        cancelPerson(item,index) {
            
            // for(let i = 0; i < this.listData.length; i++) {
            //     if(this.listData[i].paperNum == item.paperNum) {
            //         this.listData[i].checked = false;
            //         break;
            //     }
            // }
            this.showPerson.splice(index,1);
        },
        show() {
            console.log('showPerson:',this.showPerson);
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>

</style>