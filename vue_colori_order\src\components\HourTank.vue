<template>
  <div>
    <van-field v-if="type=='2'" label="客户序列号" labelWidth="8rem" v-model="tankInfo.customer"  :readonly="customerReadFlag"/>

    <van-field v-if="type=='1'" readonly clickable name="picker" v-model="tankInfo.customer" label="客户序列号" labelWidth="8rem" placeholder="点击选择"
      @click="showCustomerPicker = true"/>
    <van-popup v-model="showCustomerPicker" position="bottom">
      <van-picker show-toolbar :columns="customerColumns" @confirm="onCustomerConfirm" @cancel="showCustomerPicker = false" />
    </van-popup>

    <van-field v-model="validity" type="number" readonly label="有效期(天)" labelWidth="8rem" />
    <van-field type="number" v-model="tankInfo.output" label="报工产量(KG)" labelWidth="8rem" @blur="onOutputBlur" />
    <van-field v-model="tankInfo.sampleOutput" type="number" label="取样量" labelWidth="8rem" @blur="onSampleOutputBlur"/>
    <div v-show="batchFlag" style="padding-left: 1rem;padding-top: 0.6rem;">
      <van-row style="text-align: left;">
        <van-col span="8">损耗量:</van-col>
        <van-col span="8">物料平衡:</van-col>
        <van-col span="8">产率:</van-col>
      </van-row>
      <van-row style="text-align: left;">
        <van-col span="8">{{wastage.toFixed(5)}}</van-col>
        <van-col span="8">{{(materialPercent * 100).toFixed(5)}}%</van-col>
        <van-col span="8">{{outputPercent * 100}}%</van-col>
      </van-row>
    </div>
    <van-field-Pick v-model="tankInfo.area" v-if="workshop=='制胶车间'" label="存放区域" :columns="['A','B','C','D','E','F',
    'G','H','I','J','K','L',
    'M','N','O','P','Q','R',
    'S','T','U','V','W','X',
    'Y','Z','不合格']" />
    <van-field v-model="tankInfo.opinion" label="生产的建议或意见" labelWidth="8rem"/>
    <van-field v-model="tankInfo.operator" label="操作工" labelWidth="8rem" />
    <van-field v-model="tankInfo.remark" label="备注" labelWidth="8rem" />
    <van-field v-if="batchFlag&&abnormalFlag" v-model="abnormalReason" label="异常原因" labelWidth="8rem"  />
    <van-field v-model="tankInfo.outputQT" type="number" label="每桶容量(KG)" labelWidth="8rem" />
    <van-row>
      <van-col span="18">
        <van-field v-model="scanValue" label="编码" labelWidth="8rem"/>
      </van-col>
      <van-col span="6">
        <van-button type="primary" size="small" style="margin-top:0.5rem;" @click="onScanBlur">查询</van-button>
      </van-col>
    </van-row>
    
    <van-button type="primary" style="width:80%;margin-top:20px;" @click="scan">扫码</van-button>

    
    <div v-if="updateFlag">
      <div style="width:100%;padding:2%">
        <div
          style="width:50%;float:left;text-align:left;font-size:16px;font-weight:800"
        >
          已删除列表
        </div>
      </div>
    <div v-show="tankInfo.delList.length>0" style="width:100%;padding:2%;max-height:300px;overflow:auto;">
      <div style="margin-bottom:10px;margin-top:10px;" v-for="(item, index) in tankInfo.delList" :key="index">
        <div style="width:70%;text-align:left;">
          {{ item.tankNo }}-{{ item.tankName }}<span v-if="item.area!=null && item.area!=''">-{{item.area}}</span>
        </div>
        <div style="width:30%;float:right;color:#0000ff" @click="restore(item,index)">
          恢复
        </div>
        <van-field label="装入产量(KG)" v-model="item.output" disabled />
        <div style="clear:both;"></div>
      </div>
      <div style="clear:both;"></div>
    </div>
    </div>


    <div style="width:100%;padding:2%;margin-top: 1rem;">
      <div style="width:50%;float:left;text-align:left;font-size:16px;font-weight:800">
        储罐列表
      </div>
      <div style="width:50%;float:left;text-align:right;color:#0000ff" @click="addPot">
        添加
      </div>
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%">
      <div style="margin-bottom:10px;margin-top:10px;" v-for="(item, index) in tankInfo.cgList" :key="index">
        <div style="width:70%;text-align:left;">
          {{ item.tankNo }}-{{ item.tankName }}<span v-if="item.area!=null && item.area!=''">-{{item.area}}</span>
        </div>
        <div style="width:30%;float:right;color:#0000ff" @click="delPot(item, index)">
          删除
        </div>
        <van-field label="装入产量(KG)" v-model="item.output" @change="inputChange3(item, index)" />
        <div style="clear:both;"></div>
      </div>
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%">
      <div style="width:50%;float:left;text-align:left;font-size:16px;font-weight:800">
        坦克列表
      </div>
      <!-- <div style="width:50%;float:left;text-align:right;color:#0000ff" @click="addTank">
        添加
      </div> -->
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%">
      <div style="margin-bottom:10px;margin-top:10px;" v-for="(item, index) in tankInfo.tankList" :key="index">
        <div style="width:70%;text-align:left;">
          {{ item.tankNo }}-{{ item.tankName }}<span v-if="item.area!=null && item.area!=''">-{{item.area}}</span>
        </div>
        <div style="width:30%;float:right;color:#0000ff" @click="delTK(item, index)">
          删除
        </div>
        <van-field label="装入产量(KG)" v-model="item.output" @change="inputChange(item, index)" />
        <div style="clear:both;"></div>
      </div>
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%">
      <div style="width:50%;float:left;text-align:left;font-size:16px;font-weight:800">
        折叠桶列表
      </div>
      <!-- <div style="width:50%;float:left;text-align:right;color:#0000ff" @click="addTank">
        添加
      </div> -->
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%">
      <div style="margin-bottom:10px;margin-top:10px;" v-for="(item, index) in tankInfo.zdtList" :key="index">
        <div style="width:70%;text-align:left;">
          {{ item.tankNo }}-{{ item.tankName }}<span v-if="item.area!=null && item.area!=''">-{{item.area}}</span>
        </div>
        <div style="width:30%;float:right;color:#0000ff" @click="delZdt(item, index)">
          删除
        </div>
        <van-field label="装入产量(KG)" v-model="item.output" @change="inputChange4(item, index)" />
        <div style="clear:both;"></div>
      </div>
      <div style="clear:both;"></div>
    </div>


    <div style="width:100%;padding:2%">
      <div style="width:80%;float:left;text-align:left;font-size:16px;font-weight:800">
        青桶列表&emsp;
        <!-- 数量:
        <a-input-number v-model="QTnum" :min="1" style="width:17%;" />
        容量:
        <a-input-number v-model="outputQT" :min="0" style="width:23%;" /> -->
      </div>
      <!-- <div style="width:20%;float:left;text-align:right;color:#0000ff" @click="addQt">
        添加
      </div> -->
      <div style="clear:both;"></div>
    </div>

    <div style="width:100%;padding:2%;height:300px;overflow:auto;">
      <div style="margin-bottom:10px;margin-top:10px;" v-for="(item, index) in tankInfo.qtList" :key="index">
        <div style="width:70%;text-align:left;float:left;">
          {{ item.tankNo }}-{{ item.tankName }}<span v-if="item.area!=null && item.area!=''">-{{item.area}}</span>
        </div>
        <div style="width:30%;float:right;color:#0000ff" @click="delQT(item, index)">
          删除
        </div>
        <van-field label="装入产量(KG)" v-model="item.output" @change="inputChange2(item, index)" />
        <div style="clear:both;"></div>
      </div>
      <div style="clear:both;"></div>
    </div>
    <div style="width:100%;padding:2%;">
      <p>一共添加了{{ tankInfo.qtList.length }}桶</p>
      <p>装入产量为{{ qtoutput }}KG</p>
    </div>
    <van-button type="primary" style="width:80%;margin-top:20px;" @click="submit">提交</van-button>

    <TankModal ref="modalForm" @ok="modalFormOk"></TankModal>
  </div>
</template>

<script>
import { DatetimePicker, Toast, Indicator } from "mint-ui";
import { Dialog } from "vant";
import eventBus from "../common/eventBus";
import Vue from "vue";
import TankModal from "./list/TankModal.vue";
import { add, reduce, ride, except } from '../utils/decimal.js'
export default {
  components: { TankModal },
  data() {
    return {
      type:'1',//判断客户序列号 是输入框or选择框  1:选择框 2:输入框
      showCustomerPicker:false,
      customerColumns:[],
      tankInfo: {
        customer: "",
        operator:'',
        remark: "无",
        output: '',
        //  取样量
        sampleOutput: 0,
        //  生产的建议或意见
        opinion: '',
        tankList: [],
        qtList: [],
        cgList: [],
        zdtList: [],
        delList: [],
        type: "1",
        lpId: "",
        area:"",
        creator: localStorage.getItem("userCode")
      },
      paramsItem: [],
      tankItem: [],
      workshop: "",
      avlNum: "",
      QTnum: 1,
      updateFlag: false,
      qtoutput: 0,
      outputQT: 0,
      flags: true,
      customerReadFlag: false,
      
      validity: 0,// 有效期

      reportTimes:0,//报工次数

      materialWeight:0,//物料总重量
      expectWeight:0,//计划量

      wastage:0,//损耗量
      materialPercent:0,//物料平衡
      outputPercent:0,//产率
      abnormalReason:'',//收率异常原因
      abnormalFlag:false,//是否异常

      batchModel:'',//批记录模板类型
      firstBatch:'',//批记录模板收率

      batchFlag:false,//是否绑定批记录

      zjnrList:[], //制胶步骤


      scanValue:'',
    };
  },
  created: function() {
    this.paramsItem = JSON.parse(localStorage.getItem("bgItem"));
    console.log(this.paramsItem);
    this.tankItem = JSON.parse(localStorage.getItem("updateTankItem"));
    this.workshop = this.paramsItem.workshop;
    this.tankInfo.lpId = this.paramsItem.id;
    if (this.tankItem != null && this.tankItem != "") {
      this.updateFlag = true;
      this.customerReadFlag = true;
      // this.tankInfo.customer = this.tankItem.customer;
      this.tankInfo.output = this.tankItem.output;
      this.tankInfo.tankList = this.tankItem.tankList;
      this.tankInfo.qtList = this.tankItem.qtList;
      this.tankInfo.cgList = this.tankItem.cgList;
      this.tankInfo.zdtList = this.tankItem.zdtList;
      this.tankInfo.sampleOutput = this.tankItem.sampleOutput;
      this.tankInfo.opinion = this.tankItem.opinion;
      this.tankInfo.id = this.tankItem.id;

      this.customerColumns = this.tankItem.customer.split(',')
      if (this.customerColumns.length == 1) {
        this.tankInfo.customer = this.tankItem.customer
      }
    } else {
      this.updateFlag = false;
      this.getBatchCodeByMix(this.paramsItem.id);
    }

    // this.getCustomer(this.paramsItem.id);

    this.tankInfo.qtList.forEach(v => {
      this.qtoutput += v.output * 1;
    });

    // 查询胶体有效期
    this.$axios.get(`/jeecg-boot/app/gcWorkshop/getGlueVaildTime?code=${JSON.parse(localStorage.getItem("bgItem")).code}&book=${this.paramsItem.book}`)
      .then(res => {
        if ((res.data.code = 200)) {
          console.log(res.data.message);
          this.validity = res.data.message;
        }
      });
    this.$axios
      .get("/jeecg-boot/app/mix/getBatchCodeByMixId", { params: { id: this.paramsItem.id } })
      .then(res => {
        if (res.data.code == 200) {
          // 报工次数
          this.reportTimes = res.data.result.reportTimes
          // 物料总重量
          this.materialWeight = res.data.result.materialWeight
          // 计划量
          this.expectWeight = res.data.result.expectWeight
          this.getModel(res.data.result.id);
          this.compute()
        }
      });
    // 重置已删除的坦克列表
    this.tankInfo.delList = [];
  },
  methods: {
    onScanBlur(){
      let self = this;
      this.getTQC(this.scanValue)
    },
    scan(){
      let self = this
      if (self.tankInfo.output <= 0) {
        Toast({
          message: "产量为0，无需添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      wx.scanQRCode({
          desc: "scanQRCode desc",
          needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
          success: function (res) {
            // 回调
            var result = res.resultStr; //当needResult为1时返回处理结果
            // 处理结果 result
            self.getTQC(result);
          },
          error: function (res) {
            if (res.errMsg.indexOf("function_not_exist") > 0) {
              alert("版本过低请升级");
            }
          }
        });
    },
    getTQC(id) {
      let self = this
      if (self.tankInfo.customer == null || self.tankInfo.customer == "") {
        Toast({
          message: "请输入客户批次号",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.workshop == '制胶车间' && (self.tankInfo.area == null || self.tankInfo.area == "")) {
        Toast({
          message: "请选择所属区域",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.tankInfo.output <= 0) {
        Toast({
          message: "产量为0，无需添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      let flag = false
      this.avlNum = self.tankInfo.output;
      for (var i = 0; i < self.tankInfo.cgList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.cgList[i].output);


        if (self.tankInfo.cgList[i].tankId) {
          if (id == self.tankInfo.cgList[i].tankId) {
            flag = true
          }
        } else {
          if (id == self.tankInfo.cgList[i].id) {
            flag = true
          }
        }
      }

      for (var i = 0; i < self.tankInfo.zdtList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.zdtList[i].output);

        if (self.tankInfo.zdtList[i].tankId) {
          if (id == self.tankInfo.zdtList[i].tankId) {
            flag = true
          }
        } else {
          if (id == self.tankInfo.zdtList[i].id) {
            flag = true
          }
        }
      }
      for (var i = 0; i < self.tankInfo.tankList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.tankList[i].output);

        if (self.tankInfo.tankList[i].tankId) {
          if (id == self.tankInfo.tankList[i].tankId) {
            flag = true
          }
        } else {
          if (id == self.tankInfo.tankList[i].id) {
            flag = true
          }
        }
      }
      for (var i = 0; i < self.tankInfo.qtList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.qtList[i].output);
        if (self.tankInfo.qtList[i].tankId) {
          if (id == self.tankInfo.qtList[i].tankId) {
            flag = true
          }
        } else {
          if (id == self.tankInfo.qtList[i].id) {
            flag = true
          }
        }
      }

      if (flag) {
        Toast({
          message: '请勿重复添加',
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.avlNum <= 0) {
        Toast({
          message: "报工产量不足，无法添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }

      this.$axios.get(`/jeecg-boot/app/gcWorkshop/getTankInfoById?id=${id}&workshop=${this.paramsItem.workshop}&realProductNo=${this.paramsItem.code}&realCode=${this.tankInfo.customer}`).then(res => {
        if (res.data.code == 200) {
          if (res.data.result.tankType == 2) {
            if (!!parseInt(this.tankInfo.outputQT) && parseInt(res.data.result.realVolume) >= parseInt(this.tankInfo.outputQT)) {
              if (parseInt(this.tankInfo.outputQT) >= parseInt(this.avlNum)) {
                res.data.result.output = parseInt(this.avlNum)
              } else {
                res.data.result.output = parseInt(this.tankInfo.outputQT)
              }
            } else {
              if (parseInt(res.data.result.realVolume) >= parseInt(this.avlNum)) {
                res.data.result.output = parseInt(this.avlNum)
              } else {
                res.data.result.output = parseInt(res.data.result.realVolume)
              }
            }
          } else {
            if (parseInt(res.data.result.realVolume) >= parseInt(this.avlNum)) {
              res.data.result.output = parseInt(this.avlNum)
            } else {
              res.data.result.output = parseInt(res.data.result.realVolume)
            }
          }

          this.modalFormOk([{ ...res.data.result }], res.data.result.tankType)
          // 锁定
          this.$axios.get(`/jeecg-boot/app/gcWorkshop/addTank?ids=${res.data.result.id}`)
        } else {
          Toast({
            message: res.data.message,
            position: "bottom",
            duration: 2000
          });
        }
      });
    },
    getModel(id){
      // 获取批记录模板
      this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${id}`).then(res => {
        if (res.data.code == 200) {
          this.batchFlag=true
          this.batchModel=res.data.result.model
          this.firstBatch=res.data.result.firstBatch
          this.zjnrList=res.data.result.zjnrList
        }else{
          this.batchFlag=false
        }
      });
    },
    onOutputBlur(e){
      this.tankInfo.output=this.tankInfo.output*1
      this.compute()
    },
    onSampleOutputBlur(e){
      this.compute()
    },
    compute(){
      // 损耗量
   
      this.wastage=reduce(reduce(this.materialWeight,this.tankInfo.output),this.tankInfo.sampleOutput)

      if(this.model=='云南白药'){
        //  云南白药-----> 产率
        this.outputPercent=except(add(this.tankInfo.sampleOutput,this.tankInfo.output),this.materialWeight) 

        //  云南白药-----> 物料平衡
        this.materialPercent= except(add(this.tankInfo.sampleOutput,this.tankInfo.output),this.expectWeight) 
      }else{
        // 通用、拜耳、一车间宝洁-----> 产率

        this.outputPercent=except(this.tankInfo.output,this.expectWeight) 

         //  通用、拜耳、一车间宝洁-----> 物料平衡
         this.materialPercent= except(add(add(this.tankInfo.sampleOutput,this.tankInfo.output),this.wastage),this.expectWeight) 
      
      }

      if (this.reportTimes == 0) {
        let range = this.firstBatch.split("-");
        let min = parseFloat(range[0]) / 100;
        let max = parseFloat(range[1]) / 100;

   
        if (this.outputPercent < min || this.outputPercent > max) {
          this.abnormalFlag = true
        }else{
          this.abnormalFlag = false
        }
      }
    },

    onCustomerConfirm(value) {
      this.tankInfo.customer = value;
      this.showCustomerPicker = false;
    },
    getBatchCodeByMix(id) {
      let self = this;
      self.customerReadFlag = false;
      self.$axios
        .get("/jeecg-boot/app/mix/getBatchCodeByMixId", { params: { id: id } })
        .then(res => {
          if (res.data.code == 200) {
            this.type = '1'  //获取到批次号 选择框

            // self.tankInfo.customer = res.data.result.glueBatchCode;
            self.tankInfo.mixId = res.data.result.id;
            if (
              res.data.result.glueBatchCode == null ||
              res.data.result.glueBatchCode == ""
            ) {
              self.getCustomer(self.paramsItem.id);
            } else {
              self.customerReadFlag = true;
              self.customerColumns = res.data.result.glueBatchCode.split(',')
              if(self.customerColumns.length==1){
                self.tankInfo.customer = res.data.result.glueBatchCode
              }
            }
          } else {
            this.type = '2' //没获取到批次号 输入框

            self.getCustomer(self.paramsItem.id);
          }
          // 报工次数
          self.reportTimes=res.data.result.reportTimes
          // 物料总重量
          self.materialWeight=res.data.result.materialWeight
          // 计划量
          self.expectWeight=res.data.result.expectWeight
          console.log(res.data.result.expectWeight);
          self.getModel(res.data.result.id);
        });
    },
    restore(item, index) {
      if (item.flagType == "POT") {
        this.tankInfo.cgList.push(item);
      } else if (item.flagType == "QT") {
        this.tankInfo.qtList.push(item);
      } else if (item.flagType == "TK") {
        this.tankInfo.tankList.push(item);
      } else if (item.flagType == "ZDT") {
        this.tankInfo.zdtList.push(item);
      }
      this.tankInfo.delList.splice(index, 1);
    },
    delPot(item, index) {
      if (!item.tankId) {
        this.$axios
          .get("/jeecg-boot/app/gcWorkshop/delTank", {
            params: { id: item.id }
          })
          .then(res => {
            if (res.data.success) {
              this.tankInfo.cgList.splice(index, 1);
            } else {
              Toast({
                message: "删除失败",
                position: "bottom",
                duration: 2000
              });
            }
          });
      } else if (item.tankId != "") {
        item.flagType = "POT";
        this.tankInfo.delList.push(item);
        this.tankInfo.cgList.splice(index, 1);
        // this.$axios
        //   .get("/jeecg-boot/app/gcWorkshop/delTank2", {
        //     params: { id: item.id, tankId: item.tankId }
        //   })
        //   .then(res => {
        //     if (res.data.success) {
        //       this.tankInfo.cgList.splice(index, 1);
        //     } else {
        //       Toast({
        //         message: "删除失败",
        //         position: "bottom",
        //         duration: 2000
        //       });
        //     }
        //   });
      }
    },
    delTK(item, index) {
      if (!item.tankId) {
        this.$axios
          .get("/jeecg-boot/app/gcWorkshop/delTank", {
            params: { id: item.id }
          })
          .then(res => {
            if (res.data.success) {
              this.tankInfo.tankList.splice(index, 1);
            } else {
              Toast({
                message: "删除失败",
                position: "bottom",
                duration: 2000
              });
            }
          });
      } else if (item.tankId != "") {
        item.flagType = "TK";
        this.tankInfo.delList.push(item);
        this.tankInfo.tankList.splice(index, 1);
        // this.$axios
        //   .get("/jeecg-boot/app/gcWorkshop/delTank2", {
        //     params: { id: item.id, tankId: item.tankId }
        //   })
        //   .then(res => {
        //     if (res.data.success) {
        //       this.tankInfo.tankList.splice(index, 1);
        //     } else {
        //       Toast({
        //         message: "删除失败",
        //         position: "bottom",
        //         duration: 2000
        //       });
        //     }
        //   });
      }
    },
    delZdt(item, index) {
      if (!item.tankId) {
        this.$axios
          .get("/jeecg-boot/app/gcWorkshop/delTank", {
            params: { id: item.id }
          })
          .then(res => {
            if (res.data.success) {
              this.tankInfo.zdtList.splice(index, 1);
            } else {
              Toast({
                message: "删除失败",
                position: "bottom",
                duration: 2000
              });
            }
          });
      } else if (item.tankId != "") {
        item.flagType = "ZDT";
        this.tankInfo.delList.push(item);
        this.tankInfo.zdtList.splice(index, 1);
        // this.$axios
        //   .get("/jeecg-boot/app/gcWorkshop/delTank2", {
        //     params: { id: item.id, tankId: item.tankId }
        //   })
        //   .then(res => {
        //     if (res.data.success) {
        //       this.tankInfo.tankList.splice(index, 1);
        //     } else {
        //       Toast({
        //         message: "删除失败",
        //         position: "bottom",
        //         duration: 2000
        //       });
        //     }
        //   });
      }
    },
    delQT(item, index) {
      this.qtoutput = 0;
      if (!item.tankId) {
        this.$axios
          .get("/jeecg-boot/app/gcWorkshop/delTank", {
            params: { id: item.id }
          })
          .then(res => {
            if (res.data.success) {
              this.tankInfo.qtList.splice(index, 1);
              this.tankInfo.qtList.forEach(v => {
                this.qtoutput += v.output * 1;
              });
            } else {
              Toast({
                message: "删除失败",
                position: "bottom",
                duration: 2000
              });
            }
          });
      } else if (item.tankId != "") {
        item.flagType = "QT";
        this.tankInfo.delList.push(item);
        this.tankInfo.qtList.splice(index, 1);
        // this.$axios
        //   .get("/jeecg-boot/app/gcWorkshop/delTank2", {
        //     params: { id: item.id, tankId: item.tankId }
        //   })
        //   .then(res => {
        //     if (res.data.success) {
        //       this.tankInfo.qtList.splice(index, 1);
        //       this.tankInfo.qtList.forEach(v => {
        //         this.qtoutput += v.output * 1;
        //       });
        //     } else {
        //       Toast({
        //         message: "删除失败",
        //         position: "bottom",
        //         duration: 2000
        //       });
        //     }
        //   });
      }
    },
    getCustomer(id) {
      let self = this;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getCustomerInfo", {
          params: { lpId: id }
        })
        .then(res => {
          if (res.data.success) {
            // self.tankInfo.customer = res.data.message;
            self.customerColumns = res.data.message.split(',')
            if(self.customerColumns.length==1){
              self.tankInfo.customer = res.data.message;
            }
          }
        });
    },
    modalFormOk(item, type) {
      if (type == 1) {
        let self = this;
        for (var i = 0; i < item.length; i++) {
          if (this.updateFlag) {
            item[i].tankId = item[i].id;
            item[i].area = self.tankInfo.area
            item[i].id = "";
            self.tankInfo.tankList.push(item[i]);
          } else {
            item[i].area = self.tankInfo.area
            self.tankInfo.tankList.push(item[i]);
          }
        }
        console.log(self.tankInfo.tankList);
      } else if (type == 2) {
        let self = this;
        for (var i = 0; i < item.length; i++) {
          if (this.updateFlag) {
            item[i].tankId = item[i].id;
            item[i].area = self.tankInfo.area
            item[i].id = "";
            self.tankInfo.qtList.push(item[i]);
          } else {
            item[i].area = self.tankInfo.area
            self.tankInfo.qtList.push(item[i]);
          }
        }
        console.log(self.tankInfo.qtList);
      } else if (type == 3) {
        let self = this;
        for (var i = 0; i < item.length; i++) {
          if (this.updateFlag) {
            item[i].tankId = item[i].id;
            item[i].area = self.tankInfo.area
            item[i].id = "";
            self.tankInfo.cgList.push(item[i]);
          } else {
            item[i].area = self.tankInfo.area
            self.tankInfo.cgList.push(item[i]);
          }
        }
        console.log(self.tankInfo.cgList);
      } else if (type == 4) {
        let self = this;
        for (var i = 0; i < item.length; i++) {
          if (this.updateFlag) {
            item[i].tankId = item[i].id;
            item[i].area = self.tankInfo.area
            item[i].id = "";
            self.tankInfo.zdtList.push(item[i]);
          } else {
            item[i].area = self.tankInfo.area
            self.tankInfo.zdtList.push(item[i]);
          }
        }
        console.log(self.tankInfo.zdtList);
      }
    },
    inputChange3(item, index) {
      let self = this;

      if (parseInt(item.output) > parseInt(item.realVolume)) {
        Toast({
          message: "输入容量已超出坦克容量值",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      item.area = self.tankInfo.area

      Vue.set(self.tankInfo.cgList, index, item);
    },
    inputChange(item, index) {
      let self = this;

      if (parseInt(item.output) > parseInt(item.realVolume)) {
        Toast({
          message: "输入容量已超出坦克容量值",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      item.area = self.tankInfo.area

      Vue.set(self.tankInfo.tankList, index, item);
    },
    inputChange4(item, index) {
      let self = this;

      if (parseInt(item.output) > parseInt(item.realVolume)) {
        Toast({
          message: "输入容量已超出坦克容量值",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      item.area = self.tankInfo.area

      Vue.set(self.tankInfo.zdtList, index, item);
    },
    inputChange2(item, index) {
      this.qtoutput = 0;
      let self = this;

      if (parseInt(item.output) > parseInt(item.realVolume)) {
        Toast({
          message: "输入容量已超出青桶容量值",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      item.area = self.tankInfo.area

      Vue.set(self.tankInfo.qtList, index, item);
      this.tankInfo.qtList.forEach(v => {
        this.qtoutput += v.output * 1;
      });
    },
    addQt() {
      let self = this;
      if (self.tankInfo.customer == null || self.tankInfo.customer == "") {
        Toast({
          message: "请输入客户批次号",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.tankInfo.output <= 0) {
        Toast({
          message: "产量为0，无需添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }

      self.avlNum = self.tankInfo.output;
      for (var i = 0; i < self.tankInfo.tankList.length; i++) {
        self.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.tankList[i].output);
      }
      for (var i = 0; i < self.tankInfo.qtList.length; i++) {
        self.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.qtList[i].output);
      }

      if (self.avlNum <= 0) {
        Toast({
          message: "报工产量不足，无法添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }

      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getTankInfo", {
          params: {
            customerId: self.tankInfo.customer,
            code: self.tankInfo.code,
            workshop: self.workshop,
            type: "2",
            count: this.QTnum
          }
        })
        .then(res => {
          if (res.data.code == 200) {
            var qtInfo = res.data.result;

            //锁定青桶
            let ids = "";
            qtInfo.forEach(item => {
              ids += item.id + ",";
            });
            this.$axios
              .get(`/jeecg-boot/app/gcWorkshop/addTank?ids=${ids}`)
              .then(res => {});

            qtInfo.forEach(item => {
              if (parseInt(item.realVolume) > parseInt(self.avlNum)) {
                // 带入的值与输入框的值一样
                if (parseInt(self.outputQT) > parseInt(item.realVolume)) {
                  item.output = parseInt(item.realVolume);
                } else {
                  if (!self.outputQT) {
                    item.output = 0;
                  } else {
                    item.output = parseInt(self.outputQT);
                  }
                }
                item.area = self.tankInfo.area
              } else {
                if (!self.outputQT) {
                  item.output = 0;
                } else {
                  // item.output = parseInt(self.outputQT)
                  // 带入的值与输入框的值一样
                  if (self.outputQT > item.realVolume * 1) {
                    item.output = parseInt(item.realVolume);
                  } else {
                    if (!self.outputQT) {
                      item.output = 0;
                    } else {
                      item.output = self.outputQT;
                    }
                  }
                }
                item.area = self.tankInfo.area
              }

              if (this.updateFlag) {
                item.tankId = item.id;
                item.id = "";
              }
            });

            if (qtInfo == null || qtInfo == "") {
              Toast({
                message: "青桶数量不足，无法添加",
                position: "bottom",
                duration: 2000
              });
              return;
            } else {
              self.tankInfo.qtList.push(...qtInfo);
              this.qtoutput = 0;
              self.tankInfo.qtList.forEach(item => {
                this.qtoutput += item.output * 1;
              });
            }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    submit() {
      const self = this
      const goOn = () => {
        if (this.batchFlag) {
          let checkflag = false
          this.zjnrList.forEach(item => {
            if (item.status != 2) {
              checkflag = true
            }
          })
          if (checkflag) {
            Toast({
              message: "制胶步骤未完成复核,请检查",
              position: "bottom",
              duration: 2000
            });
            return;
          }
        }
        if (!self.tankInfo.operator) {
          Toast({
            message: "请输入操作工",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (self.validity <= 0) {
          Toast({
            message: "胶体有效期不能为0！",
            position: "bottom",
            duration: 2000
          });
          return;
        }

        if (!/^[0-9]*[1-9][0-9]*$/.test(self.validity)) {
          Toast({
            message: "请输入大于0的整数！",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        let param = {
          code: JSON.parse(localStorage.getItem("bgItem")).code,
          name: JSON.parse(localStorage.getItem("bgItem")).name,
          creator: localStorage.getItem("userCode"),
          vaildTime: self.validity
        };
        this.$axios
          .post("/jeecg-boot/app/gcWorkshop/editGlueVaildTime", param)
          .then(res => {
            if (res.data.code == 200) {
            } else {
              this.validity = "";
              Toast({
                message: "操作失败,请重新修改有效期后提交",
                position: "bottom",
                duration: 2000
              });
              return;
            }
          });

        if (
          self.tankInfo.tankList.length <= 0 &&
          self.tankInfo.qtList.length <= 0 &&
          self.tankInfo.cgList.length <= 0&&
          self.tankInfo.zdtList.length <= 0
        ) {
          Toast({
            message: "坦克/青桶/储罐列表不能为空！",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if(this.workshop=='制胶车间' && (this.tankInfo.area==null || this.tankInfo.area=='')){
          Toast({
            message: "区域不得为空！",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (self.tankInfo.output == 0) {
          Toast({
            message: "报工产量不能为0！",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (self.workshop=='香皂车间' && self.tankInfo.output > 2700) {
          Toast({
            message: "香皂车间单次报工超限，请降低报工数量！",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        let urls = "/jeecg-boot/app/gcWorkshop/addLeadOutput";
        if (this.updateFlag) {
          urls = "/jeecg-boot/app/gcWorkshop/changeOutput";
        }
        let all = 0;
        let flag = true;

        self.tankInfo.cgList.forEach(item => {
          all += item.output * 1;
          if (item.output * 1 > item.realVolume * 1) {
            flag = false;
            Toast({
              message: "超出容量",
              position: "bottom",
              duration: 2000
            });
          }
          if(this.workshop=='制胶车间' && (item.area==null || item.area=='')){
            flag = false;
            Toast({
              message: item.tankNo+"区域为空！",
              position: "bottom",
              duration: 2000
            });
          }
          if (item.output * 1 < 0) {
            flag = false;
            Toast({
              message: "容量不能为负数",
              position: "bottom",
              duration: 2000
            });
          }
        });

        self.tankInfo.tankList.forEach(item => {
          all += item.output * 1;
          if (item.output * 1 > item.realVolume * 1) {
            flag = false;
            Toast({
              message: "超出容量",
              position: "bottom",
              duration: 2000
            });
          }
          if(this.workshop=='制胶车间' && (item.area==null || item.area=='')){
            flag = false;
            Toast({
              message: item.tankNo+"区域为空！",
              position: "bottom",
              duration: 2000
            });
          }
          if (item.output * 1 < 0) {
            flag = false;
            Toast({
              message: "容量不能为负数",
              position: "bottom",
              duration: 2000
            });
          }
        });
        self.tankInfo.qtList.forEach(item => {
          all += item.output * 1;
          if (item.output * 1 > item.realVolume * 1) {
            flag = false;
            Toast({
              message: "超出容量",
              position: "bottom",
              duration: 2000
            });
          }
          if(this.workshop=='制胶车间' && (item.area==null || item.area=='')){
            flag = false;
            Toast({
              message: item.tankNo+"区域为空！",
              position: "bottom",
              duration: 2000
            });
          }
          if (item.output * 1 < 0) {
            flag = false;
            Toast({
              message: "容量不能为负数",
              position: "bottom",
              duration: 2000
            });
          }
        });
        self.tankInfo.zdtList.forEach(item => {
          all += item.output * 1;
          if (item.output * 1 > item.realVolume * 1) {
            flag = false;
            Toast({
              message: "超出容量",
              position: "bottom",
              duration: 2000
            });
          }
          if(this.workshop=='制胶车间' && (item.area==null || item.area=='')){
            flag = false;
            Toast({
              message: item.tankNo+"区域为空！",
              position: "bottom",
              duration: 2000
            });
          }
          if (item.output * 1 < 0) {
            flag = false;
            Toast({
              message: "容量不能为负数",
              position: "bottom",
              duration: 2000
            });
          }
        });

        if (all != self.tankInfo.output) {
          Toast({
            message: "产量与容量不符",
            position: "bottom",
            duration: 2000
          });
        } else {
          if (flag) {
            if (self.tankInfo.customer.indexOf(" ") >= 0) {
              Dialog.confirm({
                title: "提示",
                message: "客户序列号中含有空格,是否继续提交?"
              })
                .then(() => {
                  if (self.tankInfo.customer.length < 6) {
                    Toast({
                      message: "批次号长度不能小于六位",
                      duration: 1500
                    });
                    return;
                  } else {
                    if (this.reportTimes == 0 && this.abnormalFlag && this.abnormalReason == '') {
                      Toast({
                        message: "请填写异常原因",
                        position: "bottom",
                        duration: 2000
                      });
                      return
                    }
                    if (this.batchFlag) {
                      self.tankInfo.wastage = this.wastage
                      self.tankInfo.materialPercent = this.materialPercent
                      self.tankInfo.outputPercent = this.outputPercent
                      self.tankInfo.abnormalReason = this.abnormalReason
                    }
                    self.tankInfo.output = self.tankInfo.output * 1
                    Indicator.open({
                      text: "处理中，请稍后……",
                      spinnerType: "fading-circle"
                    });
                    self.$axios.post(urls, self.tankInfo).then(res => {
                      if (res.data.code == 200) {
                        Toast({
                          message: res.data.message,
                          position: "bottom",
                          duration: 2000
                        });
                        Indicator.close();
                        this.updateFlag = false;
                        this.$router.go(-1);
                      } else {
                        Toast({
                          message: res.data.message,
                          position: "bottom",
                          duration: 2000
                        });
                        Indicator.close();
                      }
                    });
                  }
                })
                .catch(() => {
                  Toast({
                    message: "您点击了取消",
                    position: "bottom",
                    duration: 1500
                  });
                  return;
                });
            } else {
              if (self.tankInfo.customer.length < 6) {
                Toast({
                  message: "批次号长度不能小于六位",
                  duration: 1500
                });
                return;
              } else {
                if (this.reportTimes == 0 && this.abnormalFlag && this.abnormalReason == '') {
                  Toast({
                    message: "请填写异常原因",
                    position: "bottom",
                    duration: 2000
                  });
                  return
                }
                if (this.batchFlag) {
                  self.tankInfo.wastage = this.wastage
                  self.tankInfo.materialPercent = this.materialPercent
                  self.tankInfo.outputPercent = this.outputPercent
                  self.tankInfo.abnormalReason = this.abnormalReason
                }
                Indicator.open({
                  text: "处理中，请稍后……",
                  spinnerType: "fading-circle"
                });
                self.$axios.post(urls, self.tankInfo).then(res => {
                  if (res.data.code == 200) {
                    Toast({
                      message: res.data.message,
                      position: "bottom",
                      duration: 2000
                    });
                    Indicator.close();
                    this.updateFlag = false;
                    this.$router.go(-1);
                  } else {
                    Toast({
                      message: res.data.message,
                      position: "bottom",
                      duration: 2000
                    });
                    Indicator.close();
                  }
                });
              }
            }
          } else {
            Toast({
              message: "超出容量",
              position: "bottom",
              duration: 2000
            });
          }
        }
      }

      if (except(Math.abs(reduce(this.expectWeight * 1, this.tankInfo.output * 1)), this.expectWeight * 1) > 0.1) {
        Dialog.confirm({
          title: "提示",
          message: "报工数据与分锅重量差异超出10%,确定要继续吗?"
        }).then(() => {
          goOn()
        }).catch(() => {
          Toast({
            message: "您点击了取消",
            position: "bottom",
            duration: 1500
          });
          return;
        })
      }else{
        goOn()
      }
    },
    addTank() {
      let self = this;
      if (self.tankInfo.customer == null || self.tankInfo.customer == "") {
        Toast({
          message: "请输入客户批次号",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.tankInfo.output <= 0) {
        Toast({
          message: "产量为0，无需选择坦克",
          position: "bottom",
          duration: 2000
        });
        return;
      }

      this.avlNum = self.tankInfo.output;
      for (var i = 0; i < self.tankInfo.cgList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.cgList[i].output);
      }
      for (var i = 0; i < self.tankInfo.tankList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.tankList[i].output);
      }
      for (var i = 0; i < self.tankInfo.qtList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.qtList[i].output);
      }

      if (self.avlNum <= 0) {
        Toast({
          message: "报工产量不足，无法添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      let code = JSON.parse(localStorage.getItem("bgItem")).code;
      self.$refs.modalForm.edit(
        self.tankInfo.customer,
        self.workshop,
        this.avlNum,
        1,
        self.tankInfo.tankList,
        code
      );
      self.$refs.modalForm.title = "坦克选择";
      self.$refs.modalForm.disableSubmit = true;
    },
    addPot() {
      let self = this;
      if (self.tankInfo.customer == null || self.tankInfo.customer == "") {
        Toast({
          message: "请输入客户批次号",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      if (self.tankInfo.output <= 0) {
        Toast({
          message: "产量为0，无需选择储罐",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      this.avlNum = self.tankInfo.output;
      for (var i = 0; i < self.tankInfo.cgList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.cgList[i].output);
      }
      for (var i = 0; i < self.tankInfo.tankList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.tankList[i].output);
      }
      for (var i = 0; i < self.tankInfo.qtList.length; i++) {
        this.avlNum =
          parseInt(self.avlNum) - parseInt(self.tankInfo.qtList[i].output);
      }

      if (self.avlNum <= 0) {
        Toast({
          message: "报工产量不足，无法添加",
          position: "bottom",
          duration: 2000
        });
        return;
      }
      let code = JSON.parse(localStorage.getItem("bgItem")).code;
      self.$refs.modalForm.edit(
        self.tankInfo.customer,
        self.workshop,
        this.avlNum,
        3,
        self.tankInfo.cgList,
        code
      );
      self.$refs.modalForm.title = "储罐选择";
      self.$refs.modalForm.disableSubmit = true;
    }
  }
};
</script>

<style scoped>
/deep/ .van-field__control {
  text-align: center;
}
</style>
