<template>
  <div style="text-align:left;background-color:#fff;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="已绑定配料清单" left-arrow @click-left="onClickLeft" />
    </van-sticky>

    <div v-for="(item, index) in dataArr1" :key="index" 
      style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="24">
            <span style="font-size:18px;font-weight: 700;color: #000;">{{
              item.id
              }}</span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 账套：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.book }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 车间：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.workshop }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 所属MO单：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.moId }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 批次号:</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueBatchCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体编码：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueCode }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 胶体名称：</van-col>
            <van-col span="18">
              <span style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                {{ item.glueName }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 工作中心：</van-col>
            <van-col span="18">
              <span style="color:black;">
                {{ item.jobCenter }}
              </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 重量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.expectWeight }}KG </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 原料数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.materialNumber }}种 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 标签数量：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.signNumber }}张 </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24" style="color:gary">
          <van-row>
            <van-col span="6"> 备注：</van-col>
            <van-col span="18">
              <span style="color:black;"> {{ item.remark }} </span>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row style="margin-top: 15px;">
        <van-col span="24" style="color:gary">
          <van-row :gutter="10">
            <van-col span="6" style="text-align: center;">
              <van-button type="danger" style="height: 30px;width: 80px;" @click="cancelBind(item)">解绑</van-button>
            </van-col>
            <van-col span="6" style="text-align: center;">
              <van-button type="primary" style="height: 30px;width: 80px;" @click="getMixDetail(item)">详情</van-button>
            </van-col>
            <van-col span="6" style="text-align: center;">
              <van-button type="info" style="height: 30px;width: 9rem;" @click="checkBeforeProduction(item)">制胶前检查复核</van-button>
              </van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { MessageBox } from 'mint-ui';
  export default {
    data() {
      return {
        lpId:"",
        dataArr1:[],
      };
    },
    created() {
      this.itemParams=JSON.parse(localStorage.getItem("params"))
      this.lpId = this.itemParams.id;
      this.getBindMixInfo();
    },
    methods: {
      checkBeforeProduction(item) {
        if (item.checkStatus < 4) {
          Toast({
            message: '制胶前检查未填写',
            position: 'bottom',
            duration: 2000
          });
          return
        }
        if (item.checkStatus == 99) {
          Toast({
            message: '未绑定批记录',
            position: 'bottom',
            duration: 2000
          });
          return
        }
        this.$router.push({
          name: "checkBeforeProduction",
          params: item
        });
      },
      onClickLeft(){
        let self = this;
        self.$router.go(-1);
      },
      getBindMixInfo(){
        let self = this;
        self.$axios.get('/jeecg-boot/app/mix/getBindMixInfo',{params:{lpId:self.lpId}}).then(res=>{
          if(res.data.success){
            this.dataArr1 = res.data.result
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        })
      },
      getMixDetail(item){
        this.$router.push({
          name: "FeedingCheckDetail3",
          params: item
        });
      },
      cancelBind(item){
        let self = this;
        MessageBox.confirm('是否取消绑定此配料单').then(action => {
          if(action=="confirm"){
            let params = {
              lpId:self.lpId,
              mid:item.id
            }
            self.$axios.post('/jeecg-boot/app/mix/cancelBind',params).then(res=>{
              if(res.data.success){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                self.$router.go(-1);
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            })
          }
        })
    }
      
    }}
</script>

<style scoped></style>