<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div style="text-align:left;font-size:18px;font-weight:800;padding:10px;">{{optName}}&nbsp;&nbsp;对{{staffName}}&nbsp;的评价如下：</div>

        <div style="display:flex;" v-if="vdef1>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">可靠度</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef1 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="vdef2>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">亲和力</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef2 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="vdef3>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">自我程度</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef3 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="vdef4>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">担当</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef4 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="vdef5>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">工作投入程度</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef5 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="vdef6>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">成就动机</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef6 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>



        <div style="display:flex;" v-if="vdef7>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">稳定性</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= vdef7 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="manage1>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">制定目标</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= manage1 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="manage2>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">组织工作</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= manage2 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="manage3>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">沟通交流</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= manage3 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="manage4>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">绩效评估</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= manage4 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="display:flex;" v-if="manage5>0">
            <div style="align-self:center;font-size:18px;font-weight:800;width:45%;text-align:left;margin-left:5px;">人员培养</div>
            <div class="star-rating" style="text-align:right;">
                <i v-for="n in 5"
                    :key="n"
                    :class="['star', { 'filled': n <= manage5 }]">
                    </i>
            </div>
            <div style="clear:both;"></div>
        </div>



        <div v-if="note1!=null && note1!=''" style="text-align:left;font-weight:800;margin-top:50px;margin-bottom:10px;font-size:18px;color:blue;">人员品性评估</div>
            <div>
                <van-field label="电子游戏" :value="note1" v-if="note1!=null && note1!=''" readonly/>
                <van-field label="吸烟" :value="note2" v-if="note2!=null && note2!=''" readonly/>
                <van-field label="向同事借钱" :value="note7" v-if="note7!=null && note7!=''" readonly/>
                <van-field label="赌博" :value="note3" v-if="note3!=null && note3!=''" readonly/>
                <van-field label="打架斗殴" :value="note4" v-if="note4!=null && note4!=''" readonly/>
                <van-field label="喝酒" :value="note5" v-if="note5!=null && note5!=''" readonly/>
                <van-field label="言语粗暴" :value="note6" v-if="note6!=null && note6!=''" readonly/>
            </div>


        <div v-if="struggle!=null && struggle!=''" style="text-align:left;font-weight:800;margin-top:50px;margin-bottom:10px;font-size:18px;color:magenta;">奋斗意愿评估</div>
        
        <div>
                <van-field label="奋斗意愿" :value="struggle" v-if="struggle!=null && struggle!=''" readonly/>
                <van-field label="具体事例" :value="project" v-if="project!=null && project!=''" readonly/>
                <van-field label="调动意愿" :value="transfer" v-if="transfer!=null && transfer!=''" readonly/>
                <van-field label="部门" :value="deptName" v-if="deptName!=null && deptName!=''" readonly/>
                <van-field label="岗位" :value="positionName" v-if="positionName!=null && positionName!=''" readonly/>
            </div>
        
        
            <van-button type="danger" v-if="btnShow" @click="reback()" style="margin-left:5%;width:40%;margin-top:60px;margin-bottom:30px;float:left;">驳回</van-button>
            <van-button type="primary" :loading="loading" v-if="btnShow" @click="check()" style="margin-left:10%;width:40%;margin-top:60px;margin-bottom:30px;float:left;">同意</van-button>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Toast,MessageBox } from 'mint-ui';
import { Calendar } from 'vant';

export default {
    data(){
        return{
            loading:false,
            btnShow:true,
            optName:"",
            staffName:"",
            id:"",
            person:"",
            value:"",
            vdef1:"",
            vdef2:"",
            vdef3:"",
            vdef4:"",
            vdef5:"",
            vdef6:"",
            vdef7:"",
            vdef8:"",
            vdef9:"",
            note1:"",
            note2:"",
            note3:"",
            note4:"",
            note5:"",
            note6:"",
            note7:"",
            manage1:"",
            manage2:"",
            manage3:"",
            manage4:"",
            manage5:"",
            struggle:"",
            project:"",
            userCode:"",
            transfer:"",
            deptName:"",
            positionName:""
        }
    },
    components:{
        DatetimePicker
    },
    async created() {
        this.person = this.$route.query.person
        this.userCode = this.$route.query.userCode

        console.log(this.person)

        // if(this.userCode=='HI0901050150' || this.userCode=='HI2002250004'){
        //     this.btnShow=true;
        // }

        this.value = this.person.split(",")
        
        for(var i=0;i<this.value.length;i++){
            if(this.value[i].indexOf("id")!=-1){
                this.id = this.value[i].replace('id=','').replace('NcStaffEvaluationPerson(','').replace(' ','')
            }else if(this.value[i].indexOf("optName")!=-1){
                this.optName = this.value[i].replace('optName=','').replace(')','').replace(' ','')
            }else if(this.value[i].indexOf("staffName")!=-1){
                this.staffName = this.value[i].replace('staffName=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef1")!=-1){
                this.vdef1 = this.value[i].replace('vdef1=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef2")!=-1){
                this.vdef2 = this.value[i].replace('vdef2=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef3")!=-1){
                this.vdef3 = this.value[i].replace('vdef3=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef4")!=-1){
                this.vdef4 = this.value[i].replace('vdef4=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef5")!=-1){
                this.vdef5 = this.value[i].replace('vdef5=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef6")!=-1){
                this.vdef6 = this.value[i].replace('vdef6=','').replace(' ','')
            }else if(this.value[i].indexOf("vdef7")!=-1){
                this.vdef7 = this.value[i].replace('vdef7=','').replace(' ','')
            }else if(this.value[i].indexOf("note1")!=-1){
                this.note1 = this.value[i].replace('note1=','').replace(' ','')
            }else if(this.value[i].indexOf("note2")!=-1){
                this.note2 = this.value[i].replace('note2=','').replace(' ','')
            }else if(this.value[i].indexOf("note3")!=-1){
                this.note3 = this.value[i].replace('note3=','').replace(' ','')
            }else if(this.value[i].indexOf("note4")!=-1){
                this.note4 = this.value[i].replace('note4=','').replace(' ','')
            }else if(this.value[i].indexOf("note5")!=-1){
                this.note5 = this.value[i].replace('note5=','').replace(' ','')
            }else if(this.value[i].indexOf("note6")!=-1){
                this.note6 = this.value[i].replace('note6=','').replace(' ','')
            }else if(this.value[i].indexOf("note7")!=-1){
                this.note7 = this.value[i].replace('note7=','').replace(' ','')
            }else if(this.value[i].indexOf("manage1")!=-1){
                this.manage1 = this.value[i].replace('manage1=','').replace(' ','')
            }else if(this.value[i].indexOf("manage2")!=-1){
                this.manage2 = this.value[i].replace('manage2=','').replace(' ','')
            }else if(this.value[i].indexOf("manage3")!=-1){
                this.manage3 = this.value[i].replace('manage3=','').replace(' ','')
            }else if(this.value[i].indexOf("manage4")!=-1){
                this.manage4 = this.value[i].replace('manage4=','').replace(' ','')
            }else if(this.value[i].indexOf("manage5")!=-1){
                this.manage5 = this.value[i].replace('manage5=','').replace(' ','')
            }else if(this.value[i].indexOf("struggle")!=-1){
                this.struggle = this.value[i].replace('struggle=','').replace(' ','')
            }else if(this.value[i].indexOf("project")!=-1){
                this.project = this.value[i].replace('project=','').replace(' ','')
            }else if(this.value[i].indexOf("transfer")!=-1){
                this.transfer = this.value[i].replace('transfer=','').replace(' ','')
            }else if(this.value[i].indexOf("deptName")!=-1){
                this.deptName = this.value[i].replace('deptName=','').replace(' ','')
            }else if(this.value[i].indexOf("positionName")!=-1){
                this.positionName = this.value[i].replace('positionName=','').replace(' ','')
            }
        }

        this.getEvaluationStatus(this.id)
    },
    methods: {
        getEvaluationStatus(id){
            let self=this;
            self.manageShow = false
            self.$axios.get('/jeecg-boot/app/user/getEvaluationStatus',{params:{id:id}}).then(res=>{
                if(res.data.code==200){
                    let personInfo = res.data.result
                    
                    if(personInfo.status=='5'){
                        self.$router.push({path:'/evalSuccess2',query:{status:'2'}});
                    }else if(personInfo.status=='2'){
                        self.$router.push({path:'/evalSuccess2',query:{status:'3'}});
                    }
                    
                }
            });
        },
        checkInfo(){
            let self = this
            let params = {
                id:this.id,
                vdef1:this.vdef1,
                vdef2:this.vdef2,
                vdef3:this.vdef3,
                vdef4:this.vdef4,
                vdef5:this.vdef5,
                vdef6:this.vdef6,
                vdef7:this.vdef7,
                vdef8:this.vdef8,
                vdef9:this.vdef9,
                note1:this.note1,
                note2:this.note2,
                note3:this.note3,
                note4:this.note4,
                note5:this.note5,
                note6:this.note6,
                note7:this.note7,
                manage1:this.manage1,
                manage2:this.manage2,
                manage3:this.manage3,
                manage4:this.manage4,
                manage5:this.manage5,
                struggle:this.struggle,
                transfer:this.transfer,
                deptName:this.deptName,
                positionName:this.positionName
            }
            self.loading=true;
            self.$axios.post('/jeecg-boot/app/user/submitEvaluationInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    self.$router.push({path:'/evalSuccess2',query:{status:'1'}});
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                }
            })
        },
        check(){
            MessageBox.confirm('确定通过本次评价吗?').then(action => {
                if(action == 'confirm') {
                    this.checkInfo()
                }
            });
        },
        rebackInfo(){
            let self = this;
            let params = {
                id:this.id,
                vdef1:this.vdef1,
                vdef2:this.vdef2,
                vdef3:this.vdef3,
                vdef4:this.vdef4,
                vdef5:this.vdef5,
                vdef6:this.vdef6,
                vdef7:this.vdef7,
                vdef8:this.vdef8,
                vdef9:this.vdef9,
                note1:this.note1,
                note2:this.note2,
                note3:this.note3,
                note4:this.note4,
                note5:this.note5,
                note6:this.note6,
                note7:this.note7,
                manage1:this.manage1,
                manage2:this.manage2,
                manage3:this.manage3,
                manage4:this.manage4,
                manage5:this.manage5,
                userCode:self.userCode
            }
            self.loading=true;
            self.$axios.post('/jeecg-boot/app/user/rebackEvaluationInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                    self.$router.push({path:'/evalSuccess2',query:{status:'2'}});
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.loading=false;
                }
            });
        },
        reback(){
            MessageBox.confirm('确定驳回本次评价吗?').then(action => {
                if(action == 'confirm') {
                    this.rebackInfo()
                }
            });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.star-rating {
  display: flex;
  margin-right:20px;
  justify-content: right;
  text-align: right;
  width: 45%;
}
.star {
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s;
}
.star::before {
  content: '☆';
}
.star.filled::before,
.star:hover::before {
  content: '★';
  color: gold;
}
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
/deep/ .van-field__label{
    width: 16em;
}
/deep/ .van-field__control{
    text-align: center;
    color: #00f;
}
/deep/ .van-cell{
    font-size: 1rem;
}
</style>