<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <!-- 主表单区域 -->
        <a-row>
          <a-col :span="24">
            <a-form-item
              :labelCol="labelCol"
              label="客户序列号"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入客户序列号" v-decorator="['customer',{rules:[{required:true,message:'请输入客户序列号'}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="oldReportMainName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入报工产量" disabled v-decorator="['output',{}]" ></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="oldReportName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入报工产量" disabled v-decorator="['zoutput',{}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="newReportMainName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入新报工产量" v-decorator="['newcount',{rules:[{required:true,message:'请输入新产量'},{validator: this.validateNum}]}]" ></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :label="newReportName"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入新报工产量" v-decorator="['znewcount',{rules:[{required:true,message:'请输入新产量'},{validator: this.validateNum1}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="更改原因"
              :wrapperCol="wrapperCol">
              <a-input placeholder="请输入更改原因" v-decorator="['upReason',{rules:[{required:true,message:'请输入更改原因'}]}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="换算率"
              :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['exchange',{}]"></a-input>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              label="规格"
              :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['spec',{}]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import pick from 'lodash.pick'
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "PostRecordModalUp",
    data() {
      return {
        title: "操作",
        visible: false,
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        moIds:[],
        expandedRowKeys: [],
        id: '',
        unit:'',
        type:'',
        isOk:false,
        flag:false,
        saveFlag: true,
        oldReportName:'',
        newReportName:'',
        exchange:'',
        oldReportMainName:'',
        newReportMainName:'',
        updateItem:{},
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },

      edit(id,item) {  
        this.visible = true;
        this.updateItem={}
        this.id=id;
        this.unit=item.unit

        console.log("unit:"+this.unit)

        this.oldReportMainName="原报工产量("+item.mainUnit+")"
        this.oldReportName="原报工产量("+item.unit+")"
        this.newReportMainName="原报工产量("+item.mainUnit+")"
        this.newReportName="原报工产量("+item.unit+")"
        this.orderMainModel = Object.assign({}, item);
        this.exchange=item.exchange
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.orderMainModel, 'exchange','spec'))
        });
        this.getCustomer(this.id)
      },
      update(item,items){
        let self=this;

        this.visible = true;
        this.oldReportMainName="原报工产量("+items.mainUnit+")"
        this.oldReportName="原报工产量("+items.unit+")"
        this.newReportMainName="现报工产量("+items.mainUnit+")"
        this.newReportName="现报工产量("+items.unit+")"
        this.updateItem=item;
        this.exchange=items.exchange
        this.flag=true
        this.unit=items.mainUnit
        this.orderMainModel = Object.assign({}, items);
        this.exchange=items.exchange
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.orderMainModel,'exchange','spec'))
        });
        this.getCustomer(item.lpId)
      },
      close() {
        this.$emit('close');
        this.item=[]
        this.exchange="";
        this.unit="";
        this.visible = false;
        this.updateItem={}
        this.flag=false;
        this.isOk=false;
      },
      getCustomer(id){
        let self=this
        self.$axios.get('/jeecg-boot/app/gcWorkshop/getCustomerInfo',{params:{lpId:id}}).then(res=>{
          if(res.data.success){
            self.form.setFieldsValue({customer: res.data.message ? res.data.message : null})
            if(this.updateItem.output != null && this.updateItem.output !=undefined && this.updateItem.output !=''){
              self.form.setFieldsValue({output: this.updateItem.output ? this.updateItem.output : null})
              self.form.setFieldsValue({newcount: this.updateItem.output ? this.updateItem.output : null})
              var zoutput=this.updateItem.output/this.exchange
              try{
                zoutput=parseFloat(zoutput).toFixed(2);
              }catch(e){
                zoutput='0'
              }
              self.form.setFieldsValue({zoutput: zoutput ? zoutput : null})
              self.form.setFieldsValue({znewcount: zoutput ? zoutput : null})
            }
            
          }
        })
      },
      handleOk() {
        let self=this
        if(!self.saveFlag){
          return;
        }
        self.saveFlag=false

        var znewcount = self.form.getFieldValue("znewcount")
        znewcount=parseFloat(znewcount).toFixed(2);
        self.form.setFieldsValue({znewcount: znewcount ? znewcount : null})

        this.form.validateFields((err, values) => {
          if (!err) {
            self.confirmLoading = true;

            let params={
              id:this.updateItem.id,
              lpId:this.updateItem.lpId,
              count:this.updateItem.output,
              upCount:self.form.getFieldValue("newcount"),
              upReason:self.form.getFieldValue("upReason"),
              userCode:localStorage.getItem('userCode')
            }

            console.log("---------------"+params)

            self.$axios.post('/jeecg-boot/app/gcApproval/getExOutput',params).then(res=>{
              if(res.data.code=200){
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
                this.visible=false;
              }else{
                Toast({
                  message: res.data.message,
                  position: 'bottom',
                  duration: 2000
                });
              }
            }).finally(() => {
                  self.confirmLoading = false;
                  self.close();
            });

          }else{
            try{
              console.log("err:"+JSON.stringify(err))
              console.log("values:"+JSON.stringify(values))
            }catch(e){
              console.log("e:"+e)
            }
            
          }
        });
        
      },
      handleCancel() {
        this.close()
      },
      // 验证数字
      validateNum(rule, value, callback) {
        if (new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{0,2})?$/).test(value)) {
          callback();
          var changeOutput=value/this.exchange
          try{
            changeOutput=parseFloat(changeOutput).toFixed(2);
          }catch(e){
            changeOutput='0'
          }
          this.form.setFieldsValue({znewcount: changeOutput ? changeOutput : null})
        } else {
          callback("请输入数字，小数请保留2位!");
        }
      },
      validateNum1(rule, value, callback) {
        if (new RegExp(/^(([1-9]{1}\d*)|(0{1}))(\.\d{0,2})?$/).test(value)) {
          callback();
          var changeOutput=value*this.exchange
          changeOutput=parseFloat(changeOutput).toFixed(2);
          this.form.setFieldsValue({newcount: changeOutput ? changeOutput : null})
          // this.form.setFieldsValue({znewcount: value ? value : null})
        } else {
          console.log("123")
          callback("请输入数字，小数请保留2位!");
        }
      },
      
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>