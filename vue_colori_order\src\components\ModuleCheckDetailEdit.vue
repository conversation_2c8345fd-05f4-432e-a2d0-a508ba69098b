<template>
    <div>
        <van-field readonly label="编号" :value="item.code" />
        <van-field readonly label="名称" :value="item.name" />
        <van-field readonly label="货位" :value="item.location" />
        <van-field readonly label="单位" :value="item.unit" />
        <van-field readonly label="现存量" :value="item.stock" />
        <van-field readonly label="原差异" :value="different" />
        <van-field readonly label="盘点人" :value="item.creator" />
        <van-field readonly label="模具状态" :value="item.checkStatus" />
        <van-field readonly label="盘点时间" :value="item.createTime" />

        <van-divider>修改</van-divider>
        <van-field name="checkboxGroup" label="模具状态">
            <template #input>
                <van-checkbox-group v-model="checkboxGroup" direction="horizontal">
                    <van-row>
                        <van-col span="12">
                            <van-checkbox name="完好" shape="square">完好</van-checkbox>
                        </van-col>
                        <van-col span="12">
                            <van-checkbox name="破损" shape="square">破损</van-checkbox>
                        </van-col>
                    </van-row>
                    <van-row style="margin-top:.5rem">
                        <van-col span="12">
                            <van-checkbox name="缺数" shape="square">缺数</van-checkbox>
                        </van-col>
                        <van-col span="12">
                            <van-checkbox name="卫生" shape="square">卫生</van-checkbox>
                        </van-col>
                    </van-row>
                </van-checkbox-group>
            </template>
        </van-field>
        <van-field label="盘点量" type="digit" v-model="item.checkStock" />
        <van-field label="备注" v-model="item.remarks" />
        <van-button type="info" @click="submit" style="width: 90%;">
            提交
        </van-button>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            item: {},
            different: '',
            checkboxGroup: [],
        }
    },
    watch: {
        checkboxGroup(newValue, oldValue) {
            let str = ''
            newValue.forEach(item => {
                str += item + ','
            });
            str = str.substring(0, str.length - 1);
            this.item.checkStatus = str
        }
    },
    created() {
        this.item = JSON.parse(localStorage.getItem("ModuleCheckDetailEditItem"))
        this.different = this.item.stock - this.item.checkStock
    },
    methods: {
        checkboxchange() {
            console.log(this.checkboxchange);
        },
        submit() {
            this.item.balance = this.item.stock - this.item.checkStock
            let NcPartsCheckDetailInfo = this.item
            this.$axios
                .post(`/jeecg-boot/ncApp/moldsCheck/editDetailInfo`, NcPartsCheckDetailInfo)
                .then(res => {
                    if (res.data.code == 200) {
                        localStorage.setItem('ModuleCheckDetailEditItem', JSON.stringify(this.item))
                        this.$router.push({ name: "ModuleCheckDetail" })
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
    },
}
</script>

<style scoped>
</style>