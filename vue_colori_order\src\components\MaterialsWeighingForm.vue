<template>
  <div>
    <van-sticky :offset-top="0">
      <van-nav-bar title="物料称重" left-text="返回" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-form labelWidth="100">
      <van-field v-model="info.glueName" label="胶体名称" disabled />
      <van-field v-model="info.glueCode" label="胶体编码" disabled />
      <van-field v-model="info.materialName" label="物料名称" disabled />
      <van-field v-model="info.materialCode" label="物料编码" disabled />
      <van-field v-model="info.perWeight" label="每公斤重量(KG)" disabled />
      <van-field v-model="info.respondWeight" label="需求重量(KG)" disabled />
      <van-field v-model="weightRage" label="称重范围" disabled />
      <van-field readonly clickable name="picker" v-model="balance" label="称：" placeholder="点击选择称"
        @click="showbalancePicker = true" />
      <van-popup v-model="showbalancePicker" position="bottom">
        <van-picker show-toolbar :columns="balanceColumns" @confirm="balanceConfirm"
          @cancel="showbalancePicker = false" />
      </van-popup>
      <van-field name="radio" label="单位">
        <template #input>
          <van-radio-group v-model="unit" direction="horizontal" @change="chageUnit">
            <van-radio name="g">g</van-radio>
            <van-radio name="kg">kg</van-radio>
            <van-radio name="t">t</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field v-model="info.tareWeight" name="asyncValidator" placeholder="请输入皮重" label="皮重" />
      <van-field v-model="info.actualWeight" name="asyncValidator" placeholder="请输入净重" label="净重" />

<!--       
      <van-button round type="info" block size="small" style="width:60%;margin:0 auto;"
        @click="handleScan">扫描拖码</van-button>
      <div style="width: 75%;margin: 1rem auto;overflow:hidden;">
        <input
          style="width: 70%;float: left;height: 2rem;line-height: 2rem;border: 0.1rem solid #cccccc;border-radius: 0.5rem;"
          class="uni-input" v-model="inputValue" />
        <van-button round type="info" block size="large"
          style="width: 25%;float: left;height: 2rem;line-height: 2rem;margin: 0 1% 0 1%;"
          @click="handleinput">查询</van-button>
      </div>
      <div v-for="(item, index) in stockList" :key="index">
        <van-field v-model="item.stickerId" label="托码" disabled />
        <van-field v-model="item.count" placeholder="请输入托码存量" label="托码存量" disabled />
        <van-field v-model="item.mainQuantity" placeholder="请输入使用数量" label="使用数量" />
        <van-button type="default" native-type="submit" icon="minus" size="mini" @click="minus(item, index)">
        </van-button>
      </div> -->
      <div style="margin: 16px;">
        <van-button round block type="info" @click="onSubmit">提交</van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { add, reduce, ride, except } from '../utils/decimal.js'
  export default {
    data() {
      return {
        // 单位
        unit: "kg",
        // 选择称
        showbalancePicker: false,
        weightRage: "",
        balance: "",
        info: {},
        balanceColumns: [],
        stockList: [],
        inputValue: ''
      };
    },
    created() {
      this.info = this.$route.params.item;
      this.$axios
        .get(`/jeecg-boot/app/gcMix/getBalanceByArea?area=${this.info.area}`)
        .then(res => {
          if (res.data.code == 200) {
            res.data.result.forEach(item => {
              this.balanceColumns.push(item.code);
            });
          } else {
          }
        });
      this.weightRage = this.info.mixWeight + "kg一" + this.info.maxWeight + "kg";
    },
    methods: {
      handleScan() {
        let that = this;
        wx.scanQRCode({
          desc: "scanQRCode desc",
          needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
          success: function (res) {
            // 回调
            var result = res.resultStr; //当needResult为1时返回处理结果
            that.$axios.get(`/jeecg-boot/app/gcMix/getWhStockInfo?mixBookName=${this.info.mixBookName}&mixStoreName=${this.info.mixStoreName}&stickerId=${result}`)
              .then(res => {
                if (res.data.success) {
                  console.log(res)
                  res.data.result.count = res.data.result.mainQuantity
                  res.data.result.mainQuantity = 0
                  if (res.data.result.productNo == that.info.materialCode) {
                    if (that.stockList.some(item => item.stickerId === res.data.result.stickerId)) {
                      Toast({
                        message: '该拖码已存在',
                        position: "bottom",
                        duration: 2000
                      });
                    } else {
                      that.stockList.push(res.data.result)
                    }
                    Toast({
                      message: res.data.message,
                      position: "bottom",
                      duration: 2000
                    });
                  } else {
                    that.$message.warn('该拖码原料与称重原料不符')
                  }
                } else {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                }
              });

          },
          error: function (res) {
            if (res.errMsg.indexOf("function_not_exist") > 0) {
              alert("版本过低请升级");
            }
          }
        });
      },
      handleinput() {
        let that = this;
        if (
          this.inputValue == "" ||
          this.inputValue == null ||
          this.inputValue == undefined
        ) {
          Toast({
            message: "请输入",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        this.$axios
          .get(`/jeecg-boot/app/gcMix/getWhStockInfo?mixBookName=${this.info.mixBookName}&mixStoreName=${this.info.mixStoreName}&stickerId=${this.inputValue}`)
          .then(res => {
            if (res.data.success) {
              console.log(res)
              res.data.result.count = res.data.result.mainQuantity
              res.data.result.mainQuantity = 0
              res.data.result.id = res.data.result.stockId
              res.data.result.type = this.info.type
              res.data.result.remarks = this.info.remarks

              if (res.data.result.productNo == that.info.materialCode) {
                if (that.stockList.some(item => item.stickerId === res.data.result.stickerId)) {
                  Toast({
                    message: '该拖码已存在',
                    position: "bottom",
                    duration: 2000
                  });
                } else {
                  that.stockList.push(res.data.result)
                }
                Toast({
                  message: res.data.message,
                  position: "bottom",
                  duration: 2000
                });
              } else {
                that.$message.warn('该拖码原料与称重原料不符')
              }
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          });

      },
      minus(item, index) {
        this.stockList.splice(index, 1);
      },
      balanceConfirm(value) {
        this.balance = value;
        this.showbalancePicker = false;
      },
      onSubmit() {
        if (this.balance == "") {
          Toast({
            message: "请先选择称",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        // let allWeight = 0
        // this.stockList.forEach((item) => {
        //   allWeight = add(item.mainQuantity * 1, allWeight)
        // })
        // this.info.actualWeight = allWeight
        if (this.unit == "g") {
          if (this.info.actualWeight * 1 < this.info.mixWeight * 1000 ||
            this.info.actualWeight * 1 > this.info.maxWeight * 1000

          ) {
            Toast({
              message: "重量不在范围内",
              position: "bottom",
              duration: 2000
            });
            return;
          }
        } else if (this.unit == "kg") {
          console.log('this.info.actualWeight * 1', 'this.info.maxWeight * 1');
          console.log(this.info.actualWeight * 1, this.info.maxWeight * 1);
          console.log('this.info.actualWeight * 1', 'this.info.mixWeight * 1');
          console.log(this.info.actualWeight * 1, this.info.mixWeight * 1);
          if (this.info.actualWeight * 1 < this.info.mixWeight * 1 ||
            this.info.actualWeight * 1 > this.info.maxWeight * 1
          ) {
            Toast({
              message: "重量不在范围内",
              position: "bottom",
              duration: 2000
            });
            return;
          }
        } else if (this.unit == "t") {
          if (this.info.actualWeight * 1 < this.info.mixWeight * 1 ||
            this.info.actualWeight * 1 > this.info.maxWeight * 1

          ) {
            Toast({
              message: "重量不在范围内",
              position: "bottom",
              duration: 2000
            });
            return;
          }
        }
        this.info.aboutWeight = (this.info.tareWeight * 10000000 + this.info.actualWeight * 10000000) / 10000000;
        this.info.userName = localStorage.getItem("userName");
        this.info.userCode = localStorage.getItem("userCode");
        this.info.enterType = "手输";
        this.info.status = 3;
        this.info.balance = this.balance;
        // this.info.stockList = this.stockList;
        this.$axios
          .post(`/jeecg-boot/app/gcMix/editMixDetail`, this.info)
          .then(res => {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            if (res.data.code == 200) {
              this.$router.go(-1);
            }
          });
      },
      chageUnit(e) {
        if (e == "g") {
          this.weightRage =
            this.info.mixWeight * 1000 +
            "g 一" +
            this.info.maxWeight * 1000 +
            "g";
          console.log(this.info.weightRage);
        } else if (e == "kg") {
          this.weightRage =
            this.info.mixWeight + "kg 一" + this.info.maxWeight + "kg";
          console.log(this.info.weightRage);
        } else if (e == "t") {
          this.weightRage =
            this.info.mixWeight / 1000 +
            "t 一" +
            this.info.maxWeight / 1000 +
            "t";
          console.log(this.info.weightRage);
        }
      },

      onClickLeft() {
        this.$router.go(-1);
      }
    }
  };
</script>

<style scoped>
  input:disabled,
  textarea:disabled {
    opacity: 1;
    -webkit-text-fill-color: rgb(0, 0, 0);
  }

  .van-field__control:disabled {
    -webkit-text-fill-color: black;
  }
</style>