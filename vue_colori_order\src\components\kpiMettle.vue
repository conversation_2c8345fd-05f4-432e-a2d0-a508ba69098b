<template>
    <van-tabs v-model="active" sticky @change="handleTabChange">
        <van-tab title="已打分列表">
            <van-button type="info" size="small" @click="show = true" style="width:100%">筛选</van-button>
            <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">

                <van-field v-model="queryParam.llqUserCode" clearable label="人员编码：" placeholder="请输入人员编码" />

                <van-field readonly clickable name="datetimePicker" :value="queryParam.yearMonth" label="时间选择"
                    placeholder="点击选择时间" @click="showPicker = true" />
                <van-popup v-model="showPicker" position="bottom">
                    <van-datetime-picker type="year-month" title="选择年月" :min-date="new Date(2023, 0, 1)"
                        :max-date="new Date(2222, 0, 1)" :formatter="formatter" @confirm="onYearMonthConfirm"
                        @cancel="showPicker = false" />
                </van-popup>

                <van-button type="info" @click="search" style="width: 100%" round>
                    确定
                </van-button>
            </van-popup>

            <div v-for="(item, index) in kpiMettleList" :key="item.id" class="kpi-item">
                <div class="staff-info">
                    <div class="left-section">
                        <span class="staff-name">{{ item.staffName }}</span>
                        <span class="staff-code">{{ item.llqUserCode }}</span>
                    </div>
                    <div class="right-section">
                        <div class="month">
                            <van-icon name="clock-o" />
                            <span>{{ item.yearMonth }}</span>
                        </div>
                        <div class="score">
                            <span class="score-value">{{ item.mettle }}</span>
                            <span class="score-unit">分</span>
                        </div>
                    </div>
                </div>
            </div>
        </van-tab>
        <van-tab title="奋斗精神打分">
            <van-button type="info" size="small" @click="show1 = true" style="width:100%">获取打分列表</van-button>
            <van-popup v-model="show1" position="bottom" :style="{ height: '45%' }">
                <van-field readonly clickable name="picker" v-model="queryParam1.bookName" label="账套："
                    placeholder="点击选择账套" @click="showbookNamePicker = true" />
                <van-popup v-model="showbookNamePicker" position="bottom">
                    <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                        @cancel="showbookNamePicker = false" />
                </van-popup>

                <van-field readonly clickable name="picker" v-model="queryParam1.workshop" label="车间："
                    placeholder="点击选择车间" @click="showworkshopPicker = true" />
                <van-popup v-model="showworkshopPicker" position="bottom">
                    <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                        @cancel="showworkshopPicker = false" />
                </van-popup>


                <van-field readonly clickable name="datetimePicker" :value="queryParam1.yearMonth" label="时间选择"
                    placeholder="点击选择时间" @click="showPicker = true" />
                <van-popup v-model="showPicker" position="bottom">
                    <van-datetime-picker type="year-month" title="选择年月" :min-date="new Date(2023, 0, 1)"
                        :max-date="new Date(2222, 0, 1)" :formatter="formatter" @confirm="onYearMonthConfirm"
                        @cancel="showPicker = false" />
                </van-popup>

                <van-button type="info" @click="getKpiMettleList" style="width: 100%" round>
                    确定
                </van-button>
            </van-popup>

            <div v-for="(item, index) in kpiMettleList1" :key="item.id" class="kpi-item">
                <div class="staff-info" @click="handleMark(item)">
                    <div class="left-section">
                        <span class="staff-name">{{ item.staffName }}</span>
                        <span class="staff-code">{{ item.llqUserCode }}</span>
                    </div>
                    <div class="right-section" v-if="item.id">
                        <div class="month">
                            <van-icon name="clock-o" />
                            <span>{{ item.yearMonth }}</span>
                        </div>
                        <div class="score" >
                            <span class="score-value">{{ item.mettle }}</span>
                            <span class="score-unit">分</span>
                        </div>
                    </div>
                </div>
            </div>
            <van-popup v-model="showMark" position="bottom" :style="{ height: '45%' }">
                <van-field v-model="markScore" label="打分" placeholder="请输入打分" type="digit" />
                <van-button type="info" @click="markConfirm" style="width: 100%" round>确定</van-button>
            </van-popup>
        </van-tab>
    </van-tabs>
</template>

<script>
import { Toast } from "vant";
import moment from "moment";
import { Icon } from 'vant';

export default {
    name: "kpiMettle",
    components: {
        [Icon.name]: Icon
    },
    data() {
        return {
            userCode: localStorage.getItem("userCode"),
            active: 0,
            kpiMettleList: [],
            kpiMettleList1: [],
            queryParam: {
                llqUserCode: "",
                yearMonth: moment().format('YYYY-MM'),
            },
            markScore: "",
            showMark: false,
            queryParam1: {
                bookName: "",
                workshop: "",
                yearMonth: moment().format('YYYY-MM'),
            },
            showPicker: false,

            show: false,
            show1: false,

            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],
            markItem: {},
        };
    },
    created() {
        if (this.userCode == null || this.userCode == "") {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000,
            });
            this.$router.push({
                name: "LoginIndex",
            });
        } else {
            this.$axios
                .get(`/jeecg-boot/app/warehouse/getFactoryInfo`)
                .then((res) => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach((item) => {
                            this.bookNameColumns.push(item.name);
                        });
                    } else {
                    }
                });
            this.bookNameConfirm(this.bookName);
            this.search();
        }
    },

    methods: {
        handleTabChange(index) {
            if (index == 0) {
                this.search();
            } else {
                this.getKpiMettleList();
            }
        },
        handleMark(item) {
            this.showMark = true;
            this.markItem = item;
            this.markScore = ''
        },
        markConfirm() {
            this.showMark = false;
            if (this.markItem.id) {
                this.$axios.put(`/jeecg-boot/app/ncGeneralKpi/editKpiMettle`, { ...this.markItem, mettle: this.markScore }).then(res => {
                    if (res.data.code == 200) {
                        Toast.success("打分成功");
                        this.getKpiMettleList();
                    }
                });
            } else {
                this.$axios.post(`/jeecg-boot/app/ncGeneralKpi/addKpiMettle`, { ...this.markItem, mettle: this.markScore }).then(res => {
                    if (res.data.code == 200) {
                        Toast.success("打分成功");
                        this.getKpiMettleList();
                    }
                });
            }
        },
        getKpiMettleList() {
            this.$axios.get(`/jeecg-boot/app/ncGeneralKpi/getKpiConnect?userCode=${this.userCode}&bookName=${this.queryParam1.bookName}&workshop=${this.queryParam1.workshop}&yearMonth=${this.queryParam1.yearMonth}`).then(res => {
                this.kpiMettleList1 = res.data.result;
            }).finally(() => {
                this.show1 = false;
            });
        },
        bookNameConfirm(value) {
            this.queryParam1.bookName = value;
            this.queryParam1.workshop = "";
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value },
                })
                .then((res) => {
                    if ((res.data.code = 200)) {
                        this.workshopColumns = [];
                        res.data.result.forEach((item) => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.queryParam1.workshop = value;
            this.showworkshopPicker = false;
        },
        search() {
            Toast.loading({
                message: '加载中...',
                forbidClick: true,
            });
            this.$axios
                .get(
                    `/jeecg-boot/app/ncGeneralKpi/getKpiMettleList?llqUserCode=${this.queryParam.llqUserCode}&yearMonth=${this.queryParam.yearMonth}`
                )
                .then((res) => {
                    this.show = false;
                    if (res.data.code == 200) {
                        console.log(res.data.result.records);
                        this.kpiMettleList = res.data.result.records;
                        console.log(this.kpiMettleList);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000,
                        });
                    }
                })
                .finally(() => {
                    Toast.clear();
                });
        },
        formatter(type, val) {
            if (type === 'year') {
                return `${val}年`;
            } else if (type === 'month') {
                return `${val}月`;
            }
            return val;
        },
        onYearMonthConfirm(value) {
            console.log("🚀 ~ onYearMonthConfirm ~ value:", moment(value).format('YYYY-MM'))
            this.queryParam.yearMonth = moment(value).format('YYYY-MM');
            this.showPicker = false;
        }
    }
};
</script>

<style scoped>
.kpi-item {
    background: #ffffff;
    border-radius: 10px;
    padding: 12px 16px;
    margin: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.kpi-item:active {
    background: #f9f9f9;
}

.staff-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.left-section {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.staff-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
}

.staff-code {
    font-size: 13px;
    color: #969799;
    font-weight: normal;
}

.right-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.month {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 13px;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
}

.month .van-icon {
    font-size: 14px;
    color: #969799;
}

.score {
    display: flex;
    align-items: baseline;
}

.score-value {
    font-size: 18px;
    font-weight: bold;
    color: #1989fa;
}

.score-unit {
    font-size: 12px;
    color: #1989fa;
    margin-left: 2px;
}
</style>
