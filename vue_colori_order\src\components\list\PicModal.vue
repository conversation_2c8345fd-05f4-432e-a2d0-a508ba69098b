<template>
  <a-modal
    :visible="visible"
    @cancel="handleClose"
    :footer="null">

    <van-image
      width="100%"
      height="100%"
      :src="url"
    />

  </a-modal>
</template>

<script>
  export default {
    name: "PicModal",
    data() {
      return {
        visible: false,
        url:"",
      }
    },
    methods: {
      show(url) {  
        this.visible = true;
        this.url=url
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleClose(){
        this.close()
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>