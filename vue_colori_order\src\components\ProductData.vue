<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_product_data" width="100%"/>
        <!-- <van-cell title="员工姓名" :value="userName" style="margin:5%;width:90%;border-radius:10px;"/> -->
        <!-- <van-cell title="部门" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/> -->
        <van-cell title="帐套" :value="book" @click="popupVisibleBook = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-cell title="车间" :value="workshop" @click="popupVisible = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-cell title="当前日期" :value="date" @click="c_show = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-calendar v-model="c_show" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" :show-confirm="false" position="right" />

        <div v-for="(item,index) in workInfo" :key="index">
            <div class="attence_item" @click="pushDetail(item.operationList)">
                <div class="attence_circle">
                    <div class="item_circle"></div>
                </div>
                
                <div class="item_top1">
                    <div>线体编号：</div>
                    <div class="item_status">{{item.mitosome}}</div>
                </div>
                <div class="item_top1">
                    <div>当前状态：</div>
                    <div v-if="item.workType=='维修结束'">开线</div>
                    <div v-else-if="item.workType=='换产结束'">开线</div>
                    <div v-else-if="item.workType=='培训结束'">开线</div>
                    <div v-else-if="item.workType=='挑拣结束'">开线</div>
                    <div v-else-if="item.workType=='待料结束'">开线</div>
                    <div v-else-if="item.workType=='返工结束'">开线</div>
                    <div v-else >{{item.workType}}</div>
                </div>
                <div class="item_bottom1">
                    <div>MO单号：</div>
                    <div>{{item.moId}}</div>
                </div>
                <div class="item_bottom2">
                    <div>产品名称：</div>
                    <div class="product_name">{{item.name}}</div>
                </div>
            </div>
        </div>


        <mt-popup class="popup-div" v-model="popupVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="popupSlots" @change="onValuesChange"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="popupVisible = !popupVisible">取消</div>
                    <div class="">请选择车间</div>
                    <div class="usi-btn-sure" @click="popupOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>


        <mt-popup class="popup-div" v-model="popupVisibleBook" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="popupBookSlots" @change="onValuesChange2"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="popupVisibleBook = !popupVisibleBook">取消</div>
                    <div class="">请选择工厂</div>
                    <div class="usi-btn-sure" @click="popupBookOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>


        

        <van-popup
            v-model="popup_show"
            round
            closeable
            close-icon="close"
            style="height:75%;width:90%;"
        >
            <div style="margin-top:10%">
                <van-steps direction="vertical" :active="operationList.length-1">
                <van-step v-for="(item,index) in operationList" :key="index">
                    <div class="step">
                        <h3 class="step_h">{{item.createTime}}_{{item.creator}}</h3>
                        <p class="step_p" v-if="item.id">{{item.id}}</p>
                        <p class="step_p" v-if="item.name">{{item.name}}</p>
                        <p class="step_p">{{item.moId}}{{item.workType}}</p>
                        <p class="step_p" v-if="item.count">产量:{{item.count}}</p>
                        <p class="step_p" v-if="item.comments">备注:{{item.comments}}</p>
                        <p class="step_p" v-if="item.reason">原因:{{item.reason}}</p>
                    </div>
                </van-step>
                <van-step></van-step>
                </van-steps>
            </div>



        </van-popup>



        <div style="background:#f3f4f6;height:10px;"></div>


        
    </div>
</template>
<script>
import { DatetimePicker,Indicator } from 'mint-ui';
import { Calendar } from 'vant';
export default {

    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            msg: "",
            active: 0,
            minDate:'',
            maxDate:'',
            book:'总部工厂',
            workshop:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_product_data:require('../../static/images/myProductData.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            workInfo:[],
            workLine:[],
            fee:{},
            operationList:[],
            questionTypeVal:'',
            BookVal:'',
            popupVisible:false,
            popupVisibleBook:false,
            popupSlots:[],
            popupBookSlots:[
                {
                    values:[
                        {
                            id:0,
                            name:"总部工厂"
                        },
                        {
                            id:1,
                            name:"南通工厂"
                        },
                        {
                            id:2,
                            name:"成都工厂"
                        },{
                            id:3,
                            name:"尼日利亚"
                        }
                    ]
                }
                
            ]
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.userName=localStorage.getItem('userName')
        // this.getWorkLine()
        // this.getWorkInfo()
        this.getWorkshopByBook("总部工厂")
    },
    methods: {
        pushDetail(operationList){
            this.popup_show=true
            this.operationList=operationList
        },
        getWorkLine(){
            let self=this
            self.popupSlots=[];
            let userCode=localStorage.getItem('userCode')
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getWorkLine',{params:{userCode:userCode,type:'1'}}).then(res=>{
                if(res.data.code==200){
                    self.workLine=res.data.result
                    let params={
                        values:self.workLine
                    }
                    self.popupSlots.push(params)
                    console.log(self.popupSlots)
                    self.workshop=self.workLine[0]
                    self.getWorkInfo(self.workshop)
                }
            })
        },
        getWorkInfo(workshop){
            let self=this
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let userCode=localStorage.getItem('userCode')
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getWorkInfo',{params:{userCode:userCode,workDay:this.date,workshop:workshop}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.workInfo=res.data.result
                }else{
                    Indicator.close();
                }
            })
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getWorkInfo(this.workshop)
        },
        // 问题类型弹框点击确认
        popupOk(){
            console.log("questionTypeVal:"+this.questionTypeVal.name)
            this.workshop = this.questionTypeVal.name;
            this.popupVisible = false;
            this.getWorkInfo(this.workshop)
        },
        popupBookOk(){
            this.book = this.BookVal;
            console.log(this.book)
            this.popupVisibleBook = false;
            this.getWorkshopByBook(this.book)
        },
        getWorkshopByBook(book){
            let self=this
            self.popupSlots=[]
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getFactoryInfoByDepartment',{params:{department:book}}).then(res=>{
                if(res.data.code==200){
                    self.workLine=res.data.result
                    let params={
                        values:self.workLine
                    }
                    self.popupSlots.push(params)
                    console.log(self.popupSlots)
                    self.workshop=self.workLine[0].name
                    self.getWorkInfo(self.workshop)
                }
            })
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        //问题类型的弹框picker值发生改变
        onValuesChange2(picker, values){
            this.BookVal = values[0].name;
        },
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.step {
  background: #39b54a;
  width: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  height: auto;
  color: #fff;
  border-radius: 5px;
}
.step_h{
    padding: 10px;
    margin: 0;
}
.step_p{
    margin: 0;
    padding: 10px;
    padding-top: 0;
}
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 8rem;
    background: #fff;
    margin-left: 5%;
    border-radius: 10px;
    padding-left: 3%;
    margin-top: 5%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top1{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 25%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_top2{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 25%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom1{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 25%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom2{
    float: left;
    width: 80%;
    padding-left: 3%;
    height: 25%;
    text-align: left;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
</style>