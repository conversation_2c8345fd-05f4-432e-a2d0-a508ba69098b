<template>
    <div class="order">

        <div class="top-title">
            <div class="title_order_user">
                <span>员工管理</span>
            </div>
            <div class="top-message">
                <img :src="message" width="70%" />
            </div>
        </div>

        <div style="height:4rem;">
            <div class="searchField">
                <mt-field placeholder="请输入" v-model="searchKey" ></mt-field>
            </div>
            
            <div class="searchBtn" @click="searchUser">
                <img :src="search" />
            </div>
        </div>

        <!-- <div v-for="(item,index) in userInfo" :key="index">
            <div class="userItem">
                <div class="userName-st">
                    <div>{{item.code}}_{{item.name}}</div>
                    <div>{{item.workshop}}</div>
                </div>
                
                <div class="userName-add" @click="addPerson(item)">
                    <img :src="add" width="25%"/>
                </div>
            </div>
        </div> -->


        <div class="content" v-for="(item,index) in userInfo" :key="index">
            <div class="userInfo">
                <div style="font-weight:800;font-size:18px;">{{item.name}}</div>
                <div>{{item.code}}|{{item.workshop}}</div>
            </div>
            <div class="userAdd">
                <img :src="add" width="25%" v-if="item.type=='0'" @click="addPerson(item)"/>
                <div v-if="item.type=='1'" style="color:red">未排班</div>
                <div v-if="item.type=='2'" style="color:orange">占用中</div>
                <div v-if="item.type=='3'" style="color:blue">车间不一致</div>
            </div>
            <div style="clear:both;"></div>
        </div>


        <div style="height:5rem;"></div>

        
        <div class="selectBottom">
            <div class="user_List">
                <span style="margin-left:5%;font-size: 1.2rem;">
                    当前添加人数：{{addUserInfo.length}}
                </span>
            </div>
            <div class="addBtn" @click="addUserToServe">{{$t('add')}}</div>
        </div> 

        
    </div>
</template>
<script>
import { Indicator   } from 'mint-ui';
import { Notify,Toast } from 'vant';
export default {
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            search:require('../../static/images/search.png'),
            add:require('../../static/images/userAdd.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            value:false,
            personList:[],
            addUserInfo:[],
            searchKey:'',
            preCategory:'',
            userInfo:[],
            item:[],
            questionType:'',
            questionTypeVal:'',
            status:'',
            lpId:'',
            createTime:'',
            popupVisible:false,
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let self=this;
        self.item=this.$route.params
        self.status=self.item.lastStatus
        self.createTime=self.item.createTime
        self.lpId=self.item.lpId
        self.preCategory=self.item.preCategory

        console.log(self.preCategory)

        self.searchUser()
    },
    methods:{
        addUserToServe(){
            let self=this;
            if(self.addUserInfo.length<=0){
                Notify({ type: "warning", message: "请先添加人员！" });
                return
            }
            console.log(self.status)
            if(self.status=='0'){
                self.notStartToAdd();
            }else{
                self.startedToAdd();
            }
            
        },
        notStartToAdd(){
            let self=this;
            let userCode=localStorage.getItem('userCode')
            let params={
                type:'1',
                gcWorkPlanAppList:self.addUserInfo,
                creator:userCode
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changePlanInfo',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Notify({ type: "success", message: res.data.message });
                    self.$router.go(-1);
                }else{
                    Indicator.close();
                    Notify({ type: "warning", message: res.data.message });
                }
            })
        },
        startedToAdd(){
            let self=this;
            let itemType='1';
            if(self.item.lastStatus=='2'){
                itemType='8'
            }            

            let params={
                type:itemType,
                lpId:self.lpId,
                gcWorkOperationList:self.addUserInfo,
            }
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });

            console.log("params:"+JSON.stringify(params))

            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    Notify({ type: "success", message: res.data.message });
                    self.$router.go(-1);
                }else{
                    Indicator.close();
                    Notify({ type: "warning", message: res.data.message });
                }
            })
        },
        searchUser(){
            let self=this;
            let url="";
            if(self.status=='0'){
                //未开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreeWorker";
            }else{
                //开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreePeopleInfo";
            }
            const toast = Toast.loading({
                duration: 0, // 持续展示 toast
                forbidClick: true,
                message: "正在查询员工数据..."
            });

            console.log(self.preCategory)

            self.$axios.get(url,{params:{secinfo:self.searchKey,createTime:self.createTime,lpId:self.lpId,preCategory:self.preCategory}}).then(res=>{
                toast.clear()
                if(res.data.code==200){
                    Notify({ type: "success", message: res.data.message });
                    self.userInfo=res.data.result
                    console.log(self.userInfo)
                }else{
                    Notify({ type: "warning", message: res.data.message });
                }
            })

        },
        openQuestionType(){
            this.popupVisible = true;
        },
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        addPerson(item){
            let self=this;
            item.lpId=self.lpId;
            item.pid=item.id;
            item.creator=localStorage.getItem('userName')
            console.log(item)
            this.addUserInfo.push(item);
            this.userInfo.splice(this.userInfo.indexOf(item),1);
        },
        selectData(){
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.title_order_user{
    font-size: 1.6rem;
    font-weight: 600;
    float: left;
    width: 60%;
    text-align:left;
    padding:8%;
}
.top-message{
    float: left;
    display:flex;
    align-items:center;
    width:20%;
    margin:0 auto;
}
.searchField{
    width: 70%;
    margin-left: 8%;
    margin-top:5%;
    float: left;
}
.searchBtn{
    margin-top:5%;
    float: left;
}
.selectBottom{
    height: 4rem;
    width: 100%;
    position:fixed;
    bottom:0;
    background-color: #fff;
}
.top-title{
    height:fit-content;
    width:100%;
    display:flex;
}
.userItem{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 2rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 5%;
    font-size: 1.2rem;
}
.userName-st{
    float: left;
    width: 70%;
    height: 100%;
}
.userName-add{
    float: left;
    width: 28%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
.user_List{
    float: left;
    width: 60%;
    height: 100%;
    display: flex;
    align-items: center;
}
.addBtn{
    float: left;
    width: 40%;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    justify-content: center;
    height: 100%;
    background: #5032f2;
    color: #fff;
}
.content{
    margin-left: 8%;
    margin-right: 5%;
    margin-top: 5%;
    text-align: left;
    display: flex;
    align-items: center;
    background: url('../../static/images/search_time.png');
}
.userInfo{
    width: 75%;
    float: left;
}
.userAdd{
    width: 25%;
    height: 100%;
    float: left;
    text-align: center;
}
</style>