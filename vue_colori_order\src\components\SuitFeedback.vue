<template>
    <div style="background:#f3f4f6;">
        <img :src="feedback" width="100%"/>
        <div style="padding:10px">
            <div style="text-align:left;color:#0077cb;font-size:18px;font-weight:700">您的姓名:</div>
            <van-field style="border: 1px solid #ccc;" v-model="userName"/>
        </div>

        <div style="padding:10px">
            <div style="text-align:left;color:#0077cb;font-size:18px;font-weight:700">您的工号:</div>
            <van-field style="border: 1px solid #ccc;" v-model="userCode"/>
        </div>

        <div style="padding:10px">
            <div style="text-align:left;color:#0077cb;font-size:18px;font-weight:700">您的部门:</div>
            <van-field style="border: 1px solid #ccc;" v-model="department"/>
        </div>

        <div style="padding:10px">
            <div style="text-align:left;color:#0077cb;font-size:18px;font-weight:700">申诉事实经过及理由:</div>
            <van-field type="textarea" rows="10"  v-model="content" autosize style="border: 1px solid #ccc;"/>
        </div>

        <div style="text-align:left;padding:10px;font-size:18px;font-weight:700">
            <span style="color:red;">提醒：</span><br />
            1、请勇敢说出您的想法和意见，我们需要您的反馈来改进工作；<br />
            2、为了提升投诉有效性和方便我们反馈处理结果，请您如实填写以上信息；<br />
            3、我们承诺会对您的个人信息保密，如果您遭遇到打击报复，请您及时联系HR：18015650276
        </div>

        <div>
            <van-button type="primary" :loading="BTLoading" @click="submit()" style="margin-top:15px;width:40%;border-radius:10px;">提交</van-button>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker, MessageBox,Toast } from 'mint-ui';
export default {
    data(){
        return{
            feedback:require('../../static/images/feedback.png'),
            BTLoading:false,
            content:"",
            userCode:"",
            userName:"",
            department:"",
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        
    },
    methods: {
        submit(){
            let self = this;
            if(self.userCode==null || self.userCode==''){
                Toast({
                    message: "请输入您的工号",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.userName==null || self.userName==''){
                Toast({
                    message: "请输入您的姓名",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.department==null || self.department==''){
                Toast({
                    message: "请输入您的部门",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }


            if(self.content==null || self.content==''){
                Toast({
                    message: "请输入您的意见",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            
            let params={
                userCode:self.userCode,
                userName:self.userName,
                department:self.department,
                content:self.content
            };
            MessageBox.confirm('',{
                message: '是否确认提交此建议？',
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.BTLoading = true;
                        self.$axios.post('/jeecg-boot/app/staff/saveSuitFeedback',params).then(res=>{
                            if(res.data.code==200){
                                self.BTLoading = false;
                                self.$router.push('/success');
                            }else{
                                self.BTLoading = false;
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                    self.BTLoading = false; 
                });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
}
.right_pic{
    position: absolute;
    right: 5%;
}
.car_title_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 1rem;
    color: #455a64;
    font-weight: 500;
}
.car_title_name{
    margin-left: 8%;
    font-size: 1.1rem;
    color: #000;
    font-weight: 600;
}
.car_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 0.88rem;
    color: #455a64;
}
.car_status{
    position:absolute;
    left: 20%;
    top: 8%;
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
}
.car_item{
    margin-top: 1rem;
    width: 100%;
    height: 6rem;
    background: #fff;
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 5%;
}
</style>