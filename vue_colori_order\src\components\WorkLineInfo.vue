<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div v-for="(item,index) in lines" :key="index" style="padding:8px;text-align:left;background:white">
            <div style="width:100%;height:1px;background:#333"></div>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">工作中心：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.jobCenter}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">线体描述：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.jobCenterComments}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">未开线原因</span>
                </van-col>
                <van-col span="18">
                    <van-field type="textarea" autosize rows="3" show-word-limit maxlength="200" v-model="item.reason"></van-field>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">备注</span>
                </van-col>
                <van-col span="18">
                    <van-field type="textarea" autosize rows="2" show-word-limit maxlength="100" v-model="item.remarks"></van-field>
                </van-col>
            </van-row>
            
        </div>

        <div>
            <a-button @click="submit" v-if="!writeFlag" style="margin-top:10%;width:80%;height:3rem" type="primary" icon="upload" >提交</a-button>
        </div>
    </div>
</template>
<script>
import { Toast,MessageBox  } from 'mint-ui';
export default {
    data(){
        return{
            id:"",
            lines:[],
            writeFlag:false
        }
    },
    created:function(){
        this.id = this.$route.query.id;
        console.log("id:"+this.id)
        this.getWorkLineDetailById();
    },
    methods: {
        getWorkLineDetailById(){
            let self = this;
            self.$axios.get('/jeecg-boot/app/codeModel/getWorkLineDetailById',{params:{id:this.id}}).then(res=>{
                if(res.data.code==200){
                    this.lines = res.data.result
                    for(var i=0;i<this.lines.length;i++){
                        if(this.lines[i].reason!=null && this.lines[i].reason!=''){
                            this.writeFlag = true
                        }
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        submit(){
            let self = this;
            for(var i=0;i<this.lines.length;i++){
                if(this.lines[i].reason==null || this.lines[i].reason==''){
                    Toast({
                        message: this.lines[i].jobCenter+"未填写未开线原因，请检查并填写！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            }
            let params={
                lines:this.lines,
                userCode:localStorage.getItem("userCode")
            }
            self.$axios.post('/jeecg-boot/app/codeModel/submitLinesInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    this.$router.push({path:'/worklineSuccess'});
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 22rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.ycl-style{
    color: crimson;
}
</style>