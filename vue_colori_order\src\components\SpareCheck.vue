<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="备件盘点" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <van-button type="info" @click="CheckAdd" style="width: 90%;">
                新增盘点
            </van-button>
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '20%' }">
            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />
            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in spareList" :key="index"
            style="text-align: left; margin-top: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="18">
                    <van-row>
                        <van-col span="24">创建人:{{ item.createName }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col v-if="item.status == 1" span="24">状态:进行中</van-col>
                        <van-col v-if="item.status == 2" span="24">状态:已结束</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">开始时间:{{ item.startTime }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">结束时间:{{ item.endTime }}</van-col>
                    </van-row>
                </van-col>
                <van-col span="6">
                    <van-button plain hairline type="info" @click="goDetail(item)" style="height:1.75rem">
                        查看详情
                    </van-button>
                    <van-button v-show="item.status == 1" plain hairline type="info" @click="over(item)"
                        style="height:1.75rem">
                        盘点结束
                    </van-button>
                </van-col>
            </van-row>

        </div>

    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            spareList: [],
            show: false,
            // 时间选择框是否显示
            show1: false,
            beginDay: this.getDate(-7),
            endDay: this.getDate(0),
            // 搜索条件 时间
            date: this.getDate(-7) + ' ~ ' + this.getDate(0),
        }
    },
    created() {
        this.$axios
            .get(`/jeecg-boot/ncApp/partsCheck/getMainList`)
            .then(res => {
                if (res.data.code == 200) {
                    this.spareList = res.data.result
                } else {
                    Toast({
                        message: res.data.msg,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
    },
    methods: {
        // 详情
        goDetail(item) {
            localStorage.setItem('SpareCheckItem', JSON.stringify(item))
            this.$router.push({ name: "SpareCheckDetail" })
        },
        // 盘点结束
        over(item) {
            let department = localStorage.getItem('department')
            if (department == '财务' || department == '督察') {
                if (item.status == 2) {
                    Toast({
                        message: '盘点已结束',
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    Dialog.confirm({
                        title: '',
                        message: '确认要盘点结束吗?',
                    })
                        .then(() => {
                            this.$axios
                                .get(`/jeecg-boot/ncApp/partsCheck/finishCheck?id=${item.id}&userCode=${localStorage.getItem('userCode')}`)
                                .then(res => {
                                    if ((res.data.code = 200)) {
                                        this.$axios
                                            .get(`/jeecg-boot/ncApp/partsCheck/getMainList`)
                                            .then(res => {
                                                if (res.data.code == 200) {
                                                    this.spareList = res.data.result
                                                    Toast({
                                                        message: res.data.message,
                                                        position: "bottom",
                                                        duration: 2000
                                                    });
                                                } else {
                                                    Toast({
                                                        message: res.data.message,
                                                        position: "bottom",
                                                        duration: 2000
                                                    });
                                                }
                                            });
                                    } else {
                                        Toast({
                                            message: res.data.msg,
                                            position: "bottom",
                                            duration: 2000
                                        });
                                    }
                                });
                        })
                        .catch(() => {
                            Toast({
                                message: '取消',
                                position: "bottom",
                                duration: 2000
                            });
                        });

                }
            } else {
                Toast({
                    message: "没有权限",
                    position: 'bottom',
                    duration: 2000
                });
            }

        },
        CheckAdd() {
            this.$axios
                .get(`/jeecg-boot/ncApp/partsCheck/addCheckInfo?userCode=${localStorage.getItem('userCode')}&userName=${localStorage.getItem('userName')}`)
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.$axios
                            .get(`/jeecg-boot/ncApp/partsCheck/getMainList`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.spareList = res.data.result
                                } else {
                                    Toast({
                                        message: res.data.msg,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },

        search() {
            this.$axios
                .get(`/jeecg-boot/ncApp/partsCheck/getMainList?startTime=${this.beginDay}&endTime=${this.endDay}`)
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.spareList = res.data.result
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "OrderMenu"
            });
        },
        onClickRight() {
            this.show = true;
        },
        getDate(day) {
            var date1 = new Date(),
                time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();//time1表示当前时间  
            var date2 = new Date(date1);
            date2.setDate(date1.getDate() + day);
            return date2.getFullYear() + "-" + ((date2.getMonth() + 1) < 10 ? '0' + (date2.getMonth() + 1) : (date2.getMonth() + 1)) + "-" + (date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate());
        },
        onConfirm(date) {
            const [start, end] = date;
            this.show1 = false;
            this.beginDay = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
            this.endDay = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
            this.date = `${this.beginDay}~${this.endDay}`
        },
    },
}
</script>

<style scoped>
</style>