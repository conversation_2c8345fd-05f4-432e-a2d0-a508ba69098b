<template>
    <div>
        <div style="display: flex;width: 100%;padding:3%;" v-for="(item,index) in stickerInfo" :key="index">

            <div style="flex: 1;text-align:center;align-self: center;" v-if="item.qcStatus=='0'" @click="selectItem(index,item)">
                <van-checkbox v-model="item.selected" style="margin-left:30%"></van-checkbox>
            </div>

            <div style="flex: 6;text-align:left;" @click="sendDetail(item)">
                <div :class="item.qcStatus=='1'?'ycl-style':''">托码编号：{{item.id}}</div>
                <div v-if="item.qcStatus=='0'">是否质检：否</div>
                <div v-if="item.qcStatus=='1'">是否质检：是</div>
                <div v-if="item.cxStatus=='0'">是否报工：否</div>
                <div v-if="item.cxStatus=='1'">是否报工：是</div>
                <div v-if="item.ckStatus=='0'">是否上架：否</div>
                <div v-if="item.ckStatus=='1'">是否上架：是</div>
            </div>

            <div style="clear:both;"></div>

        </div>

        <div v-if="stickerInfo.length<=0">
            <div><img src="../../static/images/nothing.png" alt=""></div>
			<p>暂无数据</p>
        </div>


        <div style="position:fixed;bottom:0;width:100%;height:3rem;background:#0f0;display: flex;
    align-items: center;
    justify-content: center;font-weight:800;" @click="submit">
            批量放行
        </div>
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
import UpdateModal from './list/UpdateModal.vue';
export default {
  components: { UpdateModal },
    data(){
        return{
            moId:'',
            itemId:'',
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal:'',
            minDate:'',
            maxDate:'',
            date:'',
            category:'',
            userName:'',
            userCode:'',
            lpId:'',
            popupVisible:false,
            stickerInfo:[],
            peopleInfo:{},
            newOrderList:[],
            workOrderList:[],
            errorOrderList:[],
            finishOrderList:[],
            questionType:'',
            c_show:false,
            questionTypeVal:'',
            clickNum:0,
            isfirst:'',
            popupVisible:false,
            leadAppInfo:[],
            popupSlots:[
                {
                    values:[
                        '全部','白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')

        this.lpId = localStorage.getItem('stickerLpId')

        this.getStickerInfo()
    },
    methods:{
        getStickerInfo(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/appQuality/getStickerInfo',{params:{lpId:self.lpId}}).then(res=>{
                if(res.data.code==200){
                    self.stickerInfo=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 1000
                    });
                }
            })
        },
        selectItem(index,item){
            let self=this
            if(item.selected){
                item.selected=false;
            }else{
                item.selected=true
            }
            self.$set(self.stickerInfo,index,item)
        },
        sendDetail(item){
            let self=this;
            self.$router.push({name:"ScanResult",params:{result:item.id,type:'1'}})
        },
        selected(item){
            let self=this
            self.$axios.get('/jeecg-boot/app/appQuality/getConnect',{params:{tableId:item.id,tableName:item.tableName,mainId:self.itemId,userCode:this.userCode}}).then(res=>{
                if(res.data.code==200){
                    self.$router.push({name:"WorkDuring",params:{id:item.id,type:'1',itemId:self.itemId,childId:'0'}})
                }
            })
        },
        hiddenToUpdateOrder(){
            this.clickNum=this.clickNum+1;
            if(this.clickNum<5){
                var lastClick=5-this.clickNum
                var msg="再点击"+lastClick+"下即可打开管理员权限！"
                Toast({
                    message: msg,
                    position: 'bottom',
                    duration: 1000
                });
            }else{
                this.clickNum=0;
                if(this.userCode=='HI1606270001' || this.userCode=='HI2002250004'){
                    this.$router.push({name:"OrderUpdate"})
                }else{
                    Toast({
                        message: "对不起，您暂无权限！",
                        position: 'bottom',
                        duration: 1000
                    });
                } 
            }

            
        },
        submit(){
            let self=this;
            var ids="";
            for(var i=0;i<self.stickerInfo.length;i++){
                if(self.stickerInfo[i].selected){
                    ids+=self.stickerInfo[i].id+";"
                }
            }
            
            self.$axios.get('/jeecg-boot/app/appQuality/batchQcCheck',{params:{ids:ids,userCode:this.userCode}}).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 1000
                    });
                    self.getStickerInfo();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 1000
                    });
                }
            })



        },
        modalFormOk(){
            this.getLeadAppInfo()
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
        },
        updateCount(item){
            let self=this;
            self.$refs.modalForms.edit(item);
            self.$refs.modalForms.title="产量更改";
            self.$refs.modalForms.disableSubmit = true;
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType(){
            this.popupVisible = true;
        },
        dateConfirm(value){
            this.newSelectedValue=this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getLeadAppInfo()
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getLeadAppInfo()
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        selectData(){
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}
.item-left{
    float: left;
    width: 20%;
}
.item-right{
    float: left;
    width: 80%;
}
.ycl-style{
    color: darkgreen;
}
</style>