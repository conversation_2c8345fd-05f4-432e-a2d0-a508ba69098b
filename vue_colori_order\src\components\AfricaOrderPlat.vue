<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_order_title">工作任务</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%" />
            </div>
        </div>
        <!-- <div class="sign">
            <img :src="plotTop" width="90%"/>
            <div class="plotName">{{peopleInfo.name}}</div>
            <div class="plotCode">{{peopleInfo.code}}</div>
            <div class="plotFactory">{{peopleInfo.department}}</div>
            <div class="plotWorkshop">{{peopleInfo.workshop}}</div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div> -->

        <div class="sign">
            <div style="width:100%;">
                <div style="float:left;text-align:left;width:70%;">
                    <span style="font-size:22px;">{{peopleInfo.name}}</span>
                    <br />
                    <span style="font-size:16px;">{{peopleInfo.code}}</span>
                </div>
                <div style="float:left;width:30%;">
                    <img :src="card" width="60%" />
                </div>
                <div style="clear:both"></div>
            </div>
            <div style="margin-top:70px;text-align:left">
                {{peopleInfo.department}}
                <br />
                {{peopleInfo.workshop}}
            </div>
        </div>
        
        <!-- <div class="menu_order" >
            <div class="menu_order_item" @click="openLine">
                <img :src="'../../static/images/startMite.png'" width="90%" />
            </div>
            <div class="menu_order_item" @click="closeLine">
                <img :src="'../../static/images/endMite.png'" width="90%" />
            </div>

        </div> -->


        <van-dialog v-model="categoryDialogshow" title="请选择班次" :direction="'horizontal'" show-cancel-button
            showConfirmButton @confirm="categoryDialogconfirm" @cancel="categoryDialogcancel">
            <div style="padding: 1rem;">
                <van-radio-group v-model="categoryA">
                    <van-radio name="白班">白班</van-radio>
                    <van-radio name="晚班">晚班</van-radio>
                </van-radio-group>
            </div>
        </van-dialog>

        <div class="sc_date">
            <div class="rq_date">{{$t('date')}}</div>
            <div class="date_work" @click="c_show = true">{{date}}</div>
            <div class="right_jt"></div>
        </div>


        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false"
            position="right" />


        <!-- <div class="pool">
            <div class="zbPool" @click="zbPool"></div>
            <div class="mid">
                <div class="midPool" @click="zbPool"></div>
            </div>
            <div class="ybPool" @click="ybPool"></div>
        </div>
 -->



        <div class="pro-report" @click="pushReport">{{$t('report')}}</div>

        <van-row>
            <van-col span="12">
                <div @click="openLine" style="width: 6rem;background-color: #B0A0F8;margin: 1rem auto;padding: 0.5rem;border-radius: 1rem;">一键开线</div>
            </van-col>
            <van-col span="12">
                <div @click="closeLine" style="width: 6rem;background-color: #B0A0F8;margin: 1rem auto;padding: 0.5rem;border-radius: 1rem;">一键关线</div>
            </van-col>
        </van-row>

        <div>
            <div class="orderType" v-if="newOrderList.length>0">{{$t('newTask')}}</div>
            <div v-for="(item,index) in newOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" v-if="item.mitosome!=null && item.mitosome!='null'">
                            <scan :class="item.mitosome.indexOf('Y')!=-1?'ycl-style':''">{{item.name}}</scan>
                        </div>
                        <div class="itemTitle" v-else>{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                        <div class="itemTitle">{{item.mitosome}}&nbsp;-&nbsp;{{item.plot}}</div>
                    </div>
                    <div class="item_add">
                        <img :src="itemAdd" width="100%" height="100%" />
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="orderType" v-if="workOrderList.length>0">{{$t('InTask')}}</div>
            <div v-for="(item,index) in workOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" v-if="item.mitosome!=null && item.mitosome!='null'">
                            <scan :class="item.mitosome.indexOf('Y')!=-1?'ycl-style':''">{{item.name}}</scan>
                        </div>
                        <div class="itemTitle" v-else>{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                        <div class="itemTitle">{{item.mitosome}}&nbsp;-&nbsp;{{item.plot}}</div>
                    </div>
                    <div class="item_add">
                        <img :src="itemAdd" height="100%" width="100%" />
                        <div style="clear:both;"></div>
                    </div>
                </div>
            </div>
        </div>


        <div>
            <div class="orderType" v-if="errorOrderList.length>0">异常调整</div>
            <div v-for="(item,index) in errorOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" v-if="item.mitosome!=null && item.mitosome!='null'">
                            <scan :class="item.mitosome.indexOf('Y')!=-1?'ycl-style':''">{{item.name}}</scan>
                        </div>
                        <div class="itemTitle" v-else>{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                        <div class="itemTitle">{{item.mitosome}}&nbsp;-&nbsp;{{item.plot}}</div>
                    </div>
                    <div class="item_add">
                        <img :src="itemAdd" width="100%" height="100%" />
                    </div>
                </div>
            </div>
        </div>


        <div>
            <div class="orderType" v-if="finishOrderList.length>0">已完结</div>
            <div v-for="(item,index) in finishOrderList" :key="index">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" v-if="item.mitosome!=null && item.mitosome!='null'">
                            <scan :class="item.mitosome.indexOf('Y')!=-1?'ycl-style':''">{{item.name}}</scan>
                        </div>
                        <div class="itemTitle" v-else>{{item.name}}</div>
                        <div class="itemTitle" v-if="item.preCategory=='1'">{{item.moId}}_白班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='4'">{{item.moId}}_晚班</div>
                        <div class="itemTitle" v-else-if="item.preCategory=='6'">{{item.moId}}_培训</div>
                        <div class="itemTitle" v-else>{{item.moId}}_无班次</div>
                        <div class="itemTitle">{{item.mitosome}}&nbsp;-&nbsp;{{item.plot}}</div>
                    </div>
                    <div class="item_add">
                        <img :src="itemAdd" width="100%" height="100%" />
                    </div>
                </div>
            </div>
        </div>


    </div>
</template>
<script>
    import { DatetimePicker, Toast, Indicator } from 'mint-ui';
    export default {
        data() {
            return {
                plotTop: require('../../static/images/plat_top.png'),
                message: require('../../static/images/message.png'),
                card: require('../../static/images/card.png'),
                itemAdd: require('../../static/images/item_add.png'),
                itemBg: require('../../static/images/item_bg.png'),
                selectedValue: this.formatDate(new Date()),
                newSelectedValue: this.formatDate(new Date()),
                dateVal: '',
                minDate: '',
                date: '',
                category: '',
                popupVisible: false,
                peopleInfo: {},
                newOrderList: [],
                workOrderList: [],
                errorOrderList: [],
                finishOrderList: [],
                questionType: '',
                c_show: false,
                questionTypeVal: '',
                popupVisible: false,
                popupSlots: [
                    {
                        values: [
                            '全部', '白班(上午)', '白班(下午)', '白班(加班)', '晚班(上半夜)', '晚班(下半夜)'
                        ]
                    }
                ],
                categoryA: '',//班次
                categoryDialogshow: false,//班次对话框显示:
                type: '',//1开线 2关线
            }
        },
        created: function () {
            let nowDate = new Date();
            this.minDate = new Date(nowDate.getTime() - 14 * 24 * 3600 * 1000);
            this.date = this.formatDate(new Date)
            this.getOrderInfo();
        },
        methods: {
            openLine() {
                let self = this;

                // this.categoryA = ''
                // this.categoryDialogshow = true;
                let params = { book: self.peopleInfo.department, userCode: localStorage.getItem('userCode'), userName: localStorage.getItem('userName'), workDay: self.date, category: '白班' }
                Indicator.open({
                    text: '正在加载中，请稍后……',
                    spinnerType: 'fading-circle'
                });
                self.$axios.post('/jeecg-boot/app/africa/autoStart', params).then(res => {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    if (res.data.code == 200) {
                        self.getOrderInfo();
                    } else {
                    }
                })
            },
            categoryDialogcancel() {
                this.categoryDialogshow = false;
            },
            categoryDialogconfirm() {
                if (this.categoryA == '' || this.categoryA == null) {
                    Toast({
                        message: '请选择班次',
                        position: 'bottom',
                        duration: 2000
                    });
                    return
                }
                let self = this;
                let userCode = localStorage.getItem('userCode')
                let userName = localStorage.getItem('userName')
                if (this.type == 1) {
                    self.$axios.get('/jeecg-boot/app/africa/autoStart', { params: { book: self.peopleInfo.department, userCode: userCode, userName: userName, workDay: self.date, category: self.categoryA } }).then(res => {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        if (res.data.code == 200) {
                            self.getOrderInfo();
                        } else {

                        }
                    })
                } else if (this.type == 2) {
                    let params = { book: self.peopleInfo.department, userCode: userCode, userName: userName, workDay: self.date, category: self.categoryA }
                    self.$axios.post('/jeecg-boot/app/africa/autoClose', params).then(res => {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                        if (res.data.code == 200) {
                            self.getOrderInfo();
                        } else {
                        }
                    })
                }
            },
            closeLine() {
                let self = this;
                // this.categoryA = ''
                // this.categoryDialogshow = true;
                let params = { book: self.peopleInfo.department, userCode: localStorage.getItem('userCode'), userName: localStorage.getItem('userName'), workDay: self.date, category: '白班' }
                Indicator.open({
                    text: '正在加载中，请稍后……',
                    spinnerType: 'fading-circle'
                });
                self.$axios.post('/jeecg-boot/app/africa/autoClose', params).then(res => {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    if (res.data.code == 200) {
                        self.getOrderInfo();
                    } else {
                    }
                })
            },
            zbPool() {
                let hour = new Date().getHours();
                // let hour=23
                if ((hour >= 10 && hour <= 14) || (hour >= 22 || hour <= 2)) {
                    localStorage.setItem('type', '1')
                    this.$router.push({ name: "PoolList", params: { type: '1' } })
                    return;
                } else {
                    Toast({
                        message: '值班池开放时间为10:00~14:00以及22:00~02:00！',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            },
            ybPool() {
                let hour = new Date().getHours();
                console.log(hour)
                if ((hour >= 15 && hour <= 19) || (hour >= 3 || hour <= 6)) {
                    localStorage.setItem('type', '2')
                    this.$router.push({ name: "PoolList", params: { type: '2' } })
                    return;
                } else {
                    Toast({
                        message: '延班池开放时间为15:00~19:00以及03:00~06:00！',
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            },
            getOrderInfo() {
                let self = this;
                let userCode = localStorage.getItem('userCode')
                self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo', { params: { userCode: userCode, workDay: self.date, preCategory: self.questionType } }).then(res => {
                    console.log(res)
                    if (res.data.code == 200) {
                        console.log(res.data.peopleInfo)
                        self.peopleInfo = res.data.result.peopleInfo
                        self.newOrderList = res.data.result.newOrderList
                        self.workOrderList = res.data.result.workOrderList
                        self.errorOrderList = res.data.result.errorOrderList
                        self.finishOrderList = res.data.result.finishOrderList
                    } else {
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            },
            pushReport() {
                this.$router.push({ name: "AfricaOrderReport" });
            },
            onConfirm(date) {
                this.c_show = false;
                this.date = this.formatDate(date);
                this.getOrderInfo()
            },
            startToNext(item) {
                let self = this;
                let userCode = localStorage.getItem('userCode')
                let params = {
                    id: item.id,
                    workDay: self.date,
                    userCode: userCode,
                    preCategory: self.questionType
                }
                localStorage.setItem('params', JSON.stringify(params));
                this.$router.push({ name: "AfricaOrderStart" })
            },
            /**
             * 打开问题类型的弹框
             */
            openQuestionType() {
                this.popupVisible = true;
            },
            dateConfirm(value) {
                this.newSelectedValue = this.formatDate(value)
                console.log(this.dateVal)
                console.log(this.newSelectedValue)
                this.getOrderInfo();
            },
            // 问题类型弹框点击确认
            popupOk() {
                this.questionType = this.questionTypeVal;
                this.popupVisible = false;
                this.getOrderInfo();
            },
            //问题类型的弹框picker值发生改变
            onValuesChange(picker, values) {
                this.questionTypeVal = values[0];
            },
            selectData() {
                if (this.newSelectedValue) {
                    this.dateVal = this.newSelectedValue
                } else {
                    this.dateVal = new Date()
                }
                this.$refs['datePicker'].open()
            },
            formatDate(secs) {
                var t = new Date(secs)
                var year = t.getFullYear()
                var month = t.getMonth() + 1
                if (month < 10) { month = '0' + month }
                var date = t.getDate()
                if (date < 10) { date = '0' + date }
                var hour = t.getHours()
                if (hour < 10) { hour = '0' + hour }
                var minute = t.getMinutes()
                if (minute < 10) { minute = '0' + minute }
                var second = t.getSeconds()
                if (second < 10) { second = '0' + second }
                return year + '-' + month + '-' + date
            }
        }
    }
</script>
<style scoped>
    .order {
        background-color: #ebecf7;
        min-height: 100%;
    }

    .top_order_title {
        font-size: 1.6rem;
        font-weight: 600;
        padding: 2rem;
        float: left;
    }

    .top_msg {
        float: right;
    }

    .items_d {
        margin: 3%;
        height: 6rem;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .item_bg {
        background-image: url('../../static/images/item_bg.png');
        width: 68%;
        height: 100%;
        text-align: left;
        float: left;
    }

    .item_add {
        width: 32%;
        float: left;
        height: 100%;
    }

    .itemTitle {
        padding: 2%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .sign {
        text-align: center;
        margin: 10px;
        color: white;
        padding: 15px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('../../static/images/plat_top.png');
    }

    .plotName {
        position: absolute;
        top: 14%;
        left: 10%;
        color: #fff;
        font-size: 1.6rem;
    }

    .plotCode {
        position: absolute;
        top: 19%;
        left: 10%;
        color: #fff;
        font-size: 1rem;
    }

    .plotCard {
        position: absolute;
        top: 14%;
        right: 8%;
        color: #fff;
    }

    .plotFactory {
        position: absolute;
        top: 30%;
        left: 10%;
        color: #fff;
    }

    .plotWorkshop {
        position: absolute;
        top: 34%;
        left: 10%;
        color: #fff;
    }

    .plotMitosome {
        position: absolute;
        top: 34%;
        left: 35%;
        color: #fff;
    }

    .plotTime {
        background: url('../../static/images/search_time.png');
        width: 80%;
        height: 2.5rem;
        margin-top: 1rem;
        margin-left: 5%;
        text-align: left;
        padding-left: 10%;
        padding-top: 1rem;
        font-size: 1.2rem;
    }

    .orderType {
        background: url('../../static/images/type_bg.png');
        background-size: 100% 100%;
        width: 22%;
        padding-left: 6%;
        height: 2.5rem;
        position: relative;
        background-repeat: no-repeat;
        margin-top: 1rem;
        margin-left: 5%;
        display: flex;
        align-items: center;
        text-align: center;
    }

    .orderType1 {
        background: url('../../static/images/type_bg.png');
        background-size: 100% 100%;
        width: 50%;
        padding-left: 6%;
        height: 2.5rem;
        position: relative;
        background-repeat: no-repeat;
        margin-top: 1rem;
        margin-left: 5%;
        display: flex;
        align-items: center;
        text-align: center;
    }

    #peopleChorseT {
        position: absolute;
        width: 100%;
        top: 1.17rem;
        height: 0.6rem;
    }

    /**问题类型弹框样式 */
    .picker-toolbar-title {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: center;
        background-color: #eee;
        height: 44px;
        line-height: 44px;
        font-size: 16px;
    }

    .usi-btn-cancel,
    .usi-btn-sure {
        color: #26a2ff;
        font-size: 16px;
    }

    .popup-div {
        width: 100%;
    }

    .pro-report {
        background: url('../../static/images/clbb.png');
        background-size: 100% 100%;
        height: 3.5rem;
        font-size: 1.4rem;
        margin-left: 5%;
        width: 90%;
        color: #fff;
        display: flex;
        padding-left: 10%;
        justify-content: left;
        align-items: center;
    }

    .sc_date {
        background: url('../../static/images/date_bg.png');
        background-size: 100% 100%;
        margin-left: 15%;
        margin-top: 5%;
        margin-bottom: 5%;
        height: 2.5rem;
        display: flex;
        align-items: center;
        font-size: 1rem;
        width: 64%;
        border-radius: 10px;
    }

    .rq_date {
        background: url('../../static/images/rq_bg.png');
        background-size: 100% 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22%;
        color: #fff;
        float: left;
    }

    .date_work {
        height: 50%;
        width: 68%;
        color: #888;
        float: left;
    }

    .right_jt {
        background: url('../../static/images/right_jt.png');
        background-size: 100% 100%;
        float: left;
        width: 6%;
        height: 60%;
    }

    .pool {
        margin-left: 5%;
        height: 3.5rem;
        margin-bottom: 5%;
        font-size: 1rem;
        width: 90%;
    }

    .zbPool {
        background: url('../../static/images/zbPool.png');
        background-size: 100% 100%;
        float: left;
        width: 23%;
        height: 100%;
    }

    .ybPool {
        background: url('../../static/images/ybPool.png');
        background-size: 100% 100%;
        float: left;
        width: 23%;
        height: 100%;
    }

    .mid {
        width: 54%;
        height: 100%;
        float: left;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .midPool {
        background: url('../../static/images/midPool.png');
        background-size: 100% 100%;
        width: 35%;
        height: 100%;
    }

    .ycl-style {
        color: crimson;
    }
    .menu_order_item {
        padding: 3%;
        float: left;
        width: 48%;
    }

    .menu_order {
        width: 100%;
        height: 11rem;
    }

    .menu_order_more_item {
        float: left;
        height: 100%;
        width: 25%;
    }
</style>