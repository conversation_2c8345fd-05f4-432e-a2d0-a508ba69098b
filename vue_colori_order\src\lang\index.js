import Vue from "vue";
import VueI18n from "vue-i18n";

import { getLanguage } from "../utils/cookie";

// Vant built-in lang
import { Locale } from 'vant';
import enUS from "vant/es/locale/lang/en-US";
import zhCN from "vant/es/locale/lang/zh-CN";

// User defined lang
import enUsLocale from "./en";
import zhCnLocale from "./zh";

Vue.use(VueI18n);

const messages = {
  'zh-CN': {
    ...zhCN,
    ...zhCnLocale
  },
  'en-US': {
    ...enUS,
    ...enUsLocale
  },
};

export const getLocale = () => {
  const cookieLanguage = getLanguage();
  if (cookieLanguage) {
    document.documentElement.lang = cookieLanguage;
    return cookieLanguage;
  }

  const language = navigator.language.toLowerCase();
  const locales = Object.keys(messages);
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      document.documentElement.lang = locale;
      return locale;
    }
  }

  // Default language is english
  return "zh-CN";
};

const CURRENT_LANG = getLocale();
// first entry
Locale.use(CURRENT_LANG, messages[CURRENT_LANG])

const i18n = new VueI18n({
  locale: CURRENT_LANG,
  messages
});

export default i18n;

