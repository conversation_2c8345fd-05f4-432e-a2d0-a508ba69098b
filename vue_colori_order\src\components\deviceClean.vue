<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">清洗</div>

        <van-field label="编号" v-model="tankNo" />

        <van-button type="info" @click="searchTank()" style="margin:10px;width:40%;border-radius:10px;">查询</van-button>
        <van-button type="primary" @click="scanTank()" style="margin:10px;width:40%;border-radius:10px;">扫码</van-button>

                <template v-if='deviceInfo.id'>
                <div v-if='deviceInfo.tankType == "设备/设施" || deviceInfo.tankType == "工器具"'>
                <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">工器具信息</div>
                <van-field label="账套" :value="deviceInfo.book" readonly />
                <van-field label="车间" :value="deviceInfo.workshop" readonly />
                <van-field label="工具编号" :value="deviceInfo.deviceNo" readonly />
                <van-field label="工具名称" :value="deviceInfo.deviceName" readonly />
                <van-field label="工具类型" :value="deviceInfo.tankType" readonly />

                <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">{{deviceInfo.tankType}}洗消状态标识卡</div>
                <van-field label="清洁消毒时间" :value="deviceInfo.recCleanTime" readonly />
                <van-field v-if="deviceInfo.tankType=='设备/设施'" label="空置有效期" :value="deviceInfo.recFreeTime" readonly />
                <van-field label="使用失效时间" :value="deviceInfo.recUseTime" readonly />
                <van-field label="操作人" :value="deviceInfo.recCleanName" readonly />
                <van-field label="复核人" :value="deviceInfo.recCheckName" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 1" value="闲置" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 2" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 3" value="使用中" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 4" value="待维修" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 5" value="待清洗" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 6" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 7" value="待复核" readonly />
            </div>
            <div v-else>
                <van-field label="坦克名称" :value="deviceInfo.tankName" readonly />
                <van-field label="坦克编号" :value="deviceInfo.tankNo" readonly />
                <van-field label="坦克类型" v-if="deviceInfo.tankType == '1'" :value="'坦克'" readonly />
                <van-field label="坦克类型" v-if="deviceInfo.tankType == '2'" :value="'青桶'" readonly />
                <van-field label="坦克类型" v-if="deviceInfo.tankType == '3'" :value="'储罐'" readonly />
                <van-field label="坦克类型" v-if="deviceInfo.tankType == '4'" :value="'折叠桶'" readonly />
                <van-field label="坦克类型" v-if="deviceInfo.tankType == '101'" :value="'次品桶'" readonly />
                <template v-if='deviceInfo.tankType==1'>
                <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">移动储罐洗消状态标识卡
                </div>
                <van-field label="清洗消毒时间" :value="deviceInfo.recCleanTime" readonly />
                <van-field label="空置有效期" :value="deviceInfo.recFreeTime" readonly />
                <van-field label="第一次装料日期" :value="deviceInfo.recChargeTime" readonly />
                <van-field label="连续使用有效期" :value="deviceInfo.recUseTime" readonly />
                <van-field label="操作人" :value="deviceInfo.recCleanName" readonly />
                <van-field label="复核人" :value="deviceInfo.recCheckName" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 1" value="闲置" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 2" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 3" value="使用中" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 4" value="待维修" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 5" value="待清洗" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 6" value="占用" readonly />
                <van-field label="状态" v-if="deviceInfo.status == 7" value="待复核" readonly />
                </template>

            </div>
            <div style="padding:10px;font-weight:800;text-align:left;font-size:16px;background:#f0f0f0">操作</div>
            <van-field v-if="deviceInfo.status == 1&&(deviceInfo.tankType == '设备/设施' || deviceInfo.tankType == '工器具')" label="点击使用" value="" readonly @click='deviceFun("use")' />
            <van-field label="点击清洗" value="" readonly @click='deviceFun("clean")' />
            <van-field label="连续使用" value="" readonly @click='deviceFun("continuous")' />
            <van-field v-if="deviceInfo.status == 7" label="点击复核" value="" readonly @click='deviceFun("check")' />
            </template>

    </div>
</template>
<script>

import { DatetimePicker, Indicator, Toast, MessageBox } from 'mint-ui';
import { Dialog } from 'vant';

let wx = window.wx

export default {
    data() {
        return {
            tankNo: "",
            deviceInfo: {}
        }
    },
    methods: {

        deviceFun(type) {
            if (!this.deviceInfo.id) {
                Toast({
                    message: '请先扫码获取信息',
                    position: "bottom",
                    duration: 2000
                });
                return
            }
            let self = this
            if (type == 'use') {
                Dialog.confirm({
                    message: "确定使用吗?",
                    theme: "round-button",
                    confirmButtonColor: "#1989fa",
                    cancelButtonColor: "#CCCCCC"
                })
                    .then(() => {
                        Indicator.open({
                            text: "处理中，请稍后……",
                            spinnerType: "fading-circle"
                        });
                        this.$axios.get(`/jeecg-boot/app/gcTankInfo/getDeviceUse?id=${self.deviceInfo.id}&userName=${localStorage.getItem("userName")}&userCode=${localStorage.getItem("userCode")}`)
                            .then(res => {
                                Indicator.close();
                                if (res.data.code == 200) {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.searchTank()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });

                    })
                    .catch(() => {
                        Toast({
                            message: "取消",
                            position: "bottom",
                            duration: 1000
                        });
                    });
            } else if (type == 'clean') {
                Dialog.confirm({
                    message: "确定清洗吗?",
                    theme: "round-button",
                    confirmButtonColor: "#1989fa",
                    cancelButtonColor: "#CCCCCC"
                })
                    .then(() => {
                        this.$axios
                            .get(`/jeecg-boot/app/gcTankInfo/getTankClean?id=${self.deviceInfo.id}&type=${self.deviceInfo.type}&userName=${localStorage.getItem("userName")}&userCode=${localStorage.getItem("userCode")}`).then(res => {
                                Indicator.close();
                                if (res.data.code == 200) {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.searchTank()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });

                    })
                    .catch(() => {
                        Toast({
                            message: "取消",
                            position: "bottom",
                            duration: 1000
                        });
                    });
            } else if (type == 'check') {
                Dialog.confirm({
                    message: "确定清洗复核吗?",
                    theme: "round-button",
                    confirmButtonColor: "#1989fa",
                    cancelButtonColor: "#CCCCCC"
                })
                    .then(() => {
                        this.$axios
                            .get(`/jeecg-boot/app/gcTankInfo/getTankCleanCheck?id=${self.deviceInfo.id}&type=${self.deviceInfo.type}&userName=${localStorage.getItem("userName")}&userCode=${localStorage.getItem("userCode")}`)
                            .then(res => {
                                Indicator.close();
                                if (res.data.code == 200) {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.searchTank()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    })
                    .catch(() => {
                        Toast({
                            message: "取消",
                            position: "bottom",
                            duration: 1000
                        });
                    });
            } else if (type == 'continuous') {
                Dialog.confirm({
                    message: "确定连续使用吗?",
                    theme: "round-button",
                    confirmButtonColor: "#1989fa",
                    cancelButtonColor: "#CCCCCC"
                })
                    .then(() => {
                        this.$axios
                            .get(`/jeecg-boot/app/gcTankInfo/getTankContinuous?id=${self.deviceInfo.id}&type=${self.deviceInfo.type}&userName=${localStorage.getItem("userName")}&userCode=${localStorage.getItem("userCode")}`)
                            .then(res => {
                                Indicator.close();
                                if (res.data.code == 200) {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    this.searchTank()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    })
                    .catch(() => {
                        Toast({
                            message: "取消",
                            position: "bottom",
                            duration: 1000
                        });
                    });
            }
        },
        searchTank() {
            let self = this
            self.getDeviceInfo(self.tankNo)
        },
        scanTank() {
            let self = this
            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function (res) {
                    self.getDeviceInfo(res.resultStr)
                },
                error: function (res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });

        },

        getDeviceInfo(id) {
            let self = this;
            if(!id){
                self.deviceInfo={}
                return
            }
            self.$axios.get('/jeecg-boot/app/gcTankInfo/getDeviceInfo', { params: { id: id } }).then(res => {
                if (res.data.code == 200) {
                    self.deviceInfo = res.data.result
                    console.log(self.deviceInfo.tankType == '设备/设施' || self.deviceInfo.tankType == '工器具');
                    if(self.deviceInfo.tankType=='1' ){
                        self.deviceInfo.type='坦克'
                        
                    }
                }
            })
        },
    }
}
</script>
<style scoped>
.order {
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
</style>