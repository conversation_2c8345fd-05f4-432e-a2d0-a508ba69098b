<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="新增" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-form validate-first @submit="onSubmit" ref="form">
            <!-- <van-field readonly clickable name="picker" v-model="formData.book" label="账套：" placeholder="点击选择账套"
                @click="showbookNamePicker = true" />
            <van-popup v-model="showbookNamePicker" position="bottom">
                <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                    @cancel="showbookNamePicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="formData.workshop" label="车间：" placeholder="点击选择车间"
                @click="showworkshopPicker = true" />
            <van-popup v-model="showworkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                    @cancel="showworkshopPicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="formData.mitosome" label="工作中心：" placeholder="点击选择工作中心"
                @click="showjobCenterPicker = true" />
            <van-popup v-model="showjobCenterPicker" position="bottom">
                <van-picker show-toolbar :columns="jobCenterColumns" @confirm="jobCenterConfirm"
                    @cancel="showjobCenterPicker = false" />
            </van-popup> -->

            <van-cell title="点击扫码" @click="handleSearch()" style="width: 90%; " />

            <van-field readonly v-model="formData.book" name="content" rows="1" label="账套：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.workshop" name="content" rows="1" label="车间：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.mitosome" name="content" rows="1" label="工作中心：" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.machineId" name="content" rows="1" label="设备ID" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.machineNo" name="content" rows="1" label="设备编码" type="text"
                :rules="validateRule" />
            <van-field readonly v-model="formData.machineName" name="content" rows="1" label="设备名称" type="text"
                :rules="validateRule" />
            <van-field v-model="formData.currentSituation" name="remarks" rows="3" autosize label="当前状况" type="textarea"
                placeholder="请输入" :rules="validateRule" />
            <van-field v-model="formData.expectSituation" name="remarks" rows="3" autosize label="预计情况" type="textarea"
                placeholder="请输入" :rules="validateRule" />

            <van-field readonly clickable name="picker" v-model="formData.type" label="改进类型：" placeholder="点击选择改进类型"
                @click="showTypePicker = true" :rules="validateRule" />
            <van-popup v-model="showTypePicker" position="bottom">
                <van-picker show-toolbar :columns="typeColumns" @confirm="typeConfirm"
                    @cancel="showTypePicker = false" />
            </van-popup>

            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="formData.uploader" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" :max-count="2" />
                </template>
            </van-field>

        </van-form>


        <van-button type="info" @click="addParts" size="mini" style="width:100%">扫码添加备件</van-button>
        <div v-for="(v, index) in detailList" :key="index" style="margin-top:2rem;">
            <van-row>

                <van-col span="24">
                    <van-button style="width: 100%;" size="mini" @click="handleDel(index)" plain>删除</van-button>
                </van-col>
            </van-row>
            <a-row>
                <a-col :span="6" style="line-height: 2rem;margin-left:1rem;"> 备件 </a-col>
                <a-col :span="16">
                    <a-select placeholder="请选择备件" style="width: 100%" v-model="v.partsId">
                        <!-- :disabled="partsListRes.some(v => v.partsId === item.id)" -->
                        <a-select-option v-for="(item, index) in v.arr" :value="item.id" :key="index">
                            {{ item.name }}--数量:({{ item.stock }})
                        </a-select-option>
                    </a-select>
                </a-col>
            </a-row>
            <van-field v-model="v.expectCount" name="partsCount" label="预计数量" placeholder="请输入" type="number" />
        </div>


        <van-button type="primary" style="width:100%;margin-top:2rem;" @click="onSubmit">提交</van-button>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from "mint-ui";
export default {

    data() {
        return {
            info: {},
            formData: {},

            validateRule: [
                { required: true, message: '不能为空', trigger: 'onBlur' },
            ],

            showbookNamePicker: false,
            bookNameColumns: [],

            showworkshopPicker: false,
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenterColumns: [],
            showTypePicker: false,
            typeColumns: ['生产维修', '设备项目'],

            pictureList: [],

            detailList: [],
        };
    },
    created() {
        this.info = this.$route.params
        console.log("🚀 ~ created ~   this.$route.params:", this.$route.params)
        this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
            if (res.data.code == 200) {
                console.log(res.data.result);
                res.data.result.forEach(item => {
                    this.bookNameColumns.push(item.name);
                });
            } else {
            }
        });
    },
    methods: {

        handleDel(i) {
            this.detailList.splice(i, 1);
        },
        addParts() {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果

                                self.getParts(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入备件编号").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getParts(self.code)
                            }
                        });
                    }
                });
        },
        getParts(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/ncApp/parts/getPartsListByCode?code=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        self.detailList.push({
                            arr: res.data.result
                        })
                    } else {
                        console.log(res.data);
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        handleSearch(v) {
            let self = this;
            self.partsCount = 0
            MessageBox.confirm("", {
                message: "请选择扫码或者录入",
                title: "提示",
                confirmButtonText: "扫码",
                cancelButtonText: "录入"
            })
                .then(action => {
                    if (action == "confirm") {
                        wx.scanQRCode({
                            desc: "scanQRCode desc",
                            needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                            scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                            success: function (res) {
                                // 回调
                                var result = res.resultStr; //当needResult为1时返回处理结果
                                self.getList(result)
                            },
                            error: function (res) {
                                if (res.errMsg.indexOf("function_not_exist") > 0) {
                                    alert("版本过低请升级");
                                }
                            }
                        });
                    }
                })
                .catch(res => {
                    if (res == "cancel") {
                        MessageBox.prompt("请录入设备ID").then(({ value, action }) => {
                            if (action == "confirm") {
                                self.code = value;
                                self.getList(self.code)
                            }
                        });
                    }
                });
        },
        getList(code) {
            let self = this
            self.$axios
                .get(
                    `/jeecg-boot/app/device/list?id=${code}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result.records[0]);
                        this.$set(self.formData, "machineName", res.data.result.records[0].deviceName)
                        this.$set(self.formData, "machineId", res.data.result.records[0].id)
                        this.$set(self.formData, "machineNo", res.data.result.records[0].deviceNo)
                        this.$set(self.formData, "book", res.data.result.records[0].bookName)
                        this.$set(self.formData, "workshop", res.data.result.records[0].workshopName)
                        this.$set(self.formData, "mitosome", res.data.result.records[0].jobCenter)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        bookNameConfirm(value) {
            this.formData.book = value;
            this.formData.workshop = '';
            this.formData.jobCenter = '';
            localStorage.setItem('feedingCheckBook', this.bookName)
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value }
                })
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.workshopColumns = []
                        res.data.result.forEach(item => {
                            this.workshopColumns.push(item.name);
                        });
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.formData.workshop = value;
            this.formData.mitosome = '';
            this.showworkshopPicker = false;
            // 查找工作中心
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
                    params: { book: this.bookName, workshop: value }
                })
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach(item => {
                            this.jobCenterColumns.push(item.jobCenter);
                        });
                    } else {
                    }
                });
        },
        jobCenterConfirm(value) {
            this.formData.mitosome = value;
            this.showjobCenterPicker = false;
        },
        typeConfirm(value) {
            this.formData.type = value;
            this.showTypePicker = false;
        },
        onSubmit() {
            this.$refs.form
                .validate()
                .then((res) => {
                    //  取消备件必选 


                    // if (this.detailList.length == 0) {
                    //     Toast({
                    //         message: "至少添加一个备件",
                    //         position: "bottom",
                    //         duration: 2000
                    //     });
                    //     return
                    // } else {
                        // this.detailList.forEach(item => {
                        //     if (!/^[0-9]*[1-9][0-9]*$/.test(item.expectCount)) {
                        //         Toast({
                        //             message: "请输入大于0的整数！",
                        //             position: "bottom",
                        //             duration: 2000
                        //         });
                        //         return;
                        //     }
                        // })
                        let param = {
                            ...this.formData,
                            detailList: this.detailList,
                            pictureList: this.pictureList,
                            userCode: localStorage.getItem('userCode'),
                            userName: localStorage.getItem('userName')
                        }
                        Indicator.open({
                            text: "处理中，请稍后……",
                            spinnerType: "fading-circle"
                        });
                        this.$axios
                            .post(`/jeecg-boot/app/improve/addMacImproveMain`, param)
                            .then(res => {
                                if (res.data.code == 200) {
                                    Indicator.close();
                                    this.$router.go(-1);
                                } else {
                                    Indicator.close();
                                    Toast({
                                        message: "操作失败",
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    // }

                })
                .catch((err) => {
                    Toast({
                        message: "未填写完成",
                        position: "bottom",
                        duration: 2000
                    });
                });

        },
        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: "文件大小不能超过 10M",
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData();
            param.append("file", file.file);
            param.append("description", "");
            param.append("type", "");
            this.$axios.post(`/jeecg-boot/app/improve/uploadPic`, param).then(res => {
                if (res.data.code == 200) {
                    console.log(res);
                    this.pictureList.push({ picUrl: res.data.message });
                } else {
                    this.uploader.splice(name.index, 1);
                    Toast({
                        message: "上传失败,请选择图片上传",
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        //删除图片
        beforeDel(file, name) {
            Dialog.confirm({
                message: "确定删除吗?",
                theme: "round-button",
                confirmButtonColor: "#1989fa",
                cancelButtonColor: "#CCCCCC"
            })
                .then(() => {
                    Indicator.open({
                        text: "处理中，请稍后……",
                        spinnerType: "fading-circle"
                    });
                    this.$axios
                        .delete(
                            `/jeecg-boot/app/mac/deletePic?id=${""}&picUrl=${this.pictureList[name.index].picUrl
                            }`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Indicator.close();
                                Toast({
                                    message: "删除成功",
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.formData.uploader.splice(name.index, 1);
                                this.pictureList.splice(name.index, 1);
                            } else {
                                Indicator.close();
                                this.formData.uploader.splice(name.index, 1);
                                Toast({
                                    message: "删除失败",
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });

                })
                .catch(() => {
                    Toast({
                        message: "取消",
                        position: "bottom",
                        duration: 1000
                    });
                });
        },
    }
};
</script>

<style scoped></style>