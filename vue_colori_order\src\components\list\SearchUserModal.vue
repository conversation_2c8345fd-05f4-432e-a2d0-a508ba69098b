<template>
  <a-modal :title="'查询人员'" :width="500" :visible="visible" okText="确定" cancelText="取消" :confirmLoading="confirmLoading"
    @ok="handleOk" @cancel="handleCancel">



    <div class="shopContent">
      <a-input-search v-model="searchValue" placeholder="请输入" @search="handleSearch" />
      <a-radio-group v-model="selectUser">
        <a-radio v-for="item in userList" :key="item.llqUserCode"  :value="item.staffName+'-'+item.llqUserCode" >
          <h4>{{item.staffName}}-{{item.llqUserCode}}</h4>
          <h5>{{item.ncDepartmentCode_dictText}}</h5>
        </a-radio>
      </a-radio-group>
    </div>
    <div v-show="!userList.length" class="shopping">
      <div><img src="../../../static/images/nothing.png" alt=""></div>
      <p>暂无数据</p>
    </div>


  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import Vue from 'vue'
import { DatetimePicker, Toast, MessageBox } from 'mint-ui';
export default {
  name: "TankModal",
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      searchValue: '',
      userList: [],
      selectUser:'',
    }
  },
  methods: {
    handleSearch() {
      let self = this;
      self.$axios
        .get('/jeecg-boot/app/staff/getAppStaffList', {
          params: {
            userName: 'cWl5dW5fYWRtaW5fMjAyMjEwMTI=',
            secName: self.searchValue,
          }
        })
        .then(res => {
          if (res.data.code = 200) {
            console.log(res);
            self.userList = res.data.result.records
          } else {
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        });
    },

    edit(record) {
      console.log(record);
      this.searchValue=''
      let self = this;
      self.visible = true;
    },
    close() {
      this.$emit('close');
      this.visible = false;
    },
    handleOk() {
      if (!this.selectUser) {
        Toast({
          message: "请选择人员",
          position: 'bottom',
          duration: 2000
        });
        return;
      } else {
        this.$emit('ok', this.selectUser);
        this.close()
      }
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>

<style scoped>
.ant-btn {
  padding: 0 10px;
  margin-left: 3px;
}

.ant-form-item-control {
  line-height: 0px;
}

/** 主表单行间距 */
.ant-form .ant-form-item {
  margin-bottom: 10px;
}

/** Tab页面行间距 */
.ant-tabs-content .ant-form-item {
  margin-bottom: 0px;
}

.fontColor {
  color: black;
}
</style>
<style lang="less" scoped>
.van-submit-bar {
  bottom: 49px;
  padding-left: 20px;

}

.shopContent {
  margin-top: 10px;
  padding-bottom: 20px;
  height: 400px;
  overflow: auto;
}

.shopping {
  text-align: center;

  img {
    width: 96px;
    height: 96px;
    margin-bottom: 25px;
  }
}

li {
  padding: 0 15px;
  background: #ffffff;
  margin-bottom: 10px;
  position: relative;
  height: 103px;

  .shopmain {
    display: flex;
    padding: 10px 8px 10px 10px;
    position: relative;

    .shops {
      display: flex;
      margin-left: 5%;

      .shopImg {
        width: 103px;
        height: 83px;
        margin: 0 7px 0 11px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .shopsright {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        h4 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-align: left;
        }

        .shoprightbot {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 260px;

          span {
            font-size: 17px;
            color: #F74022;
          }
        }
      }
    }

    .van-checkbox__icon--checked .van-icon {
      background: red !important;
    }
  }

  button {
    width: 24px;
    height: 26px;
    font-size: 20px;
    background: #F74022;
    color: #ffffff;
    border: none;
  }

  input {
    width: 48px;
  }
}

.shopradd {
  width: 98px;
  display: flex;

  .van-field__control {
    text-align: center !important;
  }
}

.van-cell {
  padding: 0;
  line-height: 26px
}

.van-field__control {
  height: 26px;
}

/deep/.ant-modal-body {
  padding: 0px;
}
</style>