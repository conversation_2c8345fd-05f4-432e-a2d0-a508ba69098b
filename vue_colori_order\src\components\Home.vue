<template>
  <div>
    <!-- tabcontainer -->
    <mt-tab-container class="page-tabbar-container" v-model="selected">
      <mt-tab-container-item id="order">
          <order></order>
      </mt-tab-container-item>
      <mt-tab-container-item id="mine">
            <mine></mine>
      </mt-tab-container-item>
    </mt-tab-container>

    <mt-tabbar v-model="selected" fixed>
      <mt-tab-item id="order">
        <img slot="icon" src="../../static/images/tab_order.png">
      </mt-tab-item>
      <mt-tab-item id="mine">
        <img slot="icon" src="../../static/images/tab_mine.png">
      </mt-tab-item>
      
    </mt-tabbar>
  </div>
</template>

<script>
import Order from '@/components/Order'
import Mine from '@/components/Mine'
import { Toast } from 'mint-ui';
const CORPID="wx3c2e87bb2e0f524d"
  export default {
    components: {
      Order,
        Mine,
        Toast
    },
    data() {
      return {
        selected: 'order',
        userCode:'',
      };
    },
    created:function(){
      // this.login();

      
        // 清除localStorage
        localStorage.removeItem('SpareCheckItem')
        localStorage.removeItem('ModuleCheckItem')
        localStorage.removeItem('moduleReceiveItem')
        localStorage.removeItem('moduleListItem')
        localStorage.removeItem('repair')
        localStorage.removeItem('SpareCheckDetailEditItem')
        localStorage.removeItem('SpareListItem')
        localStorage.removeItem('ModuleCheckDetailEditItem')

    },
    methods:{
      login(){
        var local = window.location.href
        this.code = this.getUrlCode().code
        if (this.code == null || this.code === '') { // 如果没有code，则去请求
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${CORPID}&redirect_uri=${encodeURIComponent(local)}&response_type=code&scope=snsapi_base&state=123&connect_redirect=1#wechat_redirect`
          // window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(local)}&response_type=code&scope=snsapi_base&state=123#wechat_redirect`
        } else {
          // 你自己的业务逻辑
          console.log("code:"+this.code);
          //获取用户基本信息
          this.getUserBaseInfo(this.code);
        }
      },
      getUserBaseInfo(code){
        const self=this;
        const url="/jeecg-boot/car/getUserId"
        
        self.$axios.get(url,{params:{code:code}}).then(res=>{
          if(res.data.code===200){
            self.userCode=res.data.message;
            localStorage.setItem('userCode',self.userCode);
          }
        })
      },
      getUrlCode() { // 截取url中的code方法
        var url = location.search
        this.winUrl = url
        var theRequest = new Object()
        if (url.indexOf("?") != -1) {
          var str = url.substr(1)
          var strs = str.split("&")
          for(var i = 0; i < strs.length; i ++) {
            theRequest[strs[i].split("=")[0]]=(strs[i].split("=")[1])
          }
        }
        return theRequest
      }
    }
  };
</script>
<style>
.page-tabbar-container{
  height: 100%;
}
</style>