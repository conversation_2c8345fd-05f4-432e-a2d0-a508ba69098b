<template>

  <div>
    <van-sticky :offset-top="0">
      <van-nav-bar title="查看标签" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-row>
      <van-col span="8"></van-col>
      <van-col span="8">
        <div style="width: auto;" id="qrcode" ref="qrcode"></div>
      </van-col>
      <van-col span="8"></van-col>
    </van-row>
    <p>
      {{code}}
    </p>
    <p>
      {{name}}
    </p>
    <p>
      {{batchCode}}
    </p>
  </div>

</template>

<script>

  import QRCode from 'qrcodejs2'  // 引入qrcode

  export default {

    name: "qrcode",
    data() {
      return {
        text: '',
        code: '',
        name: '',
        batchCode: '',
      }
    },
    created() {
      console.log(this.$router.history.current.query);
      this.code = this.$router.history.current.query.code
      this.name = this.$router.history.current.query.name
      this.batchCode = this.$router.history.current.query.batchCode
      this.text = this.padString(this.code, 11, "@") + '+' + this.padString(this.batchCode, 11, "@");
    },
    methods: {
      onClickLeft() {
        this.$router.go(-1);
      },
      padString(str, length, padChar) {
        while (str.length < length) {
          str += padChar;
        }
        return str;
      },
      qrcode() {

        let qrcode = new QRCode('qrcode', {

          width: 132,

          height: 132,

          text: this.text, // 需要二维码跳转的地址

          colorDark: "white", //前景色

          colorLight: "black", //背景色

        })
        // qrcode.clear() //清除二维码 
        // qrcode.makeCode('http://www.4399.com') //生成另一个新的二维码
      },
    },
    mounted() {
      this.qrcode();
    },
  }
</script>
<style>

</style>