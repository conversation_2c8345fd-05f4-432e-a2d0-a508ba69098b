<template>
    <div>
        <img src="../../static/images/spare_part.png" width="100%" />
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/begin_record.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>备件清单</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('2')">
                <img src="../../static/images/work_check.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>备件出库</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('3')">
                <img src="../../static/images/work_time.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>备件安装</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('4')">
                <img src="../../static/images/order_more.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>备件退库</p>
            </div>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
        }
    },
    created() {

    },
    methods: {
        goDetail(i) {
            this.$axios
                .get(`/jeecg-boot/ncApp/parts/getDepartmentByUserCode?userCode=${localStorage.getItem('userCode')}`)
                .then(res => {
                    if (res.data.code == 200) {
                        console.log(res)
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });

            if (i == 1) {
                this.$router.push({ name: "SpareList" })
            } else if (i == 2) {
                this.$router.push({ name: "SpareOut" })
            } else if (i == 3) {
                this.$router.push({ name: "SpareSetup" })
            } else if (i == 4) {
                this.$router.push({ name: "SpareExit" })
            }
        },
    },
}
</script>

<style scoped>
.menu_order_more_item {
    float: left;
    height: 100%;
    width: 25%;
}

.menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}
</style>