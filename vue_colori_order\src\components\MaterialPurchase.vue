<template>
    <div style="background:#f3f4f6;min-height:100%">
        
        <div v-for="(item,index) in reports" :key="index" style="padding:8px;">


            <div style="text-align:left;margin-bottom:12px;" v-for="(temp,key) in item.bomList" :key="key">
                <div style="float:left;width:20%;text-align:left;">物料编码:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.malCode}}</div>
                <div style="float:left;width:20%;text-align:left;">物料名称:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.malName}}</div>
                <div style="float:left;width:20%;text-align:left;">采购数量:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.buynum}}</div>
                <div style="float:left;width:20%;text-align:left;">所属订单:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.orderId}}</div>
                <div style="float:left;width:20%;text-align:left;">所属编码:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.parentCode}}</div>
                <div style="float:left;width:20%;text-align:left;">所属产品:</div><div style="float:left;width:80%;text-align:left;font-weight:800;">{{temp.parentName}}</div>
                <div style="clear:both;"></div>
            </div>

            <div style="clear:both;"></div>


        </div>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            id:'',
            type:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            reports:[],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.type = this.$route.query.type
        this.id = this.$route.query.id
        this.getOrderMaterial();
    },
    methods: {
        getOrderMaterial(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getOrderMaterial',{params:{type:this.type,id:this.id}}).then(res=>{
                if(res.data.code==200){
                    self.reports=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getOrderDetail(status){
            let self=this
            self.$router.push({name:"OrderInfoDetail",params:{status:status}})
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png') no-repeat;
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
.menu_order2{
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
</style>