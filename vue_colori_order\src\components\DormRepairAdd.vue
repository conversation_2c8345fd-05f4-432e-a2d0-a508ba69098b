<template>
<div class="pageRepairAdd">
    <van-nav-bar fixed
        title="维修登记"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <van-form @submit="recordSubmit">
        <van-field label="提出人工号" name="提出人工号"
            v-model="rForm.userCode"
            placeholder="提出人工号"
            @input="inputCode"
            :rules="[{ required: true, message: '提出人工号必填' }]" />
        <van-field readonly right-icon="arrow" input-align="right"
                label="维修类型" name="维修类型"
                v-model="rForm.type"
                placeholder="维修类型"
                @focus="onSelectType"
                :rules="[{ required: true, message: '维修类型必选' }]"
            />
        <van-field label="维修内容" name="维修内容"
            v-model="rForm.content"
            placeholder="维修内容"
            rows="3" autosize type="textarea"
            :rules="[{ required: true, message: '维修内容必填' }]"
        />
        <van-cell title="照片" value="损坏物件照片" title-class="vanCellClass">
            <template #label>
                <van-uploader v-model="imgFiles"
                    accept="image/*"
                    :after-read="afterReadFiles"
                    @click-preview="onClickPreview" />
            </template>
        </van-cell>

        <van-cell title="固定设施" title-class="vanCellClass">
            <van-checkbox-group v-model="rForm.articleId" ref="checkboxGroup">
                <van-row>
                    <van-col span="24">
                        <van-checkbox style="margin-top:5%;" v-for="(item, index) in articleList" :key="index"
                            :name="item.value" shape="square">
                            {{ item.label }}
                        </van-checkbox>
                    </van-col>
                </van-row>
                
            </van-checkbox-group>
        </van-cell>
        
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
        </div>
    </van-form>

<!-- 类型Picker -->
<van-popup v-model="popTypeShow" position="bottom" :style="{ height: '45%' }">
    <van-picker title="维修类型" show-toolbar
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="onTypeCancel" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview} from 'vant'
export default {
    data(){
        return{
            rForm: {
                type: '',
                content: '',
                articleId: []
            },
            tabActive: "1",
            popTypeShow: false,
            typeColumns: ['设施维修','水电维修'],
            imgFiles: [], //上传文件列表
            confirmLoading: false,
            articleList: []
        }
    },
    methods:{
        recordSubmit(){
            this.confirmLoading=true
            for(let img of this.imgFiles){
                if(img.status=='failed'){
                    Toast("请删除上传失败文件后再提交")
                    this.confirmLoading=false
                    return false
                }
                if(img.status=='uploading'){
                    Toast("请在上传完成后再提交")
                    this.confirmLoading=false
                    return false
                }
            }
            let ids = this.rForm.articleId.join(',');
            let nameList = [];
            if(this.rForm.articleId && this.rForm.articleId.length > 0) {
                
                for(let i = 0; i < this.rForm.articleId.length; i++) {
                    for(let j = 0; j < this.articleList.length; j++) {
                        if(this.rForm.articleId[i] == this.articleList[j].value) {
                            nameList.push(this.articleList[j].label)
                        }
                    }
                }
            }
            let articleName = nameList.join(',');
            let params={
                userCode: this.rForm.userCode,
                type: this.rForm.type,
                content: this.rForm.content,
                pictureUrl: this.imgFiles.map(item=>item.url).join(","),
                userCodeRequest:localStorage.getItem('userCode'),
                articleId: ids,
                articleName: articleName
            }
            console.log("/dormApi/maintain/app/maintainRecordManagerAdd",params)
            this.$axios.post('/dormApi/maintain/app/maintainRecordManagerAdd', params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.rForm={}
                        this.imgFiles=[]
                        this.$router.go(-1)
                    }else{
                        Toast.fail(res.message)
                        this.confirmLoading=false
                        console.log("ERROR",res)
                    }
                }else{
                    Toast.fail("发生错误")
                    this.confirmLoading=false
                    console.log("error",rtn)
                }
            })
        },
        afterReadFiles(file){
            console.log(file)
            let rParams=new FormData();
            if(file.constructor != Array){
                if(file.file.type.indexOf('image')==-1){
                    Toast.fail('请上传图片文件')
                    file.status='failed'
                    file.message='上传失败'
                    return
                }
                file.status='uploading'
                file.message='点击可取消'
                rParams.append("file",file.file)
            }else{
                for(var i=0;i<file.length;i++){
                    let tmpFile=file[i]
                    if(tmpFile.file.type.indexOf('image')==-1){
                        Toast.fail('请上传图片文件')
                        tmpFile.status='failed'
                        tmpFile.message='上传失败'
                        continue
                    }
                    file.status='uploading'
                    file.message='点击可取消'
                    rParams.append("file",tmpFile.file)
                }
            }
            this.$axios.post('/dormApi/sys/common/upload', rParams).then(res => {
                console.log("upload Result", res)
                const result=res.data
                if(res.status==200 && result.success){
                    if(file.constructor != Array){
                        file.status=''
                        file.message=''
                        file.url=result.message
                    }else{

                    }
                }else{
                    Toast({
                        message: result.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onClickPreview(file){
            if(file.status==='uploading'){
                Dialog.confirm({
                title: '提示',
                message: '确认取消上传？',
                }).then(()=>{
                    file.status='failed'
                    file.message='取消上传'
                })
            }else if(file.status===''){
                if(file.file.type.indexOf('image')!=-1){
                    ImagePreview({
                        images: [`/dormApi/${file.url}`],
                        closeable: true
                    })
                }
            }
        },
        onTypeConfirm(pValue, pIndex){
            this.rForm.type=pValue
            this.popTypeShow=false
        },
        onTypeCancel(){
            this.rForm.type=''
            this.popTypeShow=false
        },
        onSelectType(){
            this.popTypeShow=true
        },
        onClickLeft(){
            this.$router.go(-1)
        },
        inputCode(value) {
            // let params = {
            //     userCode: value
            // }
            let that = this;
            this.$axios.get('/dormApi/dormitory/app/getArticleByRoom', {params:{userCode:value}}).then(rtn=>{
                console.log('rtn',rtn);
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        that.articleList = []
                        for(let i = 0; i < res.result.length; i++) {
                            that.articleList.push({
                                label: res.result[i].article,
                                value: res.result[i].id,
                            });
                        }
                        Toast.success(res.message)
                        
                    }else{
                        Toast.fail(res.message)
                        
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        }
    },
    created(){ },
    filters: {
        // fType(pType){
        //     switch(pType){
        //         case "1": return "投诉";
        //         case "2": return "建议";
        //         default: return pType;
        //     }
        // },
    }
}
</script>
<style scoped>
.pageRepairAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>