<template>
  <div style="text-align:left;padding-bottom: 1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar v-if="type == 1" title="拣货单" left-arrow right-text="转单" @click-left="onClickLeft"
        @click-right="turnSingle" />
      <van-nav-bar v-else title="拣货单" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-tabs v-model="activeName" color="#1989fa" sticky offset-top="46px" @click="onClick">
      <van-tab title="未拣货" name="1">
        <div v-if="type == 1" style="margin-bottom: 3rem;">
          <!-- <van-checkbox-group v-model="result" ref="checkboxGroup">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in dataArr1"
                clickable
                :key="index"
                style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0rem auto; margin-bottom: 3%;"
              >
                <template #default>
                  <div
                    class="van-hairline--bottom"
                    style="margin-bottom:0.3rem;"
                  >
                    <van-row>
                      <van-col
                        span="24"
                        style="overflow-x: auto;white-space:nowrap;"
                      >
                        <span
                          style="font-size:18px;font-weight: 700;color: #000;"
                        >
                          {{ item.name }}</span
                        >
                      </van-col>
                    </van-row>
                  </div>

                  <van-row>
                    <van-col span="12">
                      <span style="color:gary"
                        >编&emsp;码:
                        <span style="color:black;">{{ item.code }}</span></span
                      >
                    </van-col>
                    <van-col span="12">
                      <span style="color:gary"
                        >货位号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.rackName }}</span
                        ></span
                      >
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col
                      span="24"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        批次号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.customer }}
                        </span>
                      </span>
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="12"
                      ><span style="color:gary"
                        >应拣数量:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                        >
                          {{ item.respondCount }}
                        </span>
                      </span>
                    </van-col>
                    <van-col span="12"
                      >已拣数量:
                      <span
                        style="color:black;font-size:16px;font-weight: 700;"
                      >
                        {{ item.actualCount }}
                      </span></van-col
                    >
                  </van-row>
                  <div class="van-hairline--top" style="margin-top: 1rem;">
                    <van-row>
                      <van-col span="12">
                        实拣数量：<a-input-number
                          v-model="item.pickCount"
                          :min="0"
                          style="border: none;width: 4rem;outline: none;"
                        />
                      </van-col>
                      <van-col span="12">
                        缺拣数量：<a-input-number
                          v-model="item.defectCount"
                          :min="0"
                          style="border: none;width: 4rem;outline: none;"
                        />
                      </van-col>
                    </van-row>
                    <van-row>
                      <van-col span="24">
                        <van-field
                          v-model="item.defectRemark"
                          label="缺拣原因："
                          style="margin-left:-1rem;"
                        />
                      </van-col>
                    </van-row>
                  </div>
                </template>
                <template #icon>
                  <van-checkbox
                    :name="item"
                    ref="checkboxes"
                    style="margin-right: 1rem;"
                  />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group> -->
          <!-- <div style="height: 2rem; width: 100%">&nbsp;</div>
          <van-row
            style="
        background-color:#F1F1F1;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;"
            gutter="30"
          >
            <van-col span="4">
              <van-button
                :plain="plain"
                icon="success"
                type="info"
                round
                size="mini"
                @click="toggleAll"
              >
              </van-button>
            </van-col>
            <van-col span="11"></van-col>
            <van-col span="8">
              <van-button
                round
                style="height: 25px"
                type="info"
                size="large"
                @click="picking"
                loading-type="spinner"
              >
                拣货
              </van-button>
            </van-col>
          </van-row> -->
          <div v-for="item in dataArr1" :key="item.id"
            style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0rem auto; margin-bottom: 3%;overflow: hidden;box-shadow: 2px 2px 10px #666666;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span>
                </van-col>
              </van-row>
            </div>
            <van-row>
              <van-col span="12">
                <span style="color:gary">编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span>
              </van-col>
              <van-col span="12">
                <span style="color:gary">货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.rackName
                  }}</span></span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span>
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
            <van-col span="12">
              <span style="color:gary">
                应拣辅数量:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.respondFcount }}
                </span>
              </span>
            </van-col>
          </van-row>
            <van-row>
              <van-col span="12"><span style="color:gary">应拣数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">
                    {{ item.respondCount }}
                  </span>
                </span>
              </van-col>
              <van-col span="12">已拣数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.actualCount }}
                </span>
              </van-col>
            </van-row>
            <div class="van-hairline--top" style="margin-top: 1rem;">
              <van-row>
                <van-col span="12">
                  实拣数量：
                  <a-input-number v-model="item.pickCount" :min="0" size="large"
                    :max="item.respondCount * 1 - item.actualCount * 1" style="border: none;width: 5rem;outline: none;"
                    @change="e => pickCountChange(e, item)" @blur="pickCountBlur" />
                </van-col>
                <van-col span="12">
                  缺拣数量：
                  <a-input-number v-model="item.defectCount" :min="0" size="large"
                    :max="item.respondCount * 1 - item.actualCount * 1" style="border: none;width: 5rem;outline: none;"
                    @change="e => defectCountChange(e, item)" @blur="defectCountBlur" />
                </van-col>
              </van-row>
              <van-row v-show="item.defectCount > 0">
                <van-col span="24">
                  <van-field style="margin-left:-1rem;" v-model="item.remark" label="缺拣原因：" />
                </van-col>
              </van-row>
              <van-row>
                <van-col span="20"></van-col>
                <van-col span="4">
                  <van-button size="mini" icon="success" round hairline type="primary" @click="pick(item)">确认
                  </van-button>
                </van-col>
              </van-row>
            </div>
          </div>
        </div>
        <div v-if="type == 2" style="margin-bottom: 3rem;">
          <div v-for="(item, index) in dataArr1" clickable :key="index"
            style="text-align: left;background-color:#FFF;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span>
                </van-col>
              </van-row>
            </div>

            <van-row>
              <van-col span="12">
                <span style="color:gary">编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span>
              </van-col>
              <van-col span="12">
                <span style="color:gary">货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.rackName
                  }}</span></span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span>
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">应拣辅数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.respondFcount
                  }}</span></span>
              </van-col>
            
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">应拣数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.respondCount
                  }}</span></span>
              </van-col>
              <van-col span="12">
                <span style="color:gary">已拣数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.actualCount
                  }}</span></span>
              </van-col>
            </van-row>
          </div>
        </div>
      </van-tab>
      <van-tab title="已拣货" name="2">
        <div v-if="type == 1" style="margin-bottom: 3rem;">
          <!-- <van-checkbox-group v-model="result" ref="checkboxGroup">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in dataArr2"
                clickable
                :key="index"
              >
                <template #default>
                  <div
                    class="van-hairline--bottom"
                    style="margin-bottom:0.3rem;"
                  >
                    <van-row>
                      <van-col
                        span="24"
                        style="overflow-x: auto;white-space:nowrap;"
                      >
                        <span
                          style="font-size:18px;font-weight: 700;color: #000;"
                        >
                          {{ item.name }}</span
                        >
                      </van-col>
                    </van-row>
                  </div>

                  <van-row>
                    <van-col span="12">
                      <span style="color:gary"
                        >编&emsp;码:
                        <span style="color:black;">{{ item.code }}</span></span
                      >
                    </van-col>
                    <van-col span="12">
                      <span style="color:gary"
                        >货位号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.rackName }}</span
                        ></span
                      >
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="12">
                      <span style="color:gary"
                        >托&emsp;码:
                        <span style="color:black;">{{
                          item.stickerId
                        }}</span></span
                      >
                    </van-col>
                    <van-col
                      span="12"
                      style="overflow-x: auto;white-space:nowrap;"
                    >
                      <span style="color:gary">
                        批次号:
                        <span
                          style="color:black;font-size:16px;font-weight: 700;"
                          >{{ item.customer }}
                        </span>
                      </span>
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="12"
                      >应拣数量:<span
                        style="color:black;font-size:16px;font-weight: 700;"
                        >{{ item.respondCount }}
                      </span>
                    </van-col>
                    <van-col span="12"
                      >已拣数量:<span
                        style="color:black;font-size:16px;font-weight: 700;"
                        >{{ item.actualCount }}
                      </span></van-col
                    >
                  </van-row>
                  <van-row>
                    <van-col span="24" v-if="item.defectCount != 0"
                      >少拣数量:
                      <span
                        style="color:red;font-size:16px;font-weight: 700;"
                        >{{ item.defectCount }}</span
                      >
                    </van-col>
                  </van-row>
                  <van-row>
                    <van-col span="24" v-if="item.defectCount != 0">
                      少拣原因:{{ item.defectRemark }}
                    </van-col>
                  </van-row>
                  <van-field
                    style="margin-left:-1rem;"
                    v-model="item.backCount"
                    type="digit"
                    label="退库数量："
                  />
                  <van-field
                    style="margin-left:-1rem;"
                    v-model="item.remark"
                    label="退库原因："
                  />
                </template>
                <template #icon>
                  <van-checkbox
                    :name="item"
                    ref="checkboxes"
                    style="margin-right: 1rem;"
                  />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
          <div style="height: 2rem; width: 100%">&nbsp;</div>
          <van-row
            style="
        background-color:#F1F1F1; 
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;"
            gutter="30"
          >
            <van-col span="4">
              <van-button
                :plain="plain"
                icon="success"
                type="info"
                round
                size="mini"
                @click="toggleAll"
              >
              </van-button>
            </van-col>
            <van-col span="11"></van-col>
            <van-col span="8">
              <van-button
                round
                style="height: 25px"
                type="info"
                size="large"
                @click="pickingBack"
                loading-type="spinner"
              >
                退库
              </van-button>
            </van-col>
          </van-row> -->
          <div v-for="item in dataArr2" :key="item.id"
            style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0rem auto; margin-bottom: 3%;overflow: hidden;box-shadow: 2px 2px 10px #666666;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span>
                </van-col>
              </van-row>
            </div>
            <van-row>
              <van-col span="12">
                <span style="color:gary">编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span>
              </van-col>
              <van-col span="12">
                <span style="color:gary">货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.rackName
                  }}</span></span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span>
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12"><span style="color:gary">应拣辅数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">
                    {{ item.respondFcount }}
                  </span>
                </span>
              </van-col>
              <van-col span="12">应拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.respondCount }}
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">已拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.actualCount }}
                </span>
              </van-col>
              <van-col span="12">缺拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.defectCount }}
                </span>
              </van-col>
            </van-row>
            <van-row v-show="item.defectCount > 0">
              <van-col span="24"><span style="color:gary">缺拣原因:
                  <span style="color:black;font-size:16px;font-weight: 700;">
                    {{ item.defectRemark }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-field style="margin-left:-1rem;" v-model="item.backCount" type="digit" label="退库数量：" />
            <van-field v-show="item.backCount > 0" style="margin-left:-1rem;" v-model="item.remark" label="退库原因：" />
            <van-row>
              <van-col span="20"></van-col>
              <van-col span="4">
                <van-button size="mini" icon="revoke" round hairline type="warning" @click="back(item)">退库
                </van-button>
              </van-col>
            </van-row>
          </div>
        </div>

        <div v-if="type == 2" style="margin-bottom: 3rem;">
          <div v-for="(item, index) in dataArr2" clickable :key="index"
            style="text-align: left;background-color: #FFF;padding:3%;border-radius: 10px;width: 95%;margin: .3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
            <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 700;color: #000;">
                    {{ item.name }}</span>
                </van-col>
              </van-row>
            </div>

            <van-row>
              <van-col span="12">
                <span style="color:gary">编&emsp;码:
                  <span style="color:black;">{{ item.code }}</span></span>
              </van-col>
              <van-col span="12">
                <span style="color:gary">货位号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{
                      item.rackName
                  }}</span></span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">
                <span style="color:gary">托&emsp;码:
                  <span style="color:black;">{{ item.stickerId }}</span></span>
              </van-col>
              <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
                <span style="color:gary">
                  批次号:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{ item.customer }}
                  </span>
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12"><span style="color:gary">应拣辅数量:
                  <span style="color:black;font-size:16px;font-weight: 700;">
                    {{ item.respondFcount }}
                  </span>
                </span>
              </van-col>
              <van-col span="12">应拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.respondCount }}
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="12">已拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.actualCount }}
                </span>
              </van-col>
              <van-col span="12">缺拣主数量:
                <span style="color:black;font-size:16px;font-weight: 700;">
                  {{ item.defectCount }}
                </span>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="24" v-if="item.defectCount != 0">
                <span style="color:gary">
                  少拣原因:
                  <span style="color:black;font-size:16px;font-weight: 700;">{{ item.defectRemark }}
                  </span>
                </span>
              </van-col>
            </van-row>
          </div>
        </div>
      </van-tab>
      <van-tab title="已复核" name="3">
        <div v-for="(item, index) in dataArr3" clickable :key="index"
          style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0 auto;box-shadow: 2px 2px 8px #666666; margin-top: 1.5%;">
          <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
            <van-row>
              <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                <span style="font-size:18px;font-weight: 700;color: #000;">
                  {{ item.name }}</span>
              </van-col>
            </van-row>
          </div>

          <van-row>
            <van-col span="12">
              <span style="color:gary">编&emsp;码:
                <span style="color:black;">{{ item.code }}</span></span>
            </van-col>
            <van-col span="12">
              <span style="color:gary">货位号:
                <span style="color:black;font-size:16px;font-weight: 700;">{{
                    item.rackName
                }}</span></span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12">
              <span style="color:gary">托&emsp;码:
                <span style="color:black;">{{ item.stickerId }}</span></span>
            </van-col>
            <van-col span="12" style="overflow-x: auto;white-space:nowrap;">
              <span style="color:gary">
                批次号:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.customer }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12">
              <span style="color:gary">
                应拣辅数量:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.respondFcount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12">
              <span style="color:gary">
                应拣数量:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.respondCount }}
                </span>
              </span>
            </van-col>
            <van-col span="12">
              <span style="color:gary">
                实拣数量:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.pickCount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12">
              <span style="color:gary">
                缺拣数量:
                <span style="color:red;font-size:16px;font-weight: 700;">{{ item.defectCount }}
                </span>
              </span>
            </van-col>
            <van-col span="12">
              <span style="color:gary">
                复检缺数:
                <span style="color:red;font-size:16px;font-weight: 700;">{{ item.checkDefectCount }}
                </span>
              </span>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="12" v-if="item.checkDefectCount != 0">
              <span style="color:gary">
                复检缺少原因:
                <span style="color:black;font-size:16px;font-weight: 700;">{{ item.checkRemark }}
                </span>
              </span>
            </van-col>
          </van-row>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { Toast } from "vant";
import { Indicator } from 'mint-ui';
export default {
  data() {
    return {
      type: 1,
      activeName: "1",
      // 未拣货
      dataArr1: [],
      //已拣货
      dataArr2: [],
      //已复核
      dataArr3: [],
      info: {},
      result: [],
      plain: true
    };
  },
  created() {
    this.info = this.$route.params;
    this.info.userCode = localStorage.getItem("userCode");
    this.search();
  },
  methods: {
    pickCountBlur(e) {
      if (e.target.value == "" || e.target.value == null) {
        e.target.value = 0;
      }
    },
    defectCountBlur(e) {
      if (e.target.value == "" || e.target.value == null) {
        e.target.value = 0;
      }
    },
    pickCountChange(e, item) {
      item.pickCount = Math.round(e);
      e = Math.round(e);
      console.log(e, item);
      item.defectCount = item.respondCount * 1 - item.actualCount * 1 - e;
    },
    defectCountChange(e, item) {
      item.defectCount = Math.round(e);
      e = Math.round(e);
      console.log(e, item);
      item.pickCount = item.respondCount * 1 - item.actualCount * 1 - e;
    },
    search() {
      if (this.info.id) {
        this.$axios
          .get(
            `/jeecg-boot/app/warehousePick/getPickMainSun?id=${this.info.id
            }&userCode=${localStorage.getItem("userCode")}`
          )
          .then(res => {
            if (res.data.code == 200) {
              this.dataArr1 = [];
              this.dataArr2 = [];
              this.dataArr3 = [];
              res.data.result.forEach(item => {
                item.pickCount = item.respondCount * 1 - item.actualCount * 1;
                if (item.status == 1 || item.status == 4) {
                  item.defectCount = 0;
                  this.dataArr1.push(item);
                } else if (item.status == 2) {
                  item.backCount = 0;
                  this.dataArr2.push(item);
                } else if (item.status == 3) {
                  this.dataArr3.push(item);
                }
              });
              this.dataArr1.forEach(item => {
                if (item.pickerNo != localStorage.getItem("userCode")) {
                  this.type = 2;
                }
              });
              this.dataArr2.forEach(item => {
                if (item.pickerNo != localStorage.getItem("userCode")) {
                  this.type = 2;
                }
              });
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          });
      } else {
        this.$router.go(-1);
      }
    },
    pick(item) {

      if (item.defectCount == null) {
        item.defectCount = 0;
      }
      //
      if (item.status == 1) {
        if (
          item.respondCount * 1 !=
          item.pickCount * 1 + item.actualCount * 1 + item.defectCount * 1
        ) {
          Toast({
            message: "请检查数量是否正确",
            position: "bottom",
            duration: 1500
          });
          return;
        }
      } else if (item.status == 4) {
        Indicator.open({
          text: '正在加载中，请稍后……',
          spinnerType: 'fading-circle'
        });
        this.$axios
          .get(`/jeecg-boot/app/warehouse/getStockList?stockId=${item.stockId}`)
          .then(res => {
            Indicator.close();
            if (res.data.code == 200) {
              if (res.data.result * 1 < item.pickCount * 1) {
                Toast({
                  message:
                    "实际库存为" + res.data.result + ",现已超出",
                  position: "bottom",
                  duration: 1500
                });
                return;
              } else {
                if (
                  item.respondCount * 1 !=
                  item.pickCount * 1 +
                  item.actualCount * 1 +
                  item.defectCount * 1
                ) {
                  Indicator.close();
                  Toast({
                    message: "请检查数量是否正确",
                    position: "bottom",
                    duration: 1500
                  });
                  return;
                }
              }
              if (item.defectCount > 0 && !item.remark) {
                Indicator.close();
                Toast({
                  message: "请填写缺拣原因",
                  position: "bottom",
                  duration: 1500
                });
                return;
              }
              item.userCode = localStorage.getItem("userCode");
              Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
              });
              this.$axios
                .post(`/jeecg-boot/app/warehousePick/getPickSunOutput`, item)
                .then(res => {
                  if (res.data.code == 200) {
                    Indicator.close();
                    Toast({
                      message: res.data.message,
                      position: "bottom",
                      duration: 2000
                    });
                    this.search();
                  } else {
                    Indicator.close();
                    Toast({
                      message: res.data.message,
                      position: "bottom",
                      duration: 2000
                    });
                  }
                });
            }
          });
        return;
      }

      if (item.defectCount > 0 && !item.remark) {
        Indicator.close();
        Toast({
          message: "请填写缺拣原因",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      item.userCode = localStorage.getItem("userCode");
      this.$axios
        .post(`/jeecg-boot/app/warehousePick/getPickSunOutput`, item)
        .then(res => {
          if (res.data.code == 200) {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            this.search();
          } else {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    back(item) {

      if (item.backCount > 0 && !item.remark) {
        Toast({
          message: "请填写退库原因",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      if (item.backCount * 1 > item.actualCount * 1) {
        Toast({
          message: "退库数量不能大于已拣数量",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      if (item.backCount == 0) {
        Toast({
          message: "退库数量不能等于0",
          position: "bottom",
          duration: 1500
        });
        return;
      }
      Indicator.open({
        text: '正在加载中，请稍后……',
        spinnerType: 'fading-circle'
      });
      item.userCode = localStorage.getItem("userCode");
      this.$axios
        .post(`/jeecg-boot/app/warehousePick/getPickSunInput`, item)
        .then(res => {
          if (res.data.code == 200) {

            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            this.search();
          } else {

            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },

    // picking() {
    //   if (this.result.length == 0) {
    //     Toast({
    //       message: `至少选择一条记录`,
    //       position: "bottom",
    //       duration: 2000
    //     });
    //     return;
    //   }
    //   for (let i = 0; i < this.result.length; i++) {
    //     if (!this.result[i].checkerNo == localStorage.getItem("userCode")) {
    //       Toast({
    //         message: `您不是保管员不能拣货`,
    //         position: "bottom",
    //         duration: 2000
    //       });
    //       return;
    //     }
    //     if (
    //       !this.result[i].respondCount * 1 ==
    //       this.result[i].pickCount * 1 +
    //         this.result[i].actualCount * 1 +
    //         this.result[i].defectCount * 1
    //     ) {
    //       Toast({
    //         message: `数量不正确，请检查`,
    //         position: "bottom",
    //         duration: 2000
    //       });
    //       return;
    //     }
    //     if (
    //       this.result[i].defectCount != 0 &&
    //       this.result[i].defectRemark == ""
    //     ) {
    //       Toast({
    //         message: `缺拣必须填写原因`,
    //         position: "bottom",
    //         duration: 2000
    //       });
    //       return;
    //     }
    //   }
    //   this.info.detailList = this.result;
    //   this.$axios
    //     .post(`/jeecg-boot/app/warehousePick/getPickSunOutput`, this.info)
    //     .then(res => {
    //       if (res.data.code == 200) {
    //         Toast({
    //           message: res.data.message,
    //           position: "bottom",
    //           duration: 2000
    //         });
    //         this.$router.go(-1);
    //       } else {
    //         Toast({
    //           message: res.data.message,
    //           position: "bottom",
    //           duration: 2000
    //         });
    //       }
    //     });
    // },
    pickingBack() {
      if (this.result.length == 0) {
        Toast({
          message: `至少选择一条记录`,
          position: "bottom",
          duration: 2000
        });
        return;
      }
      for (let i = 0; i < this.result.length; i++) {
        if (!this.result[i].checkerNo == localStorage.getItem("userCode")) {
          Toast({
            message: `您不是保管员不能拣货`,
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (this.result[i].backCount * 1 > this.result[i].checkCount * 1) {
          Toast({
            message: `数量不正确请检查`,
            position: "bottom",
            duration: 2000
          });
          return;
        }
        if (this.result[i].backCount * 1 != 0 && this.result[i].remark == "") {
          Toast({
            message: `退库必须填写退库原因`,
            position: "bottom",
            duration: 2000
          });
          return;
        }
      }

      this.info.detailList = this.result;
      this.$axios
        .post(`/jeecg-boot/app/warehousePick/getPickSunInput`, this.info)
        .then(res => {
          if (res.data.code == 200) {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            this.$router.go(-1);
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    toggleAll() {
      if (this.activeName == 1) {
        if (this.result.length != this.dataArr1.length) {
          this.plain = false;
          this.$refs.checkboxGroup.toggleAll(true);
        } else {
          this.plain = true;
          this.$refs.checkboxGroup.toggleAll();
        }
      } else if (this.activeName == 2) {
        if (this.result.length != this.dataArr2.length) {
          this.plain = false;
          this.$refs.checkboxGroup.toggleAll(true);
        } else {
          this.plain = true;
          this.$refs.checkboxGroup.toggleAll();
        }
      }
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    onClickLeft() {
      this.$router.go(-1);
    },

    onClick(name, title) {
      this.activeName = name;
      this.result = [];
    },
    turnSingle() {
      this.$router.push({
        name: "GoodsPickTurn",
        params: this.info
      });
    }
  }
};
</script>

<style scoped>

</style>
