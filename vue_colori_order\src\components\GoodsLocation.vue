<template>
  <div style="padding-bottom: 1%;">
    <van-sticky :offset-top="0">
      <van-nav-bar
        :title="storeName + rackName"
        right-text="筛选"
        left-arrow
        @click-left="onClickLeft"
        @click-right="onClickRight"
      />
    </van-sticky>

    <van-popup v-model="show" position="bottom" :style="{ height: '50%' }">
      <van-field
        readonly
        clickable
        name="picker"
        v-model="bookName"
        label="账套："
        placeholder="点击选择账套"
        @click="showbookNamePicker = true"
      />
      <van-popup v-model="showbookNamePicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="bookNameColumns"
          @confirm="bookNameConfirm"
          @cancel="showbookNamePicker = false"
        />
      </van-popup>

      <van-field
        v-model="storeName"
        clearable
        label="仓库："
        placeholder="请输入仓库"
      />
      <van-field
        v-model="rackName"
        clearable
        label="货位："
        placeholder="请输入货位"
      />
      <van-field
        v-model="area"
        clearable
        label="区域："
        placeholder="请输入区域"
      />
      <van-field
        v-model="customer"
        clearable
        label="批次号："
        placeholder="请输入批次号"
      />
      <van-field
        v-model="stickerId"
        clearable
        label="托码："
        placeholder="请输入托码"
      />
      <van-field
        v-model="product"
        clearable
        label="产品："
        placeholder="请输入产品名称"
      />

      <van-button type="info" @click="search1" style="width: 90%;">
        搜索
      </van-button>
    </van-popup>
    <van-checkbox-group v-model="result" ref="checkboxGroup">
      <van-cell-group>
        <van-list
          v-model="load"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell
            v-for="(item, index) in goodsLocationList"
            clickable
            :key="index"
          >
            <div style="text-align: left; margin-left: 5%">
              <van-row>
                <van-col span="24" style="overflow-x: auto;white-space:nowrap;">
                  <span style="font-size:18px;font-weight: 600;color: #000;">
                    {{ item.name }}</span
                  >
                </van-col>
              </van-row>
              <van-row>
                <van-col span="12">产品编号: {{ item.code }}</van-col>
                <van-col span="12">规格: {{ item.spec }}</van-col>
              </van-row>
              <van-row>
                <van-col span="12">
                  结存主数量: {{ item.mainQuantity }}{{ item.mainUnit }}
                </van-col>
                <van-col span="12">
                  结存辅数量: {{ item.quantity }}{{ item.unit }}
                </van-col>
              </van-row>
              <van-row>
                <van-col span="12"> 托码: {{ item.stickerId }} </van-col>
                <van-col span="12"> 规格: {{ item.spec }} </van-col>
              </van-row>
              <van-row>
                <van-col span="20">型号: {{ item.mold }}</van-col>
                <van-col span="4">
                  <van-button
                    plain
                    hairline
                    type="info"
                    size="mini"
                    @click="goDetail(item)"
                  >
                    调整
                  </van-button>
                </van-col>
              </van-row>
            </div>
          </van-cell>
        </van-list>
      </van-cell-group>
    </van-checkbox-group>

    <div style="height: 10%; width: 100%">&nbsp;</div>
    <!-- <van-row
      style="
        background-color: #fff;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 99;
        width: 100%;
        height: 5%;"
      gutter="30"
    >
      <van-col span="4">
        <van-button
          :plain="plain"
          icon="success"
          type="info"
          round
          size="mini"
          @click="toggleAll"
        >
        </van-button>
      </van-col>
      <van-col span="11"></van-col>
      <van-col span="8">
        <van-button
          round
          style="height: 25px"
          type="info"
          size="large"
          @click="AdjustAtRack"
          loading-type="spinner"
        >
          批量调整
        </van-button>
      </van-col>
    </van-row> -->
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  data() {
    return {
      goodsLocationList: [],
      showbookNamePicker: false,
      show: false,
      bookName: "",
      stickerId: "",
      area: "",
      storeName: "",
      customer: "",
      product: "",
      rackName: "",
      bookNameColumns: [],
      result: [],
      plain: true,
      finished: false,
      load: false,
      pageNo: 1,
      total: 0
    };
  },
  created() {
    this.$axios.get(`/jeecg-boot/app/warehouse/getFactoryInfo`).then(res => {
      if (res.data.code == 200) {
        console.log(res.data.result);
        res.data.result.forEach(item => {
          this.bookNameColumns.push(item.name);
        });
      } else {
      }
    });
    if (localStorage.getItem("goodsSearch")) {
      this.bookName = JSON.parse(localStorage.getItem("goodsSearch")).bookName;
      this.rackName = JSON.parse(localStorage.getItem("goodsSearch")).rackName;
      this.storeName = JSON.parse(
        localStorage.getItem("goodsSearch")
      ).storeName;
    } else {
      this.show = true;
    }
  },
  methods: {
    onLoad() {
      this.search(); // 调用方法,请求数据
    },
    goDetail(item) {
      // let info = {
      //   bookName: item.bookName,
      //   storeName: item.storeName,
      //   bookId: item.bookId,
      //   storeId: item.storeId,
      //   rackName: item.rackName,
      //   rackId: item.rackId
      // };
      this.$router.push({
        name: "GoodsLocationAdjust",
        params: {
          item: item
        }
      });
    },
    AdjustAtRack() {
      if (this.result.length > 0) {
        let info = {
          bookName: this.result[0].bookName,
          storeName: this.result[0].storeName,
          bookId: this.result[0].bookId,
          storeId: this.result[0].storeId,
          rackName: this.result[0].rackName,
          rackId: this.result[0].rackId
        };
        this.$router.push({
          name: "GoodsLocationAdjust",
          params: {
            type: 2,
            item: this.result,
            info: info
          }
        });
      } else {
        Toast({
          message: "请选择至少一个",
          position: "bottom",
          duration: 2000
        });
      }
    },
    toggleAll() {
      if (this.result.length != this.goodsLocationList.length) {
        this.plain = false;
        this.$refs.checkboxGroup.toggleAll(true);
      } else {
        this.plain = true;
        this.$refs.checkboxGroup.toggleAll();
      }
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    search1() {
      this.show = false;
      this.result = [];
      this.pageNo = 1;
      this.goodsLocationList = [];
      if (
        this.rackName == "" ||
        this.rackName == null ||
        this.rackName == undefined ||
        this.storeName == "" ||
        this.storeName == null ||
        this.storeName == undefined ||
        this.bookName == "" ||
        this.bookName == null ||
        this.bookName == undefined
      ) {
        Toast({
          message: "请填写账套,仓库,货位",
          position: "top",
          duration: 2000
        });
        return;
      } else {
        this.$axios
          .get(
            `/jeecg-boot/app/warehouse/getStockList?pageNo=${this.pageNo}&product=${this.product}&rackName=${this.rackName}&bookName=${this.bookName}&storeName=${this.storeName}&customer=${this.customer}&stickerId=${this.stickerId}&area=${this.area}`
          )
          .then(res => {
            if (res.data.code == 200) {
              this.finished = false;
              let len = res.data.result.records.length;
              if (len > 0) {
                localStorage.setItem(
                  "goodsSearch",
                  JSON.stringify({
                    bookName: res.data.result.records[0].bookName,
                    storeName: res.data.result.records[0].storeName,
                    rackName: res.data.result.records[0].rackName
                  })
                );
                this.bookName = res.data.result.records[0].bookName;
                this.storeName = res.data.result.records[0].storeName;
                this.rackName = res.data.result.records[0].rackName;
              }
              if (len == 0) {
                this.goodsLocationList = []; // 清空数组
                this.finished = true; // 停止加载
              }
              this.total = res.data.result.total; //总数
              this.goodsLocationList.push(...res.data.result.records);
              this.load = false;
              this.pageNo++; // 分页数加一
              if (this.goodsLocationList.length >= res.data.result.total) {
                this.finished = true; // 结束加载状态
              }
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          });
      }
    },
    search() {
      if (
        this.rackName == "" ||
        this.rackName == null ||
        this.rackName == undefined ||
        this.storeName == "" ||
        this.storeName == null ||
        this.storeName == undefined ||
        this.bookName == "" ||
        this.bookName == null ||
        this.bookName == undefined
      ) {
        Toast({
          message: "请填写货位",
          position: "bottom",
          duration: 2000
        });
      } else {
        this.$axios
          .get(
            `/jeecg-boot/app/warehouse/getStockList?pageNo=${this.pageNo}&product=${this.product}&rackName=${this.rackName}&bookName=${this.bookName}&storeName=${this.storeName}&customer=${this.customer}&stickerId=${this.stickerId}&area=${this.area}`
          )
          .then(res => {
            if (res.data.code == 200) {
              this.finished = false;
              let len = res.data.result.records.length;
              if (len > 0) {
                localStorage.setItem(
                  "goodsSearch",
                  JSON.stringify({
                    bookName: res.data.result.records[0].bookName,
                    storeName: res.data.result.records[0].storeName,
                    rackName: res.data.result.records[0].rackName
                  })
                );
                this.bookName = res.data.result.records[0].bookName;
                this.storeName = res.data.result.records[0].storeName;
                this.rackName = res.data.result.records[0].rackName;
              }
              if (len == 0) {
                this.goodsLocationList = []; // 清空数组
                this.finished = true; // 停止加载
              }
              this.total = res.data.result.total; //总数
              this.goodsLocationList.push(...res.data.result.records);
              this.load = false;
              this.pageNo++; // 分页数加一
              if (this.goodsLocationList.length >= res.data.result.total) {
                this.finished = true; // 结束加载状态
              }
              this.show = false;
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          });
      }
    },
    onClickLeft() {
      this.$router.replace({
        name: "GoodsOutbound"
      });
    },
    onClickRight() {
      this.pageNo = 1;
      this.show = true;
    },
    bookNameConfirm(value) {
      this.bookName = value;
      this.showbookNamePicker = false;
    }
  }
};
</script>

<style scoped></style>
