<template>
  <div>
    <div style="color: #000;text-align: left;font-size: 20px;padding:3%">
      当前报工数量为:{{ output }}{{ item.mainUnit }}
    </div>

    <a-button @click="add" type="primary" style="width:70%;margin-bottom: 3%;">新增</a-button>
    <div style="color: #000;text-align: left;font-size: 20px;padding:3%;">
      坦克列表
    </div>
    <div style="height: 400px;overflow:auto;padding:3%;position: relative;">
      <div v-for="(item, index) in goodsInfo" style="text-align: left;position: relative;padding:3%;">
        <a-button @click="delTank(item)" type="primary" style="position: absolute; right: 2%; top: 15%">删除</a-button>
        <div>坦克名称:{{ item.tankName }}---坦克编号:{{ item.tankNo }}</div>
        <div>胶体存量:{{ item.realVolume }}</div>
        <van-field label="胶体使用量" v-model="item.output" @change="outputChange(item, index)" />
      </div>
    </div>
    <a-button @click="handleOk" type="primary" style="width:70%;margin-bottom: 3%;">提交</a-button>
    <addModel ref="modalForm" @ok="modalFormOk"></addModel>
  </div>
</template>

<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from "mint-ui";
import addModel from "./list/addModel.vue";
export default {
  components: { addModel },
  data() {
    return {
      item: {},
      num: "",
      output: "0",
      goodsInfo: []
    };
  },
  created() {
    this.item = this.$route.params.a;
    this.num = this.$route.params.num;

    // 获取列表
    this.$axios
      .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
        params: {
          lpId: this.item.id
        }
      })
      .then(res => {
        if ((res.data.code = 200)) {
          this.goodsInfo = res.data.result;
          this.goodsInfo.forEach(v => {
            this.$set(v, "output", v.realVolume);
          });
        } else {
          Toast({
            message: res.data.message,
            position: "bottom",
            duration: 2000
          });
        }
      });

    this.$axios
      .get("/jeecg-boot/app/gcWorkshop/getOutputRecords", {
        params: { lpId: this.item.id }
      })
      .then(res => {
        if (res.data.success) {
          this.output = res.data.result.output;
        }
      });
  },
  methods: {
    handleOk() {
      this.goodsInfo.forEach(v => {
        v.qrId = v.id;
      });
      let GcTankUsageInfo = this.goodsInfo;

      let itemParams = JSON.parse(localStorage.getItem("params"));
      let worker = itemParams.userCode;
      let self = this;
      let params = {
        lpId: self.item.id,
        gcWorkOperationList: self.personList,
        type: this.num,
        preCategory: self.item.preCategory,
        workType: self.item.workType,
        createNo: localStorage.getItem("userCode"),
        creator: localStorage.getItem("userName"),
        repCreator: worker,
        GcTankUsageInfo
      };
      Indicator.open({
        text: "处理中，请稍后……",
        spinnerType: "fading-circle"
      });
      self.$axios
        .post("/jeecg-boot/app/gcWorkshop/getLeadDeployNew", params)
        .then(res => {
          if (res.data.code == 200) {
            Indicator.close();
            this.finishFlag = true;
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
            self.$router.push({ name: "OrderClose", params: res.data.result });
          } else {
            Indicator.close();
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    add() {
      this.$refs.modalForm.edit(this.item);
      this.$refs.modalForm.title = "新增";
      this.$refs.modalForm.disableSubmit = true;
    },
    delTank(item) {
      this.$axios
        .delete(`/jeecg-boot/app/gcWorkshop/delTankTemp?ids=${item.id}`)
        .then(res => {
          if ((res.data.code = 200)) {
            this.$axios
              .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
                params: {
                  lpId: this.item.id
                }
              })
              .then(res => {
                if ((res.data.code = 200)) {
                  this.goodsInfo = res.data.result;
                  this.goodsInfo.forEach(v => {
                    v = Object.assign(v, { output: v.realVolume });
                  });
                } else {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                }
              });
          }
        });
    },
    modalFormOk() {
      this.$axios
        .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
          params: {
            lpId: this.item.id
          }
        })
        .then(res => {
          if ((res.data.code = 200)) {
            this.goodsInfo = res.data.result;
            this.goodsInfo.forEach(v => {
              this.$set(v, "output", v.realVolume);
            });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    outputChange(item) {
      if (item.output * 1 > item.realVolume * 1) {
        Toast({
          message: "超出最大量,请重新填写",
          position: "bottom",
          duration: 2000
        });
        item.output = item.realVolume;
      }
    }
  }
};
</script>

<style scoped>
</style>
