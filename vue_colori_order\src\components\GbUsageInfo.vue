<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">灌包消耗/退回</div>
        <van-field label="所属账套" :value="book"  readonly @click="handleBook"/>
        <van-field label="胶体车间" :value="jtWorkshop"  readonly @click="handleJtWorkshop"/>
        <van-field label="储罐信息" placeholder="点击扫码/录入数据"  readonly @click="handleScan"/>
        <van-field label="坦克编号" v-model="tankInfo.tankNo"  readonly/>
        <van-field label="胶体编号" v-model="tankInfo.realProductNo"  readonly/>
        <van-field label="生产批次" v-model="tankInfo.realCode"  readonly/>
        <van-field label="胶体容量" v-model="tankInfo.curVolume"  readonly/>
        <van-field label="当前状态" v-model="tankInfo.realProductStatus"  readonly/>
        <van-field label="灌包时间" v-model="workDay" @click="c_show = true"  readonly/>
        <van-field label="灌包车间" :value="gbWorkshop"  readonly @click="handleGbWorkshop"/>
        <van-field label="工作中心" v-model="jobCenter"  readonly @click="handleJobCenter"/>
        <van-field label="灌包单号" v-model="moItem.moId"  readonly @click="getMoInfo"/>
        <van-field label="灌包组长" v-model="moItem.leaderNo"  readonly/>
        <van-field label="人事秘钥" v-model="secretKey" />
        <van-field-Pick v-model="operation" label="操作类型" required :columns="['消耗','退回']"/>
        <van-field-Pick v-model="usageStatus" v-if="operation=='消耗'" label="使用情况" required :columns="['全部消耗','部分消耗']"/>
        <van-field label="胶体重量" v-model="lastWeight"  v-if="(operation=='消耗' && usageStatus=='部分消耗') || operation=='退回'"/>

        <van-popup  v-model="showBook" position="bottom">
            <van-picker :columns="bookList"  @cancel="onBookCancel" @confirm="onBookConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showJtWorkshop" position="bottom">
            <van-picker :columns="jtWorkShopList" @cancel="onJtWorkShopCancel" @confirm="onJtWorkShopConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showGbWorkshop" position="bottom">
            <van-picker :columns="gbWorkShopList" @cancel="onGbWorkShopCancel" @confirm="onGbWorkShopConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <van-popup  v-model="showJobCenter" position="bottom">
            <van-picker :columns="jobCenterList" @cancel="onJobCenterCancel" @confirm="onJobCenterConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>


        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false" position="right" />


        <van-button type="info" @click="submit()"  style="margin:10px;width:40%;border-radius:10px;">确定</van-button>

        
    </div>
</template>
<script>
import { Indicator,MessageBox   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx
let previousRouterName = "";
let tankInfo={}
let moItem=""
let workDay=""
export default {
    data(){
        return{
            book:"",
            jtWorkshop:"",
            gbWorkshop:"",
            jobCenter:"",
            workDay:"",
            operation:"",
            lastWeight:"",
            usageStatus:"",
            secretKey:"",
            tankInfo:{
                curVolume:''
            },
            c_show:false,
            minDate:'',
            date:'',
            moItem:{},
            bookList:[],
            jtWorkShopList:[],
            gbWorkShopList:[],
            jobCenterList:[],
            showBook:false,
            showJtWorkshop:false,
            showGbWorkshop:false,
            showJobCenter:false
        }
    },
    beforeRouteEnter(to, from, next) {
        previousRouterName = from.name;
        if (from.name === "MoUsageSelect") {
            tankInfo=from.params.tankInfo;
            moItem=from.params.moItem;
            workDay=from.params.workDay;
        }
        next();
    },
    created:function(){

        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 30 * 24 * 3600 * 1000);

        this.book = localStorage.getItem('book');
        this.jtWorkshop = localStorage.getItem('jtWorkshop');
        this.gbWorkshop = localStorage.getItem('gbWorkshop');
        this.jobCenter = localStorage.getItem('gbJobCenter');
        
        this.getBook();

        console.log(this.book)

        if(this.book!=null && this.book !=''){
            this.getWorkShop(this.book,"1");
            this.getWorkShop(this.book,"2");
        }

        if(this.gbWorkshop!=null && this.gbWorkshop !=''){
            this.getJobCenter(this.gbWorkshop);
        }

        if (previousRouterName === "MoUsageSelect") {
            this.tankInfo=tankInfo
            this.workDay=workDay
            this.moItem=moItem
        }
        
    },
    methods:{
        onConfirm(date) {
            this.c_show = false;
            this.workDay = this.formatDate(date);
        },
        getBook(){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/warehouse/getFactoryInfo',null).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    let books=res.data.result
                    for(var i=0;i<books.length;i++){
                        self.bookList.push(books[i].name)
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        getMoInfo(){
            let self=this;
            self.$router.push({name:"MoUsageSelect",params:{tankInfo:self.tankInfo,workDay:self.workDay,jobCenter:self.jobCenter}})
        },
        getWorkShop(book,type){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/tank/check/getWorkShopByBook',{params:{book:book,type:type}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    if(type=='1'){
                        self.gbWorkShopList=res.data.result
                    }else{
                        self.jtWorkShopList=res.data.result
                    }
                    
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    Indicator.close();
                }
            })
        },
        getJobCenter(value){
            let self=this;
            self.$axios.get('/jeecg-boot/app/tank/check/getJobCenterByShop',{params:{workshop:value}}).then(res=>{
                if(res.data.code==200){
                    self.jobCenterList=res.data.result
                }
            })
        },
        handleScan(){
            let self=this;
            MessageBox.confirm('',{
                message: '扫码还是录入？',
                title: '提示',
                confirmButtonText: '扫码',
                cancelButtonText: '录入'
            }).then(action => {
                if(action=="confirm"){
                    wx.scanQRCode({
                        desc: 'scanQRCode desc',
                        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                        success: function(res) {
                            // 回调
                            var result = res.resultStr;//当needResult为1时返回处理结果
                            let tankIds=result.split('&')
                            self.getTankInfo(tankIds[0],"1")
                        },
                        error: function(res) {
                            if (res.errMsg.indexOf('function_not_exist') > 0) {
                                alert('版本过低请升级')
                            }
                        }
                    });
                }
            }).catch((res)=>{
                if(res=="cancel"){
                    MessageBox.prompt('请输入储罐编码').then(({ value, action }) => {
                        if(action=="confirm"){
                            self.getTankInfo(value,"2")
                        }
                    });
                }
            });
        },
        getTankInfo(value,type){
            let self=this
            self.$axios.get('/jeecg-boot/app/tank/check/getTankInfo',{params:{code:self.tankInfo.realProductNo,workshop:self.jtWorkshop,value:value,type:type}}).then(res=>{
                if(res.data.code==200){
                    self.tankInfo=res.data.result;
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        submit(){
            let self=this

            let msg="";

            console.log(self.moItem)

            if(self.moItem.moId==null || self.moItem.moId==''){
                Toast({
                    message: "请选择灌包单号",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }


            if(self.operation==null || self.operation==''){
                Toast({
                    message: "请选择操作类型！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.operation=='消耗'){
                if(self.usageStatus=='全部消耗'){
                    msg="请确认是否从坦克"+self.tankInfo.tankNo+"的全部胶体消耗至生产订单"+moItem.moId+"中？";
                }else{

                    if(self.lastWeight==null || self.lastWeight==''){
                        Toast({
                            message: "请输入需消耗的胶体重量！",
                            position: 'bottom',
                            duration: 2000
                        });
                        return;
                    }

                    msg="请确认是否从坦克"+self.tankInfo.tankNo+"减少胶体"+self.lastWeight+"KG至生产订单"+moItem.moId+"中？";
                }
            }else{

                if(self.lastWeight==null || self.lastWeight==''){
                    Toast({
                        message: "请输入需退回的胶体重量！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }

                msg="请确认是否为坦克"+self.tankInfo.tankNo+"增加胶体"+self.lastWeight+"KG";
            }

            MessageBox.confirm('',{
                message: msg,
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.usageColloidToMoId();
                    }
            }).catch((res)=>{
                            
            });

        },
        usageColloidToMoId(){
            let self=this
            let params={
                tankId:self.tankInfo.id,
                code:self.tankInfo.realProductNo,
                customer:self.tankInfo.realCode,
                gbLpId:self.moItem.lpId,
                operation:self.operation,
                usageStatus:self.usageStatus,
                jobCenter:self.jobCenter,
                workshop:self.gbWorkshop,
                lastWeight:self.lastWeight,
                moId:self.moItem.moId,
                secretKey:self.secretKey,
                gbLeaderNo:self.moItem.leaderNo,
                pullUser:localStorage.getItem('userCode')
            }
            self.$axios.post('/jeecg-boot/app/tank/check/usageColloidToMoId',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        handleBook(){
            this.showBook=true
        },
        handleJtWorkshop(){
            if(this.book==null || this.book==''){
                Toast({
                    message: "请先选择账套",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                this.showJtWorkshop=true
            }
            
        },
        handleGbWorkshop(){
            if(this.book==null || this.book==''){
                Toast({
                    message: "请先选择账套",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                this.showGbWorkshop=true
            }
            
        },
        handleJobCenter(){
            if(this.gbWorkshop==null || this.gbWorkshop==''){
                Toast({
                    message: "请先选择灌包车间",
                    position: 'bottom',
                    duration: 2000
                });
            }else{
                this.showJobCenter=true
            }
        },
        onBookConfirm(value){
            this.book = value
            localStorage.setItem('book',value)
            localStorage.setItem('workshop','')
            this.workshop = '';
            this.jobCenter = '';
            this.showBook = false
            this.getWorkShop(value,"1");
            this.getWorkShop(value,"2");
        },
        onJtWorkShopConfirm(value){
            this.jtWorkshop = value
            localStorage.setItem('jtWorkshop',value)
            this.showJtWorkshop = false
        },
        onGbWorkShopConfirm(value){
            this.gbWorkshop = value
            localStorage.setItem('gbWorkshop',value)
            this.showGbWorkshop = false
            this.getJobCenter(value);
        },
        onJobCenterConfirm(value){
            this.jobCenter = value
            localStorage.setItem('gbJobCenter',value);
            this.showJobCenter = false
            this.getPullInfo(this.jobCenter);
        },
        onBookCancel(){
            this.showBook=false;
        },
        onJtWorkShopCancel(){
            this.showJtWorkshop = false
        },
        onGbWorkShopCancel(){
            this.showGbWorkshop = false
        },
        onJobCenterCancel(){
            this.showJobCenter = false
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
</style>