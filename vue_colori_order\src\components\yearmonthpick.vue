<template>
  <div>
    <van-field
      v-bind="$attrs"
      @click="popup_seldate = !popup_seldate"
      v-model="result"
      readonly
      is-link
      arrow-direction="down"
    />
    <van-popup position="bottom" v-model="popup_seldate">
      <van-datetime-picker
        @confirm="onConfirm"
        @cancel="popup_seldate = !popup_seldate"
        :title="$attrs.label"
        type="year-month"
        :min-date="minDate"
        :max-date="maxDate"
        :formatter="formatter"
      />
    </van-popup>
  </div>
</template>

<script>
import Vue from "vue";
import { Popup, DatetimePicker } from "vant";
import "vant/lib/index.css";
import { formatDate } from "@/common/commonUtil.js";
Vue.use(Popup).use(DatetimePicker);

export default Vue.extend({
  model: { prop: "selectValue" },
  props: {
    selectValue: {
      type: String
    }
  },
  data() {
    return {
      result: this.selectValue,
      popup_seldate: false,
      minDate: new Date(1980, 1, 1),
      maxDate: new Date()
    };
  },
  methods: {
    onConfirm: function(value) {
      this.result = formatDate(value, "yyyy年MM月");
      this.popup_seldate = !this.popup_seldate;
    },
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      } else if (type === "month") {
        return `${value}月`;
      }
      return value;
    }
  },
  computed: {},
  watch: {
    selectValue: function(newVal) {
      this.result = newVal;
    },
    result(newVal) {
      this.$emit("input", newVal);
    }
  }
});
</script>

<style></style>
