<template>
<div class="pageHygieneAdd">
    <van-nav-bar fixed
        title="发布心声社区"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <van-form @submit="bulletinSubmit">
        <van-field input-align="right" label="良好数量" name="卫生良好数量"
            type="digit" maxlength="2"
            placeholder="最优数量"
            v-model="rForm.goodNumber"
            :rules="[{ required: true, message: '必填' }]"
            />
        <van-field input-align="right" label="较差数量" name="卫生较差数量"
            type="digit" maxlength="2"
            placeholder="最差数量"
            v-model="rForm.badNumber"
            :rules="[{ required: true, message: '必填' }]"
            />
        <div style="width: 90%; margin: 0 auto; padding: 10px 0; text-align: left;"><van-icon name="warning-o" />输入数量,系统宿舍卫生最好和最差的宿舍排名靠前的,发布心声社区.</div>
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">发布</van-button>
        </div>
    </van-form>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview, Form} from 'vant'
export default {
    data(){
        return{
            confirmLoading: false,
            rForm: {
                goodNumber: '',
                badNumber: '',
            },
        }
    },
    methods:{
        bulletinSubmit(){
            this.confirmLoading=true
            const rParams=JSON.parse(JSON.stringify(this.rForm))
            rParams.userCodeRequest = localStorage.getItem('userCode');
            console.log("/dormApi/dm/dmDailyCsInfo/app/unionCommunityChange", rParams)
            this.$axios.get("/dormApi/dm/dmDailyCsInfo/app/unionCommunityChange", {params: rParams}).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.$router.push("/hygieneManage")
                    }else{
                        Toast(res.message)
                        console.log("ERROR", res)
                    }
                }else{
                    Toast("发生错误")
                    console.error("ERROR", rtn)
                }
            }).catch(err=>{
                Toast("发生错误")
                console.error(err)
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        onClickLeft(){
            this.$router.go(-1)
        }
    },
    created(){ 
    },
}
</script>
<style scoped>
.pageHygieneAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>