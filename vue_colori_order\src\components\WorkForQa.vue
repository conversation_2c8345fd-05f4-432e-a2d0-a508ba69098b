<template>
    <div>

        <!-- <mt-tab-container v-model="active">
            <mt-tab-container-item id="xj-tab">
                <van-button type="primary"  @click="createNewOrder()" style="margin:15px;width:60%;border-radius:10px;">新建</van-button>
                <div v-for="(item,index) in mainInfo" :key="index" class="report_item" @click="updateItem(item)">
                    <div class="report_item_text">
                        <div class="item_left">模板名称</div>
                        <div class="item_right">{{item.tableName}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">检测区域</div>
                        <div class="item_right">{{item.areaName}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">巡检时间</div>
                        <div class="item_right">{{item.createTime}}</div>
                    </div>
                </div>
            </mt-tab-container-item>
            <mt-tab-container-item id="tab-container2">
                1
            </mt-tab-container-item>
            <mt-tab-container-item id="tab-container3">
                2
            </mt-tab-container-item>
        </mt-tab-container> -->


        <van-tabs v-model="active" animated @click="onClick">   
            <van-tab title="巡检单">
                <van-button type="primary"  @click="createNewOrder()" style="margin:15px;width:60%;border-radius:10px;">新建</van-button>

                <div v-for="(item,index) in mainInfo" :key="index" class="report_item" @click="updateItem(item)">
                    <div class="report_item_text">
                        <div class="item_left">模板名称</div>
                        <div class="item_right">{{item.tableName}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">检测区域</div>
                        <div class="item_right">{{item.areaName}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">巡检时间</div>
                        <div class="item_right">{{item.createTime}}</div>
                    </div>
                    <div style="clear:both;"></div>
                </div>
            </van-tab>   
            <van-tab title="整改单">
                <div v-for="(item,index) in updateInfo" :key="index" class="report_item" style="margin-top:20px;" @click="rectifyItem(item)">
                    <div class="report_item_text">
                        <div class="item_left">整改项目</div>
                        <div class="item_right">{{item.content}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">异常描述</div>
                        <div class="item_right">{{item.reason}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">巡检时间</div>
                        <div class="item_right">{{item.createTime}}</div>
                    </div>
                    <div style="clear:both;"></div>
                </div>
            </van-tab>   
            <van-tab title="审核单">
                <div v-for="(item,index) in checkInfo" :key="index" class="report_item" style="margin-top:20px" @click="checkItem(item)">
                    <div class="report_item_text">
                        <div class="item_left">整改项目</div>
                        <div class="item_right">{{item.content}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">异常描述</div>
                        <div class="item_right">{{item.reason}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">整改描述</div>
                        <div class="item_right">{{item.changeResult}}</div>
                    </div>
                    <div class="report_item_text">
                        <div class="item_left">整改时间</div>
                        <div class="item_right">{{item.changeTime}}</div>
                    </div>
                </div>
            </van-tab>   
        </van-tabs>




        
    </div>
</template>

<script>
import { DatetimePicker,Toast,Indicator,TabContainer, TabContainerItem } from 'mint-ui';
import eventBus from '../common/eventBus';
import ColloidModal from './list/ColloidModal.vue'
export default ({
    components: { ColloidModal,TabContainer,TabContainerItem},
    data() {
        return{
            checkInfo:{
                gcQualityMain:{
                    moId:'',
                    defV1:'是',
                    defV2:'是',
                    defV3:'是',
                    defV4:'是',
                    defV16:'是',
                    key:'否'
                }
            },
            goodsInfo:[],
            submitText:'提交',
            disabled:false,
            beginItem:{},
            show:true,
            items:{},
            mainInfo:[],
            updateInfo:[],
            checkInfo:[],
            active:0,
            type:'',
            moId:'',
            workDay:'',
            lpId:'',
            colloid:'点击获取',
            leadAppInfo:"",
            glshow:false,
            ipshow:false,
            date:"",
        }
        
    },
    created:function(){
        this.date=this.formatDate(new Date)
        console.log(this.active)
        this.getMainDetailByUserCode();
    },
    methods: {
        onClick(name, title) {    
            let self=this   
            if(title=='巡检单'){
                self.getMainDetailByUserCode();
            }else if(title=='整改单'){
                self.getIncompleteInfo();
            }else if(title=='审核单'){
                self.getApprovalList();
            }
        },
        createNewOrder(){
            let self=this;
            localStorage.setItem('qaModel',"false");
            self.$router.push({path:'/selectModelForQa'});
        },
        getApprovalList(){
            let self=this
            self.$axios.get('/jeecg-boot/app/appBos/getApprovalList',{params:{userCode:localStorage.getItem('userCode')}}).then(res=>{
                if(res.data.code==200){
                    self.checkInfo=res.data.result
                }
            })
        },
        getIncompleteInfo(){
            let self=this
            self.$axios.get('/jeecg-boot/app/appBos/getIncompleteInfo',{params:{userCode:localStorage.getItem('userCode')}}).then(res=>{
                if(res.data.code==200){
                    self.updateInfo=res.data.result
                }
            })
        },
        getMainDetailByUserCode(){
            let self=this
            self.$axios.get('/jeecg-boot/app/appBos/getMainDetailByUserCode',{params:{userCode:localStorage.getItem('userCode'),createTime:this.date}}).then(res=>{
                if(res.data.code==200){
                    self.mainInfo=res.data.result
                }
            })
        },
        checkItem(item){
            let self = this
            localStorage.setItem('checkItem',JSON.stringify(item));
            self.$router.push({name:'CheckForQa'});
        },
        rectifyItem(item){
            let self = this
            localStorage.setItem('rectifyItem',JSON.stringify(item));
            self.$router.push({name:'UpdateForQa'});
        },
        updateItem(item){
            let self = this
            console.log(item)
            self.$router.push({name:'WorkDuringForQa',params:{type:'2',item:item.detailInfoList}});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
})
</script>


<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}
.addControl{
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
.report_item_text{
    width: 90%;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 1%;
    padding-bottom: 1%;
}
.report_item{
    width: 90%;
    border-radius: 10px;
    margin: 0 auto;
    margin-bottom: 20px;
    background: cornflowerblue;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    margin-bottom: 8px;
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    margin-bottom: 8px;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
/deep/ .van-field__label{
    width: 10em;
}
/deep/.van-cell__title
{
text-align: left;
}

</style>