<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="借用记录" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">
            <van-field v-model="product" clearable label="名称：" placeholder="请输入名称" />
            <van-field v-model="workshop" clearable label="车间：" placeholder="请输入车间" />
            <van-field v-model="id" clearable label="ID：" placeholder="请输入ID" />

            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in list" :key="index"
            style="color: rgb(112 100 100 / 85%);text-align: left; margin: 3%; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="18">
                    <van-row>
                        <van-col span="24" style="display:flex;color: rgb(38 37 37 / 85%);">
                            <span style="font-size:large;font-weight:700;text-align:left">
                                {{ item.name }}
                            </span>
                        </van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">模具编号: {{ item.code }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">操&nbsp;作&nbsp;人 : {{ item.createName }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">模具状态: {{ item.moldStatus }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">归还数量: {{ item.count }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注: {{ item.remarks }}</van-col>
                    </van-row>
                </van-col>
                <van-col span="6">
                    <van-button v-if="item.status == 2" style="height: 100%;" plain type="info"
                        @click="managerConfirm(item)">确认
                    </van-button>
                    <van-button v-if="item.status == 2" style="height: 100%;" plain type="info"
                        @click="managerReject(item)">驳回
                    </van-button>
                </van-col>
            </van-row>

        </div>

    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            list: [],
            show: false,
            product: "",
            workshop: "",
            id: "",
        }
    },
    created() {
        if (localStorage.getItem('userCode') == null
            || localStorage.getItem('userCode') == '') {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000
            });
            this.$router.push({
                name: "LoginIndex",
            });
        } else {
            this.search()
        }
    },
    methods: {
        managerConfirm(item) {
            item.checker = localStorage.getItem('userCode')
            Dialog.confirm({
                title: '',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                message: '确认吗?',
            })
                .then(() => {
                    //    确认
                    item.status = 1
                    
                    let NcMoldsUsageInfo = item
                    this.$axios
                        .put(`/jeecg-boot/ncApp/moldsUsage/edit2`, NcMoldsUsageInfo)
                        .then(res => {
                            if (res.data.code == 200) {
                                Toast({
                                    message: '确认成功',
                                    position: "bottom",
                                    duration: 2000
                                });
                            } else {
                                console.log(res.data.message);
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                })
                .catch(() => {
                    Toast({
                        message: '取消',
                        position: "bottom",
                        duration: 2000
                    });
                });
        },
        managerReject(item) {
            item.checker = localStorage.getItem('userCode')
            Dialog.confirm({
                title: '',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                message: '驳回吗?',
            })
                .then(() => {
                    //    驳回
                    item.status = 0
                    let NcMoldsUsageInfo = item
                    this.$axios
                        .put(`/jeecg-boot/ncApp/moldsUsage/edit2`, NcMoldsUsageInfo)
                        .then(res => {
                            if (res.data.code == 200) {
                                Toast({
                                    message: '驳回成功',
                                    position: "bottom",
                                    duration: 2000
                                });
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                })
                .catch(() => {
                    Toast({
                        message: '取消',
                        position: "bottom",
                        duration: 2000
                    });
                });
        },
        search() {
            this.$axios
                .get(`/jeecg-boot/ncApp/moldsUsage/getUsageList2?type=2&userCode=${localStorage.getItem('userCode')}&product=${this.product}&workshop=${this.workshop}&id=${this.id}`,)
                .then(res => {
                    if (res.data.code == 200) {
                        this.list = res.data.result.records
                    } else {
                        Toast({
                            message: res.data.msg,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "ModuleOperation"
            });
        },
        onClickRight() {
            this.show = true;
        }
    },
}
</script>

<style scoped>
</style>