<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_attence" width="100%"/>
        <van-cell title="生产部门" :value="department" @click="departmentVisible=true" style="margin:5%;width:90%;border-radius:10px;"/>
        <van-cell title="生产车间" :value="workshop" @click="workshopVisible = true"  style="margin:5%;width:90%;border-radius:10px;"/>
        <van-cell title="工作中心" :value="mitosome" @click="mitosomeVisible = true"  style="margin:5%;width:90%;border-radius:10px;"/>

        <mt-popup class="popup-div" v-model="departmentVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="departmentSlots" @change="handleDepartChange"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="departmentVisible = !departmentVisible">取消</div>
                    <div class="">请选择生产部门</div>
                    <div class="usi-btn-sure" @click="departmentOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>


        <mt-popup class="popup-div" v-model="workshopVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="workshopSlots" @change="handleShopChange"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="workshopVisible = !workshopVisible">取消</div>
                    <div class="">请选择生产车间</div>
                    <div class="usi-btn-sure" @click="workshopOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>

        <mt-popup class="popup-div" v-model="mitosomeVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="mitosomeSlots" @change="handleMitosomeChange"  showToolbar @touchmove.native.stop.prevent value-key="name">
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="mitosomeVisible = !mitosomeVisible">取消</div>
                    <div class="">请选择工作中心</div>
                    <div class="usi-btn-sure" @click="mitosomeOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>
        

        <van-button type="primary" @click="checkInfos" style="width:50%;margin-top:30px;">立即检查</van-button>


        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            department:"",
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            departmentSlots:[],
            workshopSlots:[],
            mitosomeSlots:[],
            clickNum:0,
            departmentVal:"",
            workshopVal:"",
            mitosomeVal:"",
            isManager:true,
            departmentVisible:false,
            workshopVisible:false,
            mitosomeVisible:false,
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_attence:require('../../static/images/spot.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            attenceList:[],
            startTime:"",
            checkInfo:[],
            fee:{}
        }
    },
    components:{
        DatetimePicker
    },
    watch:{
        departmentVisible(n,o){
            this.departmentVal=this.departmentSlots[0].values[0]
        },
        workshopVisible(n,o){
            this.workshopVal=this.workshopSlots[0].values[0]
        },
        mitosomeVisible(n,o){
            this.mitosomeVal=this.mitosomeSlots[0].values[0]
        },
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        sessionStorage.setItem('workList','')
        this.date=this.formatDate(new Date)
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')

        console.log(localStorage.getItem('spotDepartment'))

        this.department=localStorage.getItem('spotDepartment')==undefined?'一车间':localStorage.getItem('spotDepartment')
        this.workshop=localStorage.getItem('spotWorkshop')
        this.mitosome=localStorage.getItem('spotMitosome')
        this.getDepartmentInfo()
        this.getMitosomeByShop(this.workshop)
    },
    methods: {
        getDepartmentInfo(){
            let self=this
            self.departmentSlots=[];
            self.$axios.get('/jeecg-boot/app/device/getDepartmentInfo',null).then(res=>{
                if(res.data.code==200){
                    let params={
                        values:res.data.result
                    }
                    self.departmentSlots.push(params)

                    console.log(self.department)

                    if(self.department==null || self.department==''){
                        self.department=res.data.result[0]
                    }
                    self.getWorkshopByBook(self.department)
                }
            })
        },
        handleDepartChange(picker, values){
            this.departmentVal=values[0];
        },
        handleShopChange(picker, values){
            this.workshopVal=values[0];
        },
        handleMitosomeChange(picker, values){
            this.mitosomeVal=values[0];
        },
        departmentOk(){
            this.department = this.departmentVal;
            this.departmentVisible = false;
            localStorage.setItem('spotDepartment',this.department);
            this.getWorkshopByBook(this.department)
            this.workshop="";
            this.mitosome="";
        },
        workshopOk(){
            this.workshop = this.workshopVal;
            this.workshopVisible = false;
            localStorage.setItem('spotWorkshop',this.workshop);
            this.getMitosomeByShop(this.workshop)
            this.mitosome="";
        },
        mitosomeOk(){
            this.mitosome = this.mitosomeVal;
            this.mitosomeVisible = false;
            localStorage.setItem('spotMitosome',this.mitosome);
        },
        getWorkshopByBook(department){
            let self=this
            self.workshopSlots=[];
            self.$axios.get('/jeecg-boot/app/device/getWorkshopInfo',{params:{department:department}}).then(res=>{
                if(res.data.code==200){
                    let params={
                        values:res.data.result
                    }
                    self.workshopSlots.push(params)
                    if(self.workshop==null || self.workshop==''){
                        self.workshop=res.data.result[0]
                        localStorage.setItem('spotWorkshop',self.workshop)
                    }
                    self.getMitosomeByShop(self.workshop)
                }
            })
        },
        getMitosomeByShop(workshop){
            let self=this
            self.mitosomeSlots=[];
            self.$axios.get('/jeecg-boot/app/device/getMitosomeInfo',{params:{workshop:workshop,type:'1'}}).then(res=>{
                if(res.data.code==200){
                    let params={
                        values:res.data.result
                    }
                    self.mitosomeSlots.push(params)
                    localStorage.setItem('spotMitosome',res.data.result[0])
                    self.mitosome=res.data.result[0]
                }
            })
        },
        checkInfos(){
            let self=this
            self.$axios.get('/jeecg-boot/app/device/getSpotCheckInfo',{params:{mitosome:this.mitosome}}).then(res=>{
                if(res.data.code==200){
                    self.checkInfo=res.data.result.result
                    self.startTime=res.data.result.startTime

                    for(var i=0;i<self.checkInfo.length;i++){
                        if(i==0){
                            self.checkInfo[i].show=true
                        }else{
                            self.checkInfo[i].show=false
                        }
                        for(var j=0;j<self.checkInfo[i].partSpots.length;j++){
                            self.checkInfo[i].partSpots[j].result="正常";
                            self.checkInfo[i].partSpots[j].dangerFlag="否";
                            self.checkInfo[i].partSpots[j].repairFlag="是";
                        }
                    }

                    localStorage.setItem('spotCheckInfo',JSON.stringify(self.checkInfo));
                    localStorage.setItem('spotStartTime',self.startTime);
                    localStorage.setItem('stopDepartment',self.department);
                    this.$router.push({path:'/spotCheck'});
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        pushToSpNo(){
            this.$router.push({path:'/checkSpNo'})
        },
        openManager(){
            let self=this
            self.clickNum=self.clickNum+1;
            if(self.userCode=="HI0901071284" || self.userCode=="HI1308030001" 
            || self.userCode=="HI2002250004" || self.userCode=="HI2102220002"){
                if(self.clickNum==5){
                    self.isManager=false;
                    self.clickNum=0
                }
            }
        },
        getDetail(item){
            console.log("item:"+item)
            sessionStorage.setItem('item',JSON.stringify(item))
            this.$router.push({path:'/schedualDetail'})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getAttecdanceInfo()
        },
        
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
        },
        handleCarClick(index){
            // this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 16.5rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    width: 90%;
    height: 2.2rem;
    padding-left: 5%;
    padding-right: 5%;
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.attence{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 15rem;
}
.attence_title{
    color: #5529f6;
    padding: 3%;
    font-size: 1.2rem;
    font-weight: 600;
}
.attence_item{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_item_bottom{
    width: 90%;
    height: 4rem;
    padding-left: 3%;
    margin-top: 3rem;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle{
    background: #32c7a8;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle1{
    background: #f5b874;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle2{
    background: #f3777e;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle3{
    background: #ff0000;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_bottom{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.item_status{
    background: #32c7a8;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status1{
    background: #f5b874;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status2{
    background: #f3777e;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.item_status3{
    background: #ff0000;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #fff;
    margin-left: 5%;
    padding-left: 5%;
    padding-right: 5%;
}
.attence_pg{
    height: 60rem;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
</style>