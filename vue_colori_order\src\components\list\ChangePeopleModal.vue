<template>
  <a-modal
    :title="title"
    :visible="visible"
    okText="确定" 
    cancelText="取消" 
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">

    <a-spin :spinning="confirmLoading">

      <div style="margin:5px;">
        <a-input placeholder="请输入员工姓名/编号"  v-model.trim="queryParam.searchKey"/>
      </div>
      <div style="margin:5px;">
        <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
      </div>

      <div style="margin-top:30px;">
        <van-radio-group v-model="user" v-for="(item,index) in userInfo" :key="index">
          <van-radio :name="item.llqUserCode" style="margin:20px;">{{item.llqUserCode}}-{{item.staffName}}</van-radio>
        </van-radio-group>
      </div>



    </a-spin>
  </a-modal>
</template>

<script>
import 'ant-design-vue/dist/antd.css';
import { DatetimePicker,Toast,MessageBox  } from 'mint-ui';
  export default {
    name: "ChangePeopleModal",
    data() {
      return {
        title: "操作",
        visible: false,
        id:"",
        user:"",
        selectedRowKeys: [],
        /* table选中records*/
        selectionRows: [],
        orderMainModel: {
          jeecgOrderCustomerList: [{}],
          jeecgOrderTicketList: [{}]
        },
        queryParam:{

        },
        userInfo:[],
        workshop:'',
        department:'',
        labelCol: {
          xs: {span: 24},
          sm: {span: 6},
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16},
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: "/gc/gcTechnologyPlan/addGcPlan",
          edit: "/test/order/edit",
          quota: "/gc/gcLead/getTechnology",
          getMoPlot: "/gc/gcTechnologyPlan/getRestCount",
          mode: "/gc/gcLead/getWorkmode",
          order: "/gc/gcLead/getOrderInfo",
          worker: "/gc/gcLead/getWorker",
          material: "/gc/gcLead/getMaterial",
          preplot:"/gc/gcLead/getPrePlot",
          restcount:"/gc/gcLead/getRestCount",
          getOrderNo: "/gc/gcTechnologyPlan/getOrderNo",
          orderCustomerList: "/test/order/listOrderCustomerByMainId",
          orderTicketList: "/test/order/listOrderTicketByMainId",
          toSelectEmp:"/gc/gcPeople/getPeopleInfo"
        },
        moIds:[],
        expandedRowKeys: [],
        id: ' ',
        item:[],
        description: '列表展开子表Demo',
      }
    },
    methods: {
      add() {
        // 新增
        this.edit({});
      },
      searchQuery(){
        let self=this
        self.$axios.get('/jeecg-boot/app/appBos/getPeopleList',{params:{searchKey:self.queryParam.searchKey}}).then(res=>{
          if(res.data.code==200){
            self.userInfo=res.data.result
            if(self.userInfo.length>0){
              this.user=self.userInfo[0].llqUserCode
            }
          }
        })
      },
      edit(id) {  
        this.visible = true;
        this.id=id
      },
      close() {
        this.$emit('close');
        this.id="";
        this.visible = false;
      },
      handleOk() {
        let self=this
        console.log(this.id)
        console.log(this.user)
        self.$axios.get('/jeecg-boot/app/appBos/changeLeader',{params:{id:this.id,userCode:this.user}}).then(res=>{
          if(res.data.code==200){
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
            self.$router.go(-1);
          }else{
            Toast({
              message: res.data.message,
              position: 'bottom',
              duration: 2000
            });
          }
        })
        
      },
      handleCancel() {
        this.close()
      },
      modalFormOk() {
        
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
  .fontColor{
    color: black;
  }
  
</style>