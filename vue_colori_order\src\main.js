// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import MintUI from 'mint-ui'
import Vant from'vant';
import 'mint-ui/lib/style.css'
import Antd from 'ant-design-vue';
import i18n from './lang';
import router from './router'
import axios from 'axios';
import VanFieldSelect from './components/VanFieldSelect.vue'
import MonthYearPick from './components/monthyearpick.vue'
import VueDirectiveImagePreviewer from 'vue-directive-image-previewer'
import 'vue-directive-image-previewer/dist/assets/style.css'



let width=screen.width
let height=screen.height


Vue.config.productionTip = false
Vue.prototype.$axios=axios
Vue.component('van-field-Pick',VanFieldSelect)
Vue.component('month-year-picker',MonthYearPick)
Vue.use(MintUI)
Vue.use(Vant);
Vue.use(Antd);
Vue.use(VueDirectiveImagePreviewer,{
  previewSize: 10,
  maxWidth:width,
  maxHeight:height
})

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  i18n,
  components: { App },
  template: '<App/>'
})
