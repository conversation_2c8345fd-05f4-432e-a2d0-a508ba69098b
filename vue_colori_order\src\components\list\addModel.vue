<template>
  <a-modal :title="title" :width="500" :visible="visible" okText="新增" cancelText="取消" :confirmLoading="confirmLoading"
    @ok="handleOk" @cancel="handleCancel">

    <div style="margin-bottom: 3%;">
      <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
        全选
      </a-checkbox>
      已选：{{ checkedList.length }}个
    </div>
    <div style="height: 350px;overflow:auto ;">
      <!-- <ul>
        <li v-for="(item, i) in goodsInfo" :key="i">
          <div style="text-align: left; position: relative; margin-top: 10%">
            <h4>坦克名称:{{ item.tankName }}</h4>
            <h4>坦克编号:{{ item.tankNo }}</h4>
            <h4>坦克容量:{{ item.realVolume }}</h4>
            <a-button v-if="btn == 'add'" @click="addTank(item, i)" type="primary"
              style="position: absolute; right: 2%; top: 2%">添加</a-button>
          </div>
        </li>
      </ul> -->
      <a-checkbox-group v-model="checkedList" @change="onChange">
        <a-row>
          <a-col style="background-color: #f9f9f9; margin-bottom: 2%; border-radius: 5px;" :span="24"
            v-for="(item, i) in goodsInfo" :key="i">
            <a-checkbox :value="item">
              <div>
                <h4>坦克名称:{{ item.tankName }}</h4>
                <h4>坦克编号:{{ item.tankNo }}</h4>
                <h4>坦克容量:{{ item.realVolume }}</h4>
              </div>
            </a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </div>
    <div v-show="!goodsInfo.length" class="shopping">
      <div><img src="../../../static/images/nothing.png" alt="" /></div>
      <p>暂无数据</p>
    </div>
  </a-modal>
</template>

<script>
import "ant-design-vue/dist/antd.css";
import Vue from "vue";
import { DatetimePicker, Toast, MessageBox } from "mint-ui";
import { Checkbox, SubmitBar, Card, Field, Cell, Dialog } from "vant";
export default {
  name: "OutModal",
  components: {
    [SubmitBar.name]: SubmitBar,
    [Checkbox.name]: Checkbox,
    [Card.name]: Card,
    [Field.name]: Field,
    [Cell.name]: Cell
  },
  data() {
    return {
      title: "操作",
      visible: false,
      ischecked: false,
      orderMainModel: {
        jeecgOrderCustomerList: [{}],
        jeecgOrderTicketList: [{}]
      },
      workshop: "",
      department: "",
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      moIds: [],
      expandedRowKeys: [],
      id: " ",
      item: [],
      depart: [],
      goodsInfo: [],
      record: {},
      btn: "",
      tankArr: [],
      checkedList: [],
      indeterminate: true,
      checkAll: false
    };
  },
  methods: {
    //复选框
    onChange(checkedList) {
      this.indeterminate =
        !!checkedList.length && checkedList.length < this.goodsInfo.length;
      this.checkAll = checkedList.length === this.goodsInfo.length;
    },
    onCheckAllChange(e) {
      Object.assign(this, {
        checkedList: e.target.checked ? this.goodsInfo : [],
        indeterminate: false,
        checkAll: e.target.checked
      });
    },
    edit(item) {
      console.log("item2", item);
      this.btn = "add";
      this.record = item;
      let self = this;
      self.visible = true;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getTankList", {
          params: {
            productNo: item.code,
            workshop: item.workshop,
            lpId: item.id,
            type: '0'
          }
        })
        .then(res => {
          if ((res.data.code = 200)) {
            self.goodsInfo = res.data.result;
            for (var i = 0; i < self.goodsInfo.length; i++) {
              self.goodsInfo[i].productNo = self.goodsInfo[i].realProductNo;
              self.goodsInfo[i].tankId = self.goodsInfo[i].id;
              self.goodsInfo[i].productName = self.goodsInfo[i].realProductName;
              self.goodsInfo[i].customerId = self.goodsInfo[i].realCode;
              self.goodsInfo[i].lpId = item.id;
              self.goodsInfo[i].creator = localStorage.getItem('userCode');
            }
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    close() {
      this.$emit("close");
      this.visible = false;
      this.dataSource = [];
      this.flag = 0;
      this.saveCode = "";
      this.saveStation = "";
    },
    handleOk() {
      this.$axios
        .post(`/jeecg-boot/app/gcWorkshop/addTankTemp2`, this.checkedList)
        .then(res => {
          if ((res.data.code = 200)) {
            Toast({
              message: '添加成功',
              position: "bottom",
              duration: 2000
            });
            this.visible = false;
            this.$emit("ok");
            this.checkedList = []
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });

    },
    handleCancel() {
      this.visible = false;
      this.$emit("ok");
    },
    // addTank(item, index) {
    //   this.$axios
    //     .post("/jeecg-boot/app/gcWorkshop/addTankTemp", {
    //       tankId: item.id,
    //       tankNo: item.tankNo,
    //       tankName: item.tankName,
    //       realVolume: item.realVolume,
    //       customerId: item.realCode,
    //       productNo: this.record.code,
    //       productName: this.record.name,
    //       lpId: this.record.id
    //     })
    //     .then(res => {
    //       if ((res.data.code = 200)) {
    //         this.goodsInfo.splice(index, 1)
    //         this.$emit("ok");
    //       } else {
    //         Toast({
    //           message: res.data.message,
    //           position: "bottom",
    //           duration: 2000
    //         });
    //       }
    //     });
    // }
  }
};
</script>

<style scoped>
.ant-btn {
  padding: 0 10px;
  margin-left: 3px;
}

.ant-form-item-control {
  line-height: 0px;
}

/** 主表单行间距 */
.ant-form .ant-form-item {
  margin-bottom: 10px;
}

/** Tab页面行间距 */
.ant-tabs-content .ant-form-item {
  margin-bottom: 0px;
}

.fontColor {
  color: black;
}
</style>
<style lang="less" scoped>
// .van-submit-bar {
//   bottom: 49px;
//   padding-left: 20px;
// }
// .shopContent {
//   margin-top: 10px;
//   padding-bottom: 20px;
// }
// .shopping {
//   text-align: center;
//   padding-top: 99px;
//   img {
//     width: 96px;
//     height: 96px;
//     margin-bottom: 25px;
//   }
// }
// li {
//   padding: 0 15px;
//   background: #ffffff;
//   margin-bottom: 10px;
//   position: relative;
//   height: 103px;
//   .shopmain {
//     display: flex;
//     padding: 10px 8px 10px 10px;
//     position: relative;
//     .shops {
//       display: flex;
//       margin-left: 5%;
//       .shopImg {
//         width: 103px;
//         height: 83px;
//         margin: 0 7px 0 11px;
//         img {
//           width: 100%;
//           height: 100%;
//         }
//       }
//       .shopsright {
//         width: 100%;
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//         h4 {
//           display: -webkit-box;
//           -webkit-box-orient: vertical;
//           -webkit-line-clamp: 2;
//           overflow: hidden;
//           text-align: left;
//         }
//         .shoprightbot {
//           display: flex;
//           justify-content: space-between;
//           align-items: center;
//           width: 190px;
//           span {
//             font-size: 17px;
//             color: #f74022;
//           }
//         }
//       }
//     }
//     .van-checkbox__icon--checked .van-icon {
//       background: red !important;
//     }
//   }

//   input {
//     width: 48px;
//   }
// }
// .shopradd {
//   width: 98px;
//   display: flex;
//   .van-field__control {
//     text-align: center !important;
//   }
// }
// .van-cell {
//   padding: 0;
//   line-height: 26px;
// }
// .van-field__control {
//   height: 26px;
// }
// /deep/.ant-modal-body {
//   padding: 0px;
// }
</style>
