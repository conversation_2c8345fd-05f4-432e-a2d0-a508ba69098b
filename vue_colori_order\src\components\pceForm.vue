<template>
  <div class="pce-container" v-if="!isLoading && commonInfo.name">
    <!-- 基本信息 -->
    <section class="pce-section">
      <div class="section-header">
        <i class="section-icon">📋</i>
        <h2>基本信息 <small class="employee-code"></small></h2>
      </div>
      <div class="basic-info-list">
        <!-- 个人基本信息 -->
        <div class="basic-info-item"><label>姓名</label><span>{{ commonInfo.name }}</span></div>
        <div class="basic-info-item"><label>编码</label><span>{{ userCode }}</span></div>
        <div class="basic-info-item"><label>性别</label><span>{{ commonInfo.sex }}</span></div>
        <div class="basic-info-item"><label>年龄</label><span>{{ commonInfo.age }}</span></div>
        <div class="basic-info-item"><label>婚育状况</label><span>{{ commonInfo.marriageorno }}</span></div>

        <!-- 教育背景信息 -->
        <div class="basic-info-item"><label>最高学历</label><span>{{ commonInfo.highEducation }}</span></div>
        <div class="basic-info-item long-text"><label>专业</label><span>{{ commonInfo.highProfessional }}</span></div>
        <div class="basic-info-item long-text"><label>毕业学校</label><span>{{ commonInfo.qrzSchool }}</span></div>
        <div class="basic-info-item"><label>全日制学历</label><span>{{ commonInfo.qrzEducation }}</span></div>
        <div class="basic-info-item long-text"><label>全日制专业</label><span>{{ commonInfo.qrzProfessional }}</span></div>
      </div>
    </section>

    <!-- 业绩与能力评估 -->
    <section class="pce-section">
      <div class="section-header">
        <i class="section-icon">📊</i>
        <h2>业绩与能力评估</h2>
      </div>
      <div class="eval-list">
        <div class="eval-item">
          <div class="eval-title">数据确认(审计部、财务部)</div>
          <div class="eval-content" v-html="performanceModel.dataConfirm"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span>{{ performanceModel.dataConfirmEvaluate }}</span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">奖惩记录</div>
          <div class="eval-content" v-html="performanceModel.rewardsPunishments"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="performanceModel.rewardsPunishmentsEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">专业能力(可信度)</div>
          <div class="eval-content" v-html="performanceModel.professional"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span>{{ performanceModel.professionalEvaluate }}</span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">复盘总结能力</div>
          <div class="eval-content" v-html="performanceModel.review"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="performanceModel.reviewEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">其它能力</div>
          <div class="eval-content">{{ performanceModel.other && performanceModel.other.length > 0 ?
            performanceModel.other.join(', ') : '无' }}</div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span>{{ performanceModel.otherEvaluate }}</span>
          </div>
        </div>
      </div>
      <div class="creator-info">
        <span>编制:{{ performanceModel.creator }}</span>
        <!-- <span>审核:{{ performanceModel.checker }}</span> -->
      </div>
    </section>

    <!-- 性格、品性与综合素质 -->
    <section class="pce-section">
      <div class="section-header">
        <i class="section-icon">👤</i>
        <h2>性格、品性与综合素质</h2>
      </div>

      <h3 class="sub-header">性格与家庭</h3>
      <div class="eval-list">
        <div class="eval-item">
          <div class="eval-title">交流状态</div>
          <div class="eval-content" v-html="assessmentModel.commonStatus"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.commonEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">家庭情况</div>
          <div class="eval-content" v-html="assessmentModel.familySituation"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.familyEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">性格测试结果</div>
          <div class="eval-content">
            <template v-if="commonInfo.xgcsRes && commonInfo.xgcsRes.id && characterStr">
              <div>{{ commonInfo.xgcsRes.id }} &lt;{{ commonInfo.xgcsRes.type }}&gt;：{{ characterStr }}</div>
              <div v-for="(item, index) in commonInfo.mbti" :key="randomUUID()" class="mbti-item">
                {{ item.a }}<br>{{ item.b }}
              </div>
            </template>
            <template v-else>未提供</template>
          </div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.personassEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">性格特点</div>
          <div class="eval-content" v-html="assessmentModel.personalityTraits"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.personalityEvaluate"></span>
          </div>
        </div>
      </div>

      <h3 class="sub-header">品性与道德</h3>
      <div class="eval-list">
        <div class="eval-item">
          <div class="eval-title">格局</div>
          <div class="eval-content" v-html="assessmentModel.vision"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.visionEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">职业道德</div>
          <div class="eval-content" v-html="assessmentModel.workEthics"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.workEthicsEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">可靠度</div>
          <div class="eval-content" v-html="assessmentModel.reliability"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.reliabilityEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">亲和度</div>
          <div class="eval-content" v-html="assessmentModel.affinity"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.affinityEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">自我程度</div>
          <div class="eval-content" v-html="assessmentModel.degree"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.degreeEvaluate"></span>
          </div>
        </div>
        <div class="eval-item">
          <div class="eval-title">日常行为</div>
          <div class="eval-content">{{ assessmentModel.dailyBehavior && assessmentModel.dailyBehavior.length > 0 ?
            assessmentModel.dailyBehavior.join(', ') : '无' }}</div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.dailyBehaviorEvaluate"></span>
          </div>
        </div>

        <!-- <div class="eval-item">
          <div class="eval-title">德/行</div>
          <div class="eval-content">{{ assessmentModel.moralConduct && assessmentModel.moralConduct.length > 0 ?
            assessmentModel.moralConduct.join(', ') : '无' }}</div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span>{{ assessmentModel.moralConductEvaluate }}</span>
          </div>
        </div> -->
      </div>

      <h3 class="sub-header">意愿与职业发展</h3>
      <div class="eval-list">
        <div class="eval-item simple-item">
          <div class="eval-title">工作投入</div>
          <div class="eval-content">{{ assessmentModel.workEngagement }}</div>
        </div>

        <div class="eval-item simple-item">
          <div class="eval-title">择业时更关注</div>
          <div class="eval-content">{{ assessmentModel.jobHuntingConcerns && assessmentModel.jobHuntingConcerns.length >
            0 ?
            assessmentModel.jobHuntingConcerns.join(', ') : '无' }}</div>
        </div>

        <div class="eval-item simple-item">
          <div class="eval-title">意向部门</div>
          <div class="eval-content">{{ assessmentModel.intendedDepartment }}</div>
        </div>

        <div class="eval-item simple-item">
          <div class="eval-title">意向岗位</div>
          <div class="eval-content">{{ assessmentModel.intendedPosition }}</div>
        </div>

        <!-- <div class="eval-item simple-item">
          <div class="eval-title">职业发展规划评估</div>
          <div class="eval-content">{{ assessmentModel.careerDevelopmentPlan }}</div>
        </div> -->

        <div class="eval-item simple-item">
          <div class="eval-title">能平衡个人生活与工作，但当两者发生冲突时</div>
          <div class="eval-content">{{ assessmentModel.balance }}</div>
        </div>

        <div class="eval-item simple-item">
          <div class="eval-title">意愿评估</div>
          <div class="eval-content">{{ assessmentModel.willEvaluate }}</div>
        </div>
      </div>

      <h3 class="sub-header">担当</h3>
      <div class="eval-list">
        <div class="eval-item">
          <div class="eval-title">扛责抗压力</div>
          <div class="eval-content" v-html="assessmentModel.takeOn"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.takeOnEvaluate"></span>
          </div>
        </div>

        <!-- <div class="eval-item">
          <div class="eval-title">执行力</div>
          <div class="eval-content" v-html="assessmentModel.execution"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.executionEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">反馈力</div>
          <div class="eval-content" v-html="assessmentModel.feedback"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.feedbackEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">抗压力</div>
          <div class="eval-content" v-html="assessmentModel.stress"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="assessmentModel.stressEvaluate"></span>
          </div>
        </div> -->
      </div>

      <div class="creator-info">
        <span>编制:{{ assessmentModel.creator }}</span>
        <!-- <span>审核:{{ assessmentModel.checker }}</span> -->
      </div>
    </section>

    <!-- 管理能力 - 仅在 ncLevel > 3 时显示 -->
    <section class="pce-section" v-if="commonInfo.ncLevel > 3">
      <div class="section-header">
        <i class="section-icon">👔</i>
        <h2>管理能力评估</h2>
      </div>
      <div class="eval-list">
        <div class="eval-item">
          <div class="eval-title">制定目标</div>
          <div class="eval-content" v-html="manageModel.setGoals"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="manageModel.setGoalsEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">组织分工</div>
          <div class="eval-content" v-html="manageModel.jobSharing"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="manageModel.jobSharingEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">沟通交流/激励沟通</div>
          <div class="eval-content" v-html="manageModel.interaction"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="manageModel.interactionEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">绩效评估</div>
          <div class="eval-content" v-html="manageModel.appraisal"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="manageModel.appraisalEvaluate"></span>
          </div>
        </div>

        <div class="eval-item">
          <div class="eval-title">人才培养</div>
          <div class="eval-content" v-html="manageModel.train"></div>
          <div class="eval-result">
            <span class="result-tag">测评</span>
            <span v-html="manageModel.trainEvaluate"></span>
          </div>
        </div>
      </div>
      <div class="creator-info">
        <span>编制:{{ manageModel.creator }}</span>
        <!-- <span>审核:{{ manageModel.checker }}</span> -->
      </div>
    </section>

    <!-- 视频资料 -->
    <section class="pce-section">
      <div class="section-header">
        <i class="section-icon">🎥</i>
        <h2>视频资料</h2>
      </div>
      <div class="video-list" v-if="videoList && videoList.length > 0">
        <div v-for="video in videoList" :key="video.id" class="video-item">
          <div class="video-info">
            <h4 class="video-title">{{ video.videoName }}</h4>
            <div class="video-actions">
              <button @click="playVideo(video)" class="play-btn">播放视频</button>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-video">
        <div class="no-video-icon">📹</div>
        <p>暂无视频资料</p>
      </div>
    </section>
    <!-- 评估按钮组 -->
    <div class="evaluation-buttons" v-if="loginUserCode == 'HI0901050150' || evaluationData.id">
      <div class="evaluation-title">人员评价</div>
      <div v-if="loginUserCode == 'HI0901050150'">
        <button @click="selectEvaluation('重点培养')"
          :class="['eval-btn', ((evaluationData && evaluationData.evaluation) || selectedEvaluation) === '重点培养' ? 'active' : '']">重点培养</button>
        <button @click="selectEvaluation('储备')"
          :class="['eval-btn', ((evaluationData && evaluationData.evaluation) || selectedEvaluation) === '储备' ? 'active' : '']">储备</button>
        <button @click="selectEvaluation('观察')"
          :class="['eval-btn', ((evaluationData && evaluationData.evaluation) || selectedEvaluation) === '观察' ? 'active' : '']">观察</button>
      </div>
      <div v-else-if="evaluationData && evaluationData.id">
        <button :class="['eval-btn', (evaluationData && evaluationData.evaluation) === '重点培养' ? 'active' : '']"
          disabled>重点培养</button>
        <button :class="['eval-btn', (evaluationData && evaluationData.evaluation) === '储备' ? 'active' : '']"
          disabled>储备</button>
        <button :class="['eval-btn', (evaluationData && evaluationData.evaluation) === '观察' ? 'active' : '']"
          disabled>观察</button>
      </div>
    </div>
    <!-- 视频播放弹窗 -->
    <div v-if="videoModal.visible" class="video-modal-overlay" @click="closeVideoModal">
      <div class="video-modal" @click.stop>
        <div class="video-modal-header">
          <h3>{{ currentVideo.videoName }}</h3>
          <button @click="closeVideoModal" class="close-btn">×</button>
        </div>
        <div class="video-modal-body">
          <!-- 移动端横屏提示 -->
          <!-- <div v-if="showRotateHint" class="rotate-hint">
            <div class="rotate-icon">📱</div>
            <p>为了更好的观看体验，请将手机横向旋转</p>
          </div> -->
          <video v-if="currentVideo.videoUrl" :src="currentVideo.videoUrl" controls autoplay playsinline
            webkit-playsinline x5-video-player-type="h5" x5-video-player-fullscreen="true"
            x5-video-orientation="landscape" class="video-player" @error="handleVideoError"
            @loadedmetadata="handleVideoLoaded" @click="handleVideoClick">
            您的浏览器不支持视频播放
          </video>
          <div v-else class="video-error">
            <p>视频链接无效</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 评估确认弹窗 -->
    <div v-if="confirmModal.visible" class="confirm-modal-overlay" @click="cancelConfirmation">
      <div class="confirm-modal" @click.stop>
        <div class="confirm-modal-header">
          <h3>确认</h3>
          <button @click="cancelConfirmation" class="close-btn">×</button>
        </div>
        <div class="confirm-modal-body">
          <p>是否将 {{ commonInfo.name }} 设置为 {{ selectedEvaluation }}？</p>
        </div>
        <div class="confirm-modal-footer">
          <button @click="cancelConfirmation" class="cancel-btn">取消</button>
          <button @click="confirmEvaluation" class="confirm-btn">确定</button>
        </div>
      </div>
    </div>
  </div>

  <div v-else-if="isLoading" class="pce-loading">
    <div class="loading-spinner"></div>
    <p>加载中...</p>
  </div>

  <div v-else class="pce-no-data">
    <div class="no-data-icon">📂</div>
    <p>无法加载评估数据</p>
  </div>
</template>

<script>
import { Toast } from "vant";
import { Indicator } from "mint-ui";
export default {
  name: "PceFormDisplay",
  data() {
    return {
      loginUserCode: localStorage.getItem('userCode'),
      isLoading: false,
      performanceModel: {
        id: '',
        llqUserCode: '',
        staffName: '',
        dataConfirm: '',
        dataConfirmEvaluate: '',
        rewardsPunishments: '', // 新增奖惩记录字段
        rewardsPunishmentsEvaluate: '', // 新增奖惩记录评价字段
        professional: '',
        professionalEvaluate: '',
        review: '',
        reviewEvaluate: '',
        other: [],
        otherEvaluate: '',
        creator: '',
        checker: '',
      },
      assessmentModel: {
        id: '',
        llqUserCode: '',
        staffName: '',
        commonStatus: '', // 新增交流状态字段
        commonEvaluate: '',
        familySituation: '',
        familyEvaluate: '',
        personassEvaluate: '',
        personalityTraits: '',
        personalityEvaluate: '',
        vision: '',
        visionEvaluate: '',
        workEthics: '',
        workEthicsEvaluate: '',
        reliability: '',
        reliabilityEvaluate: '',
        affinity: '',
        affinityEvaluate: '',
        degree: '',
        degreeEvaluate: '',
        dailyBehavior: [], // 新增日常行为字段
        dailyBehaviorEvaluate: '', // 新增日常行为评价字段
        moralConduct: [],
        moralConductEvaluate: '',
        workEngagement: '',
        jobHuntingConcerns: [],
        intendedDepartment: '',
        intendedPosition: '',
        careerDevelopmentPlan: '',
        balance: '',
        willEvaluate: '',
        takeOn: '',
        takeOnEvaluate: '',
        execution: '',
        executionEvaluate: '',
        feedback: '',
        feedbackEvaluate: '',
        stress: '',
        stressEvaluate: '',
        creator: '',
        checker: '',
      },
      manageModel: {
        id: '',
        llqUserCode: '',
        staffName: '',
        setGoals: '',
        setGoalsEvaluate: '',
        jobSharing: '',
        jobSharingEvaluate: '',
        interaction: '',
        interactionEvaluate: '',
        appraisal: '',
        appraisalEvaluate: '',
        train: '',
        trainEvaluate: '',
        creator: '',
        checker: '',
      },
      commonInfo: {
        name: '',
        sex: '',
        age: '',
        marriageorno: '',
        highEducation: '',
        highProfessional: '',
        qrzSchool: '',
        qrzEducation: '',
        qrzProfessional: '',
        xgcsRes: { id: '', type: '', txtshow: '' },
        mbti: [],
        ncLevel: 0, // 新增ncLevel以控制管理能力模块显示
      },
      characterStr: '',
      userCode: '',
      // 性格测试
      xgcs: '',
      xgcsRes: {},
      // 视频相关数据
      videoList: [],
      videoModal: {
        visible: false,
      },
      currentVideo: {
        videoName: '',
        videoUrl: ''
      },
      showRotateHint: false,
      // 评估相关数据
      selectedEvaluation: '', // 存储当前选中的评估类型
      confirmModal: {
        visible: false, // 控制确认弹窗的显示
      },
      // 评估数据
      evaluationData: {
        id: '',
        llqUserCode: '',
        staffName: '',
        evaluation: '', // 添加缺失的 evaluation 属性
      },
    };
  },
  created() {
    this.userCode = this.$route.query.userCode;
    this.fetchData(this.userCode);
    this.getEduBackground(this.userCode);
    this.$axios.get('/jeecg-boot/app/staff/getAppStaffList', {
      params: {
        userName: '崔龙鑫',
        llqUserCode: this.userCode
      }
    }).then(response => {
      if (response.data && response.data.success) {
        console.log('获取员工列表数据成功:', response.data.result.records[0]);
        // 这里可以添加处理返回数据的逻辑
        let info = response.data.result.assessmentList[0]
        this.commonInfo.name = info.staffName
        this.commonInfo.sex = info.sex == 'M' ? '男' : '女'
        this.commonInfo.age = info.age
        if (info.marriageorno == 1) {
          this.commonInfo.marriageorno = '未婚';
        } else if (info.marriageorno == 2) {
          this.commonInfo.marriageorno = '已婚';
        } else if (info.marriageorno == 3) {
          this.commonInfo.marriageorno = '已婚育';
        } else {
          this.commonInfo.marriageorno = info.marriageorno;
        }


        this.xgcs = ''
        this.commonInfo.xgcsRes = {}
        this.$axios.get('/jeecg-boot/nc/recruit/getHtmlShow?paperNum=' + info.paperNum).then((resa) => {
          if (resa.data.code == 200) {
            this.xgcs = resa.data.result.htmlshow
            this.commonInfo.xgcsRes = resa.data.result

            if (this.commonInfo.xgcsRes && this.commonInfo.xgcsRes.id) {
              let str = this.commonInfo.xgcsRes.id.split('');
              if (str) {
                // 先将文本按行分割
                const lines = this.commonInfo.xgcsRes.txtshow.split('\n').map(line => line.trim());
                // 只保留非空行
                const cleanLines = lines.filter(line => line.length > 0);
                // 提取维度解释部分
                const dimensionDescriptions = {};
                let currentDimension = '';
                // 查找维度解释部分
                for (let i = 0; i < cleanLines.length; i++) {
                  const line = cleanLines[i];
                  // 查找维度标题行
                  if (line.includes('我们与世界的相互作用方式') ||
                    line.includes('我们获取信息的主要方式') ||
                    line.includes('我们的决策方式') ||
                    line.includes('我们的做事方式')) {
                    currentDimension = line;
                    continue;
                  }
                  // 查找字母描述行
                  if (currentDimension && (line.startsWith('E ') || line.startsWith('I ') ||
                    line.startsWith('S ') || line.startsWith('N ') ||
                    line.startsWith('T ') || line.startsWith('F ') ||
                    line.startsWith('J ') || line.startsWith('P '))) {
                    const letter = line.charAt(0);
                    let description = line;
                    // 查找包含"例如"的下一行
                    if (i + 1 < cleanLines.length && cleanLines[i + 1].includes('例如')) {
                      description += cleanLines[i + 1];
                    }
                    dimensionDescriptions[letter] = description;
                  }
                }

                // 将MBTI字符串的每个字母对应的描述转成数组
                const mbtiArr = [];
                for (let i = 0; i < this.commonInfo.xgcsRes.id.length; i++) {
                  const letter = this.commonInfo.xgcsRes.id[i];
                  console.log("🚀 ~ open ~ dimensionDescriptions[letter]:", dimensionDescriptions[letter])
                  if (dimensionDescriptions[letter]) {
                    mbtiArr.push({
                      a: dimensionDescriptions[letter].split('例如')[0],
                      b: dimensionDescriptions[letter].split('例如')[1]
                    });
                  } else {
                    mbtiArr.push({ a: '', b: '' });
                  }
                }
                this.commonInfo.mbti = mbtiArr;
                console.log("🚀 ~ getInfo ~ this.commonInfo.mbti:", this.commonInfo.mbti)

                // 提取性格类型描述
                let str1 = this.commonInfo.xgcsRes.txtshow;
                str1.split('\n').forEach(item => {
                  if (item.indexOf('——') > 0) {
                    this.characterStr = item.split('——')[1]
                  }
                });
              }
            }
          }
          console.log('this.commonInfo', this.commonInfo)
        })
      }
    })
  },
  methods: {
    randomUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    formatDate(date, format = 'yyyy-MM-dd') {
      const map = {
        'M': date.getMonth() + 1,
        'd': date.getDate(),
        'h': date.getHours(),
        'm': date.getMinutes(),
        's': date.getSeconds(),
        'q': Math.floor((date.getMonth() + 3) / 3),
        'S': date.getMilliseconds()
      };
      return format.replace(/([yMdhmsqS])+/g, (all, t) => {
        let v = map[t];
        if (v !== undefined) {
          if (all.length > 1) {
            v = '0' + v;
            v = v.substr(v.length - 2);
          }
          return v;
        } else if (t === 'y') {
          return (date.getFullYear() + '').substr(4 - all.length);
        }
        return all;
      });
    },
    resetData() {
      this.performanceModel = {
        other: [],
        rewardsPunishments: '',
        rewardsPunishmentsEvaluate: '',
        creator: '',
        checker: ''
      };
      this.assessmentModel = {
        moralConduct: [],
        jobHuntingConcerns: [],
        dailyBehavior: [],
        commonStatus: '',
        creator: '',
        checker: ''
      };
      this.manageModel = { creator: '', checker: '' };
      this.commonInfo = { xgcsRes: { id: '', type: '', txtshow: '' }, mbti: [], ncLevel: 0 };
      this.characterStr = '';
      this.videoList = [];
      this.videoModal.visible = false;
      this.currentVideo = { videoName: '', videoUrl: '' };
      this.selectedEvaluation = ''; // 重置评估类型
      this.confirmModal.visible = false; // 关闭确认弹窗
      this.evaluationData = {
        id: '',
        llqUserCode: '',
        staffName: '',
        evaluation: '', // 确保重置时包含 evaluation 属性
      };
    },
    async fetchData(userCode) {
      if (!userCode) return;
      this.isLoading = true;
      this.resetData(); // 重置数据以防旧数据残留

      try {
        const response = await this.$axios.get(`/jeecg-boot/app/ncStaffQualityAssessment/queryQualityAssessment?llqUserCode=${userCode}`);
        if (response.data && response.data.success && response.data.result) {

          // 安全地获取并处理 annotationInfo 数据
          const annotationInfo = response.data.result.annotationInfo || {};
          this.evaluationData = {
            id: annotationInfo.id || '',
            llqUserCode: annotationInfo.llqUserCode || '',
            staffName: annotationInfo.staffName || '',
            evaluation: annotationInfo.evaluation || '',
          };
          console.log("🚀 ~ fetchData ~ this.evaluationData:", this.evaluationData);

          const result = response.data.result;
          if (result.staffInfos[0]) {
            let info = result.staffInfos[0]
            this.commonInfo.name = info.staffName
            this.commonInfo.sex = info.sex == 'M' ? '男' : '女'
            this.commonInfo.age = info.age
            if (info.marriageorno == 1) {
              this.commonInfo.marriageorno = '未婚';
            } else if (info.marriageorno == 2) {
              this.commonInfo.marriageorno = '已婚';
            } else if (info.marriageorno == 3) {
              this.commonInfo.marriageorno = '已婚育';
            } else {
              this.commonInfo.marriageorno = info.marriageorno;
            }


            this.xgcs = ''
            this.commonInfo.xgcsRes = {}
            this.$axios.get('/jeecg-boot/nc/recruit/getHtmlShow?paperNum=' + info.paperNum).then((resa) => {
              if (resa.data.code == 200) {
                this.xgcs = resa.data.result.htmlshow
                this.commonInfo.xgcsRes = resa.data.result

                if (this.commonInfo.xgcsRes && this.commonInfo.xgcsRes.id) {
                  let str = this.commonInfo.xgcsRes.id.split('');
                  if (str) {
                    const lines = this.commonInfo.xgcsRes.txtshow.split('\n').map(line => line.trim());
                    const cleanLines = lines.filter(line => line.length > 0);
                    const mbtiArr = [];
                    for (let i = 0; i < cleanLines.length; i++) {
                      if (cleanLines[i].indexOf('E') > -1 && str.includes('E')) {
                        mbtiArr.push({
                          a: cleanLines[i].split('例如')[0],
                          b: '例如' + cleanLines[i].split('例如')[1]
                        });
                      } else if (cleanLines[i].indexOf('I') > -1 && str.includes('I')) {
                        mbtiArr.push({
                          a: cleanLines[i].split('例如')[0],
                          b: '例如' + cleanLines[i].split('例如')[1]
                        });
                      } else if (cleanLines[i].indexOf('S') > -1 && str.includes('S')) {
                        mbtiArr.push({
                          a: cleanLines[i],
                          b: cleanLines[i + 1]
                        });
                      } else if (cleanLines[i].indexOf('N') > -1 && str.includes('N')) {
                        mbtiArr.push({
                          a: cleanLines[i].split('例如')[0],
                          b: '例如' + cleanLines[i].split('例如')[1]
                        });
                      } else if (cleanLines[i].indexOf('T') > -1 && str.includes('T')) {
                        mbtiArr.push({
                          a: cleanLines[i],
                          b: cleanLines[i + 1]
                        });
                      } else if (cleanLines[i].indexOf('F') > -1 && str.includes('F')) {
                        mbtiArr.push({
                          a: cleanLines[i],
                          b: cleanLines[i + 1]
                        });
                      } else if (cleanLines[i].indexOf('J') > -1 && str.includes('J')) {
                        mbtiArr.push({
                          a: cleanLines[i],
                          b: cleanLines[i + 1]
                        });
                      } else if (cleanLines[i].indexOf('P') > -1 && str.includes('P')) {
                        mbtiArr.push({
                          a: cleanLines[i].split('例如')[0],
                          b: '例如' + cleanLines[i].split('例如')[1]
                        });
                      }
                    }
                    this.commonInfo.mbti = mbtiArr;
                    // 提取性格类型描述
                    let str1 = this.commonInfo.xgcsRes.txtshow;
                    str1.split('\n').forEach(item => {
                      if (item.indexOf('——') > 0) {
                        this.characterStr = item.split('——')[1]
                      }
                    });
                  }
                }
              }
              console.log('this.commonInfo', this.commonInfo)
            })
          }


          const staffNameFromResult =
            (result.performanceList && result.performanceList[0] && result.performanceList[0].staffName) ||
            (result.assessmentList && result.assessmentList[0] && result.assessmentList[0].staffName) ||
            (result.manageList && result.manageList[0] && result.manageList[0].staffName) ||
            'N/A';



          // 填充业绩能力数据
          if (result.performanceList && result.performanceList[0]) {
            const perfData = result.performanceList[0];
            this.performanceModel = {
              ...perfData,
              other: perfData.other ? perfData.other.split(',') : [],
              rewardsPunishments: perfData.rewardsPunishments || '',
              rewardsPunishmentsEvaluate: perfData.rewardsPunishmentsEvaluate || '',
              staffName: perfData.staffName || this.commonInfo.name, // 确保staffName
              creator: perfData.creator || this.commonInfo.name,
              checker: perfData.checker || '',
            };
          } else {
            this.performanceModel.llqUserCode = result.llqUserCode || '';
            this.performanceModel.staffName = this.commonInfo.name;
            this.performanceModel.creator = this.commonInfo.name;
          }

          // 填充性格品性数据
          if (result.assessmentList && result.assessmentList[0]) {
            const assessData = result.assessmentList[0];
            this.assessmentModel = {
              ...assessData,
              moralConduct: assessData.moralConduct ? assessData.moralConduct.split(',') : [],
              jobHuntingConcerns: assessData.jobHuntingConcerns ? assessData.jobHuntingConcerns.split(',') : [],
              dailyBehavior: assessData.dailyBehavior ? assessData.dailyBehavior.split(',') : [],
              commonStatus: assessData.commonStatus || '',
              staffName: assessData.staffName || this.commonInfo.name,
              creator: assessData.creator || this.commonInfo.name,
              checker: assessData.checker || '',
            };
          } else {
            this.assessmentModel.llqUserCode = result.llqUserCode || '';
            this.assessmentModel.staffName = this.commonInfo.name;
            this.assessmentModel.creator = this.commonInfo.name;
          }

          // 填充管理能力数据
          if (result.manageList && result.manageList[0]) {
            const manageData = result.manageList[0];
            this.manageModel = {
              ...manageData,
              staffName: manageData.staffName || this.commonInfo.name,
              creator: manageData.creator || this.commonInfo.name,
              checker: manageData.checker || '',
            };
          } else {
            this.manageModel.llqUserCode = result.llqUserCode || '';
            this.manageModel.staffName = this.commonInfo.name;
            this.manageModel.creator = this.commonInfo.name;
          }
          if (this.commonInfo.name === 'N/A') {
            this.commonInfo.name = this.performanceModel.staffName || this.assessmentModel.staffName || this.manageModel.staffName || 'N/A';
          }

          // 获取视频列表
          await this.getVideoList(userCode);

        } else {
          console.error("获取评估数据失败:", response.data ? response.data.message : '未知错误或网络问题');
          this.commonInfo.name = ''; // 清除姓名以显示"无数据"消息
        }
      } catch (error) {
        console.error("请求评估数据异常:", error);
        this.commonInfo.name = ''; // 清除姓名以显示"无数据"消息
      } finally {
        this.isLoading = false;
      }
    },
    getEduBackground(llqUserCode) {
      this.$axios.get(`/jeecg-boot/app/staff/getEduBackground?llqUserCode=${llqUserCode}`).then((res) => {
        if (res.data.success) {
          let eduArr = [...res.data.result]
          let highEdu = eduArr.sort((a, b) => this.sortEducational(b.education) - this.sortEducational(a.education));
          console.log("🚀 ~ this.$axios.get ~ highEdu:", highEdu)
          let qrzEdu = eduArr.filter(item => item.educationMsg == '全日制')
          console.log("🚀 ~ this.$axios.get ~ qrzEdu:", qrzEdu)
          this.commonInfo.highEducation = highEdu[0] && highEdu[0].education || '';
          this.commonInfo.highProfessional = highEdu[0] && highEdu[0].professional || '';
          this.commonInfo.qrzSchool = qrzEdu[0] && highEdu[0].school || '';
          this.commonInfo.qrzEducation = qrzEdu[0] && highEdu[0].education || '';
          this.commonInfo.qrzProfessional = qrzEdu[0] && highEdu[0].professional || '';
        } else {
          this.eduData = []
          this.commonInfo.highEducation = '';
          this.commonInfo.highProfessional = '';
          this.commonInfo.qrzSchool = '';
          this.commonInfo.qrzEducation = '';
          this.commonInfo.qrzProfessional = '';
        }
      })

    },
    sortEducational(val) {
      if (val == '小学') {
        return 1
      } else if (val == '初中') {
        return 2
      } else if (val == '中专') {
        return 3
      } else if (val == '大专') {
        return 4
      } else if (val == '本科') {
        return 5
      } else if (val == '硕士研究生') {
        return 6
      } else if (val == '博士研究生') {
        return 7
      }
    },
    // 视频相关方法
    playVideo(video) {
      this.currentVideo = {
        videoName: video.videoName,
        videoUrl: video.videoUrl
      };
      this.videoModal.visible = true;

      // 移动端显示横屏提示
      if (this.isMobile() && window.orientation !== undefined) {
        this.showRotateHint = Math.abs(window.orientation) !== 90;

        // 监听屏幕旋转
        window.addEventListener('orientationchange', this.handleOrientationChange);

        // 3秒后自动隐藏提示
        setTimeout(() => {
          this.showRotateHint = false;
        }, 3000);
      }
    },
    closeVideoModal() {
      this.videoModal.visible = false;
      this.currentVideo = {
        videoName: '',
        videoUrl: ''
      };
      this.showRotateHint = false;

      // 恢复头部显示
      const header = document.querySelector('.video-modal-header');
      if (header) {
        header.style.display = 'flex';
      }

      // 移除屏幕旋转监听
      window.removeEventListener('orientationchange', this.handleOrientationChange);

      // 退出全屏
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else if (document.webkitFullscreenElement) {
        document.webkitExitFullscreen();
      } else if (document.mozFullScreenElement) {
        document.mozCancelFullScreen();
      } else if (document.msFullscreenElement) {
        document.msExitFullscreen();
      }
    },
    handleOrientationChange() {
      // 屏幕旋转时的处理
      setTimeout(() => {
        if (window.orientation !== undefined) {
          this.showRotateHint = Math.abs(window.orientation) !== 90;

          // 横屏时优化视频播放器
          if (Math.abs(window.orientation) === 90) {
            this.optimizeVideoForLandscape();
          }
        }
      }, 100);
    },
    optimizeVideoForLandscape() {
      // 横屏时优化视频播放器显示
      const video = document.querySelector('.video-player');
      const modal = document.querySelector('.video-modal');
      const modalBody = document.querySelector('.video-modal-body');
      const header = document.querySelector('.video-modal-header');

      if (video && modal) {
        // 调整视频播放器样式 - 不占满全屏，居中显示
        video.style.width = '85vw';
        video.style.height = '75vh';
        video.style.objectFit = 'contain';
        video.style.position = 'relative';
        video.style.zIndex = '999999';
        video.style.margin = 'auto';
        video.style.borderRadius = '8px';

        // 调整模态框样式
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.zIndex = '9999';

        // 调整模态框主体样式
        if (modalBody) {
          modalBody.style.padding = '20px';
          modalBody.style.display = 'flex';
          modalBody.style.alignItems = 'center';
          modalBody.style.justifyContent = 'center';
          modalBody.style.boxSizing = 'border-box';
        }

        // 保持头部显示，但调整样式
        if (header) {
          header.style.display = 'flex';
          header.style.background = 'rgba(0, 0, 0, 0.8)';
          header.style.color = 'white';
        }
      }
    },
    handleVideoError() {
      console.error('视频加载失败');
      // 可以在这里添加错误处理逻辑
    },
    handleVideoLoaded() {
      // 视频加载完成后的处理
      console.log('视频加载完成');

      // 确保控制栏可见
      const video = document.querySelector('.video-player');
      if (video) {
        video.setAttribute('controls', 'true');
        video.style.position = 'relative';
        video.style.zIndex = '999999';
      }
    },
    handleVideoClick() {
      // 移动端点击视频时尝试进入全屏
      if (this.isMobile()) {
        this.requestFullscreen();
      }
    },
    isMobile() {
      // 检测是否为移动设备
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
    requestFullscreen() {
      // 请求全屏播放
      const video = document.querySelector('.video-player');
      if (video) {
        if (video.requestFullscreen) {
          video.requestFullscreen();
        } else if (video.webkitRequestFullscreen) {
          video.webkitRequestFullscreen();
        } else if (video.mozRequestFullScreen) {
          video.mozRequestFullScreen();
        } else if (video.msRequestFullscreen) {
          video.msRequestFullscreen();
        }

        // 尝试锁定屏幕方向为横屏
        if (screen.orientation && screen.orientation.lock) {
          screen.orientation.lock('landscape').catch(err => {
            console.log('无法锁定屏幕方向:', err);
          });
        }
      }
    },
    // 获取视频列表
    async getVideoList(userCode) {
      try {
        const response = await this.$axios.get(`/jeecg-boot/staff/file/getVideoInfo?llqUserCode=${userCode}`);
        if (response.data && response.data.result) {
          // 将所有列表数据展开赋值给videoList
          this.videoList = [
            ...(response.data.result.videoList || []),
            ...(response.data.result.communication || []),
            ...(response.data.result.continues || []),
            ...(response.data.result.interview || []),
            ...(response.data.result.promotion || []),
            ...(response.data.result.regularization || []),
            ...(response.data.result.transfer || [])
          ].map((item, index) => ({
            id: index + 1,
            videoName: item.name,
            videoUrl: item.imageUrl
          }));
        }
      } catch (error) {
        console.error('获取视频列表失败:', error);
        this.videoList = [];
      }
    },
    selectEvaluation(type) {
      this.selectedEvaluation = type;
      this.confirmModal.visible = true;
    },
    cancelConfirmation() {
      this.confirmModal.visible = false;
      this.selectedEvaluation = '';
    },
    confirmEvaluation() {
      this.confirmModal.visible = false;
      if (!this.selectedEvaluation) {
        alert('请选择一个评估类型！');
        return;
      }

      // 显示加载指示器
      Indicator.open({
        text: "正在加载中，请稍后……",
        spinnerType: "fading-circle"
      });

      // 准备请求参数
      const params = {
        creator: localStorage.getItem('userCode') || '',
        llqUserCode: this.userCode || '',
        evaluation: this.selectedEvaluation
      };

      // 发送请求
      this.$axios.get(`/jeecg-boot/app/ncStaffQualityAssessment/sendEvaluationToHr`, { params })
        .then(res => {
          // 关闭加载指示器
          Indicator.close();

          // 显示响应消息
          Toast({
            message: res.data.message || (res.data.code === 200 ? '操作成功' : '操作失败'),
            position: "bottom",
            duration: 2000
          });

          // 如果成功，更新本地评估数据
          if (res.data.code === 200) {
            this.evaluationData.evaluation = this.selectedEvaluation;
          }
        })
        .catch(err => {
          // 处理错误
          Indicator.close();
          console.error('提交评估失败:', err);
          Toast({
            message: '提交失败，请稍后重试',
            position: "bottom",
            duration: 2000
          });
        });
    }
  }
};
</script>

<style scoped>
/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 主容器 */
.pce-container {
  padding: 16px;
  background-color: #f8f9fa;
  max-width: 800px;
  margin: 0 auto;
  text-align: left;
}

/* 顶部标题栏 */
.pce-header {
  margin-bottom: 16px;
  text-align: center;
  padding: 20px 0;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
}

.pce-employee-id {
  font-size: 20px;
  font-weight: 600;
  color: #4285f4;
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
}

.pce-employee-id:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #4285f4;
  border-radius: 2px;
}

/* 通用部分样式 */
.pce-section {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.pce-section:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  transform: translateY(-4px);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  border-bottom: 1px solid #e8eaed;
  background-color: #ffffff;
}

.section-icon {
  margin-right: 14px;
  font-style: normal;
  font-size: 24px;
  color: #4285f4;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #202124;
}

.sub-header {
  font-size: 16px;
  font-weight: 500;
  color: #4285f4;
  margin: 18px 24px 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e8eaed;
}

/* 基本信息列表 */
.basic-info-list {
  padding: 12px 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 10px;
}

.basic-info-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 0;
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  border: 1px solid #ebedf0;
}

.basic-info-item:hover {
  background-color: #e8f0fe;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  border-color: #c0d7fe;
}

.basic-info-item label {
  color: #5f6368;
  font-size: 12px;
  font-weight: 500;
  min-width: 70px;
  margin-right: 8px;
  margin-bottom: 0;
  white-space: nowrap;
}

.basic-info-item span {
  color: #202124;
  font-weight: 500;
  word-break: break-word;
  font-size: 13px;
  flex: 1;
  text-align: left;
}

.employee-code {
  font-size: 13px;
  color: #5f6368;
  font-weight: normal;
  margin-left: 8px;
}

/* 评估项目样式 */
.eval-list {
  padding: 16px 24px;
}

.eval-item {
  margin-bottom: 20px;
  border-radius: 8px;
  padding: 16px;
  background-color: #f5f7fa;
  border-left: 4px solid #4285f4;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease;
  border: 1px solid #ebedf0;
  border-left-width: 4px;
}

.eval-item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
  background-color: #f8fafd;
}

.eval-item:last-child {
  margin-bottom: 0;
}

.eval-title {
  font-weight: 600;
  text-align: left;
  color: #202124;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-size: 15px;
}

.eval-title:before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #4285f4;
  border-radius: 50%;
  margin-right: 10px;
}

.eval-content {
  color: #3c4043;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 10px;
  word-break: break-word;
  padding-left: 18px;
}

.eval-result {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  margin-top: 10px;
  padding-left: 18px;
}

.result-tag {
  background-color: #e8f0fe;
  color: #1a73e8;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 简化的评估项目 (没有评估结果) */
.simple-item {
  padding: 16px;
  margin-bottom: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.25s ease;
  border: 1px solid #ebedf0;
}

.simple-item:hover {
  background-color: #e8f0fe;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #c0d7fe;
}

.simple-item .eval-title {
  margin-bottom: 8px;
}

.simple-item .eval-content {
  margin-bottom: 0;
}

/* MBTI结果样式 */
.mbti-item {
  margin-top: 8px;
  padding-left: 18px;
  color: #5f6368;
  font-size: 13px;
  position: relative;
}

.mbti-item:before {
  content: "•";
  position: absolute;
  left: 6px;
  color: #4285f4;
  font-size: 16px;
}

/* 编制信息 */
.creator-info {
  display: flex;
  justify-content: space-between;
  padding: 14px 24px;
  font-size: 13px;
  color: #5f6368;
  border-top: 1px solid #e8eaed;
  background-color: #ffffff;
}

.creator-info span {
  display: flex;
  align-items: center;
}

.creator-info span:before {
  content: "👤";
  margin-right: 8px;
  font-size: 14px;
}

/* 加载和无数据状态 */
.pce-loading,
.pce-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  color: #5f6368;
}

.loading-spinner {
  border: 4px solid #f1f3f4;
  border-top: 4px solid #4285f4;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #5f6368;
}

.no-data-tip {
  font-size: 14px;
  color: #5f6368;
  margin-top: 10px;
}

/* 视频相关样式 */
.video-list {
  padding: 16px 24px;
}

.video-item {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #4285f4;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease;
}

.video-item:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-title {
  font-size: 16px;
  font-weight: 600;
  color: #202124;
  margin: 0;
}

.video-actions {
  display: flex;
  gap: 8px;
}

.play-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.play-btn:hover {
  background-color: #3367d6;
}

.no-video {
  text-align: center;
  padding: 40px 24px;
  color: #5f6368;
}

.no-video-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 视频弹窗样式 */
.video-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.video-modal {
  background-color: white;
  border-radius: 12px;
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.video-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e8eaed;
  background-color: #f8f9fa;
}

.video-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #202124;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #5f6368;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #e8eaed;
}

.video-modal-body {
  padding: 24px;
}

.video-player {
  width: 80%;
  height: 80%;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
}

/* 确保视频控制栏在所有状态下都可见 */
.video-player::-webkit-media-controls-panel {
  position: relative !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.8) !important;
  z-index: 999999 !important;
}

.video-player::-webkit-media-controls {
  position: relative !important;
  z-index: 999999 !important;
}

.video-player::-webkit-media-controls-overlay-play-button {
  position: relative !important;
  z-index: 999999 !important;
}

/* 移动端视频全屏样式 */
.video-player:fullscreen {
  width: 90vw;
  height: 80vh;
  object-fit: contain;
  background: #000;
  position: relative;
  z-index: 999;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-player:-webkit-full-screen {
  width: 90vw;
  height: 80vh;
  object-fit: contain;
  background: #000;
  position: relative;
  z-index: 999;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-player:-moz-full-screen {
  width: 90vw;
  height: 80vh;
  object-fit: contain;
  background: #000;
  position: relative;
  z-index: 999;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-player:-ms-fullscreen {
  width: 90vw;
  height: 80vh;
  object-fit: contain;
  background: #000;
  position: relative;
  z-index: 999;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-error {
  text-align: center;
  padding: 40px;
  color: #ea4335;
}

/* 横屏提示样式 */
.rotate-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  z-index: 1000;
  animation: fadeInOut 3s ease-in-out;
}

.rotate-icon {
  font-size: 48px;
  margin-bottom: 12px;
  animation: rotate 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(-15deg);
  }

  75% {
    transform: rotate(15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 评估按钮组样式 */
.evaluation-buttons {
  padding: 16px 24px;
  background-color: #f5f7fa;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin-top: 20px;
}

.evaluation-title {
  font-size: 16px;
  font-weight: 600;
  color: #202124;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e8eaed;
}

.evaluation-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.eval-btn {
  background-color: #e8f0fe;
  color: #1a73e8;
  border: 1px solid #c0d7fe;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.eval-btn:hover {
  background-color: #c0d7fe;
  border-color: #90c0fe;
  color: #0d47a1;
}

.eval-btn.active {
  background-color: #1a73e8;
  color: white;
  border-color: #1a73e8;
}

.eval-btn.active:hover {
  background-color: #0d47a1;
  border-color: #0d47a1;
}

/* 评估确认弹窗样式 */
.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  /* 确保在视频弹窗之上 */
}

.confirm-modal {
  background-color: white;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  padding: 24px;
}

.confirm-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8eaed;
  margin-bottom: 20px;
}

.confirm-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #202124;
}

.confirm-modal-body p {
  font-size: 16px;
  color: #3c4043;
  margin-bottom: 20px;
}

.confirm-modal-footer {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.confirm-btn,
.cancel-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.confirm-btn {
  background-color: #4285f4;
  color: white;
}

.confirm-btn:hover {
  background-color: #3367d6;
}

.cancel-btn {
  background-color: #f5f7fa;
  color: #5f6368;
  border: 1px solid #e0e0e0;
}

.cancel-btn:hover {
  background-color: #e8eaed;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .pce-container {
    padding: 12px;
  }

  .basic-info-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .section-header {
    padding: 14px 18px;
  }

  .sub-header {
    margin: 14px 18px 8px;
  }

  .basic-info-list,
  .eval-list,
  .video-list {
    padding: 12px 18px;
  }

  .creator-info {
    padding: 12px 18px;
    flex-direction: column;
    gap: 8px;
  }

  .video-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .video-modal {
    max-width: 95%;
    max-height: 95%;
  }

  .video-modal-header {
    padding: 12px 16px;
  }

  .video-modal-body {
    padding: 16px;
  }

  .evaluation-buttons {
    padding: 12px 18px;
  }

  .evaluation-options {
    flex-direction: column;
    gap: 8px;
  }

  .eval-btn {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
  }

  .confirm-modal {
    max-width: 95%;
    padding: 20px;
  }

  .confirm-modal-header {
    padding-bottom: 12px;
    margin-bottom: 15px;
  }

  .confirm-modal-header h3 {
    font-size: 18px;
  }

  .confirm-modal-body p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .confirm-modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .confirm-btn,
  .cancel-btn {
    width: 100%;
    padding: 12px 20px;
  }
}

/* 移动端横屏优化 */
@media screen and (max-width: 767px) and (orientation: landscape) {
  .video-modal {
    max-width: 100%;
    max-height: 100%;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }

  .video-modal-header {
    padding: 8px 16px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    height: auto;
    min-height: 40px;
  }

  .video-modal-body {
    padding: 20px;
    /* 添加内边距 */
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    position: relative;
    box-sizing: border-box;
  }

  .video-player {
    width: 55vw;
    /* 减少宽度到85% */
    height: 55vh;
    /* 减少高度到75% */
    max-width: none;
    border-radius: 8px;
    object-fit: contain;
    position: relative;
    z-index: 10000;
    margin: auto;
    /* 居中显示 */
  }

  /* 确保视频控制栏可见 */
  .video-player::-webkit-media-controls-panel {
    position: relative;
    z-index: 10002;
    background: rgba(0, 0, 0, 0.8);
  }

  .video-player::-webkit-media-controls {
    position: relative;
    z-index: 10002;
  }
}

/* 移动端视频播放按钮优化 */
@media screen and (max-width: 767px) {
  .play-btn {
    padding: 12px 20px;
    font-size: 16px;
    touch-action: manipulation;
  }

  .video-item {
    padding: 20px 16px;
  }

  .video-title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .video-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.basic-info-item.long-text {
  grid-column: span 2;
}

.basic-info-item.long-text span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.basic-info-item.long-text:hover span {
  overflow: visible;
  white-space: normal;
  word-break: break-word;
}

@media (max-width: 767px) {
  .basic-info-item.long-text {
    grid-column: 1;
  }
}
</style>