<template>
    <div class="order">
        <van-nav-bar
            title="宿舍-账单查询"
            left-text="返回"
            right-text="筛选"
            left-arrow
            @click-left="onClickLeft"
            @click-right="onClickRight"
        />

        <van-popup v-model="show" position="top" :style="{ height: '25%' }">
            <van-nav-bar
                title="查询条件"
            />
            <van-field
                v-model="queryList[activeName].yearMonth"
                clearable
                disabled
                label="年月"
                @click="chooseDate"
                placeholder="请输入年月（例如：202301）"
            />

            <van-popup v-model="showDate" position="top" :style="{ height: '50%' }">
                <van-datetime-picker
                    v-model="currentDate"
                    type="year-month"
                    :min-date="minDate"
                    :formatter="formatter"
                    @confirm="handleConfirm"
                    @cancel="handleCancel"
                />
            </van-popup>

            
            
            <van-row  gutter="8" style="margin-top:8px;">
                <van-col span="12">
                    <van-button @click="resetQuery" style="width: 80%;">
                        重置
                    </van-button>
                </van-col>
                <van-col span="12">
                    <van-button type="info" @click="searchQuery" style="width: 80%;">
                        查询
                    </van-button>
                </van-col>
            </van-row>
            
        </van-popup>

        <van-tabs v-model="activeName" @change="handleChange">
            <van-tab :name="0" title="个人账单">
                <van-list
                    v-model="personLoading"
                    :finished="personFinished"
                    finished-text="没有更多了"
                    @load="onPersonLoad"
                    >
                    
                    <van-cell v-for="item,index in personBillList"
                        :key="index" >


                        <van-cell-group>
                            <van-cell title="年月" :value="item.yearMonth" />
                            <van-cell title="员工编号" :value="item.userCode" />
                            <van-cell title="员工姓名" :value="item.userName" />
                            <van-cell title="水费" :value="item.waterBill" />
                            <van-cell title="电费" :value="item.electricityBill" />
                            <van-cell title="租金" :value="item.rent" />
                            <van-cell title="水电总费用" :value="item.total" />
                        </van-cell-group>
                    </van-cell>
                </van-list>
            </van-tab>
            <van-tab :name="1" title="宿舍账单">
                <van-list
                    v-model="dormitoryLoading"
                    :finished="dormitoryFinished"
                    finished-text="没有更多了"
                    @load="onDormitoryLoad"
                    >
                    
                    <van-cell v-for="item,index in dormitoryBillList"
                        :key="index" >


                        <van-cell-group>
                            <van-cell title="年月" :value="item.yearMonth" />
                            <van-cell title="区域" :value="item.areaId_dictText" />
                            <van-cell title="楼栋" :value="item.buildingId_dictText" />
                            <van-cell title="房间" :value="item.roomId_dictText" />
                            <van-cell title="水费标准" :value="item.waterPrice" />
                            <van-cell title="用水量" :value="item.waterConsumption" />
                            <van-cell title="水费" :value="item.waterBill" />
                            <van-cell title="电费标准" :value="item.electricityPrice" />
                            <van-cell title="用电量" :value="item.powerConsumption" />
                            <van-cell title="电费" :value="item.electricityBill" />
                            
                        </van-cell-group>
                    </van-cell>
                </van-list>
            </van-tab>
        </van-tabs>
        

        

        

        

        <!-- <van-button type="primary" @click="submit()"  style="margin:5px;width:40%;border-radius:10px;">提交</van-button> -->
        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            queryList:[
                {
                    userCode:null,
                    yearMonth:'',
                    pageNo:1,
                    pageSize:10,
                    total:0,
                },
                {
                    userCode:null,
                    yearMonth:'',
                    pageNo:1,
                    pageSize:10,
                    total:0,
                },
            ],
            info:{
                oldPwd:"",
                newPwd:"",
                checkPwd:"",
                radio:'2',
                sendType:'立刻',
                areaId:'',
                buildId:'',
                roomId:'',
                content:''
            },
            activeName:0,
            show:false,
            showDate:false,
            personLoading: false,
            dormitoryLoading: false,
            personBillList:[],
            dormitoryBillList:[],
            personFinished:false,
            dormitoryFinished:false,

            currentDate: new Date(),
            minDate: new Date(1980, 1, 1),
            // maxDate: new Date(2300, 12, 31),
        }
    },
    created:function(){
        let that = this;
        this.queryList[0].userCode = localStorage.getItem('userCode');
        this.queryList[1].userCode = localStorage.getItem('userCode');
        that.$axios.get('/dormApi/dm/dmHydropowerUsage/app/getHistoryPersonalBill',{params:that.queryList[that.activeName]}).then(res=>{
            console.log('res:',res);
            if(res.data.success){
                that.queryList[that.activeName].total = res.data.result.total;
                for(let i = 0; i < res.data.result.records.length; i++) {
                    
                    that.personBillList.push(res.data.result.records[i])
                    
                }
                that.show = false;
                console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.personBillList)
                console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.dormitoryBillList)
            }
        })

        
        
    },
    methods:{
        onClickLeft() {
            this.$router.go(-1);
        },
        onClickRight() {
            this.show = true;
        },
        handleChange(name,title) {
            console.log("🚀 ~ file: DmBill.vue:122 ~ handleChange ~ name,title:", name,title,this.activeName)
            let that = this;
            if((that.activeName == 0 && that.personBillList.length == 0) || (that.activeName == 1 && that.dormitoryBillList.length == 0)){
                that.searchQuery();
            }
        },
        resetQuery() {
            let that = this;
            that.queryList[that.activeName].yearMonth = null;

            if(that.activeName == 0){
                that.personBillList = [];
            } else if(that.activeName == 1) {
                that.dormitoryBillList = [];
            }

            that.searchQuery();
        },
        searchQuery() {
            let that = this;

            that.queryList[that.activeName].pageNo = 1;
            if(that.activeName == 0){
                that.personBillList = [];
                that.$axios.get('/dormApi/dm/dmHydropowerUsage/app/getHistoryPersonalBill',{params:that.queryList[that.activeName]}).then(res=>{
                    console.log('res:',res);
                    if(res.data.success){
                        that.queryList[that.activeName].total = res.data.result.total;
                        for(let i = 0; i < res.data.result.records.length; i++) {
                            
                            that.personBillList.push(res.data.result.records[i])
                            
                        }
                        
                        console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.personBillList)
                        console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.dormitoryBillList)
                    }
                    that.show = false;
                })
            } else if(that.activeName == 1) {
                that.dormitoryBillList = [];
                that.$axios.get('/dormApi/dm/dmHydropowerUsage/app/getHistoryRoomBill',{params:that.queryList[that.activeName]}).then(res=>{
                    console.log('res:',res);
                    if(res.data.success){
                        that.queryList[that.activeName].total = res.data.result.total;
                        for(let i = 0; i < res.data.result.records.length; i++) {
                            
                            that.dormitoryBillList.push(res.data.result.records[i])
                            
                        }
                        console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.personBillList)
                        console.log("🚀 ~ file: DmChoosePerson.vue:130 ~ that.$axios.get ~ that.listData:", that.dormitoryBillList)
                    }
                    that.show = false;
                })
            }
            
        },
        onPersonLoad() {
            // 异步更新数据
            let that = this;
            setTimeout(() => {
                that.queryList[that.activeName].pageNo = that.queryList[that.activeName].pageNo + 1;
                that.$axios.get('/dormApi/dm/dmHydropowerUsage/app/getHistoryPersonalBill',{params:that.queryList[that.activeName]}).then(res=>{
                    console.log('res:',res);
                    if(res.data.code==200){
                        that.queryList[that.activeName].total = res.data.result.total;
                        for(let i = 0; i < res.data.result.length; i++) {
                            
                            that.personBillList.push(res.data.result[i])
                        }
                    }
                })
                
                // 加载状态结束
                this.personLoading = false;

                // 数据全部加载完成
                if (this.personBillList.length >= that.queryList[that.activeName].total) {
                    this.personFinished = true;
                }
            }, 500);
        },
        onDormitoryLoad() {
            // 异步更新数据
            let that = this;
            setTimeout(() => {
                that.queryList[that.activeName].pageNo = that.queryList[that.activeName].pageNo + 1;
                that.$axios.get('/dormApi/dm/dmHydropowerUsage/app/getHistoryRoomBill',{params:that.queryList[that.activeName]}).then(res=>{
                    console.log('res:',res);
                    if(res.data.code==200){
                        that.queryList[that.activeName].total = res.data.result.total;
                        for(let i = 0; i < res.data.result.length; i++) {
                            
                            that.dormitoryBillList.push(res.data.result[i])
                        }
                    }
                })
                
                // 加载状态结束
                this.dormitoryLoading = false;

                // 数据全部加载完成
                if (this.dormitoryBillList.length >= that.queryList[that.activeName].total) {
                    this.dormitoryFinished = true;
                }
            }, 500);
        },
        submit(){
            let self=this
            
            if(self.info.oldPwd==null || self.info.oldPwd==""){
                Toast({
                    message: "原密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd==null || self.info.newPwd==""){
                Toast({
                    message: "新密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd!=self.info.checkPwd){
                Toast({
                    message: "新密码和确认密码不一致！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            let params={
                userCode:localStorage.getItem('userCode'),
                oldPwd:self.info.oldPwd,
                newPwd:self.info.newPwd
            }

            self.$axios.post('/jeecg-boot/app/user/updatePwd',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })


        },
        handleConfirm(value) {
            console.log("🚀 ~ file: DmBill.vue:357 ~ handleConfirm ~ value:", value)
            let res = this.formatDate(value,'yyyyMM');
            console.log("🚀 ~ file: DmBill.vue:360 ~ handleConfirm ~ res:", res)
            this.queryList[this.activeName].yearMonth = res;
            this.showDate = false;
        },
        handleCancel() {
            this.showDate = false;
        },
        formatter(type, value) {
            console.log("🚀 ~ file: DmBill.vue:356 ~ formatter ~ type, value:", type, value)
            if (type === 'year') {
                return `${value}`;
            } else if (type === 'month') {
                return `${value}`
            }
            return value;
        },

        chooseDate() {
            this.showDate = true;
        },
        
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month
        }
    }
}
</script>
<style scoped>

</style>