<template>
    <div>
        <van-nav-bar title="新增维修工单" right-text="查看当班维修工" left-arrow @click-left="onClickLeft"
            @click-right="onClickRight" />
        <van-popup v-model="showPopup" position="right" :style="{ height: '100%', width: '100%' }">
            <van-cell v-for="(item) in onWrokPeople" :key="item.telephone" :title="item.userName"
                :value="item.telephone" :label="'维修工单数:'+item.count" @click="callPhone(item)" style='margin-bottom:5%' />
            <van-button type="info" round @click="closePopup"
                style="width: 100%;height:5%;position: absolute;bottom:2%;left:0;">
                关闭
            </van-button>
        </van-popup>



        <van-form validate-first @submit="onSubmit">
            <van-field readonly clickable name="lineType" :value="lineType" label="在线/离线：" placeholder="点击选择"
                @click="showPicker2 = true" />
            <van-popup v-model="showPicker2" position="bottom">
                <van-picker show-toolbar :columns="columns2" @confirm="onConfirm2" @cancel="showPicker2 = false" />
            </van-popup>
            <van-field-Pick v-model="lineStatus" :disabled="disabled" label="是否停线" required
                :columns="['是', '否']" />
            <van-field readonly clickable name="faultType" :value="faultType" label="机修/电工：" placeholder="点击选择"
                @click="showPicker1 = true" />
            <van-popup v-model="showPicker1" position="bottom">
                <van-picker show-toolbar :columns="columns1" @confirm="onConfirm1" @cancel="showPicker1 = false" />
            </van-popup>

            <van-field v-model="operator" label="操作人员编号" placeholder="请输入操作人员编号"
                :rules="[{ validator, message: '请输入正确内容' }]" />
            <van-field v-model="operatorName" label="操作人员姓名" readonly />

            <!-- <div v-if="machineArr.length > 0">
                <van-field readonly clickable name="machine" :value="machine" label="设备名称：" placeholder="点击选择"
                    @click="showPicker3 = true" />
                <van-popup v-model="showPicker3" position="bottom">
                    <van-picker show-toolbar :columns="columns3" @confirm="onConfirm3" @cancel="showPicker3 = false" />
                </van-popup>
            </div> -->
            <div>
                <van-field v-model="info.deviceNo" disabled name="info.deviceNo" label="设备编号：" placeholder="请输入设备编号" />
                <van-field v-model="machine" disabled name="machine" label="设备名称：" placeholder="请输入设备名称" />
            </div>

            <van-field v-model="describe" name="describe" rows="3" autosize label="故障描述：" type="textarea"
                placeholder="请输入故障描述" />
            <van-field name="uploader" label="">
                <template #input>
                    <van-uploader v-model="uploader" :after-read="afterRead" :before-delete="beforeDel"
                        :max-size="10000 * 1024" @oversize="onOversize" />
                </template>
            </van-field>

            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">提交</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
import { Indicator, MessageBox } from 'mint-ui';
export default {
    data() {
        return {
            info: {},
            showPopup: false,
            lpId: '',
            lineType: '',
            lineStatus:'',
            faultType: '',
            machine: '',
            describe: '',
            deviceNo:'',
            pictureList: [],
            uploader: [],
            orderItem:[],
            // 操作人员编号
            operator: '',
            // 操作人员姓名
            operatorName: '',

            columns1: ['机修', '电工'],
            columns2: ['在线', '离线'],
            columns3: [],
            columns4: ['是', '否'],
            showPicker1: false,
            showPicker2: false,
            showPicker3: false,
            onWrokPeople: [],
            machineArr: [],
        }
    },
    created() {
        this.info = {
            moId: this.$route.params.moId,
            lpId: this.$route.params.lpId,
            status: this.$route.params.type,
            deviceNo: this.$route.params.deviceNo,
            creator: this.$route.params.creator,
            createNo: this.$route.params.createNo,
            jobCenter: this.$route.params.jobCenter,
        };
        this.orderItem = JSON.parse(localStorage.getItem("orderOtherItem"));
        this.$axios.get(`/jeecg-boot/ncApp/parts/getMachineInfo?id=${this.info.deviceNo}`)
            .then(res => {
                if (res.data.code == 200) {
                    this.machine = res.data.result.deviceName
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        if (this.info.moId == '非计件') {
            this.lineType = '离线'
        } else {
            this.lineType = '在线'
        }
        this.lineStatus='否'
        this.faultType='机修';
        this.operator = this.info.createNo;
        this.operatorName = this.info.creator;

    },
    methods: {

        // 通过编号搜索名字
        validator(value) {
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        this.operatorName = res.data.message
                        return true
                    } else {
                        this.operatorName = ''
                        Toast({
                            message: '操作人员编号不正确',
                            position: "bottom",
                            duration: 2000
                        });
                        return false
                    }
                });
        },

        // 限制图片大小
        onOversize(file) {
            console.log(file);
            Toast({
                message: '文件大小不能超过 10M',
                position: "bottom",
                duration: 2000
            });
        },
        //上传图片
        afterRead(file, name) {
            const param = new FormData()
            param.append('file', file.file)
            param.append('description', "")
            param.append('type', "")
            this.$axios
                .post(`/jeecg-boot/app/mac/uploadPic`, param)
                .then(res => {
                    if (res.data.code == 200) {
                        this.pictureList.push({ picUrl: res.data.message })
                    } else {
                        this.uploader.splice(name.index, 1)
                        Toast({
                            message: '上传失败,请选择图片上传',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        //删除图片
        beforeDel(file, name) {
            Dialog.confirm({
                message: '确定删除吗?',
                theme: 'round-button',
                confirmButtonColor: '#1989fa',
                cancelButtonColor: '#CCCCCC',
            }).then(() => {
                this.$axios
                    .delete(`/jeecg-boot/app/mac/deletePic?id=${''}&picUrl=${this.pictureList[name.index].picUrl}`)
                    .then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: '删除成功',
                                position: "bottom",
                                duration: 2000
                            });
                        } else {
                            this.uploader.splice(name.index, 1)
                            Toast({
                                message: '删除失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
                this.uploader.splice(name.index, 1)
                this.pictureList.splice(name.index, 1)
            }).catch(() => {
                Toast({
                    message: '取消',
                    position: "bottom",
                    duration: 1000
                });
            });
        },
        onSubmit(values) {
            if (this.faultType == "" || this.machine == "" || this.describe == "" || this.operator == "" || this.operatorName == "") {
                Toast({
                    message: '相关信息不完整',
                    position: "bottom",
                    duration: 2000
                });
            } else {
                values.pictureList = this.pictureList
                values.lineType = this.lineType
                values.lineStatus = this.lineStatus
                values.operator = this.operator
                values.machineId = this.info.deviceNo
                values.createNo = localStorage.getItem('userCode')
                Object.assign(values, this.info)

                Indicator.open({
                    text: '正在加载中，请稍后……',
                    spinnerType: 'fading-circle'
                });
                this.$axios.post(`/jeecg-boot/app/mac/addRepairRecord`, values).then(res => {
                    if (res.data.code == 200) {
                        Indicator.close();
                        let params = {
                            lpId: this.info.lpId,
                            type: '11',
                            createNo: localStorage.getItem('userCode'),
                            creator: localStorage.getItem('userName')
                        }
                        this.sendModes(params);
                    } else {
                        Indicator.close();
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            }
        },
        sendModes(params){
            let self = this
            
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.orderItem.lastStatus="11";
                    localStorage.setItem('orderOtherItem',JSON.stringify(this.orderItem));
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                } else {
                    Indicator.close();
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        onClickRight() {

            this.showPopup = true;
            this.$axios
                .get(`/jeecg-boot/app/mac/getRepairClass?id=${this.$route.params.lpId}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.onWrokPeople = res.data.result
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        closePopup() {
            this.showPopup = false;
        },
        onClickLeft() {
            this.$router.replace({
                name: "OrderStart",
            });
        },
        // 拨打电话
        callPhone(item) {
            this.showPopup = false;
            MessageBox.confirm('是否确认需要拨打电话?').then(action => {
                if (action == 'confirm') {
                    window.location.href = 'tel:' + item.telephone
                }
            });
        },
        onConfirm1(value) {
            this.faultType = value;
            this.showPicker1 = false;
        },
        onConfirm2(value) {
            this.lineType = value;
            this.showPicker2 = false;
        },
        onConfirm3(value) {
            this.machine = value;
            this.showPicker3 = false;
        },
    },
}
</script>

<style lang="scss" scoped>

</style>