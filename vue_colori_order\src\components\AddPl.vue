<template>
    <div class="order">

        <div class="top-title">
            <div class="title_order_user">
                <span>配料清单</span>
            </div>
            <div class="top-message">
                <img :src="message" width="70%" />
            </div>
        </div>




        <div v-for="(item, i) in items" :key="i" class="item_pl">
            <a-select placeholder="请选择订单号" v-model="item.moId" style="width: 100%; margin-right: 4px;" @change="getBookByMo(i)">
                <a-select-option v-for="c in moIds" :key="c">{{c}}</a-select-option>
            </a-select>
            <a-input placeholder="账套" disabled style="width: 100%; margin-right: 4px;margin-top:2px" v-model="item.book"/>
              
              <a-input disabled class="fontColor" placeholder="名称" style="width: 100%; margin-right: 4px" v-model="item.name"/>
              <a-input disabled class="fontColor" placeholder="计划量" style="width: 48%; margin-right: 4px" v-model="item.plot"/>
              <a-input disabled class="fontColor" placeholder="单品数量" style="width: 48%; margin-right: 4px" v-model="item.dpsl"/>
              <a-input disabled class="fontColor" placeholder="物料编码" v-model="item.code"/>
              <a-input placeholder="锅数" style="width: 48%; margin-right: 4px" v-model="item.pot"/>
              <a-input placeholder="实际产量" style="width: 48%; margin-right: 4px" v-model="item.output"/>
              <a-icon
                v-if="item.isNew"
                class="dynamic-delete-button"
                style="margin:20px;"
                type="minus-circle-o"
                @click="() => remove(item)"
              />
        </div>

        <div>
            <a-button @click="addItem" style="margin-top:10%;width:80%;height:3rem" type="dashed" icon="plus" >新增</a-button>
            <a-button @click="uploadItem" style="margin-top:10%;width:80%;height:3rem" type="primary" icon="upload" >提交</a-button>
            <a-button @click="close" style="margin:10%;width:80%;height:3rem" type="danger" icon="close" >关闭</a-button>
        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            search:require('../../static/images/search.png'),
            add:require('../../static/images/userAdd.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal:'',
            value:false,
            personList:[],
            addUserInfo:[],
            searchKey:'',
            userInfo:[],
            item:{},
            questionType:'',
            questionTypeVal:'',
            status:'',
            lpId:'',
            workDay:'',
            num:1,
            items:[],
            createTime:'',
            popupVisible:false,
            glueOrderInfo:[],
            glueOrder:[],
            moIds:[],
            books:[],
            popupSlots:[
                {
                    values:[
                        '白班(上午)','白班(下午)','白班(加班)','晚班(上半夜)','晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:function(){
        let self=this;
        self.item=this.$route.params
        self.workDay=self.item.workDay
        self.lpId=self.item.lpId
        self.items=self.item.materialList
        self.getGlueOrder();
    },
    methods:{
        getBookByMo(index){
            console.log(index)
            for(var i=0;i<this.moIds.length;i++){
                if(this.moIds[i]==this.items[index].moId){
                    this.items[index].book=this.books[i]
                    this.$set(this.items,index,this.items[index])
                    this.getGlueOrderInfo(this.items[index],index)
                }
            }
        },
        getGlueOrderInfo(item,i){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getGlueOrderInfo',{params:{moId:item.moId,book:item.book}}).then(res=>{
                if(res.data.code==200){
                    self.glueOrderInfo=res.data.result
                    item.name=self.glueOrderInfo[0].name
                    item.dpsl=self.glueOrderInfo[0].dpsl
                    item.code=self.glueOrderInfo[0].code
                    item.plot=self.glueOrderInfo[0].plot
                    this.$set(this.items,i,item)
                }
            })
        },
        getGlueOrder(){
            let self=this;
            let userCode=localStorage.getItem('userCode');
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getGlueOrder',{params:{userCode:userCode,workDay:self.workDay}}).then(res=>{
                if(res.data.code==200){
                    self.glueOrder=res.data.result
                    for(var i=0;i<self.glueOrder.length;i++){
                        self.moIds.push(self.glueOrder[i].moId)
                        self.books.push(self.glueOrder[i].book)
                    }
                }
            })
        },
        close(){
            this.$router.go(-1);
        },
        addItem() {
            let params={
                keyId:this.num,
                isNew:true,
            }
            this.items.push(params);
            this.num++;
        },
        uploadItem(){
            let self=this;
            console.log(self.items)
            for(var i=0;i<self.items.length;i++){
                if(self.items[i].moId==null || self.items[i].moId==''){
                    Toast({
                        message: "存在未输入完整清单，请填写完整或删除！",
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            }
            let params={
                lpId:self.lpId,
                gcMaterialList:self.items
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getMaterialDeploy',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        remove(keyId) {
            this.items.pop(keyId);
        },
        addUserToServe(){
            let self=this;
            if(self.addUserInfo.length<=0){
                Toast({
                    message: "请先添加人员！",
                    position: 'bottom',
                    duration: 2000
                });
                return
            }
            console.log(self.status)
            if(self.status=='0'){
                self.notStartToAdd();
            }else{
                self.startedToAdd();
            }
            
        },
        notStartToAdd(){
            let self=this;
            let userCode=localStorage.getItem('userCode')
            let params={
                type:'1',
                gcWorkPlanAppList:self.addUserInfo,
                creator:userCode
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/changePlanInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        startedToAdd(){
            let self=this;
            let itemType='1';
            if(self.item.lastStatus='2'){
                itemType='8'
            }
            let params={
                type:itemType,
                lpId:self.lpId,
                gcWorkOperationList:self.addUserInfo,
            }
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getWorkDeploy',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        searchUser(){
            let self=this;
            let url="";
            if(self.status=='0'){
                //未开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreeWorker";
            }else{
                //开线情况查询
                url="/jeecg-boot/app/gcWorkshop/getFreePeopleInfo";
            }

            self.$axios.get(url,{params:{secinfo:self.searchKey,createTime:self.createTime,lpId:self.lpId}}).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.userInfo=res.data.result
                    console.log(self.userInfo)
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })

        },
        openQuestionType(){
            this.popupVisible = true;
        },
        popupOk(){
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        addPerson(item){
            let self=this;
            item.lpId=self.lpId;
            // item.pid=item.id;
            console.log(item)
            this.addUserInfo.push(item);
            this.userInfo.splice(this.userInfo.indexOf(item),1);
        },
        selectData(){
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}
.title_order_user{
    font-size: 1.6rem;
    font-weight: 600;
    float: left;
    width: 60%;
    text-align:left;
    padding:8%;
}
.top-message{
    float: left;
    display:flex;
    align-items:center;
    width:20%;
    margin:0 auto;
}
.searchField{
    width: 70%;
    margin-left: 8%;
    margin-top:5%;
    float: left;
}
.searchBtn{
    margin-top:5%;
    float: left;
}
.selectBottom{
    height: 4rem;
    width: 100%;
    position:fixed;
    bottom:0;
    background-color: #fff;
}
.top-title{
    height:fit-content;
    width:100%;
    display:flex;
}
.userItem{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 2rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 5%;
    font-size: 1.2rem;
}
.userName-st{
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}
.userName-add{
    float: left;
    width: 28%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
.user_List{
    float: left;
    width: 60%;
    height: 100%;
    display: flex;
    align-items: center;
}
.addBtn{
    float: left;
    width: 40%;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    justify-content: center;
    height: 100%;
    background: #5032f2;
    color: #fff;
}
.data_label{
    font-size: 1.2rem;
    float: left;
    width: 40%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.data_value{
    font-size: 1.2rem;
    float: left;
    width: 59%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.data{
    width: 100%;
    background: #fff;
    height: 3rem;
    margin-bottom: 1px;
}
.data1{
    width: 100%;
    background: #fff;
    height: 6rem;
    margin-bottom: 1px;
}
.item_pl{
    width: 90%;
    margin-left: 5%;
    margin-bottom: 20px;
}
</style>