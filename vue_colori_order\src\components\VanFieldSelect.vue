<template>
  <div>
    <van-field
      v-bind="$attrs" 
      @click="popup_picker=!popup_picker"
      v-model="result"
      
      readonly
      is-link
      arrow-direction="down"
    />
    <van-popup position="bottom" v-model="popup_picker" >
    <van-picker @confirm="onConfirm" @cancel="popup_picker=!popup_picker" 
     :title="$attrs.label"
      :columns="columns"
    show-toolbar
/>  
    </van-popup>    
  </div>
</template>>
    


<script>
import Vue from "vue";
import { Popup, Picker } from "vant";
import "vant/lib/index.css";
Vue.use(Popup).use(Picker);

export default Vue.extend({
model:{prop:"selectValue"},  
props:{
    columns: {
      type: Array
    },
    selectValue: {
      type: String
    }
},  
data(){
    return {popup_picker:false,
     result: this.selectValue
       }
} ,
methods:{
  onConfirm:function (value) {
      this.result = value;
      this.popup_picker = !this.popup_picker;
      this.$emit("change", value);
    },
 } ,
computed:{

},
watch:{
 selectValue: function(newVal) {
      this.result = newVal;
    //   window.console.log("121",newVal);
    }, 
     result(newVal) {
       //  let aa =formatDate(newVal,'yyyy年MM月');
      this.$emit("input", newVal);
     //  window.console.log("11",newVal);
    }
 
}  
});
</script>

<style>
.van-cell{
    background: transparent;
    font-size: 0.8rem;
}
</style>