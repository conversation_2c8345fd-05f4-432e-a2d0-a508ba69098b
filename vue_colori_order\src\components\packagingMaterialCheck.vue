<template>
    <div style="text-align: left; background-color: #fff">
        <van-sticky :offset-top="0">
            <van-nav-bar title="包材校验" right-text="筛选" @click-right="onClickRight" left-arrow
                @click-left="onClickLeft" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
            <van-field readonly clickable v-model="workDay" label="日期：" placeholder="点击选择日期"
                @click="showWorkDay = true" />
            <van-calendar v-model="showWorkDay" type="single" @confirm="onWorkDayConfirm"
                :min-date="new Date(new Date().getFullYear())" color="#1989fa" />

            <van-field readonly clickable name="picker" v-model="bookName" label="账套：" placeholder="点击选择账套"
                @click="showbookNamePicker = true" />
            <van-popup v-model="showbookNamePicker" position="bottom">
                <van-picker show-toolbar :columns="bookNameColumns" @confirm="bookNameConfirm"
                    @cancel="showbookNamePicker = false" />
            </van-popup>

            <van-field readonly clickable name="picker" v-model="workshop" label="车间：" placeholder="点击选择车间"
                @click="showworkshopPicker = true" />
            <van-popup v-model="showworkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="workshopConfirm"
                    @cancel="showworkshopPicker = false" />
            </van-popup>
<!-- 
            <van-field readonly clickable name="picker" v-model="jobCenter" label="工作中心：" placeholder="点击选择工作中心"
                @click="showjobCenterPicker = true" />
            <van-popup v-model="showjobCenterPicker" position="bottom">
                <van-picker show-toolbar :columns="jobCenterColumns" @confirm="jobCenterConfirm"
                    @cancel="showjobCenterPicker = false" />
            </van-popup> -->
            <van-field v-model="moId" clearable label="MO单：" placeholder="请输入MO单号" />
            <van-field v-model="productNo" clearable label="产品：" placeholder="请输入产品名称或编码" />

            <van-button type="info" @click="search" style="width: 100%" round>
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in dataArr1" :key="index" @click="detail(item)" style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
            <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                    <van-col span="18">
                        <span style="font-size: 18px; font-weight: 700; color: #000">
                            {{ item.moId }}
                        </span>
                    </van-col>
                    <van-col span="6">
                        <span v-if="item.status==1" style="font-size: 18px; font-weight: 700; color:  #0f0">
                            正常
                        </span>
                        <span v-if="item.status==0" style="font-size: 18px; font-weight: 700; color:#6389fa">
                            完工
                        </span>
                        <span v-if="item.status==2" style="font-size: 18px; font-weight: 700; color: #f00">
                            异常
                        </span>
                    </van-col>
                </van-row>
            </div>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 账套：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.book }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 车间：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.workshop }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 线体：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.mitosome }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> id：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.id }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 产品编码：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.productNo }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 产品名称：</van-col>
                        <van-col span="18">
                            <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                                {{ item.productName }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 组长：</van-col>
                        <van-col span="18">
                            <span style="color: black">
                                {{ item.leaderName }}-{{ item.leaderNo }}
                            </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="24" style="color: gary">
                    <van-row>
                        <van-col span="6"> 计划量：</van-col>
                        <van-col span="18">
                            <span style="color: black"> {{ item.prePlot }} </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
import { Indicator } from "mint-ui";
import moment from "moment";
export default {
    data() {
        return {
            active: 0,
            userCode: localStorage.getItem("userCode"),
            //   拉料工校验
            dataArr1: [],
            //   操作工校验
            dataArr2: [],
            //   已完成
            dataArr3: [],

            show: false,

            showbookNamePicker: false,
            bookName: "",
            bookNameColumns: [],

            showworkshopPicker: false,
            workshop: "",
            workshopColumns: [],

            showjobCenterPicker: false,
            jobCenter: "",
            jobCenterColumns: [],
            moId: "",
            productNo: "",
            showWorkDay: false,
            workDay: moment(new Date()).format("YYYY-MM-DD"),
        };
    },
    created() {
        if (this.userCode == null || this.userCode == "") {
            Toast({
                message: "请先登录",
                position: "bottom",
                duration: 2000,
            });
            this.$router.push({
                name: "LoginIndex",
            });
        } else {
            this.$axios
                .get(`/jeecg-boot/app/warehouse/getFactoryInfo`)
                .then((res) => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach((item) => {
                            this.bookNameColumns.push(item.name);
                        });
                    } else {
                    }
                });
            this.bookName = localStorage.getItem("feedingCheckBook");
            this.bookName =='null'? this.bookName = "": this.bookName 
            console.log("🚀 ~ created ~ this.bookName:", this.bookName)
            this.bookNameConfirm(this.bookName);
            
        }
    },
    methods: {
        bookNameConfirm(value) {
            this.bookName = value;
            this.workshop = "";
            this.jobCenter = "";
            localStorage.setItem("feedingCheckBook", this.bookName);
            this.showbookNamePicker = false;
            //查找车间
            this.$axios
                .get("/jeecg-boot/app/warehouse/getFactoryInfoByCode", {
                    params: { code: value },
                })
                .then((res) => {
                    if (res.data.code == 200) {
                        this.workshopColumns = [];
                        res.data.result.forEach((item) => {
                            this.workshopColumns.push(item.name);
                        });
                        this.workshop = localStorage.getItem("feedingWorkshop");
                        // this.search();
                    } else {
                    }
                });
        },
        workshopConfirm(value) {
            this.workshop = value;
            this.jobCenter = "";
            this.showworkshopPicker = false;
            localStorage.setItem("feedingWorkshop",this.workshop);
            // 查找工作中心
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getJobCenter`, {
                    params: { book: this.bookName, workshop: value },
                })
                .then((res) => {
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        res.data.result.forEach((item) => {
                            this.jobCenterColumns.push(item.jobCenter);
                        });
                    } else {
                    }
                });
        },
        jobCenterConfirm(value) {
            this.jobCenter = value;
            this.showjobCenterPicker = false;
        },
        onClickRight() {
            this.show = true;
        },
        onWorkDayConfirm(data) {
            this.showWorkDay = false;
            this.workDay = moment(data).format("YYYY-MM-DD");
        },
        search() {
            Indicator.open({
                text: "正在加载中，请稍后……",
                spinnerType: "fading-circle",
            });
            let params = {
                moId: this.moId,
                workDay: this.workDay,
                book: this.bookName==null?'':this.bookName,
                workshop: this.workshop==null?'':this.workshop,
                jobCenter: this.jobCenter,
                userCode: this.userCode,
                productNo: this.productNo,
            };
            this.$axios
                .post(`/jeecg-boot/wms/sync/getQiMoList?`, params)
                .then((res) => {
                    this.show = false;
                    if (res.data.code == 200) {
                        console.log(res.data.result);
                        this.dataArr1 = res.data.result;
                        console.log(this.dataArr1);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000,
                        });
                    }
                })
                .finally(() => {
                    Indicator.close();
                });
        },
        onClickLeft() {
            this.$router.go(-1);
        },
        onClick(name, title) {
            console.log(name, title);
        },
        detail(item) {
            this.$router.push({
                name: "packagingMaterialCheckDetail",
                params: item,
            });
        },
    },
};
</script>

<style scoped></style>