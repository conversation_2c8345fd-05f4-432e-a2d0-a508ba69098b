<template>
  <div style="text-align:left;padding-bottom:0.1rem;">
    <van-sticky :offset-top="0">
      <van-nav-bar
        title="批次号对照表"
        left-text="返回"
        right-text="筛选"
        left-arrow
        @click-left="onClickLeft"
        @click-right="onClickRight"
      />
    </van-sticky>
    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
      <van-field
        v-model="filter.code"
        clearable
        label="产品编号："
        placeholder="请输入产品编号"
      />
      <!-- <van-field
        v-model="filter.name"
        clearable
        label="产品名称："
        placeholder="请输入产品名称"
      /> -->
      <van-field
        v-model="filter.custName"
        clearable
        label="所属客户："
        placeholder="请输入所属客户"
      />
      <!-- <van-field
        readonly
        clickable
        name="picker"
        v-model="statusText"
        label="状态："
        placeholder="点击选择状态"
        @click="statusPicker = true"
      />
      <van-popup v-model="statusPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="statusColumns"
          @confirm="statusConfirm"
          @cancel="statusPicker = false"
        />
      </van-popup> -->

      <van-button type="info" @click="search" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>

    <van-popup
      v-model="pdfShow"
      :close-on-popstate="true"
      :closeable="true"
      :style="{ height: '90%', width: '90%' }"
      @close="pdfPopupClose"
    >
      <van-row>
        <van-col span="6"> </van-col>
        <van-col span="6">
          <van-button type="default" @click="zoomIn">放大</van-button>
        </van-col>
        <van-col span="6">
          <van-button type="default" @click="zoomOut">缩小</van-button>
        </van-col>
        <van-col span="6"> </van-col>
      </van-row>
      <div
        :style="{
          cursor: 'pointer',
          transform: `rotate(${rotate}deg)`,
          width: `100%`
        }"
      >
        <pdf
          ref="pdfRef"
          :page="pageNum"
          @num-pages="pageTotalNum = $event"
          :src="
            'http://service.colori.com/jeecg-boot/sys/common/static/' + fileName
          "
        ></pdf>
      </div>
      <van-row>
        <van-col span="24">
          <h2 style="text-align:center">{{ pageNum }}/{{ pageTotalNum }}</h2>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="6">
          <van-button type="default" @click="prePage">上一页</van-button>
        </van-col>
        <van-col span="6">
          <van-button type="default" @click="nextPage">下一页</van-button>
        </van-col>
        <van-col span="6">
          <van-button type="default" @click="handleRotate(1)"
            >逆时针</van-button
          >
        </van-col>
        <van-col span="6">
          <van-button type="default" @click="handleRotate(2)"
            >顺时针</van-button
          >
        </van-col>
      </van-row>
    </van-popup>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="100"
        error-text="请求失败，点击重新加载"
      >
        <div
          v-for="(item, index) in dataArr"
          :key="index"
          style="background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;"
        >
          <van-row>
            <van-col span="16">
              <span style="color:gary;"
                >产品编号：
                <span style="color:black">{{ item.code }}</span></span
              >
            </van-col>
            <van-col span="8">
              <span style="color:gary;"
                >有效期(月)：
                <span style="color:black">{{ item.validTime }}</span></span
              >
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24">
              <span style="color:gary;"
                >产品名称：
                <span style="color:black">{{ item.name }}</span></span
              >
            </van-col>
          </van-row>
          <van-row>
            <van-col span="18"></van-col>
            <van-col span="6">
              <van-button
                size="mini"
                icon="share-o"
                round
                hairline
                type="info"
                @click="pdfPopupShow(item.filename)"
                >查看PDF
              </van-button>
            </van-col>
          </van-row>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import pdf from "vue-pdf";
export default {
  components: {
    pdf
  },
  data() {
    return {
      filter: {
        custName: "", //所属客户
        code: "", //产品编号
        name: "", //产品名称
        status: "" //状态
      },
      statusText: "", //状态
      show: false,
      statusPicker: false, //状态选择弹窗
      statusColumns: ["生效", "新增待审批", "编辑待审批", "审批中"],

      dataArr: [],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,

      //PDF
      pdfShow: false,
      pageNum: 1,
      pageTotalNum: 1,
      rotate: 0,
      rotateAdd: 90,
      scale: 100, //放大缩小
      fileName: ""
    };
  },

  methods: {
    zoomIn() {
      this.scale += 50;
      const itemEl = this.$refs["pdfRef"].$el;
      itemEl.style.width = parseInt(this.scale) + "%";
    },
    zoomOut() {
      this.scale -= 50;
      const itemEl = this.$refs["pdfRef"].$el;
      itemEl.style.width = parseInt(this.scale) + "%";
    },
    pdfPopupShow(fileName) {
      this.fileName = fileName;
      this.pdfShow = true;
    },
    pdfPopupClose() {
      console.log("popup关闭");
      this.pageNum = 1;
      this.pageTotalNum = 1;
      this.rotate = 0;
      this.rotateAdd = 90;
      this.pdfShow = false;
      this.scale = 100;
    },
    getList() {
      let quest = "";
      for (let prop in this.filter) {
        if (this.filter[prop] != "") {
          quest += `&${prop}=${this.filter[prop]}`;
        }
      }
      let userName = localStorage.getItem("userName");
      this.$axios
        .get(
          `/jeecg-boot/app/codeModel/list?userName=${userName}&pageNo=${this.pageNo}&pageSize=10` +
            quest
        )
        .then(res => {
          if (res.data.code == 200) {
            let len = res.data.result.records.length;
            if (len == 0) {
              this.dataArr = []; // 清空数组
              this.finished = true; // 停止加载
            }
            this.total = res.data.result.total;
            this.dataArr.push(...res.data.result.records);

            this.loading = false;
            if (this.dataArr.length < 10) {
              this.finished = true; // 结束加载状态
            }
            if (this.dataArr.length >= res.total) {
              this.finished = true; // 结束加载状态
            }
          } else {
            Toast({
              message: res.data.msg,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    onLoad() {
      console.log(1);
      let timer = setTimeout(() => {
        if (this.refreshing) {
          this.dataArr = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        this.getList(); // 调用上面方法,请求数据
        this.pageNo++; // 分页数加一
        this.finished && clearTimeout(timer); //清除计时器
      }, 100);
    },
    onRefresh() {
      this.finished = false; // 清空列表数据
      this.loading = true; // 将 loading 设置为 true，表示处于加载状态
      this.page = 1; // 分页数赋值为1
      this.dataArr = []; // 清空数组
      this.onLoad(); // 重新加载数据
    },
    statusConfirm(value) {
      this.statusText = value;
      switch (value) {
        case "生效":
          this.filter.status = "1";
          break;
        case "新增待审批":
          this.filter.status = "2 ";
          break;
        case "编辑待审批":
          this.filter.status = "3";
          break;
        case "审批中":
          this.filter.status = "9";
          break;
        default:
          this.filter.status = "";
      }
      this.statusPicker = false;
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onClickRight() {
      this.show = true;
    },
    prePage() {
      let p = this.pageNum;
      p = p > 1 ? p - 1 : this.pageTotalNum;
      this.pageNum = p;
    },
    nextPage() {
      let p = this.pageNum;
      p = p < this.pageTotalNum ? p + 1 : 1;
      this.pageNum = p;
    },
    handleRotate(type) {
      switch (type) {
        case 1:
          this.rotate -= this.rotateAdd;
          break;
        case 2:
          this.rotate += this.rotateAdd;
          break;
      }
    },
    pdfPopupClose() {
      console.log("popup关闭");
      this.pageNum = 1;
      this.pageTotalNum = 1;
      this.rotate = 0;
      this.rotateAdd = 90;
    },
    search() {
      let quest = `userName=${localStorage.getItem("userName")}`;
      for (let prop in this.filter) {
        if (this.filter[prop] != "") {
          quest += `&${prop}=${this.filter[prop]}`;
        }
      }
      this.$axios.get(`/jeecg-boot/app/codeModel/list?${quest}`).then(res => {
        if ((res.data.code = 200)) {
          this.dataArr = res.data.result.records;
          if (this.dataArr.length < 10) {
            this.finished = true; // 结束加载状态
          }
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped></style>
