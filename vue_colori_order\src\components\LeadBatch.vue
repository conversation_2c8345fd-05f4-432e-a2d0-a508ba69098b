<template>
    <div style="background:#f3f4f6;min-height:100%">
        <div v-for="(item,index) in planList" :key="index" style="padding:8px;text-align:left;background:white" @click="handleClick(item)">
            <van-row>
                <van-col span="14">
                    <span style="font-weight:800;font-size:18px;float:left;width:80%">{{item.moId}}</span>
                </van-col>
                <van-col span="10" style="text-align:right;">
                    <span style="font-weight:800;font-size:18px;color:red;" v-if="item.lpId==null || item.lpId==''">未排班</span>
                    <span style="font-weight:800;font-size:18px;color:green;" v-else>已排班</span>
                </van-col>
            </van-row>
            <div style="width:100%;height:1px;background:#333"></div>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">产品编码：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.code}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">产品名称：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.name}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">工作中心：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.mitosome}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">生产时间：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.createTime}}</span>
                </van-col>
            </van-row>
            <van-row>
                <van-col span="6">
                    <span style="font-weight:800;font-size:16px;float:left;width:80%">当前批次：</span>
                </van-col>
                <van-col span="18">
                    <span style="font-weight:800;font-size:16px;">{{item.batchCode}}</span>
                </van-col>
            </van-row>
        </div>
    </div>
</template>
<script>
import { Toast,MessageBox  } from 'mint-ui';
export default {
    data(){
        return{
            planList:[]
        }
    },
    created:function(){
        this.getLeadPlanByLeader();
    },
    methods: {
        getLeadPlanByLeader(){
            let self = this;
            self.$axios.get('/jeecg-boot/app/sticker/getLeadPlanByLeader',{params:{userCode:localStorage.getItem("userCode")}}).then(res=>{
                if(res.data.code==200){
                    this.planList = res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        handleClick(item){
            let self = this
            MessageBox.prompt('请输入批次号').then(({ value, action }) => {
                if(action=="confirm"){
                    self.saveLeadBatchInfo(value,item);
                }
            });
        },
        saveLeadBatchInfo(batchCode,item){
            let self = this
            let params={
                tpId:item.id,
                lpId:item.lpId,
                batchCode:batchCode,
                creator:localStorage.getItem("userCode")
            }
            self.$axios.post('/jeecg-boot/app/sticker/saveLeadBatchInfo',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    self.getLeadPlanByLeader();
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.sch_item{
    height: 22rem;
    background: #ffffff;
    border-radius: 10px;
    width: 90%;
    margin-bottom: 10px;
    margin-left: 5%;
    margin-right: 5%;
}
.sch_mo{
    font-size: 1.4rem;
    font-weight: 600;
    text-align: left;
    padding-left: 5%;
    padding-top: 4%;
    padding-bottom: 2%;
}
.sch_line{
    background: #cfcfcf;
    height: 1px;
}
.sch_item_text{
    padding-top: 2%;
}
.item_left{
    float: left;
    display: flex;
    align-items: center;
    font-size: 0.9rem; 
    height: 100%;
    width: 35%;
}
.item_right{
    float: left;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.9rem;
    height: 100%;
    width: 64%;
}
.sch_bottom{
    height: 100%;
    padding-top: 2%;
}
.leave-style{
    color: #2ff23c;
}
.ycl-style{
    color: crimson;
}
</style>