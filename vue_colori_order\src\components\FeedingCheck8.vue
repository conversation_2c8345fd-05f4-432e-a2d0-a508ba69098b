<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="标签打印申请" left-arrow @click-left="onClickLeft" right-text="筛选"
                @click-right="onClickRight" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '35%' }">

            <van-cell title="选择时间" :value="date" @click="show1 = true" />
            <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)"
                color="#1989fa" />
            <van-field v-model="param.moId" clearable label="MO单：" placeholder="请输入MO单号" />
            <van-field v-model="param.product" clearable label="胶体：" placeholder="请输入胶体名称或编码" />
            <van-field v-model="param.material" clearable label="物料：" placeholder="请输入物料名称或编码" />

            <van-button type="info" @click="getData" style="width: 100%;" round>
                确定
            </van-button>
        </van-popup>
        <div style="width:100%;height:100%;overflow: auto;">
            <div v-for="(item, index) in data" :key="index"
                style="text-align: left;background-color:#f5f5f5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
                <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
                    <van-row>
                        <van-col span="20">
                            <span style="font-size:18px;font-weight: 700;color: #000;">
                                {{ item.remark }}
                            </span>
                        </van-col>
                        <van-col span="4">
                            <span v-if="item.status==0" style="font-size:18px;font-weight: 700;color: gray;">
                                已废除
                            </span>
                            <span v-if="item.status==1" style="font-size:18px;font-weight: 700;color: blue">
                                新申请
                            </span>
                            <span v-if="item.status==2" style="font-size:18px;font-weight: 700;color: green;">
                                已通过
                            </span>
                            <span v-if="item.status==3" style="font-size:18px;font-weight: 700;color: red;">
                                已驳回
                            </span>
                        </van-col>
                    </van-row>
                </div>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6"> 胶体名称：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.glueName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6"> 胶体编码：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.glueCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6"> 原料名称：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.materialName }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6"> 原料编码：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.materialCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6"> 批次号：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.customerBatchCode }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24" style="color:gary">
                        <van-row>
                            <van-col span="6">申请时间：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.createTime }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 申请原因：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.applyReason }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 供应商：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.supplier }}
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row>
                    <van-col span="24">
                        <van-row>
                            <van-col span="6"> 重量：</van-col>
                            <van-col span="18">
                                <span
                                    style="color:black;width:100%;word-wrap:break-word; word-break:break-all; overflow: hidden;">
                                    {{ item.actualWeight }}KG
                                </span>
                            </van-col>
                        </van-row>
                    </van-col>
                </van-row>
                <van-row v-if="item.status==1">
                    <van-col span="11"></van-col>
                    <van-col span="3">
                        <van-button v-if="sign==1" style="width: 100%;" size="mini" round hairline type="primary"
                            @click="approve(item,'1')">审批</van-button>
                    </van-col>
                    <van-col span="2">
                    </van-col>
                    <van-col span="3">
                        <van-button v-if="sign==1" style="width: 100%;" size="mini" round hairline type="warning"
                            @click="approve(item,'2')">驳回</van-button>
                    </van-col>
                    <van-col span="2">
                    </van-col>
                    <van-col span="3">
                        <van-button v-if="sign!=1" style="width: 100%;" size="mini" round hairline type="danger"
                            @click="deleteItem(item)">废除</van-button>
                    </van-col>
                </van-row>
            </div>
        </div>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator, MessageBox } from "mint-ui";
    export default {
        data() {
            return {
                data: [],
                show: false,
                show1: false,
                date: this.getDate(-30) + " ~ " + this.getDate(0),
                param: {
                    beginDay: this.getDate(-30),
                    endDay: this.getDate(0),
                    moId: '',
                    product: '',
                    material: '',
                    // status: '',
                },
                sign: '',
            };
        },
        created() {
            this.sign = this.$route.query.sign
            this.getData();
        },
        methods: {
            approve(item, num) {
                if (num == 1) {

                    MessageBox.confirm('是否确认审批?').then(action => {
                        if (action == 'confirm') {
                            Indicator.open({
                                text: "加载中",
                                spinnerType: "fading-circle",
                            });
                            let param = JSON.parse(JSON.stringify(item))
                            param.status = 2;
                            this.$axios.put("/jeecg-boot/app/mixApply/approve", param)
                                .then(res => {
                                    Indicator.close();
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    if (res.data.code == 200) {
                                        this.getData()

                                    }
                                })
                        }
                    });
                } else {
                    MessageBox.confirm('是否确认驳回?').then(action => {
                        if (action == 'confirm') {
                            Indicator.open({
                                text: "加载中",
                                spinnerType: "fading-circle",
                            });
                            let param = JSON.parse(JSON.stringify(item))
                            param.status = 3;
                            this.$axios.put("/jeecg-boot/app/mixApply/approve", param)
                                .then(res => {
                                    Indicator.close();
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                    if (res.data.code == 200) {
                                        this.getData()
                                    }
                                })
                        }
                    });
                }

            },
            deleteItem(item) {
                MessageBox.confirm('是否确认废除?').then(action => {
                    if (action == 'confirm') {
                        Indicator.open({
                            text: "加载中",
                            spinnerType: "fading-circle",
                        });
                        let param = JSON.parse(JSON.stringify(item))
                        param.status = 0;
                        this.$axios.put("/jeecg-boot/app/mixApply/edit", param)
                            .then(res => {
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: "bottom",
                                    duration: 2000
                                });
                                if (res.data.code == 200) {
                                    this.getData()
                                }
                            })
                    }
                });
            },
            getData() {
                Indicator.open({
                    text: "加载中",
                    spinnerType: "fading-circle",
                });
                this.$axios.get("/jeecg-boot/app/mixApply/getMixApplyList", { params: this.param })
                    .then(res => {
                        Indicator.close();
                        if (res.data.code == 200) {
                            this.data = res.data.result;
                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    })
            },
            onClickLeft() {
                this.$router.go(-1);
            },
            onClickRight() {
                this.show = true;
            },
            onConfirm(date) {
                const [start, end] = date;
                this.show1 = false;
                this.param.beginDay =
                    start.getFullYear() +
                    "-" +
                    (start.getMonth() + 1 < 10
                        ? "0" + (start.getMonth() + 1)
                        : start.getMonth() + 1) +
                    "-" +
                    (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
                this.param.endDay =
                    end.getFullYear() +
                    "-" +
                    (end.getMonth() + 1 < 10
                        ? "0" + (end.getMonth() + 1)
                        : end.getMonth() + 1) +
                    "-" +
                    (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
                this.date = `${this.param.beginDay}~${this.param.endDay}`;
            },
            getDate(day) {
                var date1 = new Date(),
                    time1 =
                        date1.getFullYear() +
                        "-" +
                        (date1.getMonth() + 1) +
                        "-" +
                        date1.getDate(); //time1表示当前时间
                var date2 = new Date(date1);
                date2.setDate(date1.getDate() + day);
                return (
                    date2.getFullYear() +
                    "-" +
                    (date2.getMonth() + 1 < 10
                        ? "0" + (date2.getMonth() + 1)
                        : date2.getMonth() + 1) +
                    "-" +
                    (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
                );
            },
        }
    };
</script>

<style scoped></style>