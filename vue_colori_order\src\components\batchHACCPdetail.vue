<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="HACCP关键控制点" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <div v-for="(item, index) in dataArr" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <van-row>
                <div class="van-hairline--bottom" >
                    <van-row>
                        <van-col span="24" style="font-size: 1.2rem;text-align: right;">
                            <span v-if="item.status == null" style="color:rgb(207, 17, 17);">
                                未确认
                            </span>
                            <span v-if="item.status == 1" style="color:rgb(18, 18, 168);">
                                已确认
                            </span>
                            <span v-if="item.status == 2" style="color:rgb(7, 167, 7);">
                                已复核
                            </span>
                        </van-col>
                    </van-row>
                </div>
                <van-col span="24" style="color:gary;margin-top: 0.2rem;">
                    <van-row>
                        <van-col span="6"> CCP点：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.ccp }} </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row style="height: 2rem;line-height: 2rem;">
                <van-col span="24" style="color:gary">
                    <van-row>
                        <van-col span="6"> 关键限值：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.gjxz }} </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row style="height: 2rem;line-height: 2rem;">
                <van-col span="20" style="color:gary">
                    <van-row>
                        <van-col span="6"> 监控：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.jk }} </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row style="height: 2rem;line-height: 2rem;">
                <van-col span="20" style="color:gary">
                    <van-row>
                        <van-col span="6"> 确认：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.confirmerName }}-{{item.confirmTime?item.confirmTime.slice(0,10):""}}</span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row style="height: 2rem;line-height: 2rem;">
                <van-col span="20" style="color:gary">
                    <van-row>
                        <van-col span="6"> 复核：</van-col>
                        <van-col span="18">
                            <span style="color:black;"> {{ item.checkerName }}-{{item.checkTime?item.checkTime.slice(0,10):''}} </span>
                        </van-col>
                    </van-row>
                </van-col>
            </van-row>
            <van-row style="height: 2rem;line-height: 2rem;">
                <van-col span="20" style="color:gary">
                </van-col>
                <van-col span="4">
                    <van-button v-if="item.status==null" type="primary" round style="width: 100%;" size="mini"
                        @click="confirm(item,index)">确认</van-button>
                    <van-button v-if="item.status==1" type="info" round style="width: 100%;" size="mini"
                        @click="check(item)">复核</van-button>
                </van-col>
            </van-row>
        </div>

    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                data: '',
                showDate: false,
                isEdit: '1',
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                this.search(this.$route.params.id)
            }
        },
        methods: {
            search(id) {
                Indicator.open({
                    text: '正在加载中，请稍后……',
                    spinnerType: 'fading-circle'
                });
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${id}`).then(res => {
                    if (res.data.code == 200) {
                        this.isEdit = res.data.result.checkStatus
                        this.dataArr = res.data.result.gjdList
                    }
                }).finally(() => {
                    Indicator.close()
                });
            },
            confirm(item,index) {
                if(index>0&&this.dataArr[index-1].confirmTime==null){
                    Toast({
                        message: "请先确认上一条数据",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                if (item.checker == null) {
                    let params = {
                        userCode: localStorage.getItem('userCode'),
                        userName: localStorage.getItem('userName'),
                        mixId: this.info.id,
                        resultList: [{
                            ...item,
                            confirmer: localStorage.getItem('userCode'),
                            confirmerName: localStorage.getItem('userName'),
                            status: 2
                        }]
                    }
                    Indicator.open({
                        text: '正在加载中，请稍后……',
                        spinnerType: 'fading-circle'
                    });
                    this.$axios.post(`/jeecg-boot/app/batchRecord/addGjd`, params).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                            this.search(this.info.id)

                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    })
                } else {
                    item.confirmer = localStorage.getItem('userCode')
                    item.status = 2
                    Indicator.open({
                        text: '正在加载中，请稍后……',
                        spinnerType: 'fading-circle'
                    });
                    this.$axios.put(`/jeecg-boot/app/batchRecord/editGjd`, item).then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                            this.search(this.info.id)

                        } else {
                            Toast({
                                message: res.data.message,
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    })
                }
            },
            check(item) {
                let params = {
                    userCode: localStorage.getItem('userCode'),
                    userName: localStorage.getItem('userName'),
                    ...item,
                    checker: localStorage.getItem('userCode'),
                    checkerName: localStorage.getItem('userName'),
                    status: 2
                }
                Indicator.open({
                        text: '正在加载中，请稍后……',
                        spinnerType: 'fading-circle'
                    });
                this.$axios.put(`/jeecg-boot/app/batchRecord/editGjd`, params).then(res => {
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.search(this.info.id)
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                })
            },
            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>