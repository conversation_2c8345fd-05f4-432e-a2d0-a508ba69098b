<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="模具盘点" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <van-button type="info" @click="checkAddShow" style="width: 90%;">
                新增盘点
            </van-button>
        </van-sticky>


        <!-- 新建盘点选择月份 -->
        <van-popup v-model="show1" position="bottom" :style="{ height: '20%' }">
            <van-field readonly clickable name="datetimePicker" :value="month1" label="新建盘点月份" placeholder="点击选择时间"
                @click="showPicker = true" />
            <van-popup v-model="showPicker" position="bottom">
                <van-datetime-picker type="year-month" @confirm="onConfirm1" @cancel="showPicker = false" />
            </van-popup>
            <van-button type="info" @click="CheckAdd" style="width: 90%;">
                确定
            </van-button>
        </van-popup>
        <van-popup v-model="show" position="bottom" :style="{ height: '20%' }">
            <van-field v-model="workshop" clearable label="车间：" placeholder="请输入车间" />
            <van-field readonly clickable name="datetimePicker" :value="month" label="时间选择" placeholder="点击选择时间"
                @click="showPicker = true" />
            <van-popup v-model="showPicker" position="bottom">
                <van-datetime-picker type="year-month" @confirm="onConfirm" @cancel="showPicker = false" />
            </van-popup>
            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in spareList" :key="index"
            style="text-align: left; margin-top: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;">
            <van-row>
                <van-col span="18">
                    <van-row>
                        <van-col span="24">创建人:{{ item.createName }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col v-if="item.status == 1" span="24">状态:进行中</van-col>
                        <van-col v-if="item.status == 2" span="24">状态:已结束</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">月份:{{ item.month }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">开始时间:{{ item.startTime }}</van-col>
                    </van-row>
                    <van-row>
                        <van-col span="24">结束时间:{{ item.endTime }}</van-col>
                    </van-row>
                </van-col>
                <van-col span="6">
                    <van-button plain hairline type="info" @click="goDetail(item)" style="height:1.75rem">
                        查看详情
                    </van-button>
                    <van-button v-show="item.status == 1" plain hairline type="info" @click="over(item)"
                        style="height:1.75rem">
                        盘点结束
                    </van-button>
                </van-col>
            </van-row>

        </div>

    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            spareList: [],
            show: false,
            // 时间选择框是否显示
            show1: false,
            workshop: '',
            month: '',
            showPicker: false,
            month1: this.getMonth(),
        }
    },
    created() {
        this.search()
    },
    methods: {
        // 详情
        goDetail(item) {
            localStorage.setItem('ModuleCheckItem', JSON.stringify(item))
            this.$router.push({ name: "ModuleCheckDetail" })
        },
        // 盘点结束
        over(item) {
            if (item.status == 2) {
                Toast({
                    message: '盘点已结束',
                    position: "bottom",
                    duration: 2000
                });
            } else {
                Dialog.confirm({
                    title: '',
                    message: '确认要盘点结束吗?',
                })
                    .then(() => {
                        this.$axios
                            .get(`/jeecg-boot/ncApp/moldsCheck/finishCheck?id=${item.id}&userCode=${localStorage.getItem('userCode')}`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.search()
                                } else {
                                    Toast({
                                        message: res.data.message,
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    })
                    .catch(() => {
                        Toast({
                            message: '取消',
                            position: "bottom",
                            duration: 2000
                        });
                    });

            }

        },
        checkAddShow() {
            this.show1 = true
        },
        CheckAdd() {
            this.show1 = false
            this.$axios
                .get(`/jeecg-boot/ncApp/moldsCheck/addCheckInfo?userCode=${localStorage.getItem('userCode')}&userName=${localStorage.getItem('userName')}&month=${this.month1}`)
                .then(res => {
                    if (res.data.code == 200) {
                        this.search()
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },

        search() {
            this.$axios
                .get(`/jeecg-boot/ncApp/moldsCheck/getMainList?month=${this.month}&workshop=${this.workshop}&userCode=${localStorage.getItem('userCode')}`)
                .then(res => {
                    if ((res.data.code = 200)) {
                        this.spareList = res.data.result
                        this.month = ''
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            this.show = false
        },
        onClickLeft() {
            this.$router.push({
                name: "ModuleOperation"
            });
        },
        onClickRight() {
            this.show = true;
        },

        onConfirm1(time) {
            let y = time.getFullYear()
            let m = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : (time.getMonth() + 1)
            this.month1 = y + "-" + m;
            this.showPicker = false;
        },
        onConfirm(time) {
            let y = time.getFullYear()
            let m = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : (time.getMonth() + 1)
            this.month = y + "-" + m;
            this.showPicker = false;
        },
        getMonth() {
            var datetime = new Date();
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            return year + '-' + month
        },
    },
}
</script>

<style scoped>
</style>