<template>
    <div style="background:#f3f4f6;min-height:100%">

        <img :src="sc" width="100%"/>
        
        <div>
            <div class="bg_item" v-for="(item,index) in info" :key="index" @click="getMoInfoByWorkShop(item.workshop)">
                <p>
                    <span style="font-size:0.8rem">{{item.workshop}}</span>
                    <span style="font-size:0.4rem">&nbsp;&nbsp;总:{{item.routes}}</span>
                </p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='1'">开锅数：{{item.zjKgs}}</p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='2'">开线数：{{item.gbKgs}}</p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='1'">派单数：{{item.zjSendNum}}</p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='2'">派单数：{{item.gbSendNum}}</p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='1'">完工数：{{item.zjWgNum}}</p>
                <p style="font-size:0.6rem;text-align:left;padding-left:18%" v-if="item.type=='2'">完工数：{{item.gbWgNum}}</p>
            </div>
        </div>

        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            category:'白班',
            popupVisible:false,
            questionTypeVal:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            sc:require('../../static/images/sc.png'),
            fee:{},
            popupSlots:[
                {
                    values:[
                        {
                            id:0,
                            name:"白班"
                        },
                        {
                            id:1,
                            name:"晚班"
                        }
                    ]
                }
            ],
            info:"",
            type:"",
            index:"",
            workInfo:{}
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){

        this.workInfo=JSON.parse(localStorage.getItem("workInfo"))

        this.type = this.workInfo.type
        this.index = this.workInfo.index
        this.date = this.workInfo.date
        this.category = this.workInfo.category

        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        // this.date=this.formatDate(new Date)
        
        this.getWorkshopByFactory()
    },
    methods: {
        getWorkshopByFactory(){
            let self=this;
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getWorkshopByFactory',{params:{type:this.type,index:this.index,date:this.date,category:this.category}}).then(res=>{
                if(res.data.code==200){
                    self.info=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getMoInfoByWorkShop(workshop){
            let self=this
            self.$router.push({name:"JobCenterInfo",params:{workshop:workshop,date:this.date,category:this.category}})
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.sendShopToUser()
        },
        popupOk(){
            this.category = this.questionTypeVal.name;
            this.popupVisible = false;
            this.sendShopToUser()
        },
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year  + month  + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png');
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
.menu_order2{
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
.popup-div{
    width: 100%;
}
.product_name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.bg_item{
    width: 46%;
    float: left;
    margin: 2%;
    background: url('../../static/images/item_bg.png') no-repeat;
}
</style>