<template>
    <div style="text-align:left;background-color:#fff;">
        <van-sticky :offset-top="0">
            <van-nav-bar title="制胶前检查" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <div v-for="(item, index) in dataArr" :key="index"
            style="text-align: left;background-color:#F5F5F5;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;">
            <div v-if="index==0">
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
                <van-radio-group v-model="item.result" @change="(e)=>radioChage(e,item)" :disabled="info.checkStatus >= 5">
                    <van-cell-group>
                        <van-cell title="清洁消毒结果合格" clickable @click="radio = '清洁消毒结果合格'">
                            <template #right-icon>
                                <van-radio name="清洁消毒结果合格" value="清洁消毒结果合格" />
                            </template>
                        </van-cell>
                        <van-cell title="连续生产无需清洗" clickable @click="radio = '连续生产无需清洗'">
                            <template #right-icon>
                                <van-radio name="连续生产无需清洗" value="连续生产无需清洗" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>

                <van-field v-model="item.lastProduct" label-width="6.5rem" label="上批产品名称：" placeholder="请输入"
                    @blur="(e)=>lastProductfieldBlur(e,item)" :readonly="info.checkStatus >= 5"/>
                <van-field readonly clickable label="消毒有效期：" :value="item.cleanDate" @click="showCalendar" />
                <van-calendar v-model="showDate" type="single" @confirm="(value)=>onConfirm(item,index,value)"
                    :min-date="new Date(2022)" color="#1989fa"/>
            </div>
            <div v-else-if="index==dataArr.length-1">
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
            </div>
            <div v-else>
                <van-cell>
                    <template #title>
                        <div v-html="`${item.label}：${item.content}`" style="white-space:pre-wrap"></div>
                    </template>
                </van-cell>
                <van-cell>
                    <template #title>
                        <span class="custom-title">结果</span>
                    </template>
                    <template #default>
                        <div class="custom-title">
                            <van-radio-group v-model="item.result" direction="horizontal" :disabled="info.checkStatus >= 5"
                                @change="(e)=>radioChage(e,item)">
                                <van-radio name="Y" value="Y">Y</van-radio>
                                <van-radio name="N" value="N">N</van-radio>
                            </van-radio-group>
                        </div>
                    </template>
                </van-cell>
                <!-- <van-field v-model="item.result" label="内容" placeholder="请输入" @blur="(e)=>fieldBlur(e,item)" /> -->
            </div>
        </div>
        <div style="height: 3rem;"></div>
        <van-button v-show="info.checkStatus<=3" type="info" @click="submit" style="width:100%;position: fixed;bottom: 0;">提交</van-button>
    </div>
</template>

<script>
    import { Toast } from "vant";
    import { Indicator, MessageBox } from "mint-ui";
    export default {
        data() {
            return {
                userCode: localStorage.getItem("userCode"),
                dataArr: [],
                info: {},
                data: '',
                showDate: false,
                isEdit: '1',
            };
        },
        created() {
            if (this.userCode == null || this.userCode == "") {
                Toast({
                    message: "请先登录",
                    position: "bottom",
                    duration: 2000
                });
                this.$router.push({
                    name: "LoginIndex"
                });
            } else {
                this.info = this.$route.params
                console.log(this.info);
                this.search()
                // 获取上批次产品名称

            }
        },
        methods: {
            showCalendar() {
                if (this.info.checkStatus >= 5) {
                    return
                } else {
                    this.showDate = true
                }
            },
            search() {
                this.$axios.get(`/jeecg-boot/app/batchRecord/getBatchRecordInfo?mixId=${this.info.id}`).then(res => {
                    if (res.data.code == 200) {
                        this.isEdit = res.data.result.checkStatus
                        this.dataArr = res.data.result.zjzyList
                        this.$axios.get('/jeecg-boot/app/gcMix/getMixLastProduct?mixId=' + this.info.id).then(res => {
                            if (res.data.success) {
                                console.log(res.data);
                                this.dataArr[0].lastProduct = res.data.message
                            } else {
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                        this.dataArr.forEach((item, index) => {
                            if (index > 0 && item.id == null) {
                                item.result = 'Y'
                            }
                        })
                    }
                });
            },
            editItem(item) {
                if (item.id == null) {
                    return
                }
                console.log('this.info.checkStatus',this.info.checkStatus);
                if (this.info.checkStatus >= 5) {
                    Toast({
                        message: "制胶前检查已复核，无法修改",
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }
                this.$axios.put(`/jeecg-boot/app/batchRecord/editZjzySingle`, item).then(res => {
                    if (res.data.code == 200) {
                        this.search()
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            },
            fieldBlur(e, item) {
                console.log(item);
                console.log(item.result);
                this.editItem(item)
            },
            lastProductfieldBlur(e, item) {
                console.log(item);
                console.log(item.lastProduct);
                this.editItem(item)

            },
            dateChage(e, item) {
                console.log(item);
                console.log(item.cleanDate);
                this.editItem(item)

            },
            radioChage(e, item) {
                console.log(item);
                console.log(item.result);
                this.editItem(item)

            },
            submit() {
                let flag = false
                this.dataArr.forEach(item => {
                    if (item.id != null) {
                        flag = true
                    }
                })
                if (flag) {
                    Toast({
                        message: '已经提交过了',
                        position: "bottom",
                        duration: 2000
                    });
                    return
                }

                let params = {
                    userCode: localStorage.getItem('userCode'),
                    userName: localStorage.getItem('userName'),
                    mixId: this.info.id,
                    checkStatus: 3,
                    resultList: this.dataArr
                }
                console.log(params);
                Indicator.open({
                    text: "正在加载中，请稍后……",
                    spinnerType: "fading-circle"
                });
                this.$axios.post(`/jeecg-boot/app/batchRecord/addZjzy`, params).then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                        this.$router.go(-1);
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
            },
            onConfirm(item, index, e) {
                this.showDate = false
                item.cleanDate = `${e.getFullYear()}年${e.getMonth() + 1}月${e.getDate()}日`
                this.dateChage(e,item)
            },
            onClickLeft() {
                this.$router.go(-1);
            },
        },
    };
</script>

<style scoped></style>