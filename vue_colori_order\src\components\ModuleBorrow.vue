<template>
    <div>
        <van-form>
            <van-field label="数量" v-model="info.count" placeholder="请输入"
                :rules="[{ required: true, message: '请填写数量' }]" />


            <!-- 车间 -->
            <van-field readonly clickable :value="info.workShop" label="车间" placeholder="点击选择车间"
                @click="showWorkshopPicker = true" :rules="[{ required: true, message: '请选择车间' }]" />
            <van-popup v-model="showWorkshopPicker" position="bottom">
                <van-picker show-toolbar :columns="workshopColumns" @confirm="onWorkshopConfirm"
                    @cancel="showWorkshopPicker = false" />
            </van-popup>
            <!-- 操作人名字 编码 -->
            <van-field v-model="info.realNo" label="借用人编号" placeholder="请输入借用人编号"
                :rules="[{ validator, message: '必填' }]" />
            <van-field name="realName" v-model="realName" label="借用人姓名" readonly
                :rules="[{ required: true, message: '必填' }]" />


            <van-field label="备注" v-model="info.remarks" placeholder="请输入" />


            <a-button type="dashed" style="width: 80px; text-align: left" @click="addInput">
                <a-icon type="plus" /> 新增
            </a-button>
            <div style="height: 450px; overflow: auto">
                <div v-for="(item, index) in items" :key="item.keyId">
                    <van-divider>{{ index + 1 }}</van-divider>
                    <van-field readonly clickable name="picker" v-model="items[index].jobCenter" label="工作中心"
                        placeholder="请选择工作中心" @click="onShowPop(index)"
                        :rules="[{ required: true, message: '请选择工作中心' }]" />
                    <van-popup v-model="showPicker" position="bottom">
                        <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
                    </van-popup>

                    <van-field label="产品编码" v-model="item.productNo" placeholder="请输入"
                        :rules="[{ required: true, message: '请输入产品编码' }]"
                        @blur="e => onPNChange(e, index, 'productNo')" />
                    <van-field label="产品名称" v-model="item.productName" placeholder="请输入" readonly
                        :rules="[{ required: true, message: '请输入产品名称' }]" />
                    <van-field label="备注" v-model="item.remarks" placeholder="请输入" />
                    <a-button type="dashed" style="width: 80px; text-align: left" @click="removeInput(index)">
                        <a-icon type="minus" /> 删除
                    </a-button>
                </div>
            </div>
            <van-form style="margin: 16px">
                <van-button round block type="info" @click="onSubmit">提交</van-button>
            </van-form>
        </van-form>
    </div>
</template>

<script>
import { Toast } from "mint-ui";

export default {
    data() {
        return {
            info: {},
            items: [],
            jobCenter: [],
            columns: [],
            showPicker: false,
            currIndex: 0,
            // 车间数据
            workshopColumns: [],
            // 车间选择框显隐
            showWorkshopPicker: false,
            realName: ''
        };
    },
    created() {
        this.info = this.$route.params;
        this.items = [];
        if (!this.info.id) {
            this.$router.replace({
                name: "ModuleInfoDetail"
            });
        } else {
            this.info.moldsId = this.info.id;
            this.info.remarks = "";
            this.info.workShop = "";
            this.info.type = "2";
        }
        // 获取车间
        this.$axios.get(`/jeecg-boot/app/gcWorkshop/getFactoryInfoByDepartment?department=苏州`)
            .then(res => {
                if (res.data.code == 200) {
                    res.data.result.forEach(item => {
                        this.workshopColumns.push(item.name)
                    })
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
    },
    methods: {
        // 通过编号搜索名字
        validator(value) {
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${value}`)
                .then(res => {
                    if (res.data.message != null) {
                        this.realName = res.data.message
                        return true
                    } else {
                        this.realName = ''
                        Toast({
                            message: '操作人员编号不正确',
                            position: "bottom",
                            duration: 2000
                        });
                        return false
                    }
                });
        },
        // 通过产品编号带出产品名字
        onPNChange(e, index, type) {
            this.$axios
                .get(`/jeecg-boot/ncApp/molds/getGoodsInfo?code=${e.target.value}`)
                .then(res => {
                    if (res.data.code == 200) {
                        if (res.data.result) {
                            this.$set(this.items[index], "productName", res.data.result.name);
                        } else {
                            Toast({
                                message: "请重新输入产品编码",
                                position: "bottom",
                                duration: 2000
                            });
                            this.$set(this.items[index], "productName", "");
                        }
                    }
                });
        },
        onShowPop(pIndex) {
            this.currIndex = pIndex;
            this.showPicker = true;
        },
        onConfirm(value) {
            this.items[this.currIndex].jobCenter = value;
            // this.$set(this.items[i], 'jobCenter', value)
            this.showPicker = false;
        },
        addInput() {
            if (this.columns.length > 0) {
                this.items.push({
                    jobCenter: "",
                    productNo: "",
                    productName: "",
                    remarks: ""
                });
            } else {
                Toast({
                    message: "请选择车间",
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        removeInput(index) {
            this.items.splice(index, 1);
        },
        onSubmit() {
            let flag = true;
            if (flag) {
                flag = false;
                if (this.info.count * 1 > this.info.availableNumber * 1) {
                    Toast({
                        message: "借用数量不得大于可用数量",
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    if (this.items.length == 0) {
                        Toast({
                            message: "至少借用一个",
                            position: "bottom",
                            duration: 2000
                        });
                    } else {
                        this.info.type = 2;
                        this.info.detailList = this.items;
                        this.info.workshop = this.info.workShop;
                        this.info.realName = this.realName;
                        this.info.creator = localStorage.getItem("userCode");
                        this.info.createName = localStorage.getItem("userName");
                        // 数量只能是正整数
                        if (/^[1-9]\d*$/.test(this.info.count)) {
                            this.$axios
                                .post(`/jeecg-boot/ncApp/moldsUsage/add`, this.info)
                                .then(res => {
                                    if (res.data.code == 200) {
                                        this.$router.replace({
                                            name: "ModuleInfo"
                                        });
                                        flag = true;
                                    } else {
                                        Toast({
                                            message: res.data.message,
                                            position: "bottom",
                                            duration: 2000
                                        });
                                    }
                                });
                        } else {
                            Toast({
                                message: "请输入正整数",
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    }
                }
            }
        },


        // 车间确认
        onWorkshopConfirm(value) {
            this.info.workShop = value;
            this.showWorkshopPicker = false;
            this.$axios
                .get(
                    `/jeecg-boot/ncApp/molds/getJobCenter?workshop=${this.info.workshop}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.columns = []
                        res.data.result.forEach(item => {
                            this.columns.push(item.jobCenter);
                        });
                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
    }
};
</script>

<style scoped>

</style>
