<template>
    <div>
        
        <div v-for="(item,index) in orderInfo" :key="index" style="background:#f3f4f6;min-height:120px">
            <div class="content">
                <div class="order">{{item.orderId}}</div>
                <div class="order-time">{{item.createTime}}</div>
            </div>
            <div style="background:#888;height:1px;width:100%;"></div>
            <div class="product">
                <div class="product-title">
                    产品编号：
                </div>
                <div class="product-name">
                    {{item.code}}
                </div>
            </div>
            <div class="product">
                <div class="product-title">
                    产品名称：
                </div>
                <div class="product-name">
                    {{item.name}}
                </div>
            </div>
            <div class="product">
                <div class="product-title">
                    产品规格：
                </div>
                <div class="product-name">
                    {{item.spec}}
                </div>
            </div>
            <div class="product">
                <div class="clock-title">
                    需求交期：{{item.demandTime}}
                </div>
                <div class="clock-name">
                    确认交期：{{item.planCheckTime}}
                </div>
            </div>
            <div class="product">
                <div class="clock-title">
                    需求数量：{{item.demandNum}}
                </div>
                <div class="clock-name">
                    确认数量：{{item.planCheckNum}}
                </div>
            </div>
            <div class="product">
                <div class="clock-title">
                    已生产：<span v-if="item.productNum">{{item.productNum}}</span>
                    <span v-else>0</span>
                </div>
                <div class="clock-name">
                    未生产：{{item.unProductNum}}
                </div>
            </div>
            <div class="product">
                <div class="product-title">
                    业务备注：
                </div>
                <div class="product-name">
                    <span v-if="item.businessNote">{{item.businessNote}}</span>
                    <span v-else>无</span>
                </div>
            </div>
            <div class="product">
                <div class="product-title">
                    计划备注：
                </div>
                <div class="product-name">
                    <span v-if="item.comments">{{item.comments}}</span>
                    <span v-else>无</span>
                </div>
            </div>
            <div style="height:20px"></div>
            <div style="clear:both;"></div>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            personItem:require('../../static/images/person_item.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            status:"",
            orderInfo:[],
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.status=localStorage.getItem('orderStatus');
        this.getOrderDetail()
    },
    methods: {
        getOrderDetail(){
            let self=this
            self.$axios.get('/jeecg-boot/app/report/getOrderDetail',{params:{status:this.status}}).then(res=>{
                if(res.data.code==200){
                    self.orderInfo=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        getOrderMaterial(item){
            this.$router.push({path:'/orderInfoMaterial',query:{orderId:item.orderId}});
        },
        workMode(num){
            if(num==1){
                this.$router.push({path:'/orderPlat'});
            }else if(num==2){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/productControl'});
            }else if(num==3){
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/gcodeManage'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/workForQa'});
            }else if(num==4){
                if(this.userCode=="HI2002250004"){
                    this.$router.push({path:'/scgzb'});
                }
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }else if(num==5){
                
                Toast({
                    message: "系统筹备上线中,敬请期待！",
                    position: 'bottom',
                    duration: 2000
                });
                // this.$router.push({path:'/warehouseIn'});
            }

        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.content{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #fff;
    border-radius: 5px;
}
.order{
    font-weight: 600;
    font-size: 1.2rem;
    color: #000;
}
.order-time{
    font-size: 0.6rem;
    color: #888;
    align-items: flex-end;
    display: flex;
}
.order-name{
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    padding-left: 2%;
}
.product{
    width: 100%;
    padding-top: 2%;
}
.product-title{
    padding-left: 1%;
    width: 20%;
    font-size: 0.6rem;
    color: #888;
    float: left;
}
.product-name{
    width: 78%;
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    float: left;
    word-wrap: break-word;
}
.clock{
    width: 100%;
    padding-top: 2%;
}
.clock-title{
    padding-left: 1%;
    width: 47%;
    text-align: left;
    padding-left: 3%;
    font-size: 0.6rem;
    color: #888;
    float: left;
}
.clock-name{
    width: 50%;
    font-size: 0.6rem;
    color: #888;
    text-align: left;
    float: left;
    word-wrap: break-word;
}
</style>