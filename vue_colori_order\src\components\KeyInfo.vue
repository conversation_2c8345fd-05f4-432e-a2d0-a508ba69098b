<template>
    <div class="order">
        <van-button type="primary" @click="searchKey()"  style="margin:10px;width:40%;border-radius:10px;">获取秘钥</van-button>

        <div style="font-size:30px;margin-top:50px;font-weight:700;" v-if="key!='' && key != null">{{key}}</div>
        
    </div>
</template>
<script>

export default {
    data(){
        return{
            key:'',
        }
    },
    created:function(){

    },
    methods:{
        searchKey(){
            let self = this
            self.$axios.get(`/jeecg-boot/app/utils/getSecretKey?userCode=${localStorage.getItem('userCode')}`)
            .then(res => {
                if (res.data.code == 200) {
                    self.key=res.data.message
                } else {
                    Toast({
                        message: res.data.msg,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}

</style>