<template>
    <div class="order">

        

        <van-field label="原密码" :type="passwordType" v-model="info.oldPwd" :rules="[{ required: true, message: '请填写原密码' }]">
            <template slot="right-icon">
                <span class="solts" @click="switchPasswordType">
                    <van-icon name="eye" v-if="passwordType==='password'"/>
                    <van-icon name="closed-eye" v-else />
                </span>
            </template>
        </van-field>
        <van-field label="新密码" :type="passwordType2" v-model="info.newPwd" :rules="[{ required: true, message: '请填写新密码' }]">
            <template slot="right-icon">
                <span class="solts" @click="switchPasswordType2">
                    <van-icon name="eye" v-if="passwordType2==='password'"/>
                    <van-icon name="closed-eye" v-else />
                </span>
            </template>
        </van-field>
        <van-field label="确认密码" :type="passwordType3" v-model="info.checkPwd" :rules="[{ required: true, message: '请填写确认密码' }]">
            <template slot="right-icon">
                <span class="solts" @click="switchPasswordType3">
                    <van-icon name="eye" v-if="passwordType3==='password'"/>
                    <van-icon name="closed-eye" v-else />
                </span>
            </template>
        </van-field>

        <van-button type="primary" @click="submit()"  style="margin:5px;width:40%;border-radius:10px;">提交</van-button>

        <!-- <canvas id="canvas" width="200" height="200"></canvas> -->
        
    </div>
</template>
<script>
import chapter from './config/chapter'
import { DatetimePicker,Toast } from 'mint-ui';
export default {
    data(){
        return{
            info:{
                oldPwd:"",
                newPwd:"",
                checkPwd:""
            },
            passwordType:'password',
            passwordType2:'password',
            passwordType3:'password',
        }
    },
    created:function(){
        
    },
    mounted(){
        chapter('质量管理中心','克劳丽化妆品股份有限公司')
    },
    methods:{
        switchPasswordType() {
            this.passwordType = this.passwordType === 'password' ? 'text' : 'password'
        },
        switchPasswordType2(){
            this.passwordType2 = this.passwordType2 === 'password' ? 'text' : 'password'
        },
        switchPasswordType3(){
            this.passwordType3 = this.passwordType3 === 'password' ? 'text' : 'password'
        },
        submit(){
            let self=this
            
            if(self.info.oldPwd==null || self.info.oldPwd==""){
                Toast({
                    message: "原密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd==null || self.info.newPwd==""){
                Toast({
                    message: "新密码不能为空！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            if(self.info.newPwd!=self.info.checkPwd){
                Toast({
                    message: "新密码和确认密码不一致！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            let params={
                userCode:localStorage.getItem('userCode'),
                oldPwd:self.info.oldPwd,
                newPwd:self.info.newPwd
            }

            self.$axios.post('/jeecg-boot/app/user/updatePwd',params).then(res=>{
                if(res.data.code==200){
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                    self.$router.go(-1);
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 5000
                    });
                }
            })


        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>

</style>