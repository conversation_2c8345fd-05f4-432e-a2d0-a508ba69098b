<template>
    <div style="text-align:left;">
        <van-nav-bar title="维修记录详情" left-text="返回" left-arrow @click-left="onClickLeft" />
        <van-cell-group>
            <van-cell title="是否调试" :value="item.isDebug" />
            <van-cell title="是否更换配件" :value="item.isReplace" />
            <van-cell title="是否人为" :value="item.isArtificial" />
            <van-cell title="维修情况" :value="item.maintenance" />
            <van-cell title="故障描述" :label="item.content" value="" />
            <van-cell title="备注" :label="item.remarks" value="" />
            <van-uploader v-model="imgList" disabled :deletable="false" :show-upload="false" />
            <van-divider>备件</van-divider>
            <van-checkbox-group v-model="result">
                <van-checkbox v-for="(x, i) in item.partsList" :name="x.partsId" :key="i"  :disabled="x.partsCount==0">
                    <van-cell :title='`${x.code}-${x.name}-(${x.partsCount})`'/>
                </van-checkbox>
            </van-checkbox-group>

            <van-button type="primary" @click="backStock" style="width:100%;margin-top:10px;">退库</van-button>
        </van-cell-group>
    </div>
</template>

<script>
import { Indicator, MessageBox } from "mint-ui";
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            // 维修记录
            info: {},
            // 图片
            imgList: [],
            // 维修工单
            item: {},
            status: "",
            result: []
        };
    },
    created() {
        this.item = this.$route.params.item;
        this.status = this.$route.params.status;
        this.info = this.$route.params.info;

        console.log(this.$route.params);
        this.item.pictureList.forEach(item => {
            this.imgList.push({ url: item.picUrl });
        });
    },
    methods: {
        backStock() {
            let arr = [];
            this.item.partsList.forEach(item => {
                console.log(item);
                if (this.result.some(v => v == item.partsId)) {
                    arr.push({
                        ...item
                    });
                }
            });
            let params = {
                id: this.item.id,
                partsList: arr
            };
            Indicator.open({
                text: "处理中，请稍后……",
                spinnerType: "fading-circle"
            });
            this.$axios
                .post(`/jeecg-boot/app/mac/cancelRepairDetail`, params)
                .then(res => {
                    Indicator.close();
                    if (res.data.code == 200) {
                        this.$router.push({
                            name: "RepairDetail1",
                            params: { item: this.info, status: this.status, active: 1 }
                        });
                    } else {
                        Indicator.close();
                        Toast({
                            message: "操作失败",
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        onClickLeft() {
            this.$router.push({
                name: "RepairDetail1",
                params: { item: this.info, status: this.status, active: 1 }
            });
        }
    }
};
</script>

<style scoped></style>
