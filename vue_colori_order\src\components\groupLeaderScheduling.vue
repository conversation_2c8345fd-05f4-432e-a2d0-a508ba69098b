<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="组长排班" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>

        <!-- 筛选弹窗 -->
        <van-popup v-model="showFilter" position="right" :style="{ height: '100%', width: '90%' }">
            <div style="padding: 20px;">
                <van-field v-model="queryParam.book" clearable label="账套：" placeholder="请输入账套" />
                <van-field v-model="queryParam.workshop" clearable label="车间：" placeholder="请输入车间" />
                <van-field v-model="queryParam.jobCenter" clearable label="工作中心：" placeholder="请输入工作中心" />
                <van-field v-model="queryParam.userCode" clearable label="用户编码：" placeholder="请输入用户编码" />

                <!-- 日期选择 -->
                <van-field v-model="dateRangeText" readonly label="日期范围：" placeholder="请选择日期范围"
                    @click="showDatePicker = true" />

                <div style="margin-top: 20px;">
                    <van-button type="info" @click="searchQuery" style="width: 100%; margin-bottom: 10px;">
                        查询
                    </van-button>
                    <van-button @click="resetQuery" style="width: 100%;">
                        重置
                    </van-button>
                </div>
            </div>
        </van-popup>

        <!-- 日期选择器 -->

        <van-calendar v-model="showDatePicker" type="range" @confirm="onDateConfirm" :min-date="new Date(2022)"
            color="#1989fa" />

        <!-- 新增按钮 -->
        <van-button type="primary" @click="handleAdd" style="margin: 10px; width: calc(100% - 20px);" icon="plus">
            新增排班
        </van-button>

        <!-- 列表 -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100"
                error-text="请求失败，点击重新加载">
                <div v-for="(item, index) in dataSource" :key="index" style="
            text-align: left;
            margin: 10px;
            background-color: #fbf8fb;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          ">
                    <div
                        style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <span style="font-weight: 900; font-size: 16px; color: #333;">
                            {{ item.jobCenter }}
                        </span>
                        <span style="color: #666; font-size: 14px;">
                            {{ item.workDay }}
                        </span>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666;">账套：{{ item.book }}</span>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666;">车间：{{ item.workshop }}</span>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                        <span style="color: #666;">班次：{{ item.category }}</span>
                        <span style="color: #1989fa; font-weight: 500;">
                            人数：{{ item.detailList ? item.detailList.length : 0 }}
                        </span>
                    </div>

                    <div style="display: flex; justify-content: space-between;">
                        <van-button size="small" type="info" @click="handleDetail(item)"
                            style="flex: 1; margin-right: 10px;">
                            查看详情
                        </van-button>
                        <van-button size="small" type="danger" @click="deleteOne(item)" style="flex: 1;">
                            删除
                        </van-button>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>

        <!-- 详情弹窗 -->
        <van-popup v-model="detailVisible" position="bottom" :style="{ height: '70%', width: '100%' }">
            <div style="padding: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0;">排班详情</h3>
                    <van-icon name="cross" @click="handleDetailCancel" size="20" />
                </div>

                <div v-if="detailList.length > 0">
                    <div v-for="(worker, index) in detailList" :key="index" style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px;
              margin-bottom: 8px;
              background-color: #f7f8fa;
              border-radius: 6px;
            ">
                        <div>
                            <div style="font-weight: 500; margin-bottom: 4px;">{{ worker.userName }}</div>
                            <div style="color: #666; font-size: 14px;">工号：{{ worker.userCode }}</div>
                        </div>
                        <van-button size="mini" type="danger" @click="deleteWorker(worker)">
                            删除
                        </van-button>
                    </div>
                </div>
                <div v-else style="text-align: center; color: #666; padding: 40px 0;">
                    暂无人员信息
                </div>
            </div>
        </van-popup>

        <!-- 新增排班弹窗 -->
        <SchedulingAddModal ref="addModal" @ok="modalFormOk" />
    </div>
</template>

<script>
import { Toast, Dialog } from 'vant';
import SchedulingAddModal from './SchedulingAddModal.vue';

export default {
    name: "groupLeaderScheduling",
    components: {
        SchedulingAddModal
    },
    data() {
        const today = new Date();
        const todayStr = this.formatDate(today);

        return {
            loading: false,
            finished: false,
            refreshing: false,
            pageNo: 1,
            pageSize: 10,
            total: 0,

            // 筛选相关
            showFilter: false,
            showDatePicker: false,
            currentDate: today,
            dateRangeText: `${todayStr} - ${todayStr}`,

            // 查询参数
            queryParam: {
                beginDay: todayStr,
                endDay: todayStr,
                book: '',
                workshop: '',
                jobCenter: '',
                userCode: ''
            },

            // 数据
            dataSource: [],

            // 详情弹窗
            detailVisible: false,
            detailList: [],

            // API URLs
            url: {
                list: "/jeecg-boot/app/gcWorkCategory/getWorkCategoryList",
                delete: "/jeecg-boot/app/gcWorkCategory/deleteJobCenterAll",
                deleteWorker: "/jeecg-boot/app/gcWorkCategory/deleteJobCenterWorker",
                detail: "/jeecg-boot/app/gcWorkCategory/getWorkCategoryDetailList"
            }
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        // 格式化日期
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        // 加载数据
        loadData() {
            this.loading = true;

            const params = {
                ...this.queryParam,
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                creator: localStorage.getItem('userCode')
            };

            this.$axios.get(this.url.list, { params })
                .then(res => {
                    this.loading = false;

                    if (res.data.success) {
                        const records = res.data.result.records || res.data.result || [];

                        if (this.pageNo === 1) {
                            this.dataSource = records;
                        } else {
                            this.dataSource.push(...records);
                        }

                        this.total = res.data.result.total || records.length;

                        // 判断是否还有更多数据
                        if (records.length < this.pageSize || this.dataSource.length >= this.total) {
                            this.finished = true;
                        }
                    } else {
                        Toast({
                            message: res.data.message || '查询失败',
                            position: 'bottom'
                        });
                    }
                })
                .catch(error => {
                    this.loading = false;
                    Toast({
                        message: '网络错误，请重试',
                        position: 'bottom'
                    });
                    console.error('API Error:', error);
                });
        },

        // 下拉刷新
        onRefresh() {
            this.finished = false;
            this.pageNo = 1;
            this.dataSource = [];
            this.loadData();
            this.refreshing = false;
        },

        // 上拉加载更多
        onLoad() {
            if (!this.finished) {
                this.pageNo++;
                this.loadData();
            }
        },

        // 返回上一页
        onClickLeft() {
            this.$router.go(-1);
        },

        // 显示筛选
        onClickRight() {
            this.showFilter = true;
        },
        // 日期确认

        onDateConfirm(date) {
            const [start, end] = date;
            this.showDatePicker = false;
            this.queryParam.beginDay  = start.getFullYear() + "-" + ((start.getMonth() + 1) < 10 ? '0' + (start.getMonth() + 1) : (start.getMonth() + 1)) + "-" + (start.getDate() < 10 ? '0' + start.getDate() : start.getDate())
            this.queryParam.endDay  = end.getFullYear() + "-" + ((end.getMonth() + 1) < 10 ? '0' + (end.getMonth() + 1) : (end.getMonth() + 1)) + "-" + (end.getDate() < 10 ? '0' + end.getDate() : end.getDate())
            this.dateRangeText = `${this.queryParam.beginDay}~${this.queryParam.endDay}`
        },
        // 查询
        searchQuery() {
            this.showFilter = false;
            this.pageNo = 1;
            this.dataSource = [];
            this.finished = false;
            this.loadData();
        },

        // 重置查询条件
        resetQuery() {
            const today = this.formatDate(new Date());
            this.queryParam = {
                beginDay: today,
                endDay: today,
                book: '',
                workshop: '',
                jobCenter: '',
                userCode: ''
            };
            this.dateRangeText = `${today} - ${today}`;
        },

        // 新增排班
        handleAdd() {
            this.$refs.addModal.add();
        },

        // 新增成功回调
        modalFormOk() {
            this.onRefresh();
        },

        // 查看详情
        handleDetail(record) {
            if (record.detailList && record.detailList.length > 0) {
                this.detailList = record.detailList;
                this.detailVisible = true;
            } else {
                // 如果没有详情数据，尝试从接口获取
                this.getDetailList(record);
            }
        },

        // 获取详情列表
        getDetailList(record) {
            const params = {
                workDay: record.workDay,
                jobCenter: record.jobCenter,
                category: record.category
            };

            this.$axios.get(this.url.detail, { params })
                .then(res => {
                    if (res.data.success) {
                        this.detailList = res.data.result || [];
                        this.detailVisible = true;
                    } else {
                        Toast({
                            message: res.data.message || '获取详情失败',
                            position: 'bottom'
                        });
                    }
                })
                .catch(error => {
                    Toast({
                        message: '网络错误，请重试',
                        position: 'bottom'
                    });
                    console.error('API Error:', error);
                });
        },

        // 关闭详情弹窗
        handleDetailCancel() {
            this.detailVisible = false;
            this.detailList = [];
        },

        // 删除整个工作中心排班
        deleteOne(record) {
            Dialog.confirm({
                title: '确认删除',
                message: '确定要删除这个排班吗？只有创建人才可以删除。'
            }).then(() => {
                if (!record.workDay || !record.jobCenter || !record.category) {
                    Toast({
                        message: '删除数据不完整，请确保工作日期、工作中心和班次信息存在',
                        position: 'bottom'
                    });
                    return;
                }

                const params = {
                    workDay: record.workDay,
                    jobCenter: record.jobCenter,
                    category: record.category,
                    userCode: localStorage.getItem('userCode'),
                    creator: localStorage.getItem('userCode')
                };

                this.$axios.get(this.url.delete, { params })
                    .then(res => {
                        if (res.data.success) {
                            Toast({
                                message: res.data.message || '删除成功',
                                position: 'bottom'
                            });
                            this.onRefresh();
                        } else {
                            Toast({
                                message: res.data.message || '删除失败，可能您不是创建人',
                                position: 'bottom'
                            });
                        }
                    })
                    .catch(error => {
                        Toast({
                            message: '网络错误，请重试',
                            position: 'bottom'
                        });
                        console.error('API Error:', error);
                    });
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 删除工作人员
        deleteWorker(record) {
            Dialog.confirm({
                title: '确认删除',
                message: '确定要删除这个工作人员吗？只有创建人才可以删除。'
            }).then(() => {
                if (!record.id) {
                    Toast({
                        message: '记录ID不存在',
                        position: 'bottom'
                    });
                    return;
                }

                this.$axios.get(this.url.deleteWorker, { params: { id: record.id, userCode: localStorage.getItem('userCode'), creator: localStorage.getItem('userCode') } })
                    .then(res => {
                        if (res.data.success) {
                            Toast({
                                message: res.data.message || '删除成功',
                                position: 'bottom'
                            });
                            // 从详情列表中移除
                            this.detailList = this.detailList.filter(item => item.id !== record.id);
                            // 如果详情为空，关闭弹窗
                            if (this.detailList.length === 0) {
                                this.detailVisible = false;
                            }
                            // 重新加载主表数据
                            this.onRefresh();
                        } else {
                            Toast({
                                message: res.data.message || '删除失败',
                                position: 'bottom'
                            });
                        }
                    })
                    .catch(error => {
                        Toast({
                            message: '网络错误，请重试',
                            position: 'bottom'
                        });
                        console.error('API Error:', error);
                    });
            }).catch(() => {
                // 用户取消删除
            });
        }
    }
}
</script>

<style scoped></style>