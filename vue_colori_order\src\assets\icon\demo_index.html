<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2952727" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b6;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe8b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe749;</span>
                <div class="name">扫码</div>
                <div class="code-name">&amp;#xe749;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">智能优化</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">工单</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">排班off</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">蓝牙</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb80;</span>
                <div class="name">全局设置_o</div>
                <div class="code-name">&amp;#xeb80;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">个人中心</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">加班申请表</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">班组名称</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">派单提醒</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">值班</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe681;</span>
                <div class="name">管理员</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">托盘_pallet</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6bb;</span>
                <div class="name">投资产出比</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">质量保证</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">工资</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69a;</span>
                <div class="name">设备</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe872;</span>
                <div class="name">拍照</div>
                <div class="code-name">&amp;#xe872;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">扫码</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">退出</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">车辆</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">安排派车</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">工单</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">食堂</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">排班</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">考勤</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b9;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe8b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">投料烟叶称量</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">宿舍管理</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">投料</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">添加工时</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1666851308679') format('woff2'),
       url('iconfont.woff?t=1666851308679') format('woff'),
       url('iconfont.ttf?t=1666851308679') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-saoma1"></span>
            <div class="name">
              扫码
            </div>
            <div class="code-name">.icon-saoma1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhinengyouhua"></span>
            <div class="name">
              智能优化
            </div>
            <div class="code-name">.icon-zhinengyouhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongdan1"></span>
            <div class="name">
              工单
            </div>
            <div class="code-name">.icon-gongdan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuku"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.icon-shujuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paibanoff"></span>
            <div class="name">
              排班off
            </div>
            <div class="code-name">.icon-paibanoff
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lanya"></span>
            <div class="name">
              蓝牙
            </div>
            <div class="code-name">.icon-lanya
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanjushezhi_o"></span>
            <div class="name">
              全局设置_o
            </div>
            <div class="code-name">.icon-quanjushezhi_o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin"></span>
            <div class="name">
              个人中心
            </div>
            <div class="code-name">.icon-gerenzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiabanshenqingbiao"></span>
            <div class="name">
              加班申请表
            </div>
            <div class="code-name">.icon-jiabanshenqingbiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-banzumingcheng"></span>
            <div class="name">
              班组名称
            </div>
            <div class="code-name">.icon-banzumingcheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paidantixing"></span>
            <div class="name">
              派单提醒
            </div>
            <div class="code-name">.icon-paidantixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiban"></span>
            <div class="name">
              值班
            </div>
            <div class="code-name">.icon-zhiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanliyuan"></span>
            <div class="name">
              管理员
            </div>
            <div class="code-name">.icon-guanliyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuopan_pallet"></span>
            <div class="name">
              托盘_pallet
            </div>
            <div class="code-name">.icon-tuopan_pallet
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touzichanchubi"></span>
            <div class="name">
              投资产出比
            </div>
            <div class="code-name">.icon-touzichanchubi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiliangbaozheng"></span>
            <div class="name">
              质量保证
            </div>
            <div class="code-name">.icon-zhiliangbaozheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzi"></span>
            <div class="name">
              工资
            </div>
            <div class="code-name">.icon-gongzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon"></span>
            <div class="name">
              设备
            </div>
            <div class="code-name">.icon-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paizhao"></span>
            <div class="name">
              拍照
            </div>
            <div class="code-name">.icon-paizhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-saoma"></span>
            <div class="name">
              扫码
            </div>
            <div class="code-name">.icon-saoma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuichu"></span>
            <div class="name">
              退出
            </div>
            <div class="code-name">.icon-tuichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cheliang"></span>
            <div class="name">
              车辆
            </div>
            <div class="code-name">.icon-cheliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anpaipaiche-"></span>
            <div class="name">
              安排派车
            </div>
            <div class="code-name">.icon-anpaipaiche-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongdan"></span>
            <div class="name">
              工单
            </div>
            <div class="code-name">.icon-gongdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-iconzhengli_shitang"></span>
            <div class="name">
              食堂
            </div>
            <div class="code-name">.icon-iconzhengli_shitang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paiban"></span>
            <div class="name">
              排班
            </div>
            <div class="code-name">.icon-paiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaoqin"></span>
            <div class="name">
              考勤
            </div>
            <div class="code-name">.icon-kaoqin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.icon-shuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touliaoyanyechengliang"></span>
            <div class="name">
              投料烟叶称量
            </div>
            <div class="code-name">.icon-touliaoyanyechengliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiaozhizuomoban-101"></span>
            <div class="name">
              宿舍管理
            </div>
            <div class="code-name">.icon-tubiaozhizuomoban-101
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touliao"></span>
            <div class="name">
              投料
            </div>
            <div class="code-name">.icon-touliao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiagongshi"></span>
            <div class="name">
              添加工时
            </div>
            <div class="code-name">.icon-tianjiagongshi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-saoma1"></use>
                </svg>
                <div class="name">扫码</div>
                <div class="code-name">#icon-saoma1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhinengyouhua"></use>
                </svg>
                <div class="name">智能优化</div>
                <div class="code-name">#icon-zhinengyouhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongdan1"></use>
                </svg>
                <div class="name">工单</div>
                <div class="code-name">#icon-gongdan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuku"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#icon-shujuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paibanoff"></use>
                </svg>
                <div class="name">排班off</div>
                <div class="code-name">#icon-paibanoff</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lanya"></use>
                </svg>
                <div class="name">蓝牙</div>
                <div class="code-name">#icon-lanya</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanjushezhi_o"></use>
                </svg>
                <div class="name">全局设置_o</div>
                <div class="code-name">#icon-quanjushezhi_o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin"></use>
                </svg>
                <div class="name">个人中心</div>
                <div class="code-name">#icon-gerenzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiabanshenqingbiao"></use>
                </svg>
                <div class="name">加班申请表</div>
                <div class="code-name">#icon-jiabanshenqingbiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-banzumingcheng"></use>
                </svg>
                <div class="name">班组名称</div>
                <div class="code-name">#icon-banzumingcheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paidantixing"></use>
                </svg>
                <div class="name">派单提醒</div>
                <div class="code-name">#icon-paidantixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiban"></use>
                </svg>
                <div class="name">值班</div>
                <div class="code-name">#icon-zhiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanliyuan"></use>
                </svg>
                <div class="name">管理员</div>
                <div class="code-name">#icon-guanliyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuopan_pallet"></use>
                </svg>
                <div class="name">托盘_pallet</div>
                <div class="code-name">#icon-tuopan_pallet</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touzichanchubi"></use>
                </svg>
                <div class="name">投资产出比</div>
                <div class="code-name">#icon-touzichanchubi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiliangbaozheng"></use>
                </svg>
                <div class="name">质量保证</div>
                <div class="code-name">#icon-zhiliangbaozheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzi"></use>
                </svg>
                <div class="name">工资</div>
                <div class="code-name">#icon-gongzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon"></use>
                </svg>
                <div class="name">设备</div>
                <div class="code-name">#icon-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paizhao"></use>
                </svg>
                <div class="name">拍照</div>
                <div class="code-name">#icon-paizhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-saoma"></use>
                </svg>
                <div class="name">扫码</div>
                <div class="code-name">#icon-saoma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichu"></use>
                </svg>
                <div class="name">退出</div>
                <div class="code-name">#icon-tuichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cheliang"></use>
                </svg>
                <div class="name">车辆</div>
                <div class="code-name">#icon-cheliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anpaipaiche-"></use>
                </svg>
                <div class="name">安排派车</div>
                <div class="code-name">#icon-anpaipaiche-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongdan"></use>
                </svg>
                <div class="name">工单</div>
                <div class="code-name">#icon-gongdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconzhengli_shitang"></use>
                </svg>
                <div class="name">食堂</div>
                <div class="code-name">#icon-iconzhengli_shitang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paiban"></use>
                </svg>
                <div class="name">排班</div>
                <div class="code-name">#icon-paiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaoqin"></use>
                </svg>
                <div class="name">考勤</div>
                <div class="code-name">#icon-kaoqin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#icon-shuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touliaoyanyechengliang"></use>
                </svg>
                <div class="name">投料烟叶称量</div>
                <div class="code-name">#icon-touliaoyanyechengliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiaozhizuomoban-101"></use>
                </svg>
                <div class="name">宿舍管理</div>
                <div class="code-name">#icon-tubiaozhizuomoban-101</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touliao"></use>
                </svg>
                <div class="name">投料</div>
                <div class="code-name">#icon-touliao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiagongshi"></use>
                </svg>
                <div class="name">添加工时</div>
                <div class="code-name">#icon-tianjiagongshi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
