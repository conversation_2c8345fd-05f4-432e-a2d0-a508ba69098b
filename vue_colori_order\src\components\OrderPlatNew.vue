<template>
    <div class="order">
        <!-- <div style="height:6rem;">
            <div class="top_order_title">工作任务</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%" />
            </div>
        </div> -->
        <!-- <div class="sign">
            <img :src="plotTop" width="90%"/>
            <div class="plotName">{{peopleInfo.name}}</div>
            <div class="plotCode">{{peopleInfo.code}}</div>
            <div class="plotFactory">{{peopleInfo.department}}</div>
            <div class="plotWorkshop">{{peopleInfo.workshop}}</div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div> -->

        <div class="sign">
            <div style="width:100%;">
                <div style="float:left;text-align:left;width:50%;">
                    <span style="font-size:22px;">{{ peopleInfo.name }}</span>
                    <br />
                    <span style="font-size:16px;">{{ peopleInfo.code }}</span>
                </div>
                <!-- <div style="float:left;width:20%;">
                    <img :src="card" width="80%" />
                </div> -->
                <!-- 右侧按钮区域 -->
                <div style="float:right;width:30%;" class="sign-buttons">
                    <div class="action-button btn-1" @click="zbPool">
                        <span>值班</span>
                    </div>
                    <div class="action-button btn-2" @click="ybPool">
                        <span>延班</span>
                    </div>
                    <div class="action-button btn-3" @click="pushReport">
                        <span>报表</span>
                    </div>
                </div>
                <div style="clear:both"></div>
            </div>
            <div style="text-align:left">
                {{ peopleInfo.department }}
                <br />
                {{ peopleInfo.workshop }}
            </div>
        </div>


        <!-- <div class="plotTime" @click="selectData">{{newSelectedValue}}</div> -->
        <div class="sc_date">
            <div class="rq_date">{{ $t('date') }}</div>
            <div class="date_work" @click="c_show = true">{{ date }}</div>
            <div class="right_jt"></div>
        </div>


        <!-- <div class="sc_date" @click="c_show = true">{{date}}</div> -->
        <!-- <van-cell title="日期" :value="date" @click="c_show = true"  class="sc_date"/> -->
        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false"
            position="right" />


        <!-- <div class="pool">
            <div class="zbPool" @click="zbPool"></div>
            <div class="mid">
                <div class="midPool" @click="zbPool"></div>
            </div>
            <div class="ybPool" @click="ybPool"></div>
        </div>



        <div class="pro-report" @click="pushReport">{{ $t('report') }}</div> -->

        <div class="task-list-container">
            <div v-for="(item, index) in orderList" :key="index" class="task-card" @click.stop="startToNext(item)">
                <!-- 左侧内容区域 (80%) -->
                <div class="card-left-content">
                    <!-- 订单序号和状态 -->
                    <div class="order-status-row">
                        <!-- <span class="number-text">第{{ item.priorityLevel }}单</span> -->
                        <span class="number-text">第{{ index + 1 }}单:{{ getShiftText(item) }}</span>
                        <div class="status-badge" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}
                        </div>
                    </div>

                    <!-- 任务名称 -->
                    <div class="task-header">
                        <div class="task-name">
                            <span v-if="item.mitosome && item.mitosome.includes('Y')" class="ycl-style">
                                {{ item.name }}
                            </span>
                            <span v-else>{{ item.name }}</span>
                        </div>
                    </div>
                    <!-- 详细信息 -->
                    <div class="detail-info">
                        <span class="detail-text">{{ item.mitosome }}&nbsp;-&nbsp;{{ item.plot }}</span>
                        <a-progress :percent="item.plotPercent*100" size="small" class="detail-progress" />
                        <div class="action-btn suspend-btn" @click.stop="suspendTask(item)">
                            <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16H9V8H11V16M15,16H13V8H15V16Z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { DatetimePicker, Toast, Indicator, MessageBox } from 'mint-ui';
export default {
    data() {
        return {
            plotTop: require('../../static/images/plat_top.png'),
            message: require('../../static/images/message.png'),
            card: require('../../static/images/card.png'),
            itemAdd: require('../../static/images/item_add.png'),
            itemBg: require('../../static/images/item_bg.png'),
            selectedValue: this.formatDate(new Date()),
            newSelectedValue: this.formatDate(new Date()),
            dateVal: '',
            minDate: '',
            date: '',
            category: '',
            popupVisible: false,
            peopleInfo: {},
            newOrderList: [],
            workOrderList: [],
            errorOrderList: [],
            finishOrderList: [],
            questionType: '',
            c_show: false,
            questionTypeVal: '',
            popupVisible: false,
            popupSlots: [
                {
                    values: [
                        '全部', '白班(上午)', '白班(下午)', '白班(加班)', '晚班(上半夜)', '晚班(下半夜)'
                    ]
                }
            ],
            orderList: [],
        }
    },
    created: function () {
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 14 * 24 * 3600 * 1000);
        this.date = this.formatDate(new Date)
        this.getOrderInfo();
    },
    methods: {
        zbPool() {
            let hour = new Date().getHours();
            // let hour=23
            if ((hour >= 10 && hour <= 14) || (hour >= 22 || hour <= 2)) {
                localStorage.setItem('type', '1')
                this.$router.push({ name: "PoolList", params: { type: '1' } })
                return;
            } else {
                Toast({
                    message: '值班池开放时间为10:00~14:00以及22:00~02:00！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
        },
        ybPool() {
            let hour = new Date().getHours();
            console.log(hour)
            if ((hour >= 15 && hour <= 19) || (hour >= 3 || hour <= 6)) {
                localStorage.setItem('type', '2')
                this.$router.push({ name: "PoolList", params: { type: '2' } })
                return;
            } else {
                Toast({
                    message: '延班池开放时间为15:00~19:00以及03:00~06:00！',
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
        },
        getOrderInfo() {
            let self = this;
            let userCode = localStorage.getItem('userCode')
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo', { params: { userCode: userCode, workDay: self.date, preCategory: self.questionType } }).then(res => {
                console.log(res)
                if (res.data.code == 200) {
                    console.log(res.data.peopleInfo)
                    self.peopleInfo = res.data.result.peopleInfo
                    self.newOrderList = res.data.result.newOrderList
                    self.workOrderList = res.data.result.workOrderList
                    self.errorOrderList = res.data.result.errorOrderList
                    self.finishOrderList = res.data.result.finishOrderList

                    // 合并四个列表到orderList
                    let allOrders = [];

                    // 添加新任务列表(新工单)，并设置临时优先级
                    if (self.newOrderList && self.newOrderList.length > 0) {
                        self.newOrderList.forEach(item => {
                            item.listType = 'new';
                            allOrders.push(item);
                        });
                    }

                    // 添加进行中任务列表，并设置临时优先级
                    if (self.workOrderList && self.workOrderList.length > 0) {
                        self.workOrderList.forEach(item => {
                            item.listType = 'work';
                            allOrders.push(item);
                        });
                    }

                    // 添加异常调整任务列表，并设置临时优先级
                    if (self.errorOrderList && self.errorOrderList.length > 0) {
                        self.errorOrderList.forEach(item => {
                            item.listType = 'error';
                            allOrders.push(item);
                        });
                    }

                    // 添加已完成任务列表，并设置临时优先级为最低
                    if (self.finishOrderList && self.finishOrderList.length > 0) {
                        self.finishOrderList.forEach(item => {
                            item.listType = 'finish';
                            item.priorityLevel = 999; // 确保已完成的任务排在最后
                            allOrders.push(item);
                        });
                    }

                    // 按priorityLevel排序，已完成的任务会排在最后
                    self.orderList = allOrders.sort((a, b) => {
                        // 如果是已完成的任务，则排在最后
                        if (a.listType === 'finish' && b.listType !== 'finish') {
                            return 1;
                        } else if (a.listType !== 'finish' && b.listType === 'finish') {
                            return -1;
                        }

                        // 否则按照priorityLevel排序
                        return (a.priorityLevel || 0) - (b.priorityLevel || 0);
                    });
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        pushReport() {
            this.$router.push({ name: "OrderReport" });
        },
        suspendTask(item) {
            let self = this;
            MessageBox.prompt('请输入挂起原因').then(({ value, action }) => {
                if (action === 'confirm') {
                    if (!value) {
                        Toast({
                            message: '挂起原因不能为空!',
                            position: 'bottom',
                            duration: 2000
                        });
                        return;
                    }

                    let params = {
                        id: item.apsId,
                        workDay: self.date,
                        userCode: localStorage.getItem('userCode'),
                        userName: localStorage.getItem('userName'),
                        reason: value,
                    }
                    self.$axios.get('/jeecg-boot/app/gcAps/handApsPlan', { params: params }).then(res => {
                        console.log(res)
                        if (res.data.code == 200) {
                            Toast({
                                message: '挂起成功',
                                position: 'bottom',
                                duration: 2000
                            });
                            self.getOrderInfo();
                        } else {
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                }
            }).catch(() => {
                // catch cancel
            });
        },
        getShiftText(item) {
            if (!item.moId) return '无班次';
            switch (item.preCategory) {
                case '1': return `${item.moId}_白班`;
                case '4': return `${item.moId}_晚班`;
                case '6': return `${item.moId}_培训`;
                default: return `${item.moId}_无班次`;
            }
        },
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                '0': '已完工',
                '1': '新工单',
                '2': '进行中',
                '3': '异常'
            }
            return statusMap[status] || ''
        },

        // 获取状态颜色类名
        getStatusClass(status) {
            const classMap = {
                '0': 'status-finish',
                '1': 'status-new',
                '2': 'status-work',
                '3': 'status-invalid'
            }
            return classMap[status] || 'status-default'
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getOrderInfo()
        },
        startToNext(item) {
            let self = this;
            let userCode = localStorage.getItem('userCode')
            let params = {
                id: item.id,
                workDay: self.date,
                userCode: userCode,
                preCategory: self.questionType
            }
            localStorage.setItem('params', JSON.stringify(params));
            this.$router.push({ name: "OrderStart" })
        },
        /**
         * 打开问题类型的弹框
         */
        openQuestionType() {
            this.popupVisible = true;
        },
        dateConfirm(value) {
            this.newSelectedValue = this.formatDate(value)
            console.log(this.dateVal)
            console.log(this.newSelectedValue)
            this.getOrderInfo();
        },
        // 问题类型弹框点击确认
        popupOk() {
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
            this.getOrderInfo();
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values) {
            this.questionTypeVal = values[0];
        },
        selectData() {
            if (this.newSelectedValue) {
                this.dateVal = this.newSelectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate(secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order {
    background-color: #ebecf7;
    min-height: 100%;
}

.top_order_title {
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}

.top_msg {
    float: right;
}

.items_d {
    margin: 3%;
    height: 7.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.item_bg {
    background-image: url('../../static/images/item_bg.png');
    width: 100%;
    height: 100%;
    text-align: left;
    float: left;
}

.item_add {
    width: 32%;
    float: left;
    height: 100%;
}

.itemTitle {
    padding: 2%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 用户信息卡片 */
.sign {
    text-align: center;
    margin: 0 10px 10px 10px;
    color: white;
    padding: 15px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url('../../static/images/plat_top.png');
}

/* sign内部的按钮区域 */
.sign-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
    justify-content: center;
    align-items: center;
    padding-top: 10px;
}

/* 按钮样式 */
.action-button {
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    min-height: 28px;
    width: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 值班按钮 */
.btn-1 {
    background: linear-gradient(135deg, #6b66d9 0%, #4c48b4 100%);
}

/* 延班按钮 */
.btn-2 {
    background: linear-gradient(135deg, #d048c5 0%, #b034a5 100%);
}

/* 报表按钮 */
.btn-3 {
    background: linear-gradient(135deg, #9147e8 0%, #7129c8 100%);
}

.plotName {
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}

.plotCode {
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}

.plotCard {
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}

.plotFactory {
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}

.plotWorkshop {
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}

.plotMitosome {
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}

.plotTime {
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.orderType {
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 6%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}

#peopleChorseT {
    position: absolute;
    width: 100%;
    top: 1.17rem;
    height: 0.6rem;
}

/**问题类型弹框样式 */
.picker-toolbar-title {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    background-color: #eee;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
}

.usi-btn-cancel,
.usi-btn-sure {
    color: #26a2ff;
    font-size: 16px;
}

.popup-div {
    width: 100%;
}

.pro-report {
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 90%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}

.sc_date {
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left: 15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width: 64%;
    border-radius: 10px;
}

.rq_date {
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}

.date_work {
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}

.right_jt {
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}

.pool {
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom: 5%;
    font-size: 1rem;
    width: 90%;
}

.zbPool {
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.ybPool {
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}

.mid {
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}

.midPool {
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}

.ycl-style {
    color: #e74c3c;
}

.task-list-container {
    padding: 0 15px;
    padding-bottom: 15px;
    text-align: left;
}

.task-card {
    display: flex;
    align-items: stretch;
    margin-top: 15px;
    background-image: url('../../static/images/item_bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 15px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-height: 100px;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
}

/* 左侧内容区域 (80%) */
.card-left-content {
    flex: 4;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100px;
}

/* 订单序号和状态行 */
.order-status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.number-text {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
    flex-shrink: 0;
}

/* 任务名称 */
.task-header {
    margin-bottom: 8px;
}

.task-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
    flex: 1;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 状态样式 - 根据新的状态码定义 */
.status-invalid {
    background-color: #e74c3c;
    /* 红色 - 作废 */
}

.status-new {
    background-color: #3498db;
    /* 蓝色 - 新工单 */
}

.status-received {
    background-color: #17a2b8;
    /* 青色 - 已接单 */
}

.status-work {
    background-color: #f39c12;
    /* 橙色 - 进行中 */
}

.status-suspend {
    background-color: #9b59b6;
    /* 紫色 - 挂起 */
}

.status-restart {
    background-color: #2ecc71;
    /* 绿色 - 重启 */
}

.status-finish {
    background-color: #28a745;
    /* 成功绿 - 完成 */
}

.status-finish-cut {
    background-color: #6c757d;
    /* 极客蓝 - 完成(截断) */
}

.status-default {
    background-color: #6c757d;
    /* 默认灰色 */
}

/* 班次信息 */
.shift-info {
    margin-bottom: 6px;
}

.shift-text {
    font-size: 0.9rem;
    color: #34495e;
    font-weight: 500;
    background-color: rgba(52, 73, 94, 0.1);
    padding: 2px 8px;
    border-radius: 8px;
}

/* 详细信息 */
.detail-info {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-text {
    font-size: 0.85rem;
    color: #7f8c8d;
    line-height: 1.4;
    flex-shrink: 0;
    min-width: 120px;
}

.detail-progress {
    flex: 1;
    min-width: 80px;
}

/* 右侧操作区域 (20%) */
.card-right-actions {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 15px 10px;
}

/* 操作按钮通用样式 */
.action-btn {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.2s ease;
    width: 44px;
    height: 44px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 开始按钮样式 */
.start-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.start-btn:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
}

/* 挂起按钮样式 */
.suspend-btn {
    background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    color: white;
}

.suspend-btn:hover {
    background: linear-gradient(135deg, #d35400 0%, #e67e22 100%);
}

/* 详细信息行中的挂起按钮 */
.detail-info .suspend-btn {
    width: 32px;
    height: 32px;
    padding: 6px;
    flex-shrink: 0;
}

.detail-info .btn-icon {
    width: 18px;
    height: 18px;
}

/* 按钮图标 */
.btn-icon {
    width: 22px;
    height: 22px;
    fill: currentColor;
}
</style>