<template>
  <div style="text-align:left;padding-bottom:0.1rem;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="物料称重" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
        @click-right="onClickRight" />
    </van-sticky>
    <van-popup v-model="show" position="bottom" :style="{ height: '45%' }">
      <van-cell title="选择时间" :value="date" @click="show1 = true" />
      <van-calendar v-model="show1" type="range" @confirm="onConfirm" :min-date="new Date(2022)" color="#1989fa" />

      <van-field readonly clickable name="picker" v-model="queryparam.statusText" label="称重状态：" placeholder="点击选择称重状态"
        @click="showstatusPicker = true" />
      <van-popup v-model="showstatusPicker" position="bottom">
        <van-picker show-toolbar :columns="statusColumns" @confirm="statusConfirm" @cancel="showstatusPicker = false" />
      </van-popup>

      <van-field readonly clickable name="picker" v-model="queryparam.area" label="区域：" placeholder="点击选择区域"
        @click="showareaPicker = true" />
      <van-popup v-model="showareaPicker" position="bottom">
        <van-picker show-toolbar :columns="areaColumns" @confirm="areaConfirm" @cancel="showareaPicker = false" />
      </van-popup>

      <van-button type="info" @click="search1" style="width: 100%;" round>
        确定
      </van-button>
    </van-popup>

    <van-button round type="info" block size="large" style="width:90%;margin:0 auto;"
      @click="handleScan">扫码</van-button>
    <div style="width: 75%;margin: 1rem auto;overflow:hidden;">
      <input
        style="width: 70%;float: left;height: 2rem;line-height: 2rem;border: 0.1rem solid #cccccc;border-radius: 0.5rem;"
        class="uni-input" v-model="inputValue" />
      <van-button round type="info" block size="large"
        style="width: 25%;float: left;height: 2rem;line-height: 2rem;margin: 0 1% 0 1%;"
        @click="handleinput">称重</van-button>
    </div>

    <div v-for="(item, index) in dataArr" :key="index" @click="goPrint(item)"
      style="text-align: left;background-color:#fff;padding:3%;border-radius: 10px;width: 95%;overflow: hidden;margin: 0.5rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
      <div class="van-hairline--bottom" style="margin-bottom:0.3rem;">
        <van-row>
          <van-col span="20">
            <span style="font-size:18px;font-weight: 700;color: #000;">
              {{ item.id }}
            </span>
          </van-col>
          <van-col span="4">
            <span style="color: red;" v-if="item.status == 2">待称重</span>
            <span style="color: green" v-if="item.status == 3">已称重</span>
          </van-col>
        </van-row>
      </div>
      <van-row>
        <van-col span="24">
          <span style="color:gary">
            MO单号：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.moId }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24">
          <span style="color:gary">
            原料编码：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.materialCode }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary;">
            原料名称：
            <span style="color:black;">
              {{ item.materialName }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24">
          <span style="color:gary">
            胶体编码：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.glueCode }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="24">
          <span style="color:gary;">
            胶体名称：
            <span style="color:black;">
              {{ item.glueName }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row style="overflow-x: auto;white-space:nowrap;">
        <van-col span="12">
          <span style="color:gary;overflow-x: auto;white-space:nowrap;">
            每公斤重量(KG)：
            <span style="color:black">
              {{ item.perWeight }}
            </span>
          </span>
        </van-col>
        <van-col span="12">
          <span style="color:gary;overflow-x: auto;white-space:nowrap;">
            需求重量(KG)：
            <span style="color:black">
              {{ item.respondWeight }}
            </span>
          </span>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="24">
          <span style="color:gary">
            称重范围：
            <span style="color:black;overflow-x: auto;white-space:nowrap;">
              {{ item.mixWeight + "KG ~ " + item.maxWeight + "KG" }}
            </span>
          </span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
  import { Toast } from "mint-ui";
  export default {
    data() {
      return {
        date: this.getDate(-1) + " ~ " + this.getDate(0),
        beginDay: this.getDate(-1),
        endDay: this.getDate(0),
        userCode: localStorage.getItem("userCode"),
        active: 0,
        // 搜索弹出层是否显示
        show: false,
        // 时间选择显示隐藏
        show1: false,
        inputValue: "",
        dataArr: [],
        queryparam: {
          statusText: "待称重",
          status: "2",
          area: ""
        },
        // 称重状态选择框显示
        showstatusPicker: false,
        // 区域状态选择框显示
        showareaPicker: false,
        //   称重状态
        statusColumns: ["待称重", "已称重"],
        //   区域
        areaColumns: []
      };
    },
    created() {
      this.queryparam.area = localStorage.getItem("mwarea");
      this.beginDay = localStorage.getItem('weightBeginDay') == null ? this.getDate(-1) : localStorage.getItem('weightBeginDay')
      this.endDay = localStorage.getItem('weightEndDay') == null ? this.getDate(0) : localStorage.getItem('weightEndDay')
      this.queryparam.status = localStorage.getItem('weightStatus')
      this.queryparam.statusText = localStorage.getItem('weightStatusText')
      this.date = `${this.beginDay}~${this.endDay}`;
      this.$axios.get(`/jeecg-boot/app/gcMix/getAreaInfo`).then(res => {
        if (res.data.code == 200) {
          this.areaColumns = res.data.result;
        }
      });
      this.search(this.beginDay, this.endDay);
    },
    methods: {
      goPrint(item) {
        if (item.status == 2) { return }
        this.$router.push({
          name: "MaterialsWeighingLabel",
          params: {
            result: item
          }
        });
      },
      handleScan() {
        if (this.dataArr.length == 0) {
          Toast({
            message: "请先查询你要称重的物料",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        let self = this;
        wx.scanQRCode({
          desc: "scanQRCode desc",
          needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
          success: function (res) {
            // 回调
            var result = res.resultStr; //当needResult为1时返回处理结果
            // self.$router.push({
            //   name: "ScanWareInResult",
            //   params: { result: result }
            // });
            let flag = false
            self.dataArr.forEach(item => {
              if (result == item.nccCode) {
                flag = true

                item.area = self.queryparam.area;
                self.$router.push({
                  name: "MaterialsWeighingForm",
                  params: {
                    item
                  }
                });
              }
            });
            if (!flag) {
              Toast({
                message: "该区域不需要称重该原料",
                position: "bottom",
                duration: 2000
              });
            }
          },
          error: function (res) {
            if (res.errMsg.indexOf("function_not_exist") > 0) {
              alert("版本过低请升级");
            }
          }
        });
      },
      handleinput() {
        if (this.dataArr.length == 0) {
          Toast({
            message: "请先查询你要称重的物料",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        let that = this;
        if (
          this.inputValue == "" ||
          this.inputValue == null ||
          this.inputValue == undefined
        ) {
          Toast({
            message: "请输入",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        let flag = false
        console.log(1,flag);
        this.dataArr.forEach(item => {
          if (this.inputValue == item.nccCode) {
            flag = true
            item.area = this.queryparam.area;
            this.$router.push({
              name: "MaterialsWeighingForm",
              params: {
                item
              }
            });
          }
        });
        console.log(2,flag);
        if (!flag) {
          Toast({
            message: "该区域不需要称重该原料",
            position: "bottom",
            duration: 2000
          });
        }
      },
      statusConfirm(value) {
        this.queryparam.statusText = value;
        if (value == "待称重") {
          this.queryparam.status = 2;
        } else if (value == "已称重") {
          this.queryparam.status = 3;
        }
        this.showstatusPicker = false;
        localStorage.setItem('weightStatus', this.queryparam.status)
        localStorage.setItem('weightStatusText', value)
      },
      areaConfirm(value) {
        localStorage.setItem("mwarea", value);
        this.queryparam.area = value;
        this.showareaPicker = false;
      },
      search1() {
        this.search(this.beginDay, this.endDay);
        this.show = false;
      },
      search(beginDay, endDay) {
        if (
          !this.isEmpty(this.queryparam.area) ||
          !this.isEmpty(this.queryparam.status)
        ) {
          Toast({
            message: "区域与状态不能为空",
            position: "bottom",
            duration: 2000
          });
          return;
        }
        this.$axios
          .get(
            `/jeecg-boot/app/gcMix/getMixDetailInfo?beginDay=${beginDay}&endDay=${endDay}&area=${this.queryparam.area}&status=${this.queryparam.status}`
          )
          .then(res => {
            if (res.data.code == 200) {
              this.dataArr = res.data.result;
            } else {
              Toast({
                message: res.data.message,
                position: "bottom",
                duration: 2000
              });
            }
          });
      },
      onClickLeft() {
        this.$router.go(-1);
      },
      onClickRight() {
        this.show = true;
      },
      getDate(day) {
        var date1 = new Date(),
          time1 =
            date1.getFullYear() +
            "-" +
            (date1.getMonth() + 1) +
            "-" +
            date1.getDate(); //time1表示当前时间
        var date2 = new Date(date1);
        date2.setDate(date1.getDate() + day);
        return (
          date2.getFullYear() +
          "-" +
          (date2.getMonth() + 1 < 10
            ? "0" + (date2.getMonth() + 1)
            : date2.getMonth() + 1) +
          "-" +
          (date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate())
        );
      },
      onConfirm(date) {
        const [start, end] = date;
        this.show1 = false;
        this.beginDay =
          start.getFullYear() +
          "-" +
          (start.getMonth() + 1 < 10
            ? "0" + (start.getMonth() + 1)
            : start.getMonth() + 1) +
          "-" +
          (start.getDate() < 10 ? "0" + start.getDate() : start.getDate());
        this.endDay =
          end.getFullYear() +
          "-" +
          (end.getMonth() + 1 < 10
            ? "0" + (end.getMonth() + 1)
            : end.getMonth() + 1) +
          "-" +
          (end.getDate() < 10 ? "0" + end.getDate() : end.getDate());
        this.date = `${this.beginDay}~${this.endDay}`;
        localStorage.setItem('weightBeginDay', this.beginDay)
        localStorage.setItem('weightEndDay', this.endDay)
      },
      overDetail(item) {
        this.$router.push({
          name: "GoodsPickOverDetail",
          params: item
        });
      },
      isEmpty(value) {
        if (value == null || value == "" || value == undefined) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>