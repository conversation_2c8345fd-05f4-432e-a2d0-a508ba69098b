<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_hour" width="100%"/>
        <div class="menu_order2">
            <div class="menu_order_item" @click="workMode('1')">
                <img src="../../static/images/hmc.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p style="padding-top:0.5em;">员工花名册</p>
            </div>

            <div class="menu_order_item" @click="workMode('2')">
                <img src="../../static/images/resume.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p style="padding-top:0.5em;">简历更新</p>
            </div>

            <div class="menu_order_item" @click="workMode('3')">
                <img src="../../static/images/overtime.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p style="padding-top:0.5em;">加班工时</p>
            </div>

            <div class="menu_order_item" @click="workMode('13')">
                <img src="../../static/images/scheduling.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                <p style="padding-top:0.5em;">排班管理</p>
            </div>

            <div class="menu_order">
                <div class="menu_order_item" @click="workMode('4')">
                    <img src="../../static/images/updtpwd.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">密码修改</p>
                </div>
    
                <div class="menu_order_item" @click="workMode('5')">
                    <img src="../../static/images/ssRepair.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">宿舍维修</p>
                </div>
    
                <div class="menu_order_item" @click="workMode('6')">
                    <img src="../../static/images/ssHygiene.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">宿舍卫生检查</p>
                </div>
            </div>
            <div class="menu_order">
                <div class="menu_order_item" @click="workMode('7')">
                    <img src="../../static/images/ssAdvice.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">投诉与建议</p>
                </div>
                <div class="menu_order_item" @click="workMode('8')">
                    <img src="../../static/images/ssWaterPower.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">水电费管理</p>
                </div>

                <!-- <div class="menu_order_item" @click="workMode('6')">
                    <img src="../../static/images/dmNotify.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">宿舍消息通知</p>
                </div> -->

                <div class="menu_order_item" @click="workMode('9')">
                    <img src="../../static/images/dmBill.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">水电账单查询</p>
                </div>
                <div class="menu_order_item" @click="workMode('10')">
                    <img src="../../static/images/postRoutinePng.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">岗位例事</p>
                </div>
                <div class="menu_order_item" @click="workMode('11')">
                    <img src="../../static/images/kpiMettleListPng.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">奋斗精神</p>
                </div>
                <div class="menu_order_item" @click="workMode('12')">
                    <img src="../../static/images/pceFormPNG.png" style="display:block;margin:0 auto;height:50%;padding-top:10%"/>
                    <p style="padding-top:0.5em;">人员综合素质评估</p>
                </div>
            </div>
            <div class="menu_order">
               
            </div>
            <div class="menu_order">
               
            </div>

        </div>
        
        
    </div>
</template>
<script>
import { DatetimePicker,Indicator,Toast } from 'mint-ui';
import { Calendar } from 'vant';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            c_show:false,
            popup_show:false,
            date:'',
            minDate:'',
            maxDate:'',
            userCode:'',
            userName:'',
            schedual:'开始排班',
            selectedValue: this.formatDate(new Date()),
            my_hour:require('../../static/images/emp_menu.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            fee:{},
            reports:{},
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date=this.formatDate(new Date)
        this.userCode=localStorage.getItem('userCode')
        this.userName=localStorage.getItem('userName')
    },
    methods: {
        workMode(num){
            if(num==1){
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({path:'/rosterStaff'});
            }else if(num==2){
                this.$router.push({path:'/jlbmb'});
            }else if(num==3){
                this.$router.push({path:'/overTimeReport'});
            }else if(num==4){
                this.$router.push({path:'/updatePwd'});
            }else if(num==5){
                // this.$router.push({path:'/ssRepair'});
                let rParams={ userCode: localStorage.getItem('userCode') }
                this.$axios.get("/dormApi/maintain/app/managerOrCommon",{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            if(res.result==='1'){ // 宿管
                                this.$router.push({path:'/repairManage'});
                            }else{ // 普工
                                this.$router.push({path:'/ssRepair'});
                            }
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    }else{
                        Toast({
                            message: "发生错误",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            }else if(num==6){
                let rParams={ userCode: localStorage.getItem('userCode') }
                this.$axios.get("/dormApi/maintain/app/managerOrCommon",{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            if(res.result==='1'){ // 宿管
                                this.$router.push({path:'/hygieneManage'});
                            }else{ 
                                Toast({
                                    message: "仅宿管可进入",
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    }else{
                        Toast({
                            message: "发生错误",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            }else if(num==7){
                this.$router.push({path:'/advice'});
            }else if(num==8){
                let rParams={ userCode: localStorage.getItem('userCode') }
                this.$axios.get("/dormApi/maintain/app/managerOrCommon",{params: rParams}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            if(res.result==='1'){ // 宿管
                                this.$router.push({path:'/waterPowerManage'});
                            }else{ 
                                Toast({
                                    message: "仅宿管可进入",
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        }else{
                            Toast({
                                message: res.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    }else{
                        Toast({
                            message: "发生错误",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            }else if(num==9){
                this.$router.push({path:'/dmBill'});
            }else if(num==10){
                this.$axios.get(`/jeecg-boot/app/example/getExampleAuth?userCode=${localStorage.getItem('userCode')}`).then(res=>{
                    if(res.data.code===200){
                        localStorage.setItem('exampleAuth',res.data.message)
                        if(res.data.message==1){
                            this.$router.push({name:'postRoutine'});
                        }else{
                            this.$router.push({name:'postRoutine'});
                            
                            // this.$router.push({path:'/postRoutineDetail',params: {userCode:localStorage.getItem('userCode')}});
                        }
                    }else{
                        Toast({
                            message: "发生错误",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
                // 
            }else if(num==11){
                this.$router.push({path:'/kpiMettle'});
            }else if(num==12){
                this.$router.push({path:'/pceForm'});
            }else if(num==13){
                this.$router.push({path:'/groupLeaderScheduling'});
            }
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour{
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}
.hour_item{
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_other_item{
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
.hour_left{
    float: left;
    width: 45%;
}
.hour_right{
    float: left;
    width: 54%;
}
.attence_item4{
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}
.attence_circle{
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item_circle4{
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.item_top{
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}
.menu_order_item{
    float: left;
    height: 100%;
    width: 33%;
}
.menu_order{
    background: white;
    width: 100%;
    height: 5.5rem;
}
.menu_order2{
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
</style>