<template>
    <div style="background:#f3f4f6;height:100%">
        <img :src="car_top" width="100%"/>
        <div class="top_title">车辆调度一览</div>
        <div class="top_hint">在这里您可以查看车辆调度情况</div>
        <mt-button type="primary" style="margin-top:10px;width:75%;" size="small" v-on:click="selectData">{{this.selectedValue}}</mt-button>
        <mt-datetime-picker
            ref="datePicker"
            @touchmove.native.stop.prevent
            v-model="dateVal"
            class="myPicker"
            type="date"
            year-format="{value}"
            month-format="{value}"
            date-format="{value}"
            @confirm="dateConfirm()">
        </mt-datetime-picker>
        <div style="margin-top:2rem;">
            <div class="car_item" v-for="(item,index) in carList" :key="index" @click="handleCarClick(index)">
                <img :src="bus"/>
                <div class="car_status">{{item.status}}</div>
                <div style="width:15rem;">
                    <div class="car_title_text">{{item.carNum}}<span class="car_title_name">{{item.carName}}</span></div>
                    <div class="car_text">用车人：{{item.contractName}}</div>
                    <div class="car_text">司&nbsp;&nbsp;&nbsp;机：&nbsp;{{item.actName}}</div>
                </div>
                
                <img :src="jt" class="right_pic"/>
            </div>
        </div>
        
    </div>
</template>
<script>
import { DatetimePicker } from 'mint-ui';
export default {
    data(){
        return{
            dateVal: '', // 默认是当前日期
            selectedValue: this.formatDate(new Date()),
            car_top:require('../../static/images/car_top.png'),
            bus:require('../../static/images/bus.png'),
            jt:require('../../static/images/jt.png'),
            carList:[]
        }
    },
    components:{
        DatetimePicker
    },
    created:function(){
        this.getCarInfo();
    },
    methods: {
        getCarInfo(){
            const self=this;
            this.$axios.get('/jeecg-boot/car/getCarInfo',{
                params:{
                    createTime:self.selectedValue
                }
            }).then(res=>{
                if(res.data.code=200){
                    console.log(res.data.result)
                    self.carList=res.data.result
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'top',
                        duration: 5000
                    });
                }
                console.log(self.carList)
            })
        },
        selectData () { // 打开时间选择器
            // 如果已经选过日期，则再次打开时间选择器时，日期回显（不需要回显的话可以去掉 这个判断）
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        dateConfirm () { // 时间选择器确定按钮，并把时间转换成我们需要的时间格式
            this.selectedValue = this.formatDate(this.dateVal)
            this.getCarInfo();
        },
        handleCarClick(index){
            this.$router.push({path:'/carDetail',query:this.carList[index]});
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.pick_items{
    width: 100%;
}
.top_title{
    color: #0077cb;
    font-size: 1.6rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.3rem;
}
.top_hint{
    color: #455a64;
    font-weight: 600;
}
.right_pic{
    position: absolute;
    right: 5%;
}
.car_title_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 1rem;
    color: #455a64;
    font-weight: 500;
}
.car_title_name{
    margin-left: 8%;
    font-size: 1.1rem;
    color: #000;
    font-weight: 600;
}
.car_text{
    text-align: left;
    margin-left: 10%;
    margin-top: 2%;
    font-size: 0.88rem;
    color: #455a64;
}
.car_status{
    position:absolute;
    left: 20%;
    top: 8%;
    color: #fff;
    font-size: 0.7rem;
    background-color: #455a64;
    width: 2.5rem;
    height: 1rem;
    border-radius: 20px;
}
.car_item{
    margin-top: 1rem;
    width: 100%;
    height: 6rem;
    background: #fff;
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 5%;
}
</style>