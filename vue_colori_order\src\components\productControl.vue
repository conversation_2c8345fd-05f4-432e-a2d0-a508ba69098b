<template>
    <div style="min-height:100%;background:#EEE;">
        <div class="control">
            <div class="control_title">生产管制表</div>

            <div class="people-bg">
                <div style="width:100%;height:50%;">
                    <div class="people-left">
                        <img :src="avatar" width="70rem" height="70rem" class="people-avatar"/>
                    </div>
                    <div class="people-right">
                        {{peopleInfo.name}}
                        <br/>
                        {{peopleInfo.code}}
                    </div>
                </div>
                <div style="width:100%;height:50%;">

                    <div style="display:flex;align-items: center;float:left;width:25%;height:100%;" @click="pushScan">
                        <div class="box-item">
                            <div>
                                IPQC
                            </div>
                            <div>扫码验证</div>
                        </div>
                    </div>

                    <div style="display:flex;align-items: center;float:left;width:25%;height:100%;" @click="popupVisible = true">
                        <div class="box-item">
                            <div>{{workshop}}</div>
                            <div>当前车间</div>
                        </div>
                    </div>
                    
                    <div style="display:flex;align-items: center;float:left;width:25%;height:100%;" @click="sendWorker">
                        <div class="box-item">
                            <div>{{secInfo}}</div>
                            <div>查询条件</div>
                        </div>
                    </div>

                    <div style="display:flex;align-items: center;float:left;width:25%;height:100%;" @click="c_show = true">
                        <div class="box-item">
                            <div>{{date}}</div>
                            <div>当前日期</div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        <!-- <div style="height:6rem;">
            <div class="top_order_title">生产管制表</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%"/>
            </div>
        </div>
        <div class="sign">
            <img :src="plotTop" width="90%"/>
            <div class="plotName">{{peopleInfo.name}}</div>
            <div class="plotCode">{{peopleInfo.code}}</div>
            <div class="plotFactory">{{peopleInfo.department}}</div>
            <div class="plotWorkshop">{{peopleInfo.workshop}}</div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div> -->

        <!-- <van-cell title="当前车间" :value="workshop" @click="popupVisible = true" style="margin-left:5%;margin-top:2%;width:90%;border-radius:10px;"/>
        <van-cell title="查询条件" :value="secInfo" @click="sendWorker" style="margin-left:5%;margin-top:2%;width:90%;border-radius:10px;"/>
        <van-cell title="当前日期" :value="date" @click="c_show = true"  style="margin-left:5%;margin-top:2%;width:90%;border-radius:10px;"/>-->
        <van-calendar v-model="c_show" :min-date="minDate" @confirm="onConfirm" :show-confirm="false" position="right" /> 

        <mt-popup class="popup-div" v-model="popupVisible" popup-transition="popup-fade" closeOnClickModal="true" position="bottom">
            <mt-picker :slots="popupSlots" @change="onValuesChange"  showToolbar @touchmove.native.stop.prevent>
                <div class="picker-toolbar-title">
                    <div class="usi-btn-cancel" @click="popupVisible = !popupVisible">取消</div>
                    <div class="">请选择车间</div>
                    <div class="usi-btn-sure" @click="popupOk()">确定</div>
                </div>
            </mt-picker>
        </mt-popup>

        <div style="margin-top:120px;">
            <div class="orderType" v-if="waitList.length>0">待复核</div>
            <div v-for="item in waitList" :key="item.id">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle">{{item.moId}}_{{item.leaderNo}}_{{item.mitosome}}</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
            <div class="orderType" v-if="runList.length>0">生产记录</div>
            <div v-for="item in runList" :key="item.id">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle" :class="item.checkStatus=='2'?'ycl-style':''">{{item.name}}</div>
                        <div class="itemTitle">{{item.moId}}_{{item.leaderNo}}_{{item.mitosome}}</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
            <div class="orderType" v-if="closeList.length>0">关闭记录</div>
            <div v-for="item in closeList" :key="item.id">
                <div class="items_d" @click="startToNext(item)">
                    <div class="item_bg">
                        <div class="itemTitle">{{item.name}}</div>
                        <div class="itemTitle">{{item.moId}}_{{item.leaderNo}}_{{item.mitosome}}</div>
                    </div>
                    <div class="item_add">
                    </div>
                </div>
            </div>
        </div>

        <div style="clear:both;"></div>

    </div>
</template>

<script>
import { DatetimePicker,Toast,Indicator,MessageBox } from 'mint-ui';

let wx=window.wx

export default ({
    data() {
        return{
            plotTop:require('../../static/images/plat_top.png'),
            message:require('../../static/images/message.png'),
            card:require('../../static/images/card.png'),
            addControl:require('../../static/images/addProductControl.png'),
            peopleInfo:{},
            runList:[],
            waitList:[],
            closeList:[],
            workLine:[],
            popupSlots:[],
            popupVisible:false,
            questionTypeVal:'',
            userCode:'',
            c_show:false,
            workshop:'全部',
            secInfo:'',
            date:''
        }
        
    },
    created:function(){
        let nowDate = new Date();

        this.minDate = new Date(nowDate.getTime() - 7 * 24 * 3600 * 1000);
        this.date=this.formatDate(new Date)
        this.avatar=localStorage.getItem('avatar');
        this.workshop=localStorage.getItem('workshop');
        if(this.workshop==null || this.workshop==undefined || this.workshop==''){
            this.workshop="全部";
        }
        // this.setWxInfo();
        this.getWorkLine()
        // this.getMainList(workshop);
    },
    methods:{
        
        getWorkLine(){
            let self=this
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getWorkLine',{params:{userCode:localStorage.getItem('userCode'),type:'2'}}).then(res=>{
                if(res.data.code==200){
                    self.workLine.push("全部");
                    let item=res.data.result;
                    for(var n=0;n<item.length;n++){
                        self.workLine.push(item[n]);
                    }
                    let params={
                        values:self.workLine
                    }
                    self.popupSlots.push(params)
                    if(self.workshop==null || self.workshop==undefined || self.workshop==''){
                        self.workshop="全部";
                    }
                    self.getMainList(self.workshop,self.secInfo)
                }
            })
        },
        sendWorker(){
            let self=this
            MessageBox.prompt('请输入查询条件(MO单号/组长名称)').then(({ value, action }) => {
                if(action=="confirm"){
                    self.secInfo=value
                    self.getMainList(self.workshop,self.secInfo)
                }
            });
        },
        selectWorkshop(){
            let self=this
            MessageBox.prompt('请输入车间名称(可简化查询)').then(({ value, action }) => {
                if(action=="confirm"){
                    self.workshop=value
                    self.getMainList(self.workshop,self.secInfo)
                }
            });
        },
        getMainList(workshop,secInfo){
            let self=this;
            self.userCode=localStorage.getItem('userCode');
            self.$axios.get('/jeecg-boot/app/appQuality/getMainList',{params:{userCode:self.userCode,workDay:self.date,workshop:workshop,secInfo:secInfo}}).then(res=>{
                if(res.data.code==200){
                    self.peopleInfo=res.data.result.peopleInfo
                    self.runList=res.data.result.runList
                    self.waitList=res.data.result.waitList
                    self.closeList=res.data.result.closeList
                    
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                    return;
                }
            })
        },
        // 问题类型弹框点击确认
        popupOk(){
            this.workshop = this.questionTypeVal;
            this.popupVisible = false;
            localStorage.setItem('workshop',this.workshop);
            this.getMainList(this.workshop,this.secInfo)
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values){
            this.questionTypeVal = values[0];
        },
        pushScan(){

            let self=this
            var userCode=localStorage.getItem('userCode');

            // T22101500294

            // self.$router.push({name:"ScanResult",params:{result:'T24091400233',type:'1'}})

            // self.$router.push({name:"ScanColloidInfo",params:{result:'1549548010619985921',type:'2'}})
            // self.$router.push({name:"ScanWareInResult",params:{result:'R22083000269'}})

            wx.scanQRCode({
                desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                success: function(res) {
                    // 回调
                    var result = res.resultStr;//当needResult为1时返回处理结果
                    self.$router.push({name:"ScanResult",params:{result:result,type:'1'}})
                },
                error: function(res) {
                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                        alert('版本过低请升级')
                    }
                }
            });
            
        },
        pushWorkBegin(){
            if(this.peopleInfo.department!='质量管理中心'){
                Toast({
                    message: "您并不属于IPQC，无权操作！",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            localStorage.setItem('beginItem',null);
            this.$router.push({name:"WorkBegin"})
        },
        startToNext(item){
            let self=this;
            localStorage.setItem('controlItem',JSON.stringify(item));
            localStorage.setItem('peopleInfo',JSON.stringify(self.peopleInfo));
            this.$router.push({name:"WorkStart"})
        },
        onConfirm(date) {
            this.c_show = false;
            this.date = this.formatDate(date);
            this.getMainList(this.workshop,this.secInfo)
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
})
</script>


<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
    margin-bottom: 5%;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 8%;
    font-size: 20px;
    font-weight: 800;
    left: 35%;
    color: #F0F0F0;
}
.plotCode{
    position: absolute;
    top: 9%;
    left: 50%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}
.addControl{
    position: absolute;
    top: 31%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    width: 85%;
    text-align: left;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 5%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
.ycl-style{
    color: crimson;
}
.search_title{
    width: 80%;
    height: 100%;
    float: left;
    font-size: 18px;
    text-align: center;
}
.control{    
    display: inline-block;
    width: 100%;     
    height:150px;        
    background: cornflowerblue;
}
.name_pic{
    width: 25%;
    height: 100%;
    display: flex;
    float: left;
    justify-content: center;
    align-items: center;
}
.control_title{
    font-size: 20px;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 25%;
    font-weight: 800;
}
.plot1{
    display: flex;
    align-items: center;
    width: 64%;
    height: 100%;
    float: left;
}
.plot{
    width: 100%;
    height: 75%;
    display: block;
}
.plot_name{
    width: 38%;
    display: flex;
    font-size: 20px;
    float: left;
    color: #fff;
}
.plot_code{
    width: 62%;
    float: left;
    font-size: 16px;
    color: #FFF;
}
.plot_department{
    width: 38%;
    display: flex;
    font-size: 16px;
    float: left;
    color: #FFF;
    margin-top: 11%;
}
.people-bg{
    background: white;
    margin: 10px;
    height: 200px;
    text-align: left;
}
.people-left{
    width:30%;
    height: 100%;
    float:left;
    display: flex;
    justify-content: center;
    align-items: center;
}
.people-right{
    width:70%;
    height: 100%;
    float:left;
    display: flex;
    align-items: center;
}
.box-item{
    display: flex;
    width: 100%;
    height: 50%;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
}
.people-avatar{
    border-radius: 50%;
}
/deep/.van-cell__title
{
text-align: left;
}
</style>