<template>
    <div class="order">
        <div style="height:6rem;">
            <div class="top_title">其他状态</div>
            <div class="top_msg">
                <img :src="message" width="70%" style="margin-top:25%;margin-right:30%" />
            </div>
        </div>
        <div class="sign">
            <img :src="plotTop" width="90%" />
            <div class="plotName">{{ item.userName }}</div>
            <div class="plotCode">{{ item.userCode }}</div>
            <div class="plotFactory">{{ item.name }}</div>
            <div class="plotWorkshop">{{ item.moId }}</div>
            <div class="plotCard">
                <img :src="card" width="70%" />
            </div>
        </div>

        <div class="menu_order">
            <div class="menu_order_item" @click="sendMode('mnt')" :class="mntdisable ? 'click-disable' : ''">
                <img :src="mntStart" width="90%" :hidden="mntStartVis" />
                <img :src="mntEnd" width="90%" :hidden="mntEndVis" />
            </div>
            <div class="menu_order_item" @click="sendMode('change')" :class="changedisable ? 'click-disable' : ''">
                <img :src="changeStart" width="90%" :hidden="changeStartVis" />
                <img :src="changeEnd" width="90%" :hidden="changeEndVis" />
            </div>
        </div>

        <div class="menu_order">
            <div class="menu_order_item" @click="sendMode('wait')" :class="waitdisable ? 'click-disable' : ''">
                <img :src="waitStart" width="90%" :hidden="waitStartVis" />
                <img :src="waitEnd" width="90%" :hidden="waitEndVis" />
            </div>
            <div class="menu_order_item" @click="sendMode('return')" :class="returndisable ? 'click-disable' : ''">
                <img :src="returnStart" width="90%" :hidden="returnStartVis" />
                <img :src="returnEnd" width="90%" :hidden="returnEndVis" />
            </div>

        </div>

        <div class="menu_order">
            <div class="menu_order_item" @click="sendMode('other')" :class="otherdisable ? 'click-disable' : ''">
                <img :src="otherStart" width="90%" :hidden="otherStartVis" />
                <img :src="otherEnd" width="90%" :hidden="otherEndVis" />
            </div>
        </div>

        <div class="menu_order" hidden="true">
            <div class="menu_order_item" @click="sendMode('train')" :class="traindisable ? 'click-disable' : ''">
                <img :src="trainStart" width="90%" :hidden="trainStartVis" />
                <img :src="trainEnd" width="90%" :hidden="trainEndVis" />
            </div>
            <div class="menu_order_item" @click="sendMode('select')" :class="selectdisable ? 'click-disable' : ''">
                <img :src="selectStart" width="90%" :hidden="selectStartVis" />
                <img :src="selectEnd" width="90%" :hidden="selectEndVis" />
            </div>
        </div>

        <div class="menu_order">
            <div class="menu_order_item" @click="addMnt" :class="mntdisable ? 'click-disable' : ''">
                <img :src="mntAdd" width="90%" :hidden="mntEndVis" />
            </div>
            <div class="menu_order_item" @click="goRepair" :class="mntdisable ? 'click-disable' : ''">
                <img :src="repair" width="90%" :hidden="mntEndVis" />
            </div>
        </div>


        <close-modal ref="modalFormc" @ok="modalCloseFormOk"></close-modal>
    </div>
</template>
<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from 'mint-ui';
import eventBus from '../common/eventBus';
import CloseModal from './list/CloseModal.vue';
import moment from 'moment'
let wx=window.wx

export default {
    components: { CloseModal },
    data() {
        return {
            plotTop: require('../../static/images/plat_top.png'),
            message: require('../../static/images/message.png'),
            card: require('../../static/images/card.png'),
            startMite: require('../../static/images/startMite.png'),
            endMite: require('../../static/images/endMite.png'),
            lxendMite: require('../../static/images/lx_endMite.png'),
            finishMo: require('../../static/images/finishMo.png'),
            menuPic: require('../../static/images/menu_pic.png'),
            personLogo: require('../../static/images/user_logo.png'),
            mntStart: require('../../static/images/mntStart.png'),
            mntEnd: require('../../static/images/mntEnd.png'),
            mntAdd: require('../../static/images/mntAdd.png'),
            repair: require('../../static/images/repair.png'),
            changeStart: require('../../static/images/changeStart.png'),
            changeEnd: require('../../static/images/changeEnd.png'),
            waitStart: require('../../static/images/waitStart.png'),
            waitEnd: require('../../static/images/waitEnd.png'),
            trainStart: require('../../static/images/trainStart.png'),
            trainEnd: require('../../static/images/trainEnd.png'),
            returnStart: require('../../static/images/returnStart.png'),
            returnEnd: require('../../static/images/returnEnd.png'),
            selectStart: require('../../static/images/selectStart.png'),
            selectEnd: require('../../static/images/selectEnd.png'),
            otherStart: require('../../static/images/otherStart.png'),
            otherEnd: require('../../static/images/otherEnd.png'),
            selectedValue: this.formatDate(new Date()),
            dateVal: '',
            status: '',
            value: false,
            personList: [],
            item: [],
            questionType: '',
            questionTypeVal: '',
            popupVisible: false,
            mntStartVis: false,
            changeStartVis: false,
            waitStartVis: false,
            trainStartVis: false,
            otherStartVis: false,
            returnStartVis: false,
            selectStartVis: false,
            mntEndVis: true,
            changeEndVis: true,
            waitEndVis: true,
            trainEndVis: true,
            returnEndVis: true,
            otherEndVis: true,
            selectEndVis: true,
            mntdisable: false,
            changedisable: false,
            waitdisable: false,
            traindisable: false,
            returndisable: false,
            otherdisable: false,
            selectdisable: false,
            popupSlots: [
                {
                    values: [
                        '白班(上午)', '白班(下午)', '白班(加班)', '晚班(上半夜)', '晚班(下半夜)'
                    ]
                }
            ],
        }
    },
    created:async function () {
        let self = this;
        self.itemParams=JSON.parse(localStorage.getItem("params"))
        self.worker = self.itemParams.userCode;
        self.item = self.itemParams = JSON.parse(localStorage.getItem("orderOtherItem"))
        await self.getInfo(self.itemParams)
        self.personList = self.item.gcWorkPlanList
        console.log(self.item)
        self.hide();
        if (self.item.lastStatus == '11') {
            self.mntEndVis = false
            self.mntdisable = false
            self.status = '21'
        } else if (self.item.lastStatus == '12') {
            self.changeEndVis = false
            self.changedisable = false
            self.status = '22'
        } else if (self.item.lastStatus == '13') {
            self.waitEndVis = false
            self.waitdisable = false
            self.status = '23'
        } else if (self.item.lastStatus == '14') {
            self.trainEndVis = false
            self.traindisable = false
            self.status = '24'
        } else if (self.item.lastStatus == '15') {
            self.returnEndVis = false
            self.returndisable = false
            self.status = '25'
        } else if (self.item.lastStatus == '16') {
            self.selectEndVis = false
            self.selectdisable = false
            self.status = '26'
        } else if (self.item.lastStatus == '17') {
            self.otherEndVis = false
            self.otherdisable = false
            self.status = '27'
        } else if (self.item.lastStatus == '18') {
            self.hide();
        }else {
            self.show()
        }

    },
    methods: {
        getInfo(item){
            let self=this;
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get('/jeecg-boot/app/gcWorkshop/getMailInfo',{params:{userCode:item.userCode,id:item.id,workDay:moment(item.createTime).format('YYYY-MM-DD')}}).then(res=>{
                Indicator.close();
                if (res.data.code == 200) {
                    self.item.batchFlag=res.data.result.workOrderList[0].batchFlag
                    self.item.mixId=res.data.result.workOrderList[0].mixId
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        goRepair() {
            this.$router.push({
                name: "RepairOrder",
                params: {
                    active: 2
                }
            });
        },
        addMnt() {
            this.$router.push({
                name: "Repair",
                params: {
                    moId: this.item.moId,
                    lpId: this.item.id,
                    workshop: this.item.workshop,
                    jobCenter: this.item.mitosome,
                    book: this.item.book,
                    type: 11,
                    createNo: localStorage.getItem('userCode'),
                    creator: localStorage.getItem('userName')
                }
            });
        },
        modalCloseFormOk() { },
        startToNext() {

        },
        sendMode(mode) {
            let self = this;
            let num = 0;
            if (mode == "mnt") {
                if (self.status == '') {
                    if (self.item.department.indexOf('苏州') != -1) {
                        num = '11'
                        // self.item.lastStatus = '11'
                        // localStorage.setItem('orderOtherItem', JSON.stringify(self.item));
                        MessageBox.confirm('',{
                            message: '请选择维修模式？',
                            title: '提示',
                            confirmButtonText: '找人修',
                            cancelButtonText: '自己修'
                        })
                        .then(action => {
                            Toast({
                                message: "请扫描设备标签",
                                position: 'bottom',
                                duration: 2000
                            });
                            wx.scanQRCode({
                                desc: 'scanQRCode desc',
                                needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                                success: function(res) {
                                    // 回调
                                    var result = res.resultStr;//当needResult为1时返回处理结果
                                    self.$router.push({
                                        name: "Repair",
                                        params: {
                                            moId: self.item.moId,
                                            lpId: self.item.id,
                                            type: num,
                                            deviceNo:result,
                                            createNo: localStorage.getItem('userCode'),
                                            creator: localStorage.getItem('userName')
                                        }
                                    });
                                },
                                error: function(res) {
                                    if (res.errMsg.indexOf('function_not_exist') > 0) {
                                        alert('版本过低请升级')
                                    }
                                }
                            });
                        }).catch((res)=>{
                            num = '11'
                            let params = {
                                lpId: self.item.id,
                                // gcWorkOperationList:self.personList,
                                type: num,
                                createNo: localStorage.getItem('userCode'),
                                creator: localStorage.getItem('userName')
                            }
                            self.sendModes(num, params)
                        });
                    } else {
                        num = '11'
                        let params = {
                            lpId: self.item.id,
                            // gcWorkOperationList:self.personList,
                            type: num,
                            createNo: localStorage.getItem('userCode'),
                            creator: localStorage.getItem('userName')
                        }
                        self.sendModes(num, params)
                    }
                } else {
                    num = '21'
                }
            } else if (mode == "change") {
                if (self.status == '') {
                    num = '12'
                } else {
                    num = '22'
                }
            } else if (mode == "wait") {
                if (self.status == '') {
                    num = '13'
                } else {
                    num = '23'
                }
            } else if (mode == "train") {
                if (self.status == '') {
                    num = '14'
                } else {
                    num = '24'
                }
            } else if (mode == "return") {
                if (self.status == '') {
                    num = '15'
                } else {
                    num = '25'
                }
            } else if (mode == "select") {
                if (self.status == '') {
                    num = '16'
                } else {
                    num = '26'
                }
            }
            else if (mode == "other") {
                if (self.status == '') {
                    num = '17'
                } else {
                    num = '27'
                }
            }
            let params = {
                lpId: self.item.id,
                // gcWorkOperationList:self.personList,
                type: num,
                createNo: localStorage.getItem('userCode'),
                creator: localStorage.getItem('userName')
            }
            if (num > 20) {
                MessageBox.prompt('操作原因').then(({ value, action }) => {
                    if (action == "confirm") {
                        params.reason = value
                        if (num == 27) {
                            if (value == null || value == "") {
                                Toast({
                                    message: "请输入操作原因",
                                    position: 'bottom',
                                    duration: 2000
                                });
                                return;
                            }
                        }
                        if (num == 22 && self.item.workType == '1') {
                            if (self.item.checkFlag == '0') {
                                MessageBox.confirm('', {
                                    message: '是否开线？',
                                    title: '提示',
                                    confirmButtonText: '开线',
                                    cancelButtonText: '关线'
                                })
                                    .then(action => {
                                        if (action == "confirm") {
                                            // self.$router.push({name:"WorkBegin",params:{items:self.item,type:1,moId:self.item.moId,workDay:self.item.createTime,checkFlag:self.item.checkFlag}})
                                            self.$router.push({ name: "WorkBegin", params: { items: params, type: 1, lpId: self.item.id, checkFlag: self.item.checkFlag } })
                                        }
                                    }).catch((res) => {
                                        console.log("res:" + res)
                                        params.type = "98"
                                        params.count = 0
                                        params.gcWorkOperationList = self.personList
                                        params.preCategory = self.item.preCategory
                                        params.workType = self.item.workType
                                        params.repCreator = localStorage.getItem('userCode')
                                        // self.$refs.modalFormc.edit(params);
                                        // self.$refs.modalFormc.title="关线录入";
                                        // self.$refs.modalFormc.disableSubmit = true;
                                        // params.type='98'
                                        self.sendModesNew('22', params)
                                    });

                            } else if (self.item.checkFlag == '1') {
                                self.getMoStatus(num, params)
                            } else {
                                self.sendModes(num, params)
                            }
                        } else if(num == 22 && self.item.workType == '4'){
                            self.checkMixture(num,params);
                        } else{
                            self.sendModes(num, params)
                        }
                    }
                });
            } else {
                if(num!='11'){
                    self.sendModes(num, params)
                }
            }

        },
        checkMixture(num,params){
            let self=this
            self.$axios.get('/jeecg-boot/app/mix/checkMixture',{params:{lpId:self.item.id}}).then(res=>{
                if(res.data.success){
                    if(res.data.message == '1'){
                        MessageBox.confirm('', {
                            message: '是否开线？',
                            title: '提示',
                            confirmButtonText: '开线',
                            cancelButtonText: '关线'
                        }).then(action => {
                            if (action == "confirm") {
                                self.$router.push({name:"MixtureSelect",params:{lpId:self.item.id}})
                            }
                        }).catch((res) => {
                            console.log("res:" + res)
                            params.type = "98"
                            params.count = 0
                            params.gcWorkOperationList = self.personList
                            params.preCategory = self.item.preCategory
                            params.workType = self.item.workType
                            params.repCreator = localStorage.getItem('userCode')
                            self.sendModesNew('22', params)
                        });
                    }else{
                        if (self.item.batchFlag == 1) {
                            Toast({
                                message: "请确认是否绑定批记录",
                                position: 'bottom',
                                duration: 2000
                            });
                        } else if (self.item.batchFlag == 99) {
                            // 暂不绑定配料单
                            self.sendModes(num, params)
                        } else if (self.item.batchFlag == 5) {
                            // 已绑定配料单 且 绑定批记录 且 已复核
                            self.sendModes(num, params)
                        } else {
                            // 已绑定配料单 且 绑定批记录 且 未复核
                            let obj = {
                                id: self.item.mixId,
                                lpId: self.item.lpId
                            }
                            self.$router.push({
                                name: "checkBeforeProduction",
                                params: obj
                            });
                        }
                    }
                }else{
                    self.sendModes(num,params)
                }
            })
        },
        getMoStatus(num, params) {
            let self = this
            self.$axios.get('/jeecg-boot/app/appQuality/getCheckStatus', { params: { lpId: self.item.id } }).then(res => {
                if (res.data.code == 200) {
                    if (res.data.message == '2') {
                        Indicator.close();
                        self.sendModes(num, params)
                    } else if (res.data.message == '0') {
                        // self.$router.push({name:"WorkBegin",params:{items:self.item,type:1,moId:self.item.moId,workDay:self.item.createTime,checkFlag:self.item.checkFlag}})
                        self.$router.push({ name: "WorkBegin", params: { items: params, type: 1, lpId: self.item.id, checkFlag: self.item.checkFlag } })
                    } else {
                        Toast({
                            message: "请等待IPQC复核！",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        sendModes(num, params) {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this

            if (num == '21') {
                this.$axios
                    .get(`/jeecg-boot/app/mac/checkStatus?lpId=${self.item.id}`)
                    .then(res => {
                        if (res.data.code == 200) {
                            if (res.data.message != '0') {
                                self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                                    if (res.data.code == 200) {
                                        Indicator.close();
                                        self.hide();
                                        if (num == '11') {
                                            self.mntEndVis = false
                                            self.mntdisable = false
                                            self.status = '21'
                                        } else if (num == '12') {
                                            self.changeEndVis = false
                                            self.changedisable = false
                                            self.status = '22'
                                        } else if (num == '13') {
                                            self.waitEndVis = false
                                            self.waitdisable = false
                                            self.status = '23'
                                        } else if (num == '14') {
                                            self.trainEndVis = false
                                            self.traindisable = false
                                            self.status = '24'
                                        } else if (num == '15') {
                                            self.returnEndVis = false
                                            self.returndisable = false
                                            self.status = '25'
                                        } else if (num == '16') {
                                            self.selectEndVis = false
                                            self.selectdisable = false
                                            self.status = '26'
                                        } else if (num == '17') {
                                            self.otherEndVis = false
                                            self.otherdisable = false
                                            self.status = '27'
                                        } else {
                                            self.show();
                                            self.status = ''
                                        }
                                        Toast({
                                            message: res.data.message,
                                            position: 'bottom',
                                            duration: 2000
                                        });
                                    } else {
                                        Indicator.close();
                                        self.$router.go(-1);
                                        Toast({
                                            message: res.data.message,
                                            position: 'bottom',
                                            duration: 2000
                                        });
                                    }
                                })
                            } else {
                                Indicator.close();
                                Toast({
                                    message: '不能关闭',
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        } else {
                            Indicator.close();
                            Toast({
                                message: '失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            } else {
                self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                    if (res.data.code == 200) {
                        Indicator.close();
                        self.hide();
                        if (num == '11') {
                            self.mntEndVis = false
                            self.mntdisable = false
                            self.status = '21'
                        } else if (num == '12') {
                            self.changeEndVis = false
                            self.changedisable = false
                            self.status = '22'
                        } else if (num == '13') {
                            self.waitEndVis = false
                            self.waitdisable = false
                            self.status = '23'
                        } else if (num == '14') {
                            self.trainEndVis = false
                            self.traindisable = false
                            self.status = '24'
                        } else if (num == '15') {
                            self.returnEndVis = false
                            self.returndisable = false
                            self.status = '25'
                        } else if (num == '16') {
                            self.selectEndVis = false
                            self.selectdisable = false
                            self.status = '26'
                        } else if (num == '17') {
                            self.otherEndVis = false
                            self.otherdisable = false
                            self.status = '27'
                        } else {
                            self.show();
                            self.status = ''
                        }
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    } else {
                        Indicator.close();
                        // self.$router.go(-1);
                        Toast({
                            message: res.data.message,
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                })
            }

        },
        sendModesNew(num, params) {
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            let self = this
            console.log(num)
            self.$axios.post('/jeecg-boot/app/gcWorkshop/getLeadDeployNew', params).then(res => {
                if (res.data.code == 200) {
                    Indicator.close();
                    self.hide();
                    if (num == '11') {
                        self.mntEndVis = false
                        self.mntdisable = false
                        self.status = '21'
                    } else if (num == '12') {
                        self.changeEndVis = false
                        self.changedisable = false
                        self.status = '22'
                    } else if (num == '13') {
                        self.waitEndVis = false
                        self.waitdisable = false
                        self.status = '23'
                    } else if (num == '14') {
                        self.trainEndVis = false
                        self.traindisable = false
                        self.status = '24'
                    } else if (num == '15') {
                        self.returnEndVis = false
                        self.returndisable = false
                        self.status = '25'
                    } else if (num == '16') {
                        self.selectEndVis = false
                        self.selectdisable = false
                        self.status = '26'
                    } else if (num == '17') {
                        self.otherEndVis = false
                        self.otherdisable = false
                        self.status = '27'
                    } else {
                        self.show();
                        self.status = ''
                    }
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                } else {
                    Indicator.close();
                    self.$router.go(-1);
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        hide() {
            let self = this
            self.mntStartVis = true
            self.changeStartVis = true
            self.waitStartVis = true
            self.trainStartVis = true
            self.returnStartVis = true
            self.selectStartVis = true
            self.otherStartVis = true
            self.mntEndVis = true
            self.changeEndVis = true
            self.waitEndVis = true
            self.trainEndVis = true
            self.returnEndVis = true
            self.selectEndVis = true
            self.otherEndVis = true
            self.mntdisable = true
            self.changedisable = true
            self.waitdisable = true
            self.traindisable = true
            self.returndisable = true
            self.selectdisable = true
            self.otherdisable = true
        },
        show() {
            let self = this
            self.mntStartVis = false
            self.changeStartVis = false
            self.waitStartVis = false
            self.trainStartVis = false
            self.returnStartVis = false
            self.selectStartVis = false
            self.otherStartVis = false
            self.mntEndVis = true
            self.changeEndVis = true
            self.waitEndVis = true
            self.trainEndVis = true
            self.returnEndVis = true
            self.selectEndVis = true
            self.otherEndVis = true
            self.mntdisable = false
            self.changedisable = false
            self.waitdisable = false
            self.traindisable = false
            self.returndisable = false
            self.selectdisable = false
            self.otherdisable = false
        },
        openQuestionType() {
            this.popupVisible = true;
        },
        popupOk() {
            this.questionType = this.questionTypeVal;
            this.popupVisible = false;
        },
        //问题类型的弹框picker值发生改变
        onValuesChange(picker, values) {
            this.questionTypeVal = values[0];
        },
        selectData() {
            if (this.selectedValue) {
                this.dateVal = this.selectedValue
            } else {
                this.dateVal = new Date()
            }
            this.$refs['datePicker'].open()
        },
        formatDate(secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order {
    background-color: #ebecf7;
}

.top_title {
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}

.click-disable {
    pointer-events: none;
}

.top_msg {
    float: right;
}

.items_d {
    padding: 5%;
    height: 6rem;
}

.item_bg {
    background-image: url('../../static/images/item_bg.png');
    width: 60%;
    height: 6rem;
    text-align: left;
    float: left;
}

.item_add {
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 39%;
    float: left;
    height: 6rem;
}

.itemTitle {
    padding: 5%;
}

.sign {
    text-align: center;
}

.plotName {
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}

.plotCode {
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}

.plotCard {
    position: absolute;
    top: 14%;
    right: 8%;
    color: #fff;
}

.plotFactory {
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}

.plotWorkshop {
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}

.plotMitosome {
    position: absolute;
    top: 38%;
    left: 35%;
    color: #fff;
}

.plotTime {
    background: url('../../static/images/search_time.png');
    width: 90%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}

.orderType {
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}

.menu_order_item {
    padding: 3%;
    float: left;
    width: 43%;
}

.menu_order {
    width: 100%;
    height: 11rem;
}

.more {
    background-image: url('../../static/images/more.png');
    background-size: 100%, 100%;
    width: 33%;
    height: 3rem;
    display: flex;
    align-items: center;
    margin-left: 5%;
    color: #fff;
    background-repeat: no-repeat;
}

.more p {
    display: inline;
    text-align: center;
    width: 90%;
}

.other {
    background-image: url('../../static/images/other.png');
    background-size: 100%, 100%;
    width: 90%;
    margin: 5%;
    height: 12rem;
    background-repeat: no-repeat;
}

.other p {
    font-weight: 600;
    text-align: left;
    padding: 10%;
}

.person_item {
    background-image: url('../../static/images/person_item.png');
    background-repeat: no-repeat;
    background-size: 100%, 100%;
    margin: 5%;
    height: 18rem;
}

.person_name {
    float: left;
    display: flex;
    align-items: center;
    width: 70%;
    height: 100%;
}

.person_menu {
    float: right;
    width: 26%;
    position: absolute;
    right: 0.5rem;
}

.person_line {
    background-color: #d8d2f7;
    height: 1px;
    width: 90%;
    margin-left: 5%;
}

.person-top {
    padding: 5%;
    height: 2rem;
}

.person-left {
    float: left;
    width: 65%;
    height: 100%;
    text-align: left;
}

.person-right {
    float: left;
    display: flex;
    align-items: center;
    width: 33%;
    height: 100%;
}

.circle {
    width: 10px;
    height: 10px;
    background-color: #4a1bf6;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.person_menu_item {
    padding: 5%;
}

.addUser {
    background-image: url('../../static/images/addUser.png');
    background-size: 100% 100%;
    width: 90%;
    margin: 5%;
}

.picker-toolbar-title {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    background-color: #eee;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
}

.usi-btn-cancel,
.usi-btn-sure {
    color: #26a2ff;
    font-size: 16px;
}

.popup-div {
    width: 100%;
}
</style>