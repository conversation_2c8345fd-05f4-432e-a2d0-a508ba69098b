<template>
    <div>
        <img src="../../static/images/feeding_check.png" width="100%" />
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('4')">
                <img src="../../static/images/FeedingCheckDetail4.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>复核员校验</p>
            </div>
            <!-- <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/FeedingCheckDetail1.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>拉料工校验</p>
            </div> -->
            <div class="menu_order_more_item" @click="goDetail('2')">
                <img src="../../static/images/FeedingCheckDetail2.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>操作工校验</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/FeedingCheckDetail1.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>预混料复核</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('9')">
                <img src="../../static/images/feedCheckPng.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>投料校验</p>
            </div>
        </div>
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('3')">
                <img src="../../static/images/FeedingCheckDetail3.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>校验已完成</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('6')">
                <img src="../../static/images/FeedingCheckDetail6.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>已投产</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('5')">
                <img src="../../static/images/FeedingCheckDetail5.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>单据状态</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('10')">
                <img src="../../static/images/errorListPng.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>异常单据</p>
            </div>
        </div>
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('7')">
                <img src="../../static/images/FeedingCheckDetail7.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>查看标签</p>
            </div>
           
            <div class="menu_order_more_item" @click="goDetail('8')">
                <img src="../../static/images/FeedingCheckDetail8.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>标签打印申请</p>
            </div>
        </div>
    </div>
</template>

<script>
    import { Toast } from "vant";
    export default {
        data() {
            return {
            }
        },
        created() {
        },
        methods: {
            goDetail(i) {
                if (i == 1) {
                    this.$router.push({ name: "FeedingCheck1" })
                } else if (i == 2) {
                    this.$router.push({ name: "FeedingCheck2" })
                } else if (i == 3) {
                    this.$router.push({ name: "FeedingCheck3" })
                } else if (i == 4) {
                    this.$router.push({ name: "FeedingCheck4" })
                } else if (i == 5) {
                    this.$router.push({ name: "FeedingCheck5" })
                } else if (i == 6) {
                    this.$router.push({ name: "FeedingCheck6" })
                } else if (i == 7) {
                    this.$router.push({ name: "FeedingCheck7" })
                } else if (i == 8) {
                    this.$router.push({ name: "FeedingCheck8" })
                } else if (i == 9) {
                    this.$router.push({ name: "FeedingCheck9" })
                } else if (i == 10) {
                    this.$router.push({ name: "FeedingCheckErrorList" })
                }
            },
        },
    }
</script>

<style scoped>
    .menu_order_more_item {
        float: left;
        height: 100%;
        width: 25%;
    }

    .menu_order_more {
        background: #f8f3f3;
        border-radius: 10px;
        margin-top: 5%;
        margin-left: 5%;
        width: 90%;
        height: 5.5rem;
    }
</style>