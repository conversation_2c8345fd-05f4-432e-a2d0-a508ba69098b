<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    okText="确定"
    cancelText="取消"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-button @click="add" type="primary" style="width:100%;margin-bottom: 3%;"
      >新增</a-button
    >
    <div v-for="item in goodsInfo" style="text-align: left;position: relative;">
      <a-button
        @click="delTank(item)"
        type="primary"
        style="position: absolute; right: 2%; top: 15%"
        >删除</a-button
      >
      <div>坦克名称:{{ item.tankName }}---坦克编号:{{ item.tankNo }}</div>
      <div>胶体存量:{{ item.realVolume }}</div>
      <van-field label="胶体使用量" v-model="item.output" />
    </div>
    <div v-show="!goodsInfo.length" class="shopping">
      <div><img src="../../../static/images/nothing.png" alt="" /></div>
      <p>暂无数据</p>
    </div>
    <addModel ref="modalForm" @ok="modalFormOk"></addModel>
  </a-modal>
</template>

<script>
import "ant-design-vue/dist/antd.css";
import Vue from "vue";
import { DatetimePicker, Toast, MessageBox } from "mint-ui";
import { Checkbox, SubmitBar, Card, Field, Cell, Dialog } from "vant";
import addModel from "./addModel";
export default {
  name: "OutModal",
  components: {
    [SubmitBar.name]: SubmitBar,
    [Checkbox.name]: Checkbox,
    [Card.name]: Card,
    [Field.name]: Field,
    [Cell.name]: Cell,
    addModel
  },
  data() {
    return {
      title: "操作",
      visible: false,
      ischecked: false,
      orderMainModel: {
        jeecgOrderCustomerList: [{}],
        jeecgOrderTicketList: [{}]
      },
      workshop: "",
      department: "",
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      moIds: [],
      expandedRowKeys: [],
      id: " ",
      item: [],
      depart: [],
      goodsInfo: [],
      record: {},
      tankArr: []
    };
  },
  methods: {
    edit(record) {
      console.log("record", record);
      this.record = record;
      let self = this;
      self.visible = true;
      self.$axios
        .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
          params: {
            lpId: record.id
          }
        })
        .then(res => {
          if ((res.data.code = 200)) {
            self.goodsInfo = res.data.result;
            this.goodsInfo.forEach(v => {
                    v=Object.assign(v,{output:v.realVolume})
                  });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    },
    add() {
      this.$refs.modalForm.edit(this.record);
      this.$refs.modalForm.title = "新增";
      this.$refs.modalForm.disableSubmit = true;
    },
    delTank(item) {
      this.$axios
        .delete(`/jeecg-boot/app/gcWorkshop/delTankTemp?id=${item.id}`)
        .then(res => {
          if ((res.data.code = 200)) {
            this.$axios
              .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
                params: {
                  lpId: this.record.id
                }
              })
              .then(res => {
                if ((res.data.code = 200)) {
                  this.goodsInfo = res.data.result;
                  this.goodsInfo.forEach(v => {
                    v=Object.assign(v,{output:v.realVolume})
                  });
                } else {
                  Toast({
                    message: res.data.message,
                    position: "bottom",
                    duration: 2000
                  });
                }
              });
          }
        });
    },
    close() {
      this.$emit("close");
      this.visible = false;
      this.dataSource = [];
      this.flag = 0;
      this.saveCode = "";
      this.saveStation = "";
    },
    handleOk() {
      this.goodsInfo.forEach(v=>{
        v.qrId=v.id
      })
        console.log('@arr',this.goodsInfo)
        let GcTankUsageInfo=this.goodsInfo
      this.$axios
        .post("/jeecg-boot/app/gcWorkshop/checkTankTemp", 
          GcTankUsageInfo
        )
        .then(res => {
          if ((res.data.code = 200)) {
            console.log(res.data)
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
      this.visible = false;
      this.$emit("ok");
    },
    handleCancel() {
      this.visible = false;
    },
    modalFormOk() {

      this.$axios
        .get("/jeecg-boot/app/gcWorkshop/getTankTemp", {
          params: {
            lpId: this.record.id
          }
        })
        .then(res => {
          if ((res.data.code = 200)) {
            this.goodsInfo = res.data.result;
            this.goodsInfo.forEach(v => {
                    v=Object.assign(v,{output:v.realVolume})
                  });
          } else {
            Toast({
              message: res.data.message,
              position: "bottom",
              duration: 2000
            });
          }
        });
    }
  }
};
</script>

<style scoped>
.ant-btn {
  padding: 0 10px;
  margin-left: 3px;
}

.ant-form-item-control {
  line-height: 0px;
}

/** 主表单行间距 */
.ant-form .ant-form-item {
  margin-bottom: 10px;
}

/** Tab页面行间距 */
.ant-tabs-content .ant-form-item {
  margin-bottom: 0px;
}
.fontColor {
  color: black;
}
</style>
<style lang="less" scoped>
// .van-submit-bar {
//   bottom: 49px;
//   padding-left: 20px;
// }
// .shopContent {
//   margin-top: 10px;
//   padding-bottom: 20px;
// }
// .shopping {
//   text-align: center;
//   padding-top: 99px;
//   img {
//     width: 96px;
//     height: 96px;
//     margin-bottom: 25px;
//   }
// }
// li {
//   padding: 0 15px;
//   background: #ffffff;
//   margin-bottom: 10px;
//   position: relative;
//   height: 103px;
//   .shopmain {
//     display: flex;
//     padding: 10px 8px 10px 10px;
//     position: relative;
//     .shops {
//       display: flex;
//       margin-left: 5%;
//       .shopImg {
//         width: 103px;
//         height: 83px;
//         margin: 0 7px 0 11px;
//         img {
//           width: 100%;
//           height: 100%;
//         }
//       }
//       .shopsright {
//         width: 100%;
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//         h4 {
//           display: -webkit-box;
//           -webkit-box-orient: vertical;
//           -webkit-line-clamp: 2;
//           overflow: hidden;
//           text-align: left;
//         }
//         .shoprightbot {
//           display: flex;
//           justify-content: space-between;
//           align-items: center;
//           width: 190px;
//           span {
//             font-size: 17px;
//             color: #f74022;
//           }
//         }
//       }
//     }
//     .van-checkbox__icon--checked .van-icon {
//       background: red !important;
//     }
//   }

//   input {
//     width: 48px;
//   }
// }
// .shopradd {
//   width: 98px;
//   display: flex;
//   .van-field__control {
//     text-align: center !important;
//   }
// }
// .van-cell {
//   padding: 0;
//   line-height: 26px;
// }
// .van-field__control {
//   height: 26px;
// }
// /deep/.ant-modal-body {
//   padding: 0px;
// }
</style>
