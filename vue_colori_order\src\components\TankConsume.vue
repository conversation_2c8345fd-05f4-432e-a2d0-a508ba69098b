<template>
    <div>
        <van-sticky>
            <van-nav-bar title="胶体消耗" left-text="返回" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
            <!-- 按钮区 -->

        </van-sticky>

        <van-row style="background-color:#fff;position: fixed;bottom: 0;right: 0;z-index: 99;width: 100%;height: 5%;"
            gutter="30">
            <van-col span="4">
                <van-button :plain="plain" icon="success" type="info" round size="mini" @click="toggleAll">
                </van-button>
            </van-col>
            <van-col span="3"></van-col>
            <van-col span="8">
                <van-button round style="height:30px" type="info" size="large" @click="consumePart">部分消耗
                </van-button>
            </van-col>
            <van-col span="8">
                <van-button round style="height:30px" type="info" size="large" @click="consumeAll" :loading="loading"
                    loading-type="spinner">全部消耗</van-button>
            </van-col>
        </van-row>


        <!-- 弹出层 -->
        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">

            <!-- 搜索条件 -->
            <van-field v-model="product" label="产品名称" placeholder="请输入坦克名称" />
            <van-field v-model="tank" label="坦克名称" placeholder="请输入坦克名称" />
            <van-field v-model="customerId" label="客户批次号" placeholder="请输入客户批次号" />
            <van-button type="info" @click="search1" style="width: 100%;" round>
                搜索
            </van-button>
        </van-popup>


        <!-- 部分消耗 -->
        <van-popup v-model="show1" style="width: 80%;">
            <van-field v-model="output" type="number" label="消耗量" placeholder="请输入消耗量" />
            <van-button type="info" @click="consume" style="width: 95%;margin: 10% 0 10% 0" :loading="loading1"
                loading-type="spinner">
                确定
            </van-button>
        </van-popup>
        <!-- 列表 -->
        <van-list offset="20" v-model="load" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <van-checkbox-group v-model="result" ref="checkboxGroup">
                <van-cell-group>
                    <van-cell v-for="(item, index) in tankArr" clickable :key="index" @click="toggle(index)">
                        <template #default>
                            <div style="text-align: left;margin-left:5% ;">

                                <van-row gutter="20">
                                    <van-col span="8" style="overflow-x: auto;white-space:nowrap;">{{ item.tankName }}
                                    </van-col>
                                    <van-col span="8" style="overflow-x: auto;white-space:nowrap;">{{ item.tankNo }}
                                    </van-col>
                                    <van-col span="8" style="overflow-x: auto;white-space:nowrap;">
                                        存量:{{ (item.tankVolume * 1000000000 - item.realVolume * 1000000000) / 1000000000 }}
                                    </van-col>
                                </van-row>
                                <van-row gutter="20">
                                    <van-col span="8" style="overflow-x: auto;white-space:nowrap;">{{ item.realProductNo
                                    }}
                                    </van-col>
                                    <van-col span="8" style="overflow-x: auto;white-space:nowrap;">{{ item.realCode }}
                                    </van-col>
                                </van-row>
                                <van-row>
                                    <van-col span="24" style="overflow-x: auto;white-space:nowrap;">{{
                                            item.realProductName
                                    }}</van-col>
                                </van-row>
                            </div>
                        </template>
                        <template #icon>
                            <van-checkbox :name="item" ref="checkboxes" />
                        </template>

                    </van-cell>
                </van-cell-group>
            </van-checkbox-group>
        </van-list>
        <div style="height:10%;width: 100%;">&nbsp;</div>

    </div>
</template>

<script>
import { DatetimePicker, Toast } from 'mint-ui';
import { Dialog } from 'vant';
export default {
    data() {
        return {
            result: [],
            tankArr: [],
            show: false,
            show1: false,
            product: '',
            tank: '',
            customerId: '',
            output: 0,
            loading: false,
            loading1: false,

            // 全选按钮
            plain: true,
            finished: false,
            load: false,
            pageNo: 1,
            total: 0,
        }
    },
    methods: {
        onLoad() {
            this.search(); // 调用上面方法,请求数据
        },
        onClickLeft() {
            this.$router.go(-1)
        },
        onClickRight() {
            this.show = true
            this.pageNo = 1
        },
        search() {
            this.$axios.get(`/jeecg-boot/app/gcWorkshop/getPackedTank?userCode=${localStorage.getItem('userCode')}&product=${this.product}&customerId=${this.customerId}&tank=${this.tank}&pageNo=${this.pageNo}`).then(res => {
                if (res.data.code == 200) {
                    this.finished = false
                    let len = res.data.result.records.length;
                    if (len == 0) {
                        this.tankArr = []; // 清空数组
                        this.finished = true; // 停止加载
                    }
                    this.total = res.data.result.total;  //总数
                    this.tankArr.push(...res.data.result.records);
                    this.load = false;
                    this.pageNo++; // 分页数加一
                    if (this.tankArr.length >= res.data.result.total) {
                        this.finished = true; // 结束加载状态
                    }
                }
            })
            this.show = false
        },
        // 筛选
        search1() {
            this.result = []
            this.pageNo = 1
            this.tankArr = []
            this.$axios.get(`/jeecg-boot/app/gcWorkshop/getPackedTank?userCode=${localStorage.getItem('userCode')}&product=${this.product}&customerId=${this.customerId}&tank=${this.tank}&pageNo=${this.pageNo}`).then(res => {
                if (res.data.code == 200) {
                    this.finished = false
                    let len = res.data.result.records.length;
                    if (len == 0) {
                        this.tankArr = []; // 清空数组
                        this.finished = true; // 停止加载
                    }
                    this.total = res.data.result.total;  //总数
                    this.tankArr.push(...res.data.result.records);
                    this.load = false;
                    this.pageNo++; // 分页数加一
                    console.log(this.tankArr.length, res.data.result.total);
                    if (this.tankArr.length >= res.data.result.total) {
                        this.finished = true; // 结束加载状态
                    }
                }
            })
            this.show = false
        },
        checkAll() {
            this.$refs.checkboxGroup.toggleAll(true);
        },
        toggleAll() {
            if (this.result.length != this.tankArr.length) {
                this.plain = false
                this.$refs.checkboxGroup.toggleAll(true);
            } else {
                this.plain = true
                this.$refs.checkboxGroup.toggleAll();
            }
        },
        toggle(index) {
            this.$refs.checkboxes[index].toggle();
        },
        // 全部消耗
        consumeAll() {
            if (this.result.length == 0) {
                Toast({
                    message: `请至少选择一个`,
                    position: 'bottom',
                    duration: 1000
                });
            } else {
                Dialog.confirm({
                    message: '确定要消耗胶体吗?',
                })
                    .then(() => {
                        this.loading = true
                        let ids = ''
                        this.result.forEach(item => {
                            ids += item.id + ','
                        })
                        const output = 999999999
                        let param = {
                            tankId: ids,
                            output: output,
                            type: '2',
                            creater: localStorage.getItem('userCode')
                        }
                        console.log(param);
                        this.$axios.post(`/jeecg-boot/app/gcWorkshop/reset`,
                            param
                        ).then(res => {
                            if (res.data.code == 200) {
                                ids = ''
                                Toast({
                                    message: `操作成功`,
                                    position: 'bottom',
                                    duration: 1000
                                });
                                this.loading = false
                            }
                        })
                    })
                    .catch(() => {
                        Toast({
                            message: `您点击了取消`,
                            position: 'bottom',
                            duration: 1000
                        });
                    });

            }
        },
        // 部分消耗
        consumePart() {
            if (this.result.length == 0) {
                Toast({
                    message: `请至少选择一个`,
                    position: 'bottom',
                    duration: 1000
                });
            } else {
                this.show1 = true
            }
        },
        // 部分消耗发送请求
        consume() {
            Dialog.confirm({
                message: '确定要消耗胶体吗?',
            })
                .then(() => {
                    this.loading1 = true
                    let ids = ''
                    this.result.forEach(item => {
                        ids += item.id + ','
                    })
                    let param = {
                        tankId: ids,
                        output: this.output,
                        type: '2',
                        creater: localStorage.getItem('userCode')
                    }
                    console.log(param);
                    this.$axios.post(`/jeecg-boot/app/gcWorkshop/reset`,
                        param
                    ).then(res => {
                        if (res.data.code == 200) {
                            this.loading1 = false
                            this.show1 = false
                            ids = ''
                            this.output = 0
                            Toast({
                                message: `消耗了${this.output}KG胶体`,
                                position: 'bottom',
                                duration: 1000
                            });
                        }
                    })
                })
                .catch(() => {
                    Toast({
                        message: `您点击了取消`,
                        position: 'bottom',
                        duration: 1000
                    });
                });
        }
    },
}
</script>

<style lang="less" scoped>
</style>