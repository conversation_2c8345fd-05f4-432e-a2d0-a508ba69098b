<template>
    <div style="text-align:left;">
        <van-nav-bar v-show="active == 0 && status != 2" title="工单详情" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 1 && status != 2" title="工单详情" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 0 && status == 2" title="工单详情" left-arrow @click-left="onClickLeft" />
        <van-nav-bar v-show="active == 1 && status == 2" title="维修记录" left-arrow @click-left="onClickLeft" />


        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">
            <!-- 是领导 填写-->
            <!-- <div v-show="isLeader == true">
                <van-field v-model="acceptNo" label="人员编号" placeholder="请输入人员编号" right-icon="search"
                    @click-right-icon='search' />
                <van-field v-model="acceptName" label="人员姓名" readonly />
            </div> -->
            <!-- 不是领导 选择领导-->


            <!-- 修改之后 --都是选择 领导选择员工列表,员工选择领导列表 -->
            <van-field readonly clickable :value="acceptName" label="接单人" placeholder="点击选择"
                @click="showPicker = true" />
            <van-popup v-model="showPicker" position="bottom">
                <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
            </van-popup>
            <van-field v-model="reason" label="转单原因：" placeholder="请输入转单原因" />

            <van-button type="info" @click="giveOut" style="width: 100%;" round>
                转单
            </van-button>
        </van-popup>
        <div v-if="status == 1">
            <van-cell-group>
                <van-cell title="维修工单号" :value="info.id" />
                <van-cell title="线体" :value="info.jobCenter" />
                <van-cell title="MO单号" :value="info.moId" />
                <van-cell title="设备名称" :value="info.machine" />
                <van-cell title="等待时长" :value="info.waitMinutes==null?'':info.repairMinutes+'分钟'" />
                <van-cell title="维修时长" :value="info.repairMinutes==null?'':info.repairMinutes+'分钟'" />
                <van-cell title="组长" :value="info.creator" />
                <van-cell title="在离线" :value="info.lineType" />
                <van-cell title="故障描述" :label="info.describe" value="" />
                <van-uploader v-if="info.pictureList.length > 0" v-model="imgList" disabled :deletable='false'
                    :show-upload='false' />
            </van-cell-group>
            <div style="margin: 16px;">
                <!-- <van-button v-if="status == 1" round block type="info" @click="accept">接单</van-button>
                <van-button v-if="status == 2" round block type="info" @click="over">关单</van-button> -->
            </div>
        </div>
        <div v-else>
            <van-tabs v-model="active" color="#1989fa" sticky>
                <van-tab title="工单详情">
                    <van-cell-group>
                        <van-cell title="维修工单号" :value="info.id" />
                        <van-cell title="线体" :value="info.jobCenter" />
                        <van-cell title="MO单号" :value="info.moId" />
                        <van-cell title="设备名称" :value="info.machine" />
                        <van-cell title="等待时长" :value="info.waitMinutes" />
                        <van-cell title="维修时长" :value="info.repairMinutes" />
                        <van-cell title="组长" :value="info.creator" />
                        <van-cell title="在离线" :value="info.lineType" />
                        <van-cell title="接单人" :value="info.leader" />
                        <van-cell title="故障描述" :label="info.describe" value="" />
                        <van-uploader v-if="info.pictureList.length > 0" v-model="imgList" disabled :deletable='false'
                            :show-upload='false' />
                    </van-cell-group>
                    <div style="margin: 16px;">
                        <!-- <van-button v-if="status == 1" round block type="info" @click="accept">接单</van-button>
                        <van-button v-if="status == 2" round block type="info" @click="over">关单</van-button> -->
                    </div>
                </van-tab>
                <van-tab title="维修记录">
                    <div style="padding:3%;">
                        <div v-for="(item, index) in info.detailList" :key="index" @click="goDetail(item)"
                            style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 3%;">
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">是否调试：{{ item.isDebug }}</p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">是否更换配件：{{ item.isReplace }}
                                </p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">是否人为：{{ item.isArtificial }}
                                </p>
                            </div>
                            <div style="display: flex;">
                                <p style="width:60%;overflow-x: auto;white-space:nowrap;">维修情况：{{ item.maintenance }}
                                </p>
                            </div>
                        </div>
                    </div>
                </van-tab>

            </van-tabs>
        </div>
    </div>
</template>

<script>
import { Toast, Dialog } from "vant";
export default {
    data() {
        return {
            active: 0,
            // 0 已完成 1未接单 2已接单
            status: '',
            // 工单信息
            info: {},
            // 图片
            imgList: [],
            // 转单 弹框显隐
            show: false,
            // 转单 判断是否是领导
            isLeader: false,
            // 转单原因
            reason: '',
            // 领导列表
            leaderList: [],
            // 接单人 姓名
            acceptName: '',
            // 接单人 工号
            acceptNo: '',
            // 领导名字列表
            columns: [],
            // 选择领导弹框显隐
            showPicker: false,
        }
    },
    created() {
        this.status = this.$route.params.status
        if (this.$route.params.active) {
            this.active = this.$route.params.active
        }
        // this.info = this.$route.params.item
        this.info = JSON.parse(localStorage.getItem("repair"))
        if (this.status == '2') {
            this.$axios
                .get(
                    `/jeecg-boot/app/mac/getAppRecordList?userCode=${localStorage.getItem('userCode')}&status=${this.status}&id=${this.$route.params.item.id}`
                )
                .then(res => {
                    if (res.data.code == 200) {
                        this.info = res.data.result[0]
                    } else {
                        Toast({
                            message: '失败',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        }
        this.info.pictureList.forEach(item => {
            this.imgList.push({ url: item.picUrl })
        })
        // 判断登录人是否是领导
        this.$axios
            .get(`/jeecg-boot/app/mac/getIsLeader?userCode=${localStorage.getItem('userCode')}`)
            .then(res => {
                if (res.data.code == 200) {
                    // 重置columns
                    this.columns = []
                    if (res.data.message == 0) {
                        this.isLeader = false
                        // 不是领导 获取领导列表
                        this.$axios
                            .get(`/jeecg-boot/app/mac/getLeaderList?userCode=${localStorage.getItem('userCode')}`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.leaderList = res.data.result
                                    this.leaderList.forEach(item => {
                                        this.columns.push(item.lead)
                                    })
                                } else {
                                    Toast({
                                        message: '查询失败',
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    } else {
                        this.isLeader = true
                        // 是领导 获取员工列表
                        this.$axios
                            .get(`/jeecg-boot/app/mac/getWorkerList?userCode=${localStorage.getItem('userCode')}`)
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.leaderList = res.data.result
                                    this.leaderList.forEach(item => {
                                        this.columns.push(item.lead)
                                    })
                                } else {
                                    Toast({
                                        message: '查询失败',
                                        position: "bottom",
                                        duration: 2000
                                    });
                                }
                            });
                    }
                } else {
                    Toast({
                        message: '查询失败',
                        position: "bottom",
                        duration: 2000
                    });
                }
            });


    },
    methods: {
        // 关单时 输入userCode 搜寻人员名称
        search() {
            this.$axios
                .get(`/jeecg-boot/app/utils/getStaffNameByCode?userCode=${this.acceptNo}`)
                .then(res => {
                    if (res.data.message != null) {
                        console.log(res.data.message);
                        this.acceptName = res.data.message
                    } else {
                        Toast({
                            message: '人员编号错误,请重新输入',
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        // 转单 弹框显示
        giveOutOrder() {
            if (this.info.changeStatus == 1) {
                this.show = true;
            } else {
                Toast({
                    message: '已达转单次数上限,不能转单',
                    position: "bottom",
                    duration: 2000
                });
            }
        },
        // 转单 提交
        giveOut() {
            if (this.acceptNo == null && this.acceptName == null && this.reason == null) {
                this.$axios
                    .get(`/jeecg-boot/app/mac/giveOutOrder?id=${this.info.id}&leader=${this.info.leader}&leaderNo=${this.info.leaderNo}&acceptNo=${this.acceptNo}&acceptName=${this.acceptName}&reason=${this.reason}`)
                    .then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: '转单成功',
                                position: "bottom",
                                duration: 2000
                            });
                            this.show = false;
                        } else {
                            Toast({
                                message: '转单失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            } else {
                Toast({
                    message: '请选择转单人,填写转单原因',
                    position: "bottom",
                    duration: 2000
                });
            }

        },
        //接单
        accept() {
            Dialog.confirm({
                message: '确定接单吗?',
                theme: 'round-button',
                confirmButtonColor: '#1989fa',
                cancelButtonColor: '#CCCCCC',
            }).then(() => {
                this.$axios
                    .get(
                        `/jeecg-boot/app/mac/changeOrders?id=${this.info.id}&status=2&leader=${localStorage.getItem('userName')}&leaderNo=${localStorage.getItem('userCode')}`
                    )
                    .then(res => {
                        if (res.data.code == 200) {
                            Toast({
                                message: '接单成功',
                                position: "bottom",
                                duration: 2000
                            });
                            this.$router.push({
                                name: "RepairOrder",
                            });
                        } else {
                            Toast({
                                message: '接单失败',
                                position: "bottom",
                                duration: 2000
                            });
                        }
                    });
            }).catch(() => {
                Toast({
                    message: '取消',
                    position: "bottom",
                    duration: 1000
                });
            });

        },
        // 关单
        over() {
            let flag = false
            if (this.info.detailList) {
                this.info.detailList.forEach(item => {
                    if (item.maintenance == '已修好') {
                        flag = true
                    }
                });
            }
            if (flag) {
                Dialog.confirm({
                    message: '确定关单吗?',
                    theme: 'round-button',
                    confirmButtonColor: '#1989fa',
                    cancelButtonColor: '#CCCCCC',
                }).then(() => {
                    this.$axios
                        .get(
                            `/jeecg-boot/app/mac/changeOrders?id=${this.info.id}&status=0`
                        )
                        .then(res => {
                            if (res.data.code == 200) {
                                Toast({
                                    message: '关单成功',
                                    position: "bottom",
                                    duration: 2000
                                });
                                this.$router.push({
                                    name: "RepairOrder",
                                });
                            } else {
                                Toast({
                                    message: '关单失败',
                                    position: "bottom",
                                    duration: 2000
                                });
                            }
                        });
                }).catch(() => {
                    Toast({
                        message: '取消',
                        position: "bottom",
                        duration: 1500
                    });
                });
            } else {
                Toast({
                    message: '设备未修好，不能关单',
                    position: "bottom",
                    duration: 1500
                });
            }
        },
        //维修记录 新增
        onClickRight() {
            let info = this.info
            this.$router.push({
                name: "RepairAdd",
                params: { info }
            });
        },
        onClickLeft() {
            this.$router.push({
                name: "RepairOrder",
            });
        },
        // 维修记录 详情
        goDetail(item) {
            this.$router.push({
                name: "RepairDetail2See",
                params: { item, info: this.info, status: this.status }
            });
        },
        // 选择领导确认方法
        onConfirm(value) {
            this.acceptName = value;
            this.showPicker = false;
            this.leaderList.forEach(item => {
                if (this.acceptName == item.lead) {
                    this.acceptNo = item.leaderNo
                }
            })
            console.log(this.acceptName, this.acceptNo);
        },
    },
}
</script>

<style scoped>
</style>