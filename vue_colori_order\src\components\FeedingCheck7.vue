<template>
  <div style="text-align:left;background-color:#fff;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="查看标签" left-arrow @click-left="onClickLeft" />
    </van-sticky>

    
    <div v-for="item in data" :key="item.code">
      <van-cell :title="item.name" :value="item.code" is-link @click="click(item)" />
    </div>
  </div>
</template>

<script>
  import { Toast } from "vant";
  import { Indicator } from "mint-ui";
  export default {

    data() {
      return {
        data: [
          {
            name: '去离子水/Water Purified(10000001)',
            code: '1430001',
            batchCode: '',
          },
          {
            name: '去离子水/Water Purified(13003292)',
            code: '1430058',
            batchCode: '',
          },
          {
            name: '去离子水',
            code: '23600000003',
            batchCode: '',
          },
        ],
        currentDate:new Date()
      };
    },

    methods: {
      onClickLeft() {
        this.$router.go(-1);
      },
      click(item) {
        // 获取当前日期
        const currentDate = new Date();
        // 获取年、月、日
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要加1，并且确保两位数格式
        const day = String(currentDate.getDate()).padStart(2, '0'); // 确保两位数格式
        // 拼接成 YYYYMMDD 格式
        const formattedDate = `${year}${month}${day}`;
        console.log(formattedDate);
        item.batchCode = formattedDate
        this.$router.push({ path: '/ScanInCheck', query: item });
      },
    }
  };
</script>

<style scoped></style>