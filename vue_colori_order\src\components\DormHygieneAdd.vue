<template>
<div class="pageHygieneAdd">
    <van-nav-bar fixed
        title="宿舍卫生"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
        />
    <van-form @submit="hygieneSubmit">
        <van-field readonly right-icon="arrow" input-align="right"
                label="宿舍" name="宿舍"
                v-model="rForm.ssNameList"
                placeholder="宿舍"
                @focus="onSelectSS"
                :rules="[{ required: true, message: '必选' }]"
            />
        <van-field input-align="right" label="分数" name="分数"
            type="digit" maxlength="3"
            placeholder="0-100分"
            v-model="rForm.score"
            :rules="[{ required: true, message: '必填',validator: scoreValidator }]"
            />
        <van-field label="照片" name="照片" input-align="right" readonly :rules="[{ required: true, message: '至少上传一张' }]">
            <template #input>
                <van-uploader v-model="rForm.fileList"
                    :max-count="1"
                    accept="image/*"
                    :disabled="!rForm.ssNameList || rForm.ssNameList=='' "
                    :after-read="afterReadFiles"
                    @click-preview="onClickPreview" />
            </template>
        </van-field>
        <div style="margin: 16px;">
            <van-button round block type="info" native-type="submit" :loading="confirmLoading" :disabled="confirmLoading">提交</van-button>
        </div>
    </van-form>

<!-- 区域、楼栋、房间Picker -->
<van-popup v-model="showSSPicker" position="bottom" :style="{ height: '45%' }">
    <van-cascader
        v-model="cascaderSelected"
        title="请选择宿舍"
        :options="ssOptions"
        @close="onCascaderClose"
        @change="onCascaderChange"
        @finish="onCascaderFinish" />
</van-popup>
</div>
</template>
<script>
import {Dialog,Toast,ImagePreview, Form} from 'vant'
export default {
    data(){
        return{
            rForm: {
                ssNameList: '', 
                ssIdList: '',
                fileList: [], //上传文件列表
            },
            cascaderSelected: '',
            showSSPicker: false,
            ssOptions: [],
            confirmLoading: false,
        }
    },
    methods:{
        hygieneSubmit(){
            this.confirmLoading=false
            const mFormData=JSON.parse(JSON.stringify(this.rForm))
            console.log(mFormData)
            if(mFormData.fileList.length>0){
                for(let imgFile of mFormData.fileList){
                    if(imgFile.status!=""){
                        Toast.fail("上传完成后再提交")
                        return false
                    }
                }
            }else{
                Toast.fail("请上传图片")
                return false
            }
            const [mAreaId, mBuildingId, mRoomId]=mFormData.ssIdList.split("/")
            let params={
                areaId: mAreaId,
                buildingId: mBuildingId,
                roomNo: mRoomId,
                score: mFormData.score,
                pictureUrl: mFormData.fileList.map(item=>item.url).join(","),
                unionCommunity: "0",
                userCodeRequest:localStorage.getItem('userCode'),
            }
            console.log("/dormApi/dm/dmDailyCsInfo/app/healthRecordAdd",params)
            this.$axios.post("/dormApi/dm/dmDailyCsInfo/app/healthRecordAdd",params).then(rtn => {
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        Toast.success(res.message)
                        this.$router.push("/hygieneManage")
                    }else{
                        Toast(res.message)
                        console.log("ERROR", res)
                    }
                }else{
                    Toast("发生错误")
                    console.log("ERROR", rtn)
                }
            }).finally(()=>{
                this.confirmLoading=true
            })
            // this.$axios

        },
        onCascaderFinish({value, selectedOptions, tabIndex}){
            console.log(value, selectedOptions, tabIndex)
            if(tabIndex===2){
                this.rForm.ssIdList=selectedOptions.map(item=>item.value).join("/")
                this.rForm.ssNameList=selectedOptions.map(item=>item.text).join("/")
                this.showSSPicker=false
            }
        },
        onCascaderChange({ value, selectedOptions, tabIndex }){
            if(value && tabIndex===0){ // 选择区域->查询楼栋
                let params={ areaId: value,userCodeRequest:localStorage.getItem('userCode') }
                this.$axios.get("/dormApi/dormitory/app/getBuildingListAll",{params: params}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            selectedOptions[0].children=res.result.map(item=>{ return { text:item.buildingName, value: item.id, children: [] } })
                        }else{
                            Toast.fail(res.message)
                        }
                    }else{
                        Toast.fail("发生错误")
                    }
                })
            }
            if(value && tabIndex===1){// 选择楼栋->查询房间
                let params={ buildingId: value,userCodeRequest:localStorage.getItem('userCode') }
                this.$axios.get("/dormApi/dormitory/app/getRoomInfoListAll",{params: params}).then(rtn=>{
                    if(rtn.status===200){
                        const res=rtn.data
                        if(res.success){
                            selectedOptions[1].children=res.result.map(item=>{ return { text:item.roomNumber, value: item.id } })
                        }else{
                            Toast.fail(res.message)
                        }
                    }else{
                        Toast.fail("发生错误")
                    }
                })
            }
        },
        onCascaderClose(){
            this.cascaderSelected='',
            this.rForm.ssIdList='',
            this.rForm.ssNameList='',
            this.showSSPicker=false
        },
        onSelectSS(){
            this.showSSPicker=true
        },
        async afterReadFiles(file){
            console.log(file)
            const selectedSS=this.rForm.ssNameList
            let rParams=new FormData();
            if(file.constructor != Array){
                if(file.file.type.indexOf('image')==-1){
                    Toast.fail('请上传图片文件')
                    file.status='failed'
                    file.message='上传失败'
                    return
                }
                if(!selectedSS || selectedSS==""){
                    Toast.fail('请先选择宿舍')
                    file.status='failed'
                    file.message='上传失败'
                    return false
                }else{
                    const mResult = await this.getImageWaterMark(file.content)
                    if(mResult){
                        file.file=new File([mResult], file.file.name, { type: mResult.type })
                    }else{
                        Toast.fail("水印失败")
                        console.error("ERROR", mResult)
                        file.status='failed'
                        file.message='上传失败'
                        return false
                    }
                }
                file.status='uploading'
                file.message='点击可取消'
                rParams.append("file",file.file)
            }
            this.$axios.post('/dormApi/sys/common/upload', rParams).then(rtn => {
                console.log("upload Result", rtn)
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.message){
                        if(file.constructor != Array){
                            file.status=''
                            file.message=''
                            file.url=res.message
                        }
                    }else{
                        Toast({
                            message: res.msg.msg,
                            position: 'bottom',
                            duration: 2000
                        });
                        console.error("ERROR-2",res)
                    }
                }else{
                    Toast({
                        message: "发生错误",
                        position: 'bottom',
                        duration: 2000
                    });
                    console.error("ERROR-1",rtn)
                }
            })
        },
        onClickPreview(file){// 点击预览
            console.log(file)
            if(file.status==='uploading'){
                Dialog.confirm({
                title: '提示',
                message: '确认取消上传？',
                }).then(()=>{
                    file.status='failed'
                    file.message='取消上传'
                })
            }else if(file.status===''){
                if(file.file.type.indexOf('image')!=-1){
                    ImagePreview({
                        images: [`/dormApi/${file.url}`],
                        closeable: true
                    })
                }
            }
        },
        scoreValidator(val){
            if(val*1>100){
                Toast.fail("分数不可>100")
                return false
            }else{
                return true
            }
        },
        onClickLeft(){
            this.$router.go(-1)
        },
        getAreaList(){
            let params = {userCodeRequest:localStorage.getItem('userCode')};
            this.$axios.get("/dormApi/dormitory/app/getAreaListAll",params).then(rtn=>{
                if(rtn.status===200){
                    const res=rtn.data
                    if(res.success){
                        this.ssOptions=res.result.map(item=>{
                            return {
                                text: item.areaName,
                                value: item.id,
                                children: [],
                            }
                        })
                    }else{
                        Toast.fail(res.message)
                    }
                }else{
                    Toast.fail("发生错误")
                }
            })
        },
        getImageWaterMark(base64){// 图片加宿舍房间水印
            const selectedSS=this.rForm.ssNameList.split("/").join("-")
            return new Promise(resolve=>{
                const mImg=new Image()
                mImg.setAttribute("crossOrigin",'Anonymous')
                mImg.src=base64
                mImg.onload=()=>{
                    const canvas = document.createElement('canvas');
                    canvas.width = mImg.naturalWidth;
                    canvas.height = mImg.naturalHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(mImg, 0, 0);
                    ctx.fillStyle = 'red';
                    ctx.textBaseline = 'middle';
                    const remFontSize = canvas.width / 35;
                    ctx.font = `${remFontSize}px Arial`;
                    const wmText = selectedSS;
                    ctx.fillText(wmText, 50, 50);
                    canvas.toBlob(resolve);
                }
            })
        }
    },
    created(){ 
        this.getAreaList()
    },
}
</script>
<style scoped>
.pageHygieneAdd{
    background: #F1F1F1;
    padding-top: 50px;
}
.vanCellClass{
    color: #646566;
    text-align: left;
}
</style>