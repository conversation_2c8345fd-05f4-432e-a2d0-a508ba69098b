<template>
    <div>
        <van-cell-group style="text-align:center;">
            <div style="margin:10px;text-align:left">托码数据</div>
            <van-field label="托码编号" :value="detailInfoList.id" readonly/>
            <van-field label="客户序列号" :value="detailInfoList.customer" readonly />
            <van-field label="生产日期" :value="detailInfoList.workDay" readonly />
            <van-field label="总箱数" :value="allQuantity" readonly />
            <van-field label="当前状态" :value="status" readonly />
        </van-cell-group>

        
        <div v-for="(item,index) in detailInfoList.detailInfoList" :key="index" style="margin-top:15px;">
            <div style="margin-top:15px;margin-left:10px;margin-bottom:15px;text-align:left">产品数据{{index+1}}</div>
            <van-field label="MO编号" :value="item.moId" readonly/>
            <van-field label="产品编号" :value="item.code" readonly/>
            <van-field label="产品名称" :value="item.name" type="textarea" rows="2" autosize readonly/>
            <van-field label="线体" :value="item.mitosome" readonly/>
            <van-field label="组长" :value="item.leaderNo" readonly/>
            <van-field label="箱数" :value="item.quantity" readonly/>
            <van-field label="最小单位数量" :value="item.mainQuantity" readonly/>
        </div>

        <van-button type="danger" v-show="show" @click="checkCodeInfo('0')" style="margin:10px;width:80%;border-radius:10px;">设为不合格</van-button>
        <van-button type="primary" v-show="pass_show" @click="checkCodeInfo('1')" style="margin:10px;width:80%;border-radius:10px;">设为放行</van-button>
    </div>
</template>

<script>
import { DatetimePicker,Toast,Indicator,MessageBox } from 'mint-ui';
export default ({
    data() {
        return{
            itemParams:{},
            detailInfoList:{},
            allQuantity:0,
            show:true,
            status:'放行',
            pass_show:false,
            workTitle:'设为不合格'
        }
        
    },
    created:function(){
        let self=this;
        
        if(self.$route.params.item!=null){
            self.detailInfoList=JSON.parse(self.$route.params.item);
            self.allQuantity=0
            if(self.detailInfoList.status=='1'){
                self.show=true;
                self.pass_show=false;
                self.status="放行"
            }else{
                self.show=false;
                self.pass_show=true;
                self.status="不合格"
            }
            for(var i=0;i<self.detailInfoList.detailInfoList.length;i++){
                self.allQuantity=parseInt(self.detailInfoList.detailInfoList[i].quantity)+self.allQuantity
            }
        }

    },
    methods: {
        selectMoId(){
            this.$router.push({name:"SearchMoId"})
        },
        checkCodeInfo(num){
            let self=this;
            let msg="";
            if(num=='0'){
                msg="确认设此托码为不合格状态？";
            }else{
                msg="确认设此托码为放行状态？";
            }
             MessageBox.confirm(msg).then(action => {
                if(action=="confirm"){
                    Indicator.open({
                        text: '处理中，请稍后……',
                        spinnerType: 'fading-circle'
                    });
                    self.$axios.get('/jeecg-boot/app/appQuality/changeCodeStatus',{params:{id:self.detailInfoList.id,userCode:localStorage.getItem('userCode')}}).then(res=>{
                        if(res.data.code==200){
                            Indicator.close();
                            self.$router.go(-1);
                        }else{
                            Indicator.close();
                            Toast({
                                message: res.data.message,
                                position: 'bottom',
                                duration: 2000
                            });
                        }
                    })
                }
             });


            
        }
    }
})
</script>


<style scoped>
.order{
    background-color: #ebecf7;
    min-height: 100%;
}
.top_order_title{
    font-size: 1.6rem;
    font-weight: 600;
    padding: 2rem;
    float: left;
}
.top_msg{
    float: right;
}
.items_d{
    padding: 5%;
    height: 6rem;
}
.item_bg{
    background-image: url('../../static/images/item_bg.png');
    width: 68%;
    height: 6rem;
    text-align: left;
    float: left;
}
.item_add{
    background-image: url('../../static/images/item_add.png');
    background-repeat: no-repeat;
    width: 32%;
    float: left;
    height: 6rem;
}
.itemTitle{
    padding: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sign{
    text-align: center;
}
.plotName{
    position: absolute;
    top: 14%;
    left: 10%;
    color: #fff;
    font-size: 1.6rem;
}
.plotCode{
    position: absolute;
    top: 19%;
    left: 10%;
    color: #fff;
    font-size: 1rem;
}
.plotCard{
    position: absolute;
    top: 16%;
    right: 8%;
    color: #fff;
}
.addControl{
    position: absolute;
    top: 33%;
    right: 8%;
    color: #fff;
}
.plotFactory{
    position: absolute;
    top: 30%;
    left: 10%;
    color: #fff;
}
.plotWorkshop{
    position: absolute;
    top: 34%;
    left: 10%;
    color: #fff;
}
.plotMitosome{
    position: absolute;
    top: 34%;
    left: 35%;
    color: #fff;
}
.plotTime{
    background: url('../../static/images/search_time.png');
    width: 80%;
    height: 2.5rem;
    margin-top: 1rem;
    margin-left: 5%;
    text-align: left;
    padding-left: 10%;
    padding-top: 1rem;
    font-size: 1.2rem;
}
.orderType{
    background: url('../../static/images/type_bg.png');
    background-size: 100% 100%;
    width: 22%;
    padding-left: 10%;
    height: 2.5rem;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 1rem;
    margin-left: 5%;
    display: flex;
    align-items: center;
    text-align: center;
}
#peopleChorseT{
  position: absolute;
  width: 100%;
  top:1.17rem;
  height: 0.6rem;
}
/**问题类型弹框样式 */
.picker-toolbar-title {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #eee;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
}
.usi-btn-cancel,.usi-btn-sure{
    color:#26a2ff;
    font-size: 16px;
}
.popup-div{
    width: 100%;
}
.pro-report{
    background: url('../../static/images/clbb.png');
    background-size: 100% 100%;
    height: 3.5rem;
    font-size: 1.4rem;
    margin-left: 5%;
    width: 80%;
    color: #fff;
    display: flex;
    padding-left: 10%;
    justify-content: left;
    align-items: center;
}
.sc_date{
    background: url('../../static/images/date_bg.png');
    background-size: 100% 100%;
    margin-left:15%;
    margin-top: 5%;
    margin-bottom: 5%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width:64%;
    border-radius:10px;
}
.rq_date{
    background: url('../../static/images/rq_bg.png');
    background-size: 100% 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22%;
    color: #fff;
    float: left;
}
.date_work{
    height: 50%;
    width: 68%;
    color: #888;
    float: left;
}
.right_jt{
    background: url('../../static/images/right_jt.png');
    background-size: 100% 100%;
    float: left;
    width: 6%;
    height: 60%;
}
.pool{
    margin-left: 5%;
    height: 3.5rem;
    margin-bottom:5%;
    font-size: 1rem;
    width:90%;
}
.zbPool{
    background: url('../../static/images/zbPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.ybPool{
    background: url('../../static/images/ybPool.png');
    background-size: 100% 100%;
    float: left;
    width: 23%;
    height: 100%;
}
.mid{
    width: 54%;
    height: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
}
.midPool{
    background: url('../../static/images/midPool.png');
    background-size: 100% 100%;
    width: 35%;
    height: 100%;
}
/deep/ .van-field__label{
    width: 10em;
}

</style>