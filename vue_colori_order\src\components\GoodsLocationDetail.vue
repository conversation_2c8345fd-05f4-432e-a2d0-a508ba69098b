<template>
    <div>
        <van-nav-bar title="货位详情" left-arrow @click-left="onClickLeft" />
        <!-- <van-field label="账套" :value="info.bookname" readonly /> -->
        <van-field label="仓库" :value="info.storeName" readonly />
        <van-field label="货位" :value="info.rackName" readonly />
        <van-field label="产品编码" :value="info.code" readonly />
        <van-field label="产品名称" :value="info.name" readonly />
        <van-field label="库存辅数量" :value="info.quantity" readonly />
        <van-field label="库存主数量" :value="info.mainQuantity" readonly />
        <van-field label="冻结辅数量" :value="info.frozenQuantity" readonly />
        <van-field label="冻结主数量" :value="info.frozenMainQuantity" readonly />
        <!-- <van-field label="状态" :value="info.status" readonly /> -->

        <van-button round block type="info" @click="goodsLocationAdjust">货位调整</van-button>
    </div>
</template>

<script>
export default {
    data() {
        return {
            info: {}
        }
    },
    created() {
        if (localStorage.getItem("GoodsLocationItem")) {
            this.info = JSON.parse(localStorage.getItem("GoodsLocationItem"))
        } else {
            this.$router.replace({
                name: "GoodsLocation"
            });
        }

    },
    methods: {
        onClickLeft() {
            this.$router.replace({
                name: "GoodsLocation"
            });
        },
        goodsLocationAdjust() {
            this.$router.replace({
                name: "GoodsLocationAdjust"
            });
        },
    },
}
</script>

<style scoped>

</style>