<template>
    <div>
        <van-sticky :offset-top="0">
            <van-nav-bar title="盘点详情" right-text="筛选" left-arrow @click-left="onClickLeft"
                @click-right="onClickRight" />
        </van-sticky>

        <van-popup v-model="show" position="bottom" :style="{ height: '20%' }">
            <van-field v-model="product" clearable label="模具：" placeholder="请输入模具" />
            <van-button type="info" @click="search" style="width: 90%;">
                确定
            </van-button>
        </van-popup>

        <div v-for="(item, index) in spareList" :key="index"
            style="text-align: left; margin-bottom: 3%;background-color: #fbf8fb;padding:3%;border-radius: 5px;"
            @click="goDetail(item)">
            <van-row>
                <van-col span="24">名称:{{ item.name }}</van-col>
            </van-row>
            <van-row>
                <van-col span="12">编号:{{ item.code }}</van-col>
                <van-col span="12">货位:{{ item.location }}</van-col>
            </van-row>

            <van-row>
                <van-col span="8">现存量:{{ item.stock }}</van-col>
                <van-col span="8">盘点量:{{ item.checkStock }}</van-col>
                <van-col span="8">差异:{{ item.balance }}</van-col>
            </van-row>
            <van-row>
                <van-col span="8">单位:{{ item.unit }}</van-col>
            </van-row>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            show: false,
            item: {},
            product: '',
            spareList: [],
        }
    },
    created() {
        this.item = JSON.parse(localStorage.getItem("ModuleCheckItem"))
        console.log(this.item)
        // 获取列表
        this.getDetailList()
    },
    methods: {
        goDetail(item) {
            if (this.item.status == '2') {
                Toast({
                    message: '盘点已结束',
                    position: "bottom",
                    duration: 2000
                })
            } else {
                localStorage.setItem('ModuleCheckDetailEditItem', JSON.stringify(item))
                this.$router.push({ name: "ModuleCheckDetailEdit" })
            }
        },
        getDetailList() {
            this.$axios
                .get(`/jeecg-boot/ncApp/moldsCheck/getDetailList?id=${this.item.id}&userCode=${localStorage.getItem('userCode')}&product=${this.product}`)
                .then(res => {
                    if (res.data.code == 200) {
                        console.log("@", res.data.result);
                        this.spareList = res.data.result

                    } else {
                        Toast({
                            message: res.data.message,
                            position: "bottom",
                            duration: 2000
                        });
                    }
                });
        },
        search() {
            this.show = false
            this.getDetailList()
        },
        onClickLeft() {
            this.$router.replace({ name: "ModuleCheck" })
        },
        onClickRight() {
            this.show = true;
        },
    },
}
</script>

<style scoped>
</style>