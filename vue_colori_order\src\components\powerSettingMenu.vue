<template>
    <div>
        <img src="../../static/images/powerSettingMenuPng.png" width="100%" />
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/FeedingCheckDetail7.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>拉料加料</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('2')">
                <img src="../../static/images/FeedingCheckDetail8.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>投料</p>
            </div>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
        }
    },
    created() {
    },
    methods: {
        goDetail(i) {
            if (i == 1) {
                this.getPermissionByColloid("2");
            } else if (i == 2) {
                this.getPermissionByColloid("12");
            }
        },
        getPermissionByColloid(type) {
            let self = this
            self.$axios.get('/jeecg-boot/app/tank/check/getPermissionByColloid', { params: { userCode: localStorage.getItem("userCode"), type: type } }).then(res => {
                if (res.data.success) {
                    if (res.data.result >= 1) {
                       if (type == '2') {
                            self.$router.push({ path: '/powerSetting?type='+type });
                        } else if (type == '12') {
                            self.$router.push({ path: '/powerSetting?type='+type });
                        }
                    } else {
                        Toast({
                            message: "对不起，您没有权限！",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
    },
}
</script>

<style scoped>
.menu_order_more_item {
    float: left;
    height: 100%;
    width: 25%;
}

.menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}
</style>