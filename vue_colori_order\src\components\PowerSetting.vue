<template>
    <div class="order">

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px">权限控制</div>
        <van-field label="员工编号"  v-model="userCode" @change="getUserName"/>
        <van-field label="员工姓名" :value="userName"  readonly/>
        <van-field label="权限类型" :value="power" readonly @click="showPower=true"/>

        <van-button type="primary" @click="settingPower()"  style="margin:10px;width:40%;border-radius:10px;">设置</van-button>
        <van-button type="info" @click="searchPower()" :loading="loading" loading-type="spinner" style="margin:10px;width:40%;border-radius:10px;">查询</van-button>

        <van-popup  v-model="showPower" position="bottom">
            <van-picker :columns="powerList"  @cancel="onCancel" @confirm="onConfirm" confirm-button-text='确定' cancel-button-text='取消' :show-toolbar='true' :default-index="0"/>
        </van-popup>

        <div style="text-align:left;font-size:16px;font-weight:800;padding-top:10px;padding-bottom:10px" v-if="personList.length>0">人员清单</div>

        <div v-for="(item,index) in personList" :key="index" style="text-align:left;padding:5px;margin:5px;background:#FFF;" @click="deletePower(item)">
            

            <div style="width:100%;font-size:14px;color:#888888">员工编号：{{item.code}}</div>
            <div style="width:100%;font-size:14px;color:#888888">员工名称：{{item.name}}</div>
            <div style="width:100%;font-size:14px;color:#888888">权限类型：{{item.type=='3'?'拉料':(item.type=='4'?'加料':'投料')}}</div>


            <div style="clear:both"></div>
        </div>

    </div>
</template>
<script>
import { Indicator,MessageBox   } from 'mint-ui';
import { Notify,Toast } from 'vant';

let wx=window.wx

export default {
    data(){
        return{
            powerList:["拉料","加料","投料"],
            showPower:false,
            userCode:'',
            userName:'',
            power:'拉料',
            personList:[],
            loading:false,
            type:''
        }
    },
    created:function(){
        this.type=this.$route.query.type;
        if(this.type==2){
            this.power='加料';
            this.powerList=["拉料","加料"];
        }else if(this.type==12){
            this.power='投料';
            this.powerList=['投料'];
        }
        this.searchPower();
    },
    methods:{
        deletePower(item){
            let self=this
            let power="";
            if(item.type=='3'){
                power="拉料";
            }else if(item.type=='4'){
                power="加料";
            }else if(item.type=='5'){
                power="投料";
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            MessageBox.confirm('',{
                message: "是否确定删除"+item.name+"的"+power+"权限？",
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        self.$axios.delete('/jeecg-boot/app/tank/check/deletePermissionByColloid',{params:{id:item.id}}).then(res=>{
                            if(res.data.code==200){
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.searchPower();
                            }else{
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                Indicator.close();     
            });
        },
        searchPower(){
            let self=this
            let value="";
            if(self.power=='拉料'){
                value="3";
            }else if(self.power=='加料'){
                value="4";
            }else if(self.power=='投料'){
                value="5";
            }
            Indicator.open({
                text: '正在加载中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.loading=true
            self.$axios.get('/jeecg-boot/app/tank/check/queryPermissionUserByType',{params:{userCode:this.userCode,type:value}}).then(res=>{
                if(res.data.code==200){
                    Indicator.close();
                    self.loading=false;
                    self.personList=res.data.result
                }else{
                    self.loading=false;
                    Indicator.close();
                }
            })
        },
        settingPower(){
            let self=this
            let value="";
            if(self.power=='拉料'){
                value="3";
            }else if(self.power=='加料'){
                value="4";
            }else if(self.power=='投料'){
                value="5";
            }
            let params={
                code:self.userCode,
                name:self.userName,
                type:value
            }

            if(self.userCode==null || self.userCode==''){
                Toast({
                    message: "请输入员工编号",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }

            if(self.userName==null || self.userName==''){
                Toast({
                    message: "请输入员工姓名",
                    position: 'bottom',
                    duration: 2000
                });
                return;
            }
            
            MessageBox.confirm('',{
                message: "是否确定将"+self.userName+"设置"+self.power+"权限？",
                title: '提示',
                }).then(action => {
                    if(action=="confirm"){
                        Indicator.open({
                            text: '正在加载中，请稍后……',
                            spinnerType: 'fading-circle'
                        });
                        self.$axios.post('/jeecg-boot/app/tank/check/savePermissionByColloid',params).then(res=>{
                            if(res.data.code==200){
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                                self.searchPower();
                            }else{
                                Indicator.close();
                                Toast({
                                    message: res.data.message,
                                    position: 'bottom',
                                    duration: 2000
                                });
                            }
                        })
                    }
                }).catch((res)=>{
                        Indicator.close();    
                });
        },
        onCancel(){
            this.showPower=false
        },
        onConfirm(value){
            let self=this
            self.power=value
            self.showPower=false
        },
        getUserName(){
            let self = this
            self.$axios.get('/jeecg-boot/app/tank/check/getUserNameByCode',{params:{userCode:self.userCode}}).then(res=>{
                if(res.data.code==200){
                    self.userName=res.data.message
                }
            })
        },
        formatDate (secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.order{
    background-color: #ebecf7;
    display: block;
    min-height: 100%;
}

</style>