<template>
  <van-popup v-model="visible" position="bottom" :style="{ height: '90%', width: '100%' }" closeable
    close-icon-position="top-right">
    <div style="padding: 20px;">
      <h3 style="margin: 0 0 20px 0; text-align: center;">选择人员</h3>

      <!-- 搜索表单 -->
      <van-form>
        <van-field v-model="queryParam.leader" clearable label="组长" placeholder="请输入组长" />
        <van-field v-model="queryParam.userCode" clearable label="用户编码" placeholder="请输入用户编码" />
        <van-field v-model="queryParam.userName" clearable label="用户姓名" placeholder="请输入用户姓名" />
        <van-field v-model="queryParam.groupcode" clearable label="组名" placeholder="请输入组名" />

        <div style="margin: 15px 0;">
          <van-button type="primary" @click="searchByQuery" style="width: 48%; margin-right: 4%;" icon="search">
            查询
          </van-button>
        </div>
      </van-form>

      <!-- 人员列表 -->
      <div style="max-height: 250px; overflow-y: auto;">
        <van-checkbox-group v-model="selectedIds" @change="onSelectionChange">
          <div v-for="(item, index) in dataSource" :key="index" style="
              display: flex;
              align-items: center;
              padding: 12px;
              margin-bottom: 8px;
              background-color: #f7f8fa;
              border-radius: 6px;
            ">
            <van-checkbox :name="item.id" style="margin-right: 12px;" />
            <van-row style="width: 90%;">
              <van-col span="8" style="text-align: left;">{{ item.name }}</van-col>
              <van-col span="8" style="text-align: center;">{{ item.code }}</van-col>
              <van-col span="8" style="text-align: right;">{{ item.station }}</van-col>
            </van-row>
          </div>
        </van-checkbox-group>

        <div v-if="dataSource.length === 0 && !loading" style="text-align: center; color: #666; padding: 40px 0;">
          暂无人员数据，请调整搜索条件
        </div>

        <div v-if="loading" style="text-align: center; padding: 20px;">
          <van-loading size="24px">加载中...</van-loading>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
        <div style="margin-bottom: 10px; color: #666; text-align: center;">
          已选择 {{ selectedWorkers.length }} 人
        </div>
        <van-button type="primary" @click="handleConfirm" style="width: 100%; margin-bottom: 10px;"
          :disabled="selectedWorkers.length === 0">
          确认选择
        </van-button>
        <van-button @click="handleCancel" style="width: 100%;">
          取消
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Toast } from 'vant';

export default {
  name: "WorkerSelectModal",
  data() {
    return {
      visible: false,
      loading: false,

      // 查询参数
      queryParam: {
        leader: '',
        userCode: '',
        userName: '',
        groupcode: ''
      },

      // 数据
      dataSource: [],
      selectedIds: [],
      selectedWorkers: [],

      // 表单数据（从父组件传入）
      formData: {},

      // API URL
      url: {
        getWorkerList: "/jeecg-boot/app/gcWorkCategory/getWorkerList"
      }
    }
  },

  methods: {
    // 显示弹窗
    show(formData) {
      this.formData = formData || {};
      this.resetData();
      this.visible = true;
      // 默认查询一次数据
      this.loadData();
    },

    // 重置数据
    resetData() {
      this.queryParam = {
        leader: '',
        userCode: '',
        userName: '',
        groupcode: ''
      };
      this.dataSource = [];
      this.selectedIds = [];
      this.selectedWorkers = [];
    },

    // 加载数据
    loadData() {
      // 检查查询条件
      const { leader, userCode, userName, groupcode } = this.queryParam;
      if (!leader && !userCode && !userName && !groupcode) {
        Toast({
          message: '请至少输入一个查询条件',
          position: 'bottom'
        });
        return;
      }

      this.loading = true;

      // 过滤空值参数
      const params = {};
      Object.keys(this.queryParam).forEach(key => {
        if (this.queryParam[key]) {
          params[key] = this.queryParam[key];
        }
      });

      this.$axios.get(this.url.getWorkerList, { params })
        .then(res => {
          this.loading = false;
          if (res.data.success) {
            this.dataSource = res.data.result || [];
          } else {
            this.dataSource = [];
            Toast({
              message: res.data.message || '查询失败',
              position: 'bottom'
            });
          }
        })
        .catch(error => {
          this.loading = false;
          this.dataSource = [];
          Toast({
            message: '网络错误，请重试',
            position: 'bottom'
          });
          console.error('API Error:', error);
        });
    },

    // 查询
    searchByQuery() {
      this.loadData();
    },

    // 重置搜索条件
    searchReset() {
      this.queryParam = {
        leader: '',
        userCode: '',
        userName: '',
        groupcode: ''
      };
      this.dataSource = [];
      this.selectedIds = [];
      this.selectedWorkers = [];
    },

    // 选择变化
    onSelectionChange(selectedIds) {
      this.selectedWorkers = this.dataSource.filter(item =>
        selectedIds.includes(item.id)
      );
    },

    // 确认选择
    handleConfirm() {
      if (this.selectedWorkers.length === 0) {
        Toast({
          message: '请至少选择一个人员',
          position: 'bottom'
        });
        return;
      }

      this.$emit('confirm', this.selectedWorkers);
      this.handleCancel();
    },

    // 取消
    handleCancel() {
      this.visible = false;
      this.resetData();
    }
  }
}
</script>

<style scoped>
.van-popup {
  border-radius: 20px 20px 0 0;
}

.van-checkbox {
  margin-right: 12px;
}
</style>
