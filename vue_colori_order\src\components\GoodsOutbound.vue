<template>
    <div>
        <img src="../../static/images/GoodsOutbound.png" width="100%" />
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('1')">
                <img src="../../static/images/ModuleInfo.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>货位查询</p>
            </div>
        </div>
        <div class="menu_order_more">
            <div class="menu_order_more_item" @click="goDetail('2')">
                <img src="../../static/images/PositionAdjustment.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>指派</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('3')">
                <img src="../../static/images/picking.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>拣货</p>
            </div>
            <div class="menu_order_more_item" @click="goDetail('4')">
                <img src="../../static/images/pickingCheck.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>复核</p>
            </div>

            <div class="menu_order_more_item" @click="goDetail('5')">
                <img src="../../static/images/pickingOver.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p>完结</p>
            </div>

        </div>

    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
        }
    },
    created() {

    },
    methods: {
        goDetail(i) {
            if (i == 1) {
                this.$router.push({ name: "GoodsLocation" })
            } else if (i == 2) {
                this.$router.push({ name: "GoodsAssigned" })
            } else if (i == 3) {
                this.$router.push({ name: "GoodsPick" })
            } else if (i == 4) {
                this.$router.push({ name: "GoodsCheck" })
            } else if (i == 5) {
                this.$router.push({ name: "GoodsPickOver" })
            } else if (i == 6) {
                this.$router.push({ name: "" })
            }
        },
    },
}
</script>

<style scoped>
.menu_order_more_item {
    float: left;
    height: 100%;
    width: 25%;
}

.menu_order_more {
    background: #f8f3f3;
    border-radius: 10px;
    margin-top: 5%;
    margin-left: 5%;
    width: 90%;
    height: 5.5rem;
}
</style>