<template>
    <div>
        <div>
            <img :src="home" class="factory" />
            <img :src="logo" class="logo" />
            <van-field label="用户名" v-model="userCode" class="field1" />
            <van-field label="密码" v-model="password" class="field2" />
            <van-button type="primary" class="loginBtn" @click="submit()">登陆</van-button>
        </div>
    </div>
</template>

<script>
import { DatetimePicker, Toast, MessageBox, Indicator } from 'mint-ui';
export default {
    components: {
        Toast
    },
    data() {
        return {
            home: require('../../static/images/home.png'),
            logo: require('../../static/images/colori_logo.png'),
            userCode: '',
            password: '',
            staffInfo: {}
        };
    },
    created: function () {

    },
    methods: {
        submit() {
            let self = this
            Indicator.open({
                text: '处理中，请稍后……',
                spinnerType: 'fading-circle'
            });
            self.$axios.get(`/jeecg-boot/app/external/login?userCode=${self.userCode}&password=${self.password}`).then(res => {
                if (res.data.code == 200) {
                    console.log("🚀 ~ self.$axios.get ~ res.data:", res.data)
                    Indicator.close();
                    self.staffInfo = res.data.result;
                    localStorage.setItem('equiUserCode', res.data.result.userCode);
                    localStorage.setItem('equiUserId', res.data.result.id);
                    localStorage.setItem('equiUserName', res.data.result.userName);
                    self.$router.push({ path: '/equiMenu', query: { id: this.$route.query.id, type: this.$route.query.type } })
                } else {
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 1000
                    });
                }
            });
        }

    }
};
</script>
<style scoped>
.page-tabbar-container {
    height: 100%;
}

.factory {
    background-size: 100% 100%;
    height: 100%;
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 1;
}

.logo {
    z-index: 99;
    position: absolute;
    top: 150px;
    width: 41%;
    left: 30%;
}

.field1 {
    position: absolute;
    z-index: 99;
    top: 35%;
}

.field2 {
    position: absolute;
    z-index: 99;
    top: 40%;
}

.loginBtn {
    position: absolute;
    z-index: 999;
    top: 50%;
    left: 30%;
    width: 40%;
}
</style>