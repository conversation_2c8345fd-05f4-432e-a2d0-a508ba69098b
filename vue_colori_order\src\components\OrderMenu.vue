<template>
    <div style="background:#f3f4f6;min-height:100%">
        <img :src="my_hour" width="100%" />
        <div style="color:black;font-weight:800;text-align:left;padding:10px;">生产管理</div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('1')">
                <img src="../../static/images/orderMenu.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">车间工单</p>
            </div>
            <div class="menu_order_item" @click="workMode('111')">
                <img src="../../static/images/orderMenu.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">车间工单（新）</p>
            </div>

            <div class="menu_order_item" @click="workMode('23')">
                <img src="../../static/images/sunhao.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">灌包损耗</p>
            </div>

            <div class="menu_order_item" @click="workMode('12')">
                <img src="../../static/images/moduleOperation.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">模具管理</p>
            </div>

        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('16')">
                <img src="../../static/images/FeedingCheck.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">投料校验</p>
            </div>

            <div class="menu_order_item" @click="workMode('21')">
                <img src="../../static/images/laliao.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">胶体拉料</p>
            </div>


            <div class="menu_order_item" @click="workMode('22')">
                <img src="../../static/images/jialiao.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">胶体灌装</p>
            </div>

        </div>

        <div class="menu_order">

            <div class="menu_order_item" @click="workMode('24')">
                <img src="../../static/images/sczs.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">生产追溯</p>
            </div>

            <div class="menu_order_item" @click="workMode('25')">
                <img src="../../static/images/power.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">权限管理</p>
            </div>

            <div class="menu_order_item" @click="workMode('30')">
                <img src="../../static/images/tank.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">坦克管理</p>
            </div>
            
            <!-- <div class="menu_order_item" @click="workMode('27')">
                <img src="../../static/images/weighing.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">物料称重</p>
            </div> -->
        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('36')">
                <img src="../../static/images/deviceCleanPng.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">设备清洗</p>
            </div>
            <div class="menu_order_item" @click="workMode('34')">
                <img src="../../static/images/bcjy.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">包材校验</p>
            </div>
            <div class="menu_order_item" @click="workMode('35')">
                <img src="../../static/images/binding.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">箱托绑定</p>
            </div>
        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('38')">
                <img src="../../static/images/batch.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">批次维护</p>
            </div>
            <div class="menu_order_item" @click="workMode('26')" v-if="keyFlag">
                <img src="../../static/images/key.png" style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">秘钥设置</p>
            </div>
        </div>
        <div style="color:black;font-weight:800;text-align:left;padding:10px;">质量管理</div>
        <div class="menu_order2">

            <div class="menu_order_item" @click="workMode('2')">
                <img src="../../static/images/productControl.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">生产管制</p>
            </div>

            <div class="menu_order_item" @click="workMode('20')">
                <img src="../../static/images/jtjc.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">胶体检验</p>
            </div>


            <div class="menu_order_item" @click="workMode('3')">
                <img src="../../static/images/xj.png" style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">BOS巡检表</p>
            </div>

        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('28')">
                <img src="../../static/images/batchNoToCodeImg.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">批次号对照表</p>
            </div>

            <div class="menu_order_item" @click="workMode('29')">
                <img src="../../static/images/batchRecordPng.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">制胶批记录</p>
            </div>

        </div>
        <div style="color:black;font-weight:800;text-align:left;padding:10px;">仓库管理</div>

        <div class="menu_order2">

            <div class="menu_order_item" @click="workMode('6')">
                <img src="../../static/images/warehouse.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">成品入库</p>
            </div>


            <div class="menu_order_item" @click="workMode('13')">
                <img src="../../static/images/warehouseOut.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">成品出库</p>
            </div>


            <div class="menu_order_item" @click="workMode('14')">
                <img src="../../static/images/warehouse.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">材料入库</p>
            </div>


        </div>

        <div style="color:black;font-weight:800;text-align:left;padding:10px;">设备管理</div>

        <div class="menu_order2">

            <div class="menu_order_item" @click="workMode('7')">
                <img src="../../static/images/RepairOrder.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">维修工单</p>
            </div>
            <div class="menu_order_item" @click="workMode('4')">
                <img src="../../static/images/qrCode.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">PM表</p>
            </div>

            <div class="menu_order_item" @click="workMode('5')">
                <img src="../../static/images/smrk.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">点检表</p>
            </div>



        </div>

        <div class="menu_order">

            <div class="menu_order_item" @click="workMode('15')">
                <img src="../../static/images/equipmentScheduling.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">设备排班</p>
            </div>

            <div class="menu_order_item" @click="workMode('9')">
                <img src="../../static/images/SparePart.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">备件管理</p>
            </div>

            <div class="menu_order_item" @click="workMode('10')">
                <img src="../../static/images/SpareCheck.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">备件盘点</p>
            </div>
        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('31')">
                <img src="../../static/images/improvementImg.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">设备改进</p>
            </div>
            <div class="menu_order_item" @click="workMode('32')">
                <img src="../../static/images/emd016.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">EMD016</p>
            </div>
            <div class="menu_order_item" @click="workMode('33')">
                <img src="../../static/images/emd021.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">EMD021</p>
            </div>
        </div>
        <div class="menu_order">
            <div class="menu_order_item" @click="workMode('37')">
                <img src="../../static/images/emd022.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">EMD022</p>
            </div>
            <div class="menu_order_item" @click="workMode('39')">
                <img src="../../static/images/equipmentInspectionPng.png"
                    style="display:block;margin:0 auto;height:50%;padding-top:10%" />
                <p style="padding-top:0.5em;">通用巡检</p>
            </div>
        </div>
    </div>
</template>
<script>
import { DatetimePicker, Indicator, Toast } from 'mint-ui';
import { Calendar } from 'vant';

let wx = window.wx

export default {
    data() {
        return {
            dateVal: '', // 默认是当前日期
            c_show: false,
            popup_show: false,
            date: '',
            minDate: '',
            maxDate: '',
            userCode: '',
            userName: '',
            keyFlag:false,
            schedual: '开始排班',
            selectedValue: this.formatDate(new Date()),
            my_hour: require('../../static/images/my_menu.png'),
            bus: require('../../static/images/bus.png'),
            jt: require('../../static/images/jt.png'),
            fee: {},
            reports: {},
        }
    },
    components: {
        DatetimePicker
    },
    created: function () {
        let nowDate = new Date();
        this.minDate = new Date(nowDate.getTime() - 90 * 24 * 3600 * 1000);
        this.maxDate = nowDate
        this.date = this.formatDate(new Date)
        this.userCode = localStorage.getItem('userCode')
        this.userName = localStorage.getItem('userName')
        this.setWxInfo();

        this.$axios.get(`/jeecg-boot/app/utils/getSecretKeyPower?userCode=${localStorage.getItem('userCode')}`)
            .then(res => {
                if (res.data.code == 200) {
                    if(res.data.result>0){
                        this.keyFlag=true;
                    }
                } else {
                    Toast({
                        message: res.data.msg,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });

        this.$axios
            .get(`/jeecg-boot/ncApp/parts/getDepartmentByUserCode?userCode=${localStorage.getItem('userCode')}`)
            .then(res => {
                if (res.data.code == 200) {
                    localStorage.setItem('department', res.data.message)
                } else {
                    Toast({
                        message: res.data.msg,
                        position: "bottom",
                        duration: 2000
                    });
                }
            });
    },
    methods: {
        setWxInfo() {
            let self = this
            let url = window.location.href.split("#")[0];
            self.$axios.get('/jeecg-boot/app/wx/getWxInfo', { params: { url: url } })
                .then(res => {
                    wx.config({
                        beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                        debug: false,
                        appId: res.data.result.appId,
                        timestamp: res.data.result.timestamp,
                        nonceStr: res.data.result.noncestr,
                        signature: res.data.result.signature,
                        jsApiList: [
                            'scanQRCode',

                            // 蓝牙
                            "openBluetoothAdapter",
                            "onBluetoothAdapterStateChange",
                            "onBluetoothDeviceFound",
                            "createBLEConnection",
                            "onBLEConnectionStateChange",
                            "getBLEDeviceServices",
                            "closeBluetoothAdapter",
                            "getBLEDeviceCharacteristics",
                            "getBluetoothAdapterState",
                            "getBluetoothDevices",
                            "closeBLEConnection",
                            "notifyBLECharacteristicValueChange",
                            "onBLECharacteristicValueChange",
                            "startBluetoothDevicesDiscovery",
                            "stopBluetoothDevicesDiscovery",
                            "writeBLECharacteristicValue",
                            "getSystemInfo",
                            "getConnectedBluetoothDevices",

                            
                        ]
                    });
                })
        },
        workMode(num) {
            let self = this
            if (num == 1) {
                if(localStorage.getItem("userCode").indexOf('N')!=-1 || localStorage.getItem("userCode")=='HI1208030005'){
                    this.$router.push({ path: '/africaOrderPlat' });
                }else{
                    this.$router.push({ path: '/orderPlat' });
                }
            }if (num == 111) {
                this.$router.push({ path: '/OrderPlatNew' });
            } else if (num == 2) {
                // if(this.userCode=="HI2002250004"){
                //     this.$router.push({path:'/productControl'});
                // }
                // Toast({
                //     message: "系统筹备上线中,敬请期待！",
                //     position: 'bottom',
                //     duration: 2000
                // });
                this.$router.push({ path: '/productControl' });
            } else if (num == 3) {
                this.$router.push({ path: '/workForQa' });
            } else if (num == 4) {
                this.$router.push({ path: '/pmMain' });
            } else if (num == 5) {
                this.$router.push({ path: '/spotMain' });
            } else if (num == 6) {
                this.$router.push({ path: '/storeSticker' });
                // wx.scanQRCode({
                //     desc: 'scanQRCode desc',
                //     needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
                //     scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
                //     success: function(res) {
                //         // 回调
                //         var result = res.resultStr;//当needResult为1时返回处理结果
                //         self.$router.push({name:"ScanResult",params:{result:result,type:'3'}})
                //     },
                //     error: function(res) {
                //         if (res.errMsg.indexOf('function_not_exist') > 0) {
                //             alert('版本过低请升级')
                //         }
                //     }
                // });
            } else if (num == 7) {
                this.$router.push({ path: '/RepairOrder' });
            } else if (num == 8) {
                this.$router.push({ path: '/TankConsume' });
            } else if (num == 9) {
                let department = localStorage.getItem('department')
                if (department == '设备') {
                    this.$router.push({ path: '/SparePart' });
                } else {
                    Toast({
                        message: "备件管理仅对设备部开放",
                        position: 'bottom',
                        duration: 2000
                    });
                }
            } else if (num == 10) {
                let department = localStorage.getItem('department')
                if (department == '财务' || department == '督察' || department == '设备') {
                    this.$router.push({ path: '/SpareCheck' });
                } else {
                    Toast({
                        message: "备件盘点仅对财务部、督察部、设备部开放",
                        position: 'bottom',
                        duration: 2000
                    });
                }
            } else if (num == 12) {
                this.$router.push({ path: '/ModuleOperation' });
            } else if (num == 13) {
                this.$router.push({ path: '/GoodsOutbound' });
            } else if (num == 14) {

            } else if (num == 15) {
                this.$router.push({ path: '/EquipmentScheduling' });
            } else if (num == 16) {
                this.$router.push({ path: '/Feeding' });
            } else if (num == 20) {
                this.getPermissionByColloid("1");
            } else if (num == 21) {
                this.getPermissionByColloid("3");
            } else if (num == 22) {
                this.getPermissionByColloid("4");
            }else if (num == 23) {
                this.$router.push({ path: '/gbUsageInfo' });
            } else if (num == 24) {
                this.$router.push({ path: '/ProductionTraceability' });
            } else if (num == 25) {
                // this.getPermissionByColloid("2");
                this.$router.push({ path: '/powerSettingMenu' });
            } else if (num == 26) {
                this.$router.push({ path: '/keyInfo' });
            } else if (num == 27) {
                this.$router.push({ path: '/materialsWeighing' });
            } else if (num == 28) {
                this.$router.push({ path: '/BatchNoToCode' });
            } else if (num == 29) {
                this.$router.push({ path: '/batchRecord' });
            } else if (num == 30) {
                this.$router.push({ path: '/tankInfo' });
            } else if (num == 31) {
                this.$router.push({ path: '/eqImprovement' });
            } else if (num == 32) {
                this.$router.push({ path: '/emd016List' });
            } else if (num == 33) {
                this.$router.push({ path: '/emd021List' });
            } else if (num == 34) {
                this.$router.push({ path: '/packaging' });
            } else if (num == 35) {
                this.$router.push({ path: '/binding' });
            } else if (num == 36) {
                this.$router.push({ path: '/deviceClean' });
            } else if (num == 37) {
                this.$router.push({ path: '/emd022List' });
            } else if (num == 38) {
                this.$router.push({ path: '/leadBatch' });
            } else if (num == 39) {
                this.$router.push({ path: '/equipmentInspectionList' });
            }

        },
        getPermissionByColloid(type){
            let self=this
            self.$axios.get('/jeecg-boot/app/tank/check/getPermissionByColloid',{params:{userCode:localStorage.getItem("userCode"),type:type}}).then(res=>{
                if(res.data.success){
                    if(res.data.result>=1){
                        if(type=='1'){
                            self.$router.push({ path: '/colloidCheck' });
                        }else if(type=='2'){
                            self.$router.push({ path: '/powerSetting' });
                        }else if(type=='3'){
                            self.$router.push({ path: '/colloidPull' });
                        }else if(type=='4'){
                            self.$router.push({ path: '/colloidPush' });
                        }
                    }else{
                        Toast({
                            message: "对不起，您没有权限！",
                            position: 'bottom',
                            duration: 2000
                        });
                    }
                }else{
                    Toast({
                        message: res.data.message,
                        position: 'bottom',
                        duration: 2000
                    });
                }
            })
        },
        formatDate(secs) {
            var t = new Date(secs)
            var year = t.getFullYear()
            var month = t.getMonth() + 1
            if (month < 10) { month = '0' + month }
            var date = t.getDate()
            if (date < 10) { date = '0' + date }
            var hour = t.getHours()
            if (hour < 10) { hour = '0' + hour }
            var minute = t.getMinutes()
            if (minute < 10) { minute = '0' + minute }
            var second = t.getSeconds()
            if (second < 10) { second = '0' + second }
            return year + '-' + month + '-' + date
        }
    }
}
</script>
<style scoped>
.hour {
    margin-left: 5%;
    width: 90%;
    background: #fff;
    border-radius: 10px;
    height: 11rem;
}

.hour_item {
    width: 100%;
    height: 2rem;
    padding: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.hour_other_item {
    width: 100%;
    height: 2rem;
    padding-left: 3%;
    padding-right: 3%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.hour_left {
    float: left;
    width: 45%;
}

.hour_right {
    float: left;
    width: 54%;
}

.attence_item4 {
    width: 90%;
    height: 2rem;
    padding-left: 3%;
}

.attence_circle {
    width: 5%;
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item_circle4 {
    background: #888888;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}

.item_top {
    float: left;
    width: 88%;
    padding-left: 3%;
    height: 50%;
    text-align: left;
    display: flex;
    align-items: center;
}

.menu_order_item {
    float: left;
    height: 100%;
    width: 33%;
}

.menu_order {
    background: white;
    width: 100%;
    height: 5.5rem;
}

.menu_order2 {
    background: white;
    width: 100%;
    margin-top: 10px;
    height: 5.5rem;
}
</style>