<template>
    <div>
        <van-nav-bar title="备件详情" left-arrow @click-left="onClickLeft" right-text="调整货位"
            @click-right="changeLocation" />
        <van-field label="所属设备" :value="info.machine" readonly />
        <van-field label="厂家" :value="info.factory" readonly />
        <van-field label="名称" :value="info.name" readonly />
        <van-field label="编码" :value="info.code" readonly />
        <van-field label="品牌" :value="info.brand" readonly />
        <van-field label="分类" :value="info.category" readonly />
        <van-field label="采购周期" :value="info.period" readonly />
        <van-field label="现存量" :value="info.stock" readonly />
        <van-field label="安全库存" :value="info.safetyStock" readonly />
        <van-field label="规格" :value="info.spec" readonly />
        <van-field label="单位" :value="info.unit" readonly />
        <van-field label="型号" :value="info.model" readonly />
        <van-field label="货位" :value="info.location" readonly />


        <van-popup v-model="show" position="bottom" :style="{ height: '30%' }">
            <van-field v-model="location" clearable label="货位：" placeholder="请输入货位" />
            <van-button type="info" @click="change" style="width: 90%;">
                确定
            </van-button>
        </van-popup>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    data() {
        return {
            info: {},
            show: false,
            location: '',
        }
    },
    created() {
        this.info = JSON.parse(localStorage.getItem("SpareListItem"))
        this.location = ''
        console.log(this.info)
    },
    methods: {
        changeLocation() {
            this.location = ''
            this.show = true
        },
        change() {
            console.log(!this.location.trim());
            if (!this.location.trim()) {
                Toast({
                    message: '货位不能为空',
                    position: "bottom",
                    duration: 2000
                });
                return
            }
            this.$axios.post(`/jeecg-boot/ncApp/parts/locationEdit`,
                { id: this.info.id, userCode: localStorage.getItem('userCode'), location: this.location }
            ).then(res => {
                this.show = false
                if (res.data.success) {
                    this.info.location=this.location
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                } else {
                    Toast({
                        message: res.data.message,
                        position: "bottom",
                        duration: 2000
                    });
                }
            })
        },
        onClickLeft() {
            this.$router.push({
                name: "SpareList"
            });
        }
    },
}
</script>

<style scoped></style>